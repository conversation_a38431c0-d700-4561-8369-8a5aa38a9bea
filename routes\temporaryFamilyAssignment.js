const HttpStatus = require('../lib/constants/http-status');

const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const FactoryController = require('../modules/familyAssignment/controllers/assignment.controller');
const familyAssignmentService = require('../modules/familyAssignment/services/assignment.service');
const pensionsService = require('../modules/pensions/services/pension.service');

const FAMILY_ASSIGNMENT = '/regularizar-pensiones/inactivar-reactivar/reasignacion-familiar';

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    familyAssignmentService,
    pensionsService
  });

  router.get('/', validateAccess(), controller.getAll);
  router.get('/was-inactivated', validateAccess(), controller.wasInactivated);
  router.post(
    '/bulk',
    validateAccess(),
    startContextMiddleware,
    getUser(FAMILY_ASSIGNMENT),
    controller.insertAll
  );
  router.get('/exist', validateAccess(), controller.existTemporaryAssignment);
};

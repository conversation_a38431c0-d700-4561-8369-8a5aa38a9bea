/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const sharedService = require('./dbService');
const service2 = require('../../reservedAmountDisabilityPension/services/setReservedAmount.service');
const setValuesToReservedAmountPensions = require('../../../resources/setValuesToReservedAmountPension.json');
const ProcessedJobModel = require('../models/processedJob');

describe('zero values pension Test', () => {
  beforeAll(beforeAllTests);
  let pensionService2;

  beforeEach(() => {
    pensionService2 = {
      getAllAndFilter: jest.fn(() =>
        Promise.resolve({ result: setValuesToReservedAmountPensions })
      ),
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('set reserved amount on first pension', async () => {
    const { _doc } = setValuesToReservedAmountPensions[0];
    const result = service2.setReservedAmountPension(_doc);
    expect(result.reservedAmounts.forDisability).toBe(19.87);
  });

  it('set reserved amount on second pension for x.xx5 values rounded', async () => {
    const { _doc } = setValuesToReservedAmountPensions[1];
    const result = service2.setReservedAmountPension(_doc);
    expect(result.reservedAmounts.forDisability).toBe(19.01);
  });

  it('create mark', async () => {
    const yearExpected = '2020';
    const monthExpected = '04';
    const nameExpected = 'SetValuesToZero' || 'SetValuesToReservedAmount';
    const { month, year, name } = await sharedService.createProcessedFileMark(
      yearExpected,
      monthExpected,
      nameExpected
    );

    expect(month).toBe(4);
    expect(year).toBe(2020);
    expect(name).toBe('SetValuesToZero');
  });

  it('get Create Mark', async () => {
    const yearExpected = '2020';
    const monthExpected = '04';
    const nameExpected = 'SetValuesToZero' || 'SetValuesToReservedAmount';
    await sharedService.createProcessedFileMark(yearExpected, monthExpected, nameExpected);
    const result = await sharedService.getProcessedFileMark(
      yearExpected,
      monthExpected,
      nameExpected
    );
    expect(result).toBe(true);
  });

  it('processed the reserved amount pensions success', async () => {
    const result = await service2.reservedDisabilityPension(pensionService2);
    expect(result.completed).toBe(true);
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-console */
const moment = require('moment');
const mongoose = require('mongoose');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./ufValue.service');

describe('uf values tests', () => {
  beforeAll(beforeAllTests);

  it('should find previous month IPCS value from IPCS collection', async () => {
    const previousMonth = moment()
      .endOf('month')
      .subtract(32, 'days')
      .toDate();
    await mongoose.connection.db
      .collection('ipcs')
      .insertOne({ date: previousMonth, value: '103.55' });
    const { value } = await service.findPreviousMonthIPCS();
    expect(value).toBe('103.55');
  });

  it('should create UF value document', async () => {
    await service
      .createUfValue({ value: 28690.73, date: '2020-04-30' })
      .then(({ upserted }) => expect(upserted[0]._id).toBeDefined())
      .catch(e => console.error(e));
  });

  it('should get UF value document', async () => {
    const ufValue = await service.getCurrentUfValue().catch(e => console.error(e));
    expect(ufValue.value).toBe(28690.73);
    expect(ufValue.date.toString()).toBe(
      moment('2020-04-30', 'DD-MM-YYYY')
        .toDate()
        .toString()
    );
    expect(ufValue.period).toBe(moment().format('YYYY-MM'));
  });

  afterEach(async () => {
    await mongoose.connection.db
      .collection('ipcs')
      .deleteMany({})
      .catch(e => console.error(e));
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

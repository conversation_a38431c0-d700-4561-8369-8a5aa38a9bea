const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'manuallyInactivateMarkedPensions',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_MANUALLY_INACTIVATE_MARKED_PENSIONS_FREQUENCY,
  description: 'Inactivar pensiones marcadas manualmente',
  endPoint: 'manuallyinactivatemarkedpensions',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.markDependency
};

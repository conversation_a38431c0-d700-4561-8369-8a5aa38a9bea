const percentageDefined = [
  {
    validator(value) {
      return value.toString().trim();
    },
    message: field => {
      return `${field.path} Existen campos obligatorios vacíos`;
    }
  },
  {
    validator(value) {
      return /^\d+\.\d{4}$/gm.test(value);
    },
    message: field => {
      return `${field.path} Dato inválido`;
    }
  }
];

const valueDefined = [
  {
    validator(value) {
      return value.toString().trim();
    },
    message: field => {
      return `${field.path} Existen campos obligatorios vacíos`;
    }
  },
  {
    validator(value) {
      return /^\d{2,3}(\.\d{0,2})?$/.test(value);
    },
    message: field => {
      return `${field.path} Dato inválido`;
    }
  }
];

module.exports = { percentageDefined, valueDefined };

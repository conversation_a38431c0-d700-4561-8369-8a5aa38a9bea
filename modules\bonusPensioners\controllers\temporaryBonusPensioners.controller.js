/* eslint-disable consistent-return */
const service = require('../services/temporary.service');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  function manageDownloadError(res, error) {
    if (error.code === HttpStatus.NOTFOUND)
      res
        .status(HttpStatus.NOTFOUND)
        .json({ error: true, message: error.message })
        .end();

    manageError(res, error);
  }

  function manageImportError(res, error, result) {
    if (error.code === HttpStatus.BAD_REQUEST)
      res
        .status(HttpStatus.BAD_REQUEST)
        .json({ error: true, message: error.message, result })
        .end();

    manageError(res, error);
  }

  return {
    getFileString: async (req, res) => {
      const { isError, error, result } = await service.downloadBonusPensionersFileString();
      Logger.info('Get temporary bonus pensioners file');
      if (isError) {
        Logger.error(`Get bonus pensioners file error: ${error}`);
        manageDownloadError(res, error);
      }
      res.status(HttpStatus.OK).json(result);
    },
    postFileString: async (req, res) => {
      const file = req.body;
      const { isError, error, result } = await service.processFile(file);
      Logger.info('Import temporary bonus pensioners file');
      if (isError) {
        Logger.error(`Import temporary bonus pensioners file error: ${error}`);
        manageImportError(res, error, result);
      }
      res.status(HttpStatus.OK).json();
    }
  };
};

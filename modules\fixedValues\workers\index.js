const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const setFixedWorkerModule = require('./setFixedValues.worker');
const resetWorkerModule = require('./resetFixedValues.worker');
const { resetFixedValues, setFixedValues } = require('../modifiers');

module.exports = {
  setFixedBasePensionAndArticlesValue: {
    name: 'setFixedBasePensionAndArticlesValue',
    worker: async ({ Logger, done }) => {
      return setFixedWorkerModule.workerFn({ Logger, done, service, logService, setFixedValues });
    },
    repeatInterval: process.env.CRON_SET_FIXED_BASE_PENSION_VALUE_FREQUENCY,
    description: 'Fijar valores de pensión baje, art 40 y 41(SET)',
    endPoint: 'setfixedbasepensionandarticlesvalue',
    cronMark: setFixedWorkerModule.cronMark,
    dependencyMark: setFixedWorkerModule.cronDenpendency1
  },
  resetFixedBasePensionAndArticlesValue: {
    name: 'resetFixedBasePensionAndArticlesValue',
    worker: async ({ Logger, done }) => {
      return resetWorkerModule.workerFn({ Logger, done, service, logService, resetFixedValues });
    },
    repeatInterval: process.env.CRON_RESET_FIXED_BASE_PENSION_VALUE_FREQUENCY,
    description: 'Resetear (devolver) los valores fijados de penisón base, Art.40 y 41',
    endPoint: 'resetfixedbasepensionandarticlesvalue',
    cronMark: resetWorkerModule.cronMark,
    dependencyMark: resetWorkerModule.dependency
  }
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const rulesValidator = require('../../../models/basePensionRules');

const PensionModel = require('../../../models/pension');
const service = require('./base.pension.service');
const pensionsData = require('../../../resources/pensionMinimun.json');

const createCollection = name => mongoose.connection.db.createCollection(name, rulesValidator);
describe('Base pension service Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(async () => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);

    const basePensionModel = await createCollection('basePensionRules');

    const workAccident = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión por accidente de trabajo',
      age,
      type: 'pension',
      value: { minimun: '40.00', law19403: '100.00', law19539: '150.00', law19953: '200.00' }
    }));

    const widowhoodWithChildrens = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez con hijos',
      age,
      type: 'pension',
      value: { minimun: '100.00', law19403: '150.00', law19539: '200.00', law19953: '250.00' }
    }));

    await basePensionModel.insertMany([...widowhoodWithChildrens, ...workAccident]);
  });

  it('should return enabled pensions', async () => {
    pensionsData[0].pensionType = 'Pensión de viudez con hijos';
    pensionsData[0].dateOfBirth = moment('1980-01-01', 'YYYY-MM-DD');
    pensionsData[0].basePension = 20000.1;
    pensionsData[1].basePension = 24000.05;
    pensionsData[1].pensionType = 'Pensión de viudez con hijos';
    pensionsData[2].basePension = 150000.05;

    const newPensions = await service.calculateBasePension(pensionsData);
    expect(pensionsData.length).toBe(6);
    expect(newPensions.length).toBe(5);
    expect(newPensions[0].basePension).toBe(20000.1);
    expect(newPensions[0].law19403).toBe(0);
    expect(newPensions[0].law19539).toBe(0);
    expect(newPensions[0].law19953).toBe(0);
    expect(newPensions[1].basePension).toBe(24000.05);
    expect(newPensions[1].law19403).toBe(0);
    expect(newPensions[1].law19539).toBe(0);
    expect(newPensions[1].law19953).toBe(0);
    expect(newPensions[2].basePension).toBe(150000.05);
    expect(newPensions[2].law19403).toBe(0);
    expect(newPensions[2].law19539).toBe(0);
    expect(newPensions[2].law19953).toBe(0);
    expect(newPensions[3].basePension).toBe(40);
    expect(newPensions[4].basePension).toBe(40);
    expect(newPensions[4].law19403).toBe(0);
    expect(newPensions[4].law19539).toBe(0);
    expect(newPensions[4].law19953).toBe(0);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await mongoose.connection.db
      .dropCollection('basePensionRules')
      .catch(err => console.error(err));
    await PensionModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const Model = require('../../../models/discountsAndAssets');
const PensionModel = require('../../../models/pension');
const logService = require('../../sharedFiles/services/jobLog.service');
const { getNBusinessDaysService } = require('../../businessDays/services/businessDays.service');
const { getFirstNbusinessDays, getMonthHolidays } = require('../../sharedFiles/helpers');
const TaxableService = require('../../taxablePensions/services/dbService');
const liquidationService = require('../../liquidation/services/liquidation.service');
const {
  createLiquidationReport: liquidationReportService
} = require('../../netPensionsLiquidationReports/liquidationsReports/services/dbService');
const {
  calculateTotalAssetAndDiscount: calculateService
} = require('../../totalAssetsAndDiscounts/services/dbService');
const pensionService = require('../../pensions/services/pension.service');
const {
  netPension: netPensionService
} = require('../../netPensionsLiquidationReports/netPensions/services/dbService');
const afpService = require('../../nomenclators/afp/services/index.service');
const ufService = require('../../UFvalue/services/ufValue.service');
const cajaService = require('../../nomenclators/cajaCompensacion/services/index.service');

const {
  findOneAndUpdateWithTracking,
  createWithTracking,
  customDiffForAssetsAndDiscounts
} = require('../../trackingUserActivity/services/trackingUserActivity.service');

const service = {
  async findOneAndUpdate({
    _id,
    data,
    user,
    pensionModel = PensionModel,
    taxableService = TaxableService,
    calculateTotalAssetAndDiscount = calculateService,
    netPension = netPensionService,
    createLiquidationReport = liquidationReportService,
    findOneAndUpdateWithTrackingService = findOneAndUpdateWithTracking
  }) {
    try {
      const limitDay = process.env.DISCOUNTS_AND_ASSETS_NON_FORMULABLE_UPDATE_DEADLINE;
      const currentDate = moment()
        .startOf('day')
        .toDate();

      const { beneficiaryRut, causantRut } = await findOneAndUpdateWithTrackingService({
        model: Model,
        user,
        fullQuery: [{ _id }, { ...data }],
        customDiffAlgorithm: customDiffForAssetsAndDiscounts
      });

      const pension = await pensionModel
        .findOne({
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          enabled: true
        })
        .populate('discountsAndAssets');

      const { businessDays, error: businessDaysError } = await getNBusinessDaysService(
        currentDate,
        currentDate.getDate(),
        getFirstNbusinessDays,
        getMonthHolidays
      );

      if (businessDaysError) {
        throw new Error(businessDaysError.message);
      }

      const bDaysToCurrentDate = businessDays.filter(
        bDay => moment(bDay, 'YYYY-MM-DD').diff(moment(currentDate), 'days') <= 0
      );

      if (bDaysToCurrentDate.length > limitDay) {
        const pensions = pension ? [pension] : [];

        await calculateTotalAssetAndDiscount(beneficiaryRut, causantRut);
        const { error: taxableError } = await taxableService.taxablePension(
          pensions,
          liquidationService
        );
        if (taxableError) throw new Error(taxableError);

        const newPension = await pensionModel
          .findOne({
            'beneficiary.rut': beneficiaryRut,
            'causant.rut': causantRut,
            enabled: true
          })
          .lean();

        await netPension(
          pensionService,
          liquidationService,
          afpService,
          ufService,
          cajaService,
          newPension
        );
        await createLiquidationReport(newPension);
      }
      return { result: pension, isError: false, error: null };
    } catch (error) {
      return { result: null, isError: true, error };
    }
  },

  async create({ data, user }) {
    try {
      const result = await createWithTracking({ model: Model, user, document: data });
      return { result, isError: false, error: null };
    } catch (error) {
      return { result: null, isError: true, error };
    }
  },
  async isReadyToUpdate() {
    try {
      const nDays = process.env.DISCOUNTS_AND_ASSETS_UPDATE_DEADLINE;
      const bulkLoadIpsMark = 'UNIFIED_BULKLOAD_AND_IPS';
      const unifiedAssetsAndDiscountsMark =
        'UNIFIED_TOTAL_ASSETS_AND_DISCOUNTS_WITH_NET_PENSION_LIQUIDATIONS_REPORT';

      const currentDate = moment()
        .startOf('day')
        .toDate();

      const bulkLoadIpsWorkerExecuted = await logService.existsLog(bulkLoadIpsMark);
      const unifiedAssetsAndDiscountsExecuted = await logService.existsLog(
        unifiedAssetsAndDiscountsMark
      );
      const { businessDays, error } = await getNBusinessDaysService(
        currentDate,
        nDays,
        getFirstNbusinessDays,
        getMonthHolidays
      );

      if (error) {
        throw new Error(error);
      }

      const [lastBusinessDay] = businessDays.slice(-1);
      const hasBusinessDayPassed =
        moment(lastBusinessDay, 'YYYY-MM-DD').diff(moment(currentDate), 'days') < 0;
      const result = {
        bulkLoadIpsWorkerExecuted: !!bulkLoadIpsWorkerExecuted,
        unifiedAssetsAndDiscountsExecuted: !!unifiedAssetsAndDiscountsExecuted,
        hasBusinessDayPassed
      };
      return { result, error: null };
    } catch (error) {
      return { result: null, error };
    }
  },
  async isReadyToUpdateNonFormulable() {
    try {
      const nDays = process.env.DISCOUNTS_AND_ASSETS_NON_FORMULABLE_UPDATE_DEADLINE;
      const postLiquidationCheckpointReporMark = 'POST_LIQUIDATION_CHECKPOINT_REPORT';
      const unifiedUnifiedAndGenerateBankUploadMark = 'GENERATE_AND_UPLOAD_BANK_FILE';

      const currentDate = moment()
        .startOf('day')
        .toDate();

      const postLiquidationCheckpointReportWorkerExecuted = await logService.existsLog(
        postLiquidationCheckpointReporMark
      );
      const unifiedAndGenerateBankUploadExecuted = await logService.existsLog(
        unifiedUnifiedAndGenerateBankUploadMark
      );
      const { businessDays, error } = await getNBusinessDaysService(
        currentDate,
        nDays,
        getFirstNbusinessDays,
        getMonthHolidays
      );

      if (error) {
        throw new Error(error);
      }

      const [lastBusinessDay] = businessDays.slice(-1);
      const hasBusinessDayPassed =
        moment(lastBusinessDay, 'YYYY-MM-DD').diff(moment(currentDate), 'days') < 0;
      const result = {
        postLiquidationCheckpointReportWorkerExecuted: !!postLiquidationCheckpointReportWorkerExecuted,
        unifiedUnifiedAndGenerateBankUploadExecuted: !!unifiedAndGenerateBankUploadExecuted,
        hasBusinessDayPassed
      };
      return { result, error: null };
    } catch (error) {
      return { result: null, error };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const Model = require('../../../models/discountsAndAssets');
const PensionModel = require('../../../models/pension');
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('./dbService');

const discountsAndAssetsData = require('../../../resources/discountsAndAssets.json');
const pensions = require('../../../resources/pensions.json');

describe('DiscountsAndAssets service Test', () => {
  beforeAll(beforeAllTests);
  let taxableService;

  const pensionModel = {
    findOne: jest.fn(() => {
      return {
        populate: jest.fn(() =>
          Promise.resolve({ ...pensions, discountsAndAssets: { ...discountsAndAssetsData } })
        )
      };
    })
  };

  const discountsAndAssetsModel = {
    findByIdAndUpdate: jest.fn(() => {
      return {
        lean: jest.fn(() =>
          Promise.resolve({ beneficiaryRut: '11111111-1', causantRut: '99999999-9' })
        )
      };
    })
  };

  const findOneAndUpdateWithTrackingService = jest.fn(() =>
    Promise.resolve({ beneficiaryRut: '11111111-1', causantRut: '99999999-9' })
  );

  taxableService = {
    taxablePension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
  };

  const calculateTotalAssetAndDiscount = jest.fn(() => Promise.resolve());
  const netPension = jest.fn(() => Promise.resolve({ completed: true, error: null }));
  const createLiquidationReport = jest.fn(() => Promise.resolve());

  it('should find one and update', async () => {
    const { error, isError } = await service.findOneAndUpdate({
      discountsAndAssetsModel,
      pensionModel,
      taxableService,
      calculateTotalAssetAndDiscount,
      netPension,
      createLiquidationReport,
      findOneAndUpdateWithTrackingService
    });
    expect(error).toBe(null);
    expect(isError).toBe(false);
  });

  it('should find one and update but it fail', async () => {
    process.env.DISCOUNTS_AND_ASSETS_NON_FORMULABLE_UPDATE_DEADLINE = '0';
    pensionModel.findOne = jest.fn(() => {
      return {
        populate: jest.fn(() => Promise.resolve())
      };
    });
    taxableService = {
      taxablePension: jest.fn(() => Promise.resolve({ error: 'Error taxable service' }))
    };

    const { error } = await service.findOneAndUpdate({
      discountsAndAssetsModel,
      pensionModel,
      taxableService,
      calculateTotalAssetAndDiscount,
      netPension,
      createLiquidationReport,
      findOneAndUpdateWithTrackingService
    });
    expect(error).toStrictEqual(new Error('Error taxable service'));
  });

  it('should get if is ready to update discount and asset of pension', async () => {
    const {
      result: {
        bulkLoadIpsWorkerExecuted,
        unifiedAssetsAndDiscountsExecuted,
        hasBusinessDayPassed
      },
      error
    } = await service.isReadyToUpdate();

    expect(error).toBe(null);
    expect(hasBusinessDayPassed).toBe(false);
    expect(bulkLoadIpsWorkerExecuted).toBe(false);
    expect(unifiedAssetsAndDiscountsExecuted).toBe(false);
  });

  it('should create one and return it', async () => {
    const { error, result, isError } = await service.create({
      data: {
        ...discountsAndAssetsData,
        beneficiaryRut: '********-3',
        causantRut: '********-4'
      }
    });
    const { _id } = result;
    expect(_id).toBeDefined();

    expect(error).toBe(null);
    expect(isError).toBe(false);
    expect(result.beneficiaryRut).toBe('********-3');
  });

  it('should fail at create a new assetAndDiscount', async () => {
    jest
      .spyOn(Model, 'create')
      .mockImplementationOnce(() => Promise.reject(new Error('discounts and assets error')));
    const { error } = await service.create({
      ...discountsAndAssetsData,
      beneficiaryRut: '********-3',
      causantRut: '********-4'
    });

    expect(error).toStrictEqual(new Error('discounts and assets error'));
  });

  it('should get if is ready to update discount and asset non formulable  of pension', async () => {
    const {
      result: {
        postLiquidationCheckpointReportWorkerExecuted,
        unifiedUnifiedAndGenerateBankUploadExecuted,
        hasBusinessDayPassed
      },
      error
    } = await service.isReadyToUpdateNonFormulable();

    expect(error).toBe(null);
    expect(hasBusinessDayPassed).toBe(false);
    expect(postLiquidationCheckpointReportWorkerExecuted).toBe(false);
    expect(unifiedUnifiedAndGenerateBankUploadExecuted).toBe(false);
  });

  it('fail log service', async () => {
    jest.spyOn(logService, 'existsLog').mockImplementationOnce(() => {
      throw new Error('fail log service');
    });
    const { error } = await service.isReadyToUpdateNonFormulable();

    expect(error.message).toBe('fail log service');
  });
  afterEach(async () => {
    process.env.DISCOUNTS_AND_ASSETS_NON_FORMULABLE_UPDATE_DEADLINE = null;
    await Model.deleteMany({}).catch(e => console.error(e));
    await PensionModel.deleteMany({}).catch(e => console.error(e));
  });

  afterAll(afterAllTests);
});

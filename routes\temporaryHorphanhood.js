const HttpStatus = require('../lib/constants/http-status');

const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const FactoryController = require('../modules/orphanhood/controllers/orphanhood.controller');
const orphanhoodService = require('../modules/orphanhood/services/orphanhood.service');
const pensionsService = require('../modules/pensions/services/pension.service');

const HORPHANHOODENDPOINT = '/regularizar-pensiones/inactivar-reactivar/orfandad';

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    orphanhoodService,
    pensionsService
  });

  router.get('/was-inactivated', validateAccess(), controller.wasInactivatedOrphanhood);
  router.post(
    '/bulk',
    validateAccess(),
    startContextMiddleware,
    getUser(HORPHANHOODENDPOINT),
    controller.insertAllOrphanhood
  );
};

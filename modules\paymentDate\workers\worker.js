const cronDescription = 'calculate payment dates:';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = `${cronDescription} process completed successfully`;
const cronMark = 'CALCULATE_PAYMENT_DATES_WORKER';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({ Logger, logService, service, job, done }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`${cronDescription} process started`);
    const { err: errCurrent } = await service.generatePaymentDates(new Date().getFullYear());

    if (errCurrent) throw new Error(errCurrent);
    const { err: errNext } = await service.generatePaymentDates(new Date().getFullYear() + 1);
    if (errCurrent) throw new Error(errNext);

    Logger.info(successMessage);
    await logService.saveLog(cronMark);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

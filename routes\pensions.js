const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const CriteriaBuilder = require('../lib/builders/criteria-builder');
const validateAccess = require('../lib/auth/validate');
const FactoryController = require('../modules/linkPensions/controllers/link.controller');
const { setUserAndEndpointInfo } = require('../lib/middleware/setUserAndEndpointInfo');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const SEE_MORE_ENDPOINT = '/pensionados/consulta-pensionados/ver-mas';
const LINK_PENSION_ENDPOINT = '/nuevas-pensiones/importar';

module.exports = router => {
  const linkPensionsController = FactoryController(
    { HttpStatus, <PERSON>rro<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, CriteriaBuilder } // deps injected
  );

  router.get('/', validateAccess(), linkPensionsController.getAll);

  router.get('/link', validateAccess(), linkPensionsController.alreadyLinked);
  router.post(
    '/link',
    validateAccess(),
    startContextMiddleware,
    getUser(LINK_PENSION_ENDPOINT),
    linkPensionsController.link
  );
  router.get('/actionIsAllowed', validateAccess(), linkPensionsController.actionIsAllowed);
  router.delete('/link/annulment', validateAccess(), linkPensionsController.cancel);
  router.post('/validatePensionsToInsert', validateAccess(), (req, res) =>
    linkPensionsController.validatePensionsToInsert(req, res)
  );
  router.put(
    '/updateAssetsAndDiscountOfPension',
    validateAccess(),
    setUserAndEndpointInfo(SEE_MORE_ENDPOINT),
    linkPensionsController.updateAssetsAndDiscountOfPension
  );
};

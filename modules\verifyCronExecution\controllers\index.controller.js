module.exports = ({ HttpStatus, LogService, Logger }) => {
  return {
    verifyCronExecution: async (req, res) => {
      try {
        const { cronName } = req.params;
        Logger.info(`Verifying cron ${cronName} execution`);
        const result = await LogService.existsLog(cronName);
        const alreadyExecuted = !!result;
        res.status(HttpStatus.OK).json({ result: { alreadyExecuted, error: null } });
      } catch (error) {
        res
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json({ result: { alreadyExecuted: null, error } });
      }
    }
  };
};

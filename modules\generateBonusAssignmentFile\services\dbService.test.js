const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const resourcePensions = require('../../../resources/pensionsWinterBonus.json');
const resourcePensionsBonus = require('../../../resources/temporaryPensionerBonus.json');
const TemporaryBonusPensioners = require('../models/temporaryBonusPensioners');

describe('Create file bonus service test', () => {
  beforeAll(beforeAllTests);

  let mocks;

  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn()
    };
    jest.spyOn(TemporaryBonusPensioners, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('set pensioner bonus', async () => {
    await TemporaryBonusPensioners.create(resourcePensionsBonus);
    service.filterPensioners = jest.fn(() => Promise.resolve(resourcePensions));

    const { completed } = await service.setPensionersBonus();

    const count = await TemporaryBonusPensioners.countDocuments();

    expect(completed).toBe(true);
    expect(mocks.startTransaction).toBeCalled();
    expect(mocks.commitTransaction).toBeCalled();
    expect(mocks.abortTransaction).not.toBeCalled();
    expect(count).toBe(3);
  });

  afterAll(afterAllTests);
});

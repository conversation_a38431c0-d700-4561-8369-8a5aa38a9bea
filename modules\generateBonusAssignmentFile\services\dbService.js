const PensionModel = require('../../../models/pension');
const TemporaryBonusPensioners = require('../models/temporaryBonusPensioners');

const TYPE_VALIDITY = /No vigente/i;
const CODE_INSTITUTION = 34;
const RUT_INSTITUTION = 70360100;
const CHECK_DIGIT_INSTITUTION = '6';
const PAY_BONUS_DEFAULT = 'SI';

const PENSION_TYPES = [
  { typePension: 'Pensión por accidente de trabajo', idType: 1 },
  { typePension: 'Pensión por enfermedad profesional', idType: 2 },
  { typePension: 'Pensión por accidente de trayecto', idType: 3 },
  { typePension: 'Pensión de viudez con hijos', idType: 4 },
  { typePension: 'Pensión de viudez sin hijos', idType: 4 },
  { typePension: 'Pensión de madre de hijo de filiación no matrimonial con hijos', idType: 5 },
  { typePension: 'Pensión de madre de hijo de filiación no matrimonial sin hijos', idType: 5 },
  { typePension: 'Pensión por orfandad', idType: 6 },
  { typePension: 'Pensión de orfandad de padre y madre', idType: 6 }
];

const formatTypePension = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .replace(/[èé]/gi, 'e')
    .replace(/[áà]/gi, 'a')
    .toLowerCase()
    .trim();

const reduceTypeToObj = types => {
  return types.reduce((typeObj, type) => {
    const { typePension, idType } = type;
    const key = formatTypePension(typePension);
    return { ...typeObj, [key]: idType };
  }, {});
};

const service = {
  async filterPensioners() {
    return PensionModel.aggregate([
      {
        $match: {
          validityType: { $not: TYPE_VALIDITY },
          enabled: true
        }
      }
    ]);
  },

  async getPensionersBonus() {
    const pensioners = await this.filterPensioners();
    const typesObj = reduceTypeToObj(PENSION_TYPES);
    return pensioners.map(pensioner => {
      const {
        basePension,
        law19403,
        law19539,
        law19953,
        pensionType,
        pensionStartDate,
        dateOfBirth,
        numberOfCharges,
        beneficiary,
        article40
      } = pensioner;
      const { rut, name, lastName, mothersLastName } = beneficiary;
      const pensionBaseSum = Math.round(basePension + article40 + law19403 + law19539 + law19953);
      const rutAux = rut.split('-');
      const rutPensioner = rutAux.slice(0, 1).toString();
      const checkDigit = rutAux.slice(1, 2).toString();
      const idType = +typesObj[formatTypePension(pensionType)];
      const namePensioner = `${lastName} ${mothersLastName} ${name}`;

      return {
        pensionerRut: rutPensioner,
        pensionerCheckDigit: checkDigit,
        pensionerName: namePensioner,
        dateOfBirth,
        institutionCode: CODE_INSTITUTION,
        institutionRut: RUT_INSTITUTION,
        institutionCheckDigit: CHECK_DIGIT_INSTITUTION,
        pensionStartDate,
        pensionAmount: pensionBaseSum,
        numberOfCharges,
        pensionType: idType,
        payBonus: PAY_BONUS_DEFAULT
      };
    });
  },

  async createTemporaryPensionsBonus(bonusPensioners) {
    const session = await TemporaryBonusPensioners.startSession();
    session.startTransaction();
    try {
      await TemporaryBonusPensioners.deleteMany({}).exec();

      const bulk = TemporaryBonusPensioners.collection.initializeOrderedBulkOp();
      const promiseFunctions = bonusPensioners.map(pension => async () => {
        return bulk.insert({
          ...pension,
          updatedAt: new Date(),
          createdAt: new Date()
        });
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }
      if (bulk.length) {
        await bulk.execute();
      }
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async setPensionersBonus() {
    try {
      const bonusPensioners = await this.getPensionersBonus();
      const { completed, error } = await this.createTemporaryPensionsBonus(bonusPensioners);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

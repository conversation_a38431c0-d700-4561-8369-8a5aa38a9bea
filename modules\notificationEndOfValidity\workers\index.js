const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'send-notification-email',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SEND_NOTIFICATION_EMAIL,
  description: 'Pensionados que están prontos a expirar',
  endPoint: 'notificationendofvalidity',
  cronMark: workerModule.markOfCron,
  dependencyMark: workerModule.dependencyMark
};

const moment = require('moment');
const parseXmlToJSON = require('./xmlObjectManipulation');
const soapRequest = require('./ipcRequest');
const { roundValue } = require('../../sharedFiles/helpers');

const MAX_VARIATION = 10;
const READJUSTMENT_MONTH_IPC = 10;

const formatDate = date => moment(date, 'DD-MM-YYYY').toISOString();
const getVariation = (lastMonthIPC, actualIPC) => {
  if (!lastMonthIPC || !actualIPC) {
    throw new Error('Error trying to get the variation');
  }

  const actualVariation = (lastMonthIPC / actualIPC - 1) * 100;
  const [number = '0', decimals = '0000'] = `${actualVariation}`.split('.');
  const fourDecimals = decimals.slice(0, 4);
  return `${number}.${fourDecimals}`;
};

const calculateField = (field, percentageIPC) => {
  const value = field || 0;
  const percentage = percentageIPC || 0;
  return roundValue(Number(value) * (1 + Number(percentage) / 100));
};

const monthIsDecember = date => {
  const dateAux = new Date(date);
  return dateAux.getMonth() === READJUSTMENT_MONTH_IPC;
};

const isLastPercentageChange = (percentage, date) => {
  return +percentage >= MAX_VARIATION || monthIsDecember(date);
};

const createIpcObject = (previousMonth, previousMonthValue, lastVariation = {}) => {
  if (!previousMonth || !previousMonthValue || !Object.keys(lastVariation).length) {
    throw new Error('Empty values for ipc object creation');
  }
  const date = formatDate(previousMonth);
  const percentage = getVariation(previousMonthValue, lastVariation.value);
  return {
    date,
    value: previousMonthValue,
    percentage,
    isLastPercentageChange: isLastPercentageChange(percentage, date)
  };
};

const getPreviousMonthIPC = async parserFn => {
  return new Promise((resolve, reject) => soapRequest(resolve, reject))
    .then(async data => {
      const result = await parserFn(data);
      return { result, error: false };
    })
    .catch(error => {
      return { result: {}, error };
    });
};

module.exports = {
  getPreviousMonthIPC,
  calculateField,
  createIpcObject,
  parseXmlToJSON
};

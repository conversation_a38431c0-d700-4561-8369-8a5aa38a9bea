const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'SEND_CIRCULAR_FILE_2480';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const cronDescription = 'Generar y enviar archivo circular al SFTP';
const noDatafoundMessage = 'No datos encontrados para la generacion del archivo.';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({ Logger, done, service, logService, Sftp, fileGenerationUtils, job }) => {
  try {
    Logger.info(`${cronMark} Cron execution start. Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark} starting process. Generating circular file...`);
    const fileName = await service.generateFile(fileGenerationUtils);

    if (!fileName) {
      throw new Error(noDatafoundMessage);
    }

    Logger.info(`${cronMark} file generated succesfully to location: ${fileName}`);

    Logger.info(`${cronMark} Zipping and uploading circular file to SFTP server...`);
    const uploadedFile = await service.uploadFileToSftpServer({
      fileName,
      fileGenerationUtils,
      Sftp
    });
    Logger.info(`${cronMark} file zipped and uploaded. Zip file location: ${uploadedFile}`);

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark}: execution completed.`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

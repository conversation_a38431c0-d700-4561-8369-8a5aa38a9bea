const { check } = require('express-validator');

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const regRule = new RegExp(regex, 'i');

const codeRegex = /(\d{3})/;
const codeRule = new RegExp(codeRegex, 'i');
const validators = [
  check('servipag.city')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('servipag.name')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('servipag.address')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('servipag.code')
    .notEmpty()
    .withMessage('code should not be empty')
    .isLength({ min: 3, max: 3 })
    .withMessage('min a & max length should be 3')
    .matches(codeRule)
    .withMessage('code should match the format')
];

module.exports = validators;

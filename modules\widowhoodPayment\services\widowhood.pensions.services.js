const moment = require('moment');

const PensionModel = require('../../../models/pension');
const { roundValue } = require('../../sharedFiles/helpers');

const VALIDITY_TYPE = /No\s+vigente/i;
const PENSION_TYPES = [
  /Pensi[oó]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+sin\s+hijos/i
];

const INACTIVATION_REASON = /Matrimonio/i;
const LIMIT_AGE_WIDOWHOOD = 45;
const TWO_YEARS_IN_MONTH = 24;

const filterPensioners = async () => {
  const currentYear = moment().year();
  const currentMonth = moment().month();
  return PensionModel.find({
    validityType: { $in: [VALIDITY_TYPE] },
    pensionType: { $in: PENSION_TYPES },
    inactivationDate: {
      $gte: new Date(currentYear, currentMonth, 1),
      $lt: new Date(currentYear, currentMonth + 1, 1)
    },
    inactivationReason: { $in: [INACTIVATION_REASON] },
    enabled: true
  }).lean();
};

const calculateWidowhoodBonus = pensionerList => {
  return pensionerList.map(
    ({ dateOfBirth, endDateOfValidity, assets, basePension, ...pensionerData }) => {
      const ageWhenPensionExpires = moment(endDateOfValidity).diff(moment(dateOfBirth), 'years');
      const endDateOfValidityDiffNow = moment(endDateOfValidity)
        .startOf('month')
        .diff(moment().startOf('month'), 'months');

      if (endDateOfValidityDiffNow >= 0 && ageWhenPensionExpires >= LIMIT_AGE_WIDOWHOOD) {
        return {
          ...pensionerData,
          dateOfBirth,
          endDateOfValidity,
          basePension,
          assets: {
            ...assets,
            marriageBonus: roundValue(TWO_YEARS_IN_MONTH * basePension)
          }
        };
      }

      if (ageWhenPensionExpires >= LIMIT_AGE_WIDOWHOOD) {
        let SUBSTRACTION =
          TWO_YEARS_IN_MONTH -
          moment()
            .startOf('month')
            .diff(moment(endDateOfValidity).startOf('month'), 'months');

        SUBSTRACTION = Math.max(SUBSTRACTION, 0);

        return {
          ...pensionerData,
          dateOfBirth,
          endDateOfValidity,
          basePension,
          assets: {
            ...assets,
            marriageBonus: roundValue(SUBSTRACTION * basePension)
          }
        };
      }
      return {
        ...pensionerData,
        dateOfBirth,
        endDateOfValidity,
        basePension,
        assets: {
          ...assets,
          marriageBonus: 0
        }
      };
    }
  );
};

const service = {
  setWidowhoodBonus: async pensionService => {
    try {
      const pensioners = await filterPensioners();
      const updatedPensioners = calculateWidowhoodBonus(pensioners);
      const { completed, error } = await pensionService.updatePensions(updatedPensioners);

      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

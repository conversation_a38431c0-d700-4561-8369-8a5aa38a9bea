const removeFields = (req, ...fields) => {
  fields.forEach(element => {
    delete req[element];
  });
};

const entities = {
  amp: '&',
  apos: "'",
  lt: '<',
  gt: '>',
  quot: '"',
  nbsp: '\xa0'
};

const entityPattern = /&([a-z]+);/gi;

const decodeHtml = text => {
  return text.replace(entityPattern, function rep(match, value) {
    const entity = value.toLowerCase();
    if (Object.prototype.hasOwnProperty.call(entities, entity)) {
      return entities[entity];
    }
    return match;
  });
};

module.exports = {
  removeFields,
  decodeHtml
};

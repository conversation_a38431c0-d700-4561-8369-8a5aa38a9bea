const moment = require('moment');
const { checkIfAllProcessAreExecuted } = require('../../sharedFiles/helpers');

const getStatusForProcess = (haveNoMarks, executed, remainingDays) => {
  let result = { statusText: '-', statusColor: 'black' };

  if (haveNoMarks) {
    return result;
  }

  if (executed) {
    result = { statusText: 'Ejecutado', statusColor: 'green' };
  }
  if (!executed && remainingDays < 0) {
    result = { statusText: 'No se ejecutó', statusColor: 'red' };
  }
  if (!executed && remainingDays >= 0) {
    result = { statusText: 'Por ejecutar', statusColor: remainingDays > 2 ? 'gray' : 'orange' };
  }

  return result;
};

const getNBusinessDaysService = async (
  date,
  numberOfDays,
  getFirstNbusinessDays,
  getMonthHolidaysFn
) => {
  try {
    if (numberOfDays < 1 || numberOfDays > moment(date).daysInMonth())
      throw new Error('number of business days is more than days in month');
    const businessDays = await getFirstNbusinessDays(date, numberOfDays, getMonthHolidaysFn);
    return {
      businessDays,
      error: null
    };
  } catch (error) {
    return {
      businessDays: [],
      error
    };
  }
};

const getDiffInDays = (currentDate, dueDate) =>
  moment(dueDate, 'YYYY-MM-DD')
    .startOf('day')
    .diff(currentDate, 'days');

const getRemainingDaysForProcesses = async (getDueDaysList, businessDays, currentDate) => {
  try {
    const currentDayInMonth = moment(currentDate).startOf('day');
    const dueDaysList = getDueDaysList();

    const dueDaysForProcesses = await Promise.all(
      dueDaysList.map(async ({ lastDependencyMarks, numberOfDays, ...fields }) => {
        const remainingDays = getDiffInDays(currentDayInMonth, businessDays[numberOfDays - 1]);
        const dueDate = moment(businessDays[numberOfDays - 1], 'YYYY-MM-DD').format('DD-MM-YYYY');
        const executed = await checkIfAllProcessAreExecuted(lastDependencyMarks);
        const { statusText, statusColor } = getStatusForProcess(
          !lastDependencyMarks.length,
          executed,
          remainingDays
        );

        return {
          ...fields,
          remainingDays,
          dueDate,
          statusText,
          statusColor
        };
      })
    );

    return { dueDaysForProcesses, errorDueDaysForProcesses: false };
  } catch (error) {
    return { dueDaysForProcesses: [], errorDueDaysForProcesses: error };
  }
};

module.exports = { getNBusinessDaysService, getRemainingDaysForProcesses };

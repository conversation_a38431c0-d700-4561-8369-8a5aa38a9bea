/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./post.worker');
const ProcessedJobModel = require('../../../sharedFiles/models/processedJob');

describe('Inactivate by retirement Post-Worker test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let done;
  let logService;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(() => jest.fn()),
      error: jest.fn(() => jest.fn())
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });

  it('should call service to inactivate pensions if there is no error', async () => {
    Logger = {
      info: jest.fn(() => jest.fn()),
      error: jest.fn(() => jest.fn())
    };
    service = {
      inactivatePensionsByRetirement: jest
        .fn()
        .mockResolvedValue({ hasError: false, inactivationError: null })
    };
    await workerModule.workerFn({ Logger, service, done, logService });
    expect(Logger.info.mock.calls.length).toBe(5);
  });

  it('should return if there is an error', async () => {
    Logger = {
      info: jest.fn(() => jest.fn()),
      error: jest.fn(() => jest.fn())
    };
    service = {
      inactivatePensionsByRetirement: jest
        .fn()
        .mockResolvedValue({ inactivationError: new Error('error') })
    };
    await workerModule.workerFn({ Logger, service, done, logService });
    expect(Logger.info.mock.calls.length).toBe(3);
  });

  it('throw error ', async () => {
    service.inactivatePensionsByRetirement = () => {
      throw Error('service error');
    };
    await workerModule.workerFn({ Logger, service, done, logService });
    expect(Logger.error).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

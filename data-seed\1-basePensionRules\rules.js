const workAccident = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70 <= Edad < 75'
  },
  {
    age: 'Edad >= 75'
  }
].map(({ age }) => ({
  label: 'Pensión por accidente de trabajo',
  age,
  type: 'pension',
  value: { minimun: null }
}));

const commutingAccident = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70<= Edad < 75'
  },
  {
    age: 'Edad >= 75'
  }
].map(({ age }) => ({
  label: 'Pensión por accidente de trayecto',
  age,
  type: 'pension',
  value: { minimun: null }
}));

const sickness = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70<= Edad < 75'
  },
  {
    age: 'Edad >= 75'
  }
].map(({ age }) => ({
  label: 'Pensión por enfermedad profesional',
  age,
  type: 'pension',
  value: { minimun: null }
}));

const widowhoodWithChildrens = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70 <= Edad < 75'
  },
  {
    age: 'Edad >= 75',
    value: { law19953: null }
  }
].map(({ age, value }) => ({
  label: 'Pensión de viudez con hijos',
  age,
  type: 'pension',
  value: { minimun: null, law19403: null, law19539: null, ...value }
}));

const childlessWidowhood = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70 <= Edad < 75'
  },
  {
    age: 'Edad >= 75',
    value: { law19953: null }
  }
].map(({ age, value }) => ({
  label: 'Pensión de viudez sin hijos',
  age,
  type: 'pension',
  value: { minimun: null, law19403: null, law19539: null, ...value }
}));

const filiationWithChildrens = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70 <= Edad < 75'
  },
  {
    age: 'Edad >= 75',
    value: { law19953: null }
  }
].map(({ age, value }) => ({
  label: 'Pensión de madre de hijo de filiación no matrimonial con hijos',
  age,
  type: 'pension',
  value: { minimun: null, law19403: null, law19539: null, ...value }
}));

const childlessFiliation = [
  {
    age: 'Edad < 70'
  },
  {
    age: '70 <= Edad < 75'
  },
  {
    age: 'Edad >= 75',
    value: { law19953: null }
  }
].map(({ age, value }) => ({
  label: 'Pensión de madre de hijo de filiación no matrimonial sin hijos',
  age,
  type: 'pension',
  value: { minimun: null, law19403: null, law19539: null, ...value }
}));

const orphand = [
  {
    label: 'Pensión por orfandad',
    type: 'pension',
    age: 'Edad > 0',
    value: { minimun: null }
  }
];

const bonus = [
  { label: 'Bono Invierno', type: 'bonus', age: null, value: null },
  {
    label: 'Aguinaldo de fiestas patrias',
    type: 'bonus',
    age: null,
    value: null
  },
  { label: 'Aguinaldo de navidad', type: 'bonus', age: null, value: null },
  { label: 'Aguinaldo fiestas patrias por carga familiar', type: 'bonus', age: null, value: null },
  { label: 'Aguinaldo navidad por carga familiar', type: 'bonus', age: null, value: null }
];

module.exports = [
  ...workAccident,
  ...commutingAccident,
  ...sickness,
  ...widowhoodWithChildrens,
  ...childlessWidowhood,
  ...filiationWithChildrens,
  ...childlessFiliation,
  ...orphand,
  ...bonus
];

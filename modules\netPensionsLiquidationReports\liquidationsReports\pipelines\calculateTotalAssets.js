const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');

const paths = [
  'article40',
  'article41',
  'assets.aps',
  'basePension',
  'assets.rebsal',
  'assets.winterBonus',
  'assets.marriageBonus',
  'assets.christmasBonus',
  'assets.forFamilyAssignment',
  'assets.netTotalNonFormulable',
  'assets.nationalHolidaysBonus',
  'assets.adjustedHealthExemption',
  'assets.taxableTotalNonFormulable',
  'retroactiveAmounts.forBonuses',
  'retroactiveAmounts.forRejection',
  'retroactiveAmounts.forFamilyAssignment',
  'retroactiveAmounts.forInstitutionalPatient',
  'retroactiveAmounts.forNetTotalNonFormulableAssets',
  'retroactiveAmounts.forPayCheck',
  'retroactiveAmounts.forSurvival',
  'retroactiveAmounts.forDisability',
  'law19403',
  'law19539',
  'law19953'
];

const calculate = pension => ({
  ...pension,
  liquidation: {
    ...pension.liquidation,
    totalAssets: roundValue(recursiveSum(pension, paths))
  }
});

module.exports = calculate;

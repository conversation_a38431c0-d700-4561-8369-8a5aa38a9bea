/* eslint-disable no-restricted-syntax */
/* eslint-disable no-unused-expressions */

const service = {
  async createTemporaryPensions(temporaryList, Model) {
    let isBulked = false;

    const session = await Model.startSession();
    session.startTransaction();
    try {
      await Model.deleteMany({});

      const bulk = Model.collection.initializeOrderedBulkOp();
      const promiseFunctions = temporaryList.map(pension => async () => {
        isBulked = true;
        return bulk.insert({
          ...pension,
          enabled: true,
          updatedAt: new Date(),
          createdAt: new Date()
        });
      });

      for await (const fn of promiseFunctions) {
        await fn();
      }

      isBulked && (await bulk.execute());
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (e) {
      await session.abortTransaction();
      return { completed: false, error: e };
    }
  }
};

module.exports = { ...service };

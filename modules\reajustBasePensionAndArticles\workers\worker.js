const cronDescription = 'reajust bsse pension & articles';
const cronMark = 'REAJUST_BASEPENSION_AND_ARTICLES';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = 'Proceso completado con éxito.';
const dependenciesArray = [
  'CALCULATE_DAYS_TO_PAY_WORKER',
  'CRON_BASE_MINIMUN_PENSION_WORKER',
  'SET_FIXED_BASE_PENSION_VALUE'
];

const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const getMissingDependencyMessage = dep => `Dependencia "${dep}" aún no ejecutada`;

const workerFn = async ({ Logger, done, logService, service, modifier, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    if (!(await logService.allMarksExists(dependenciesArray))) {
      Logger.info(getMissingDependencyMessage(dependenciesArray));
      return { message: getMissingDependencyMessage(dependenciesArray), executionCompleted: false };
    }

    Logger.info(`Starting cron readjustmen base pension, article40 & article41`);
    const { error } = await service.reajustBasePensionAndArticles(modifier);
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`End of base pension readjustmen, article40 y article41`);

    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependenciesArray, workerFn };

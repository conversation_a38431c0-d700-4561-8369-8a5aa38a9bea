/* eslint-disable consistent-return */

const logService = require('../../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const pensionService = require('../../../pensions/services/pension.service');
const workerModule = require('./worker');

module.exports = {
  name: 'inactivatePensionsByWidowUnderFourtyFive',
  worker: deps => workerModule.workerFn({ service, logService, pensionService, ...deps }),
  description: 'Inactivar viudas <45 años sin cargas o asignar vitalicia si >45 años con cargas',
  endPoint: 'inactivatebywidowunderfourtyfive',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const { codeRule, codeFormatter } = require('./validator');

describe('Banks validators service Test', () => {
  beforeAll(beforeAllTests);

  it('should format a 3 digit code', () => {
    const ruled1 = '100jjjj';
    const ruled2 = '000';
    const ruled3 = '0a0aaaa1';
    const ruled4 = 'anything$%&111';
    const ruled5 = 'efervevbte';
    expect(codeFormatter(ruled1)).toBe('100');
    expect(codeFormatter(ruled2)).toBe('000');
    expect(codeFormatter(ruled3)).toBe('001');
    expect(codeFormatter(ruled4)).toBe('111');
    expect(codeFormatter(ruled5)).toBe('');
  });
  it('should validate a 3 digit code', () => {
    const ruled1 = '001';
    const ruled2 = '000';
    const ruled3 = 10;
    const ruled4 = 'anything$%&';
    expect(codeRule.test(ruled1)).toBe(true);
    expect(codeRule.test(ruled2)).toBe(true);
    expect(codeRule.test(ruled3)).toBe(false);
    expect(codeRule.test(ruled4)).toBe(false);
  });

  afterEach(async () => {});

  afterAll(afterAllTests);
});

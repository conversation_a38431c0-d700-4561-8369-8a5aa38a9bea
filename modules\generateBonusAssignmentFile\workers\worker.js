const markDependency = 'MANUALLY_INACTIVATE_MARKED_PENSIONS';
const missingDepMsg = `No se ha ejecutado la dependencia ${markDependency}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const descriptionOfCron = 'set-pensioners-bonus';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el año actual.';
const markOfCron = 'SET_PENSIONERS_BONUS';
const successMessage = `El proceso ${markOfCron} se completó correctamente`;
const workerFn = async ({ Logger, logService, service, job, done }) => {
  try {
    Logger.info(`${descriptionOfCron}: cron dependency verification started...`);
    if (!(await logService.existsLog(markDependency))) {
      Logger.info(missingDepMsg);
      return { message: missingDepMsg, status: 'UNAUTHORIZED' };
    }
    Logger.info(
      `${descriptionOfCron} checking whether this process was previously executed or not`
    );
    const { existsLog } = await logService.existsLogAndRetry(markOfCron);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${descriptionOfCron} process started`);
    const { error } = await service.setPensionersBonus();
    if (error) throw new Error(error);

    await logService.saveLog(markOfCron);
    Logger.info(`${descriptionOfCron} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${descriptionOfCron} ${error}`);
    await logService.retryLog(markOfCron);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${descriptionOfCron} ${error}` };
  } finally {
    done();
  }
};

module.exports = { markOfCron, markDependency, workerFn };

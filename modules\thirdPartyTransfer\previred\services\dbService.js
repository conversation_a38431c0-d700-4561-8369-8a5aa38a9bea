/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const tmp = require('tmp');
const fsClient = require('fs').promises;

const { safeValue } = require('../../../../lib/utils/object-utils');
const { getTextLine, getExtraTextLines } = require('./fileGenerator');

const VALIDITY_TYPE = /No vigente/i;

const getValidPensions = async pensionService => {
  return pensionService.getAllAndFilter({
    enabled: true,
    validityType: { $nin: [/No vigente/i] },
    createdAt: {
      $gte: moment()
        .startOf('month')
        .startOf('day')
        .toDate()
    }
  });
};

const getTaxablePension = async (pension, liquidationService) => {
  const { result: liquidationResults } = await liquidationService.getAllAndFilter({
    enabled: true,
    beneficiaryRut: safeValue(pension, 'beneficiary.rut'),
    causantRut: safeValue(pension, 'causant.rut')
  });
  let taxablePension = 0;
  if (liquidationResults.length > 0) {
    const { _id, ...liquidationObject } = liquidationResults[0]._doc;
    taxablePension = liquidationObject.taxablePension;
  }
  return taxablePension;
};

const service = {
  async generatePreviredFile(
    pensionService,
    liquidationService,
    afpService,
    isapreService,
    Logger
  ) {
    const { result: afps } = await afpService.getAfps({ enabled: true });
    const { result: isapres } = await isapreService.getIsapres({ enabled: true });
    const { result: pensionList } = await getValidPensions(pensionService);
    const textFile = [];
    await Promise.all(
      pensionList.map(async pension => {
        const taxablePension = await getTaxablePension(pension, liquidationService);
        textFile.push(getTextLine({ pension, type: 1, afps, isapres, taxablePension }));
        const extraLines = getExtraTextLines({ pension, taxablePension });
        if (extraLines.length > 0) textFile.push(...extraLines);
        const pensionHistory = await pensionService.getPensionHistoryByMonth(
          pension.beneficiary.rut,
          pension.causant.rut,
          { validityType: VALIDITY_TYPE }
        );
        let currentMonth = '';

        pensionHistory.some(oldPension => {
          if (!currentMonth) {
            currentMonth = moment(oldPension.createdAt);
          } else if (
            currentMonth.subtract(1, 'months').isSame(moment(oldPension.createdAt), 'month')
          ) {
            currentMonth = moment(oldPension.createdAt);
          } else {
            return true;
          }
          textFile.push(getTextLine({ pension: oldPension, type: 2, afps, isapres }));
          return false;
        });
      })
    );

    const data = textFile.join('\n');
    const tempDirectory = tmp.dirSync().name;
    const fileName = `prev${moment().format('MMYYYY')}.txt`;
    const filePath = `${tempDirectory}/${fileName}`;

    await fsClient.writeFile(filePath, data).catch(err => Logger.error(err));

    return { filePath, fileName };
  }
};

module.exports = { getTaxablePension, ...service };

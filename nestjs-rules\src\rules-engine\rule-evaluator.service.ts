import { Injectable, Logger } from '@nestjs/common';
import { RuleContext } from './rules-engine.service';

export interface Condition {
  field: string;
  operator: string;
  value: any;
  logicalOperator?: 'AND' | 'OR';
  nested?: Condition[];
}

/**
 * Evaluador de Condiciones para Reglas de Pensiones
 * Evalúa condiciones complejas usando JSON Logic y operadores personalizados
 */
@Injectable()
export class RuleEvaluator {
  private readonly logger = new Logger(RuleEvaluator.name);

  /**
   * Evalúa un conjunto de condiciones contra el contexto
   */
  async evaluateConditions(conditions: Condition[], context: RuleContext): Promise<boolean> {
    if (!conditions || conditions.length === 0) {
      return true; // Sin condiciones = siempre aplicable
    }

    try {
      return this.evaluateConditionGroup(conditions, context);
    } catch (error) {
      this.logger.error('Error evaluating conditions:', error);
      return false;
    }
  }

  /**
   * Evalúa un grupo de condiciones con operadores lógicos
   */
  private evaluateConditionGroup(conditions: Condition[], context: RuleContext): boolean {
    if (conditions.length === 0) {
      return true;
    }

    if (conditions.length === 1) {
      return this.evaluateSingleCondition(conditions[0], context);
    }

    // Evaluar múltiples condiciones
    let result = this.evaluateSingleCondition(conditions[0], context);

    for (let i = 1; i < conditions.length; i++) {
      const condition = conditions[i];
      const conditionResult = this.evaluateSingleCondition(condition, context);
      const operator = conditions[i - 1].logicalOperator || 'AND';

      if (operator === 'AND') {
        result = result && conditionResult;
      } else if (operator === 'OR') {
        result = result || conditionResult;
      }

      // Optimización: si es AND y ya es false, no evaluar más
      if (operator === 'AND' && !result) {
        break;
      }
    }

    return result;
  }

  /**
   * Evalúa una condición individual
   */
  private evaluateSingleCondition(condition: Condition, context: RuleContext): boolean {
    // Manejar condiciones anidadas
    if (condition.nested && condition.nested.length > 0) {
      return this.evaluateConditionGroup(condition.nested, context);
    }

    const fieldValue = this.getFieldValue(condition.field, context);
    const expectedValue = condition.value;
    const operator = condition.operator;

    return this.applyOperator(fieldValue, operator, expectedValue, context);
  }

  /**
   * Obtiene el valor de un campo del contexto usando dot notation
   */
  private getFieldValue(fieldPath: string, context: RuleContext): any {
    const paths = fieldPath.split('.');
    let value: any = context;

    for (const path of paths) {
      if (value === null || value === undefined) {
        return null;
      }

      // Manejar casos especiales
      if (path === 'pension') {
        value = context.pension;
      } else if (path === 'currentDate') {
        value = context.currentDate;
      } else if (path === 'referenceValues') {
        value = context.referenceValues;
      } else if (path.startsWith('ref:')) {
        // Valor de referencia: ref:UF, ref:MINIMUM_SALARY
        const refName = path.substring(4);
        return context.referenceValues.get(refName);
      } else if (path.startsWith('calc:')) {
        // Valor calculado: calc:totalBasePension
        const calcName = path.substring(5);
        return this.getCalculatedValue(calcName, context);
      } else {
        value = value[path];
      }
    }

    return value;
  }

  /**
   * Obtiene valores calculados dinámicamente
   */
  private getCalculatedValue(calcName: string, context: RuleContext): any {
    const pension = context.pension;

    switch (calcName) {
      case 'totalBasePension':
        return Number(pension.basePension || 0) + 
               Number(pension.article40 || 0) + 
               Number(pension.article41 || 0);

      case 'totalLaws':
        return Number(pension.law19403 || 0) + 
               Number(pension.law19539 || 0) + 
               Number(pension.law19953 || 0);

      case 'age':
        if (pension.dateOfBirth) {
          const birthDate = new Date(pension.dateOfBirth);
          const currentDate = context.currentDate;
          return Math.floor((currentDate.getTime() - birthDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        }
        return 0;

      case 'pensionYears':
        if (pension.pensionStartDate) {
          const startDate = new Date(pension.pensionStartDate);
          const currentDate = context.currentDate;
          return Math.floor((currentDate.getTime() - startDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000));
        }
        return 0;

      case 'currentMonth':
        return context.currentDate.getMonth() + 1;

      case 'currentYear':
        return context.currentDate.getFullYear();

      case 'isActive':
        return pension.enabled && 
               (!pension.endDateOfValidity || new Date(pension.endDateOfValidity) > context.currentDate);

      case 'hasCharges':
        return (pension.numberOfCharges || 0) > 0;

      case 'isInstitutionalPatient':
        return pension.institutionalPatient === true;

      default:
        this.logger.warn(`Unknown calculated value: ${calcName}`);
        return null;
    }
  }

  /**
   * Aplica operadores de comparación
   */
  private applyOperator(fieldValue: any, operator: string, expectedValue: any, context: RuleContext): boolean {
    // Normalizar valores para comparación
    const normalizedFieldValue = this.normalizeValue(fieldValue);
    const normalizedExpectedValue = this.normalizeValue(expectedValue);

    switch (operator.toLowerCase()) {
      case '=':
      case 'equals':
      case 'eq':
        return normalizedFieldValue === normalizedExpectedValue;

      case '!=':
      case 'not_equals':
      case 'ne':
        return normalizedFieldValue !== normalizedExpectedValue;

      case '>':
      case 'greater_than':
      case 'gt':
        return Number(normalizedFieldValue) > Number(normalizedExpectedValue);

      case '>=':
      case 'greater_than_or_equal':
      case 'gte':
        return Number(normalizedFieldValue) >= Number(normalizedExpectedValue);

      case '<':
      case 'less_than':
      case 'lt':
        return Number(normalizedFieldValue) < Number(normalizedExpectedValue);

      case '<=':
      case 'less_than_or_equal':
      case 'lte':
        return Number(normalizedFieldValue) <= Number(normalizedExpectedValue);

      case 'in':
        return Array.isArray(normalizedExpectedValue) && 
               normalizedExpectedValue.includes(normalizedFieldValue);

      case 'not_in':
        return Array.isArray(normalizedExpectedValue) && 
               !normalizedExpectedValue.includes(normalizedFieldValue);

      case 'contains':
        return String(normalizedFieldValue).toLowerCase()
               .includes(String(normalizedExpectedValue).toLowerCase());

      case 'not_contains':
        return !String(normalizedFieldValue).toLowerCase()
                .includes(String(normalizedExpectedValue).toLowerCase());

      case 'starts_with':
        return String(normalizedFieldValue).toLowerCase()
               .startsWith(String(normalizedExpectedValue).toLowerCase());

      case 'ends_with':
        return String(normalizedFieldValue).toLowerCase()
               .endsWith(String(normalizedExpectedValue).toLowerCase());

      case 'is_null':
        return normalizedFieldValue === null || normalizedFieldValue === undefined;

      case 'is_not_null':
        return normalizedFieldValue !== null && normalizedFieldValue !== undefined;

      case 'is_empty':
        return normalizedFieldValue === '' || 
               normalizedFieldValue === null || 
               normalizedFieldValue === undefined ||
               (Array.isArray(normalizedFieldValue) && normalizedFieldValue.length === 0);

      case 'is_not_empty':
        return normalizedFieldValue !== '' && 
               normalizedFieldValue !== null && 
               normalizedFieldValue !== undefined &&
               (!Array.isArray(normalizedFieldValue) || normalizedFieldValue.length > 0);

      case 'between':
        if (Array.isArray(normalizedExpectedValue) && normalizedExpectedValue.length === 2) {
          const numValue = Number(normalizedFieldValue);
          return numValue >= Number(normalizedExpectedValue[0]) && 
                 numValue <= Number(normalizedExpectedValue[1]);
        }
        return false;

      case 'not_between':
        if (Array.isArray(normalizedExpectedValue) && normalizedExpectedValue.length === 2) {
          const numValue = Number(normalizedFieldValue);
          return numValue < Number(normalizedExpectedValue[0]) || 
                 numValue > Number(normalizedExpectedValue[1]);
        }
        return true;

      case 'date_equals':
        return this.compareDates(normalizedFieldValue, normalizedExpectedValue, 'equals');

      case 'date_before':
        return this.compareDates(normalizedFieldValue, normalizedExpectedValue, 'before');

      case 'date_after':
        return this.compareDates(normalizedFieldValue, normalizedExpectedValue, 'after');

      case 'date_between':
        if (Array.isArray(normalizedExpectedValue) && normalizedExpectedValue.length === 2) {
          return this.compareDates(normalizedFieldValue, normalizedExpectedValue[0], 'after_or_equal') &&
                 this.compareDates(normalizedFieldValue, normalizedExpectedValue[1], 'before_or_equal');
        }
        return false;

      case 'regex':
        try {
          const regex = new RegExp(normalizedExpectedValue);
          return regex.test(String(normalizedFieldValue));
        } catch (error) {
          this.logger.error(`Invalid regex pattern: ${normalizedExpectedValue}`);
          return false;
        }

      case 'custom':
        // Operador personalizado - evaluar función custom
        return this.evaluateCustomOperator(normalizedFieldValue, normalizedExpectedValue, context);

      default:
        this.logger.warn(`Unknown operator: ${operator}`);
        return false;
    }
  }

  /**
   * Normaliza valores para comparación consistente
   */
  private normalizeValue(value: any): any {
    if (value === null || value === undefined) {
      return null;
    }

    // Convertir strings de números a números
    if (typeof value === 'string' && !isNaN(Number(value)) && value.trim() !== '') {
      return Number(value);
    }

    // Convertir strings de booleanos
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true') return true;
      if (lowerValue === 'false') return false;
    }

    return value;
  }

  /**
   * Compara fechas
   */
  private compareDates(date1: any, date2: any, comparison: string): boolean {
    const d1 = new Date(date1);
    const d2 = new Date(date2);

    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
      return false;
    }

    switch (comparison) {
      case 'equals':
        return d1.getTime() === d2.getTime();
      case 'before':
        return d1.getTime() < d2.getTime();
      case 'after':
        return d1.getTime() > d2.getTime();
      case 'before_or_equal':
        return d1.getTime() <= d2.getTime();
      case 'after_or_equal':
        return d1.getTime() >= d2.getTime();
      default:
        return false;
    }
  }

  /**
   * Evalúa operadores personalizados
   */
  private evaluateCustomOperator(fieldValue: any, expectedValue: any, context: RuleContext): boolean {
    // Implementar lógica personalizada según necesidades específicas
    // Por ejemplo: validaciones complejas de negocio
    
    if (typeof expectedValue === 'object' && expectedValue.function) {
      switch (expectedValue.function) {
        case 'isEligibleForAps':
          return this.isEligibleForAps(context);
        
        case 'isEligibleForBonus':
          return this.isEligibleForBonus(context, expectedValue.bonusType);
        
        case 'hasValidAfpAffiliation':
          return this.hasValidAfpAffiliation(context);
        
        default:
          this.logger.warn(`Unknown custom function: ${expectedValue.function}`);
          return false;
      }
    }

    return false;
  }

  /**
   * Funciones personalizadas de validación
   */
  private isEligibleForAps(context: RuleContext): boolean {
    const pension = context.pension;
    const eligibleTypes = ['INVALIDEZ_TOTAL', 'INVALIDEZ_PARCIAL', 'SUPERVIVENCIA'];
    return eligibleTypes.includes(pension.pensionType);
  }

  private isEligibleForBonus(context: RuleContext, bonusType: string): boolean {
    const pension = context.pension;
    const assets = pension.assets as any;
    
    // Verificar configuración de bonos
    if (assets?.bonusConfig?.payBonus === 'NO') {
      return false;
    }

    // Verificar exclusiones específicas
    if (assets?.bonusConfig?.bonusExclusions?.includes(bonusType)) {
      return false;
    }

    return true;
  }

  private hasValidAfpAffiliation(context: RuleContext): boolean {
    const pension = context.pension;
    const validAfps = ['PROVIDA', 'HABITAT', 'PLANVITAL', 'CUPRUM', 'CAPITAL'];
    return pension.afpAffiliation && validAfps.includes(pension.afpAffiliation);
  }
}

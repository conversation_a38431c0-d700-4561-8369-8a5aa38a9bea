const getToken = req => {
  let token;
  if (
    req.headers.authorization &&
    req.headers.authorization.split(' ')[0] === 'Bearer' &&
    req.headers.authorization.split(' ')[1] !== 'undefined'
  ) {
    [token] = req.headers.authorization.split(' ').slice(-1);
  }
  if (req.headers['x-access-token'] || req.params.token) {
    token = req.headers['x-access-token'] || req.params.token;
  }
  return token;
};
module.exports = getToken;

const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  worker: deps => workerModule.workerFn({ service, logService, pensionService, ...deps }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'reservedAssetsAndDiscountsAmountCalculation',
  description:
    'Calculo de montos reservados para pensión base, Art 40 y 41, Aguinaldos y haberes y descuentos no formulables',
  endPoint: 'calculatereservedassetsanddiscounts',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

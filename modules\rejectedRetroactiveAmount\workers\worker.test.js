/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker set reserved and retroactive amount on pensions', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();

    service = {
      calculateRetroactiveRejectedPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      ),
      calculateRetroactivePayCheckRefundedPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).not.toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).not.toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependency mark not found', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).not.toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(3);
  });

  it('throw an error by rejected', async () => {
    service = {
      calculateRetroactiveRejectedPensions: jest.fn(() =>
        Promise.reject(new Error('error by rejected'))
      ),
      calculateRetroactivePayCheckRefundedPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };

    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('throw an error by refunded', async () => {
    service = {
      calculateRetroactiveRejectedPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      ),
      calculateRetroactivePayCheckRefundedPensions: jest.fn(() =>
        Promise.reject(new Error('error by refunded'))
      )
    };

    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateRetroactiveRejectedPensions).toBeCalled();
    expect(service.calculateRetroactivePayCheckRefundedPensions).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });
  afterAll(afterAllTests);
});

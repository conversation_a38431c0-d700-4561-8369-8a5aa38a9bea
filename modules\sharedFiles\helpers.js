const moment = require('moment');
const axios = require('axios');
const AdmZip = require('adm-zip');
const fs = require('fs');

const logService = require('./services/jobLog.service');

const connectToFTPServer = async (client, credentials) => {
  try {
    await client.access({ ...credentials });
    return { connected: true, error: false };
  } catch (error) {
    return { connected: false, error };
  }
};

const compressFile = (filePaths, destinationPath) => {
  try {
    const zip = new AdmZip();
    if (Array.isArray(filePaths)) {
      filePaths.forEach(path => {
        zip.addLocalFile(path);
      });
    } else {
      zip.addLocalFile(filePaths);
    }
    zip.writeZip(destinationPath);
    return destinationPath;
  } catch (error) {
    throw new Error(`Error Compressing File: ${error}`);
  }
};

const getPreviousMonthDate = specificDay => {
  const previousMonth = moment()
    .subtract(1, 'month')
    .endOf('month');
  const year = previousMonth.year();
  const month = previousMonth.format('MM');
  const day = previousMonth.format('DD');
  return `${year}-${month}-${specificDay || day}`;
};

const getKeysValueRecursively = (obj, keys, defaultValue) => {
  const [key] = keys;
  if (!obj[key]) return defaultValue;
  if (keys.length === 1) return obj[key];
  return getKeysValueRecursively(obj[key], keys.slice(1), defaultValue);
};

const getPathValue = (obj, path, defaultValue) => {
  const keys = path.split('.');
  return getKeysValueRecursively(obj, keys, defaultValue);
};

const recursiveSum = (obj, pathsArr) => {
  const [path] = pathsArr;
  if (pathsArr.length === 1) return getPathValue(obj, path, 0);
  return getPathValue(obj, path, 0) + recursiveSum(obj, pathsArr.slice(1));
};

const recursiveCount = (obj, pathsArr) => {
  if (pathsArr.length === 0) return 0;
  const [path] = pathsArr;
  const value = getPathValue(obj, path, 0) > 0 ? 1 : 0;
  return value + recursiveCount(obj, pathsArr.slice(1));
};

const roundValue = (value, decimalPlaces = 2) => {
  const decimals = decimalPlaces >= 0 ? decimalPlaces : 2;
  return Math.round((+value + Number.EPSILON) * 10 ** decimals) / 10 ** decimals;
};

const escapeStringToRegex = value => {
  let regex = value.trim();
  regex = regex
    .replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')
    .replace(/\s+/g, '\\s+')
    .replace(/[aáàâäãå]/gi, '[aáàâäãå]')
    .replace(/[eéèêë]/gi, '[eéèêë]')
    .replace(/[iíìîï]/gi, '[iíìîï]')
    .replace(/[oóòôöõ]/gi, '[oóòôöõ]')
    .replace(/[uúùûü]/gi, '[uúùûü]')
    .replace(/[nñ]/gi, '[nñ]');
  return new RegExp(regex, 'i');
};

const removeAccents = str => str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
const getLastDayOfMonth = () => {
  const lastDayOfMonth = moment()
    .endOf('month')
    .format('YYYY-MM-DD');
  return `${lastDayOfMonth}`;
};

async function getMonthHolidays(year, month) {
  const API_URL = `${process.env.HOLIDAYS_URL}/${year}/${month}`.replace(/\/+([0-9]+)/g, '/$1');
  let holidays = [];
  holidays = await axios
    .get(API_URL)
    .then(response => (response.data.error ? { data: [] } : response))
    .catch(() => ({
      data: []
    }));
  return holidays.data.map(holiday => holiday.fecha);
}

const getFirstNbusinessDays = async (_date, totalDays, getMonthHolidaysFn) => {
  const year = moment(_date).year();
  const month = moment(_date).month() + 1;
  const holidays = await getMonthHolidaysFn(year, month);
  const businessDays = [];
  const Sunday = 0;
  const Saturday = 6;

  let date = moment(_date).startOf('month');

  while (businessDays.length < totalDays) {
    if (date.day() === Sunday) date = date.add(1, 'days');
    if (date.day() === Saturday) date = date.add(2, 'days');
    if (holidays.indexOf(date.format('YYYY-MM-DD')) < 0) {
      businessDays.push(date.format('YYYY-MM-DD'));
    }
    date = date.add(1, 'days');
  }

  return businessDays;
};
const RUT_REGEX = /(\d{1,2})\.?(\d{3})\.?(\d{3})-?(\d|k)/i;
const validateRUT = rut => rut.replace(/\./g, '').match(RUT_REGEX);
const cleanDots = text => text.replace(/[.\s]+/g, '');

const formatRut = rut => {
  if (!rut || rut.trim().length < 8) return '';
  return rut.includes('-')
    ? cleanDots(rut)
    : `${cleanDots(rut).slice(0, -1)}-${cleanDots(rut).slice(-1)}`;
};

const pipe = (...functions) => args => functions.reduce((arg, fn) => fn(arg), args);

const getPreviousMonthAndYear = () => {
  return [
    moment()
      .date(0)
      .month() + 1,
    moment()
      .date(0)
      .year()
  ];
};

const extractZip = (source, destination, type) => {
  try {
    const zip = new AdmZip(source);
    const zipContentPath = `${destination}/${type}/`;
    zip.extractAllTo(zipContentPath, true);
    return zipContentPath;
  } catch (err) {
    throw new Error(err);
  }
};

const getFormatedDate = date => {
  const stringDate = date.toString();
  const cleanDate = stringDate && stringDate.replace(/[^0-9]+/gi, '');
  if (!cleanDate) return null;
  const formatedDate = moment(cleanDate, 'YYYYMMDD');
  if (!formatedDate.isValid()) return null;
  if (moment().diff(formatedDate) < 0) return null;
  return new Date(formatedDate.format());
};

const getFormatedRut = (rut, country) => {
  if (country && country.toUpperCase() === 'C') {
    const cleanedRut = rut && rut.replace(/[^0-9kK]+/gi, '').replace(/^0+/, '');
    const { length } = cleanedRut;
    const dv = rut && cleanedRut.substr(length - 1);
    const rutWithoutDV = cleanedRut.slice(0, length - 1);
    return (cleanedRut && `${rutWithoutDV}-${dv}`) || null;
  }
  return rut.trim();
};

const checkIfAllProcessAreExecuted = async (listOfMarks = []) => {
  let result = true;

  await Promise.all(
    listOfMarks.map(async mark => {
      if (!(await logService.existsLog(mark))) {
        result = false;
      }
    })
  );

  return result;
};

const getFileSizeMB = async filePath => {
  const BYTES_PER_MB = 1024 ** 2;
  const fileStats = await fs.promises.stat(filePath);
  return fileStats.size / BYTES_PER_MB;
};

module.exports = {
  connectToFTPServer,
  getPreviousMonthDate,
  recursiveSum,
  recursiveCount,
  getPathValue,
  roundValue,
  escapeStringToRegex,
  removeAccents,
  getLastDayOfMonth,
  getMonthHolidays,
  getFirstNbusinessDays,
  validateRUT,
  formatRut,
  getPreviousMonthAndYear,
  pipe,
  extractZip,
  compressFile,
  getFormatedDate,
  getFormatedRut,
  cleanDots,
  checkIfAllProcessAreExecuted,
  getFileSizeMB
};

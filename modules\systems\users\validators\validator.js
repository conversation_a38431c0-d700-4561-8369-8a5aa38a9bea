const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const regRule = new RegExp(regex, 'i');

const EMAIL_PATTERN = /(^[a-z0-9]+([a-z0-9-_.]+(?![-._]))@([a-z0-9]+)(\.[a-z0-9]{2,4})+$)/i;

const emailMatchRule = email => EMAIL_PATTERN.test(email);

module.exports = {
  regRule,
  emailMatchRule
};

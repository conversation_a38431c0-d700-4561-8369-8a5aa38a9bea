const {
  generateBonusFileContent,
  downloadFileAsBase64,
  decodeBase64File,
  readFileContent,
  validateFileLines
} = require('./fileGenerator');
const temporaryBonusPensionersModel = require('../../generateBonusAssignmentFile/models/temporaryBonusPensioners');
const temporaryImportedBonusPensionersModel = require('../models/temporaryImportedBonusPensioners');

const createTemporaryImportedBonusPensioners = async bonusPensionersList => {
  const session = await temporaryImportedBonusPensionersModel.startSession();
  session.startTransaction();
  try {
    await temporaryImportedBonusPensionersModel.deleteMany({}).exec();
    const bulkOperation = temporaryImportedBonusPensionersModel.collection.initializeOrderedBulkOp();

    const promiseFunctions = bonusPensionersList.map(bonusPensioner => async () => {
      return bulkOperation.insert({
        ...bonusPensioner,
        updatedAt: new Date(),
        createdAt: new Date()
      });
    });

    // eslint-disable-next-line no-restricted-syntax
    for await (const fn of promiseFunctions) {
      await fn();
    }
    if (bulkOperation.length) {
      await bulkOperation.execute();
    }
    await session.commitTransaction();
    return { completed: true, error: null };
  } catch (error) {
    await session.abortTransaction();
    return { completed: false, error };
  }
};

const removeAccents = value => value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

const normalizeImportedData = fileLines => {
  const result = [];

  fileLines.forEach(line => {
    result.push({
      pensionerRut: `${Number(line.pensionerRut)}-${line.pensionerCheckDigit.toUpperCase()}`,
      name: line.name,
      dateBirth: line.dateBirth,
      institutionCode: line.institutionCode,
      institutionRut: `${line.institutionRut}-${line.institutionCheckDigit}`,
      informationPARGP: line.informationPARGP,
      informationPARNB: line.informationPARNB,
      startDate: line.startDate,
      amountOtherPension: parseInt(line.amountOtherPension, 10),
      numberLoads: line.numberLoads,
      pensionType: line.pensionType,
      otherAssets: line.otherAssets,
      payBonus: removeAccents(line.payBonus).toUpperCase()
    });
  });

  return result;
};

const service = {
  async downloadBonusPensionersFileString() {
    try {
      const pensionerBonusList = await temporaryBonusPensionersModel.find({}).exec();

      const content = generateBonusFileContent(pensionerBonusList);
      const { result, error } = downloadFileAsBase64(content);

      if (error) return { isError: true, error };

      return { isError: false, result };
    } catch (error) {
      return { isError: true, error };
    }
  },

  async processFile(file) {
    const { fileString } = file;
    const { result: fileContent, error: decodeError } = decodeBase64File(fileString);
    if (decodeError) return { isError: true, error: decodeError };

    const fileLines = readFileContent(fileContent);
    const errorLines = validateFileLines(fileLines);
    if (errorLines.length)
      return {
        isError: true,
        error: {
          code: 400,
          message: 'Existen errores en el archivo que impidieron su importación.'
        },
        result: errorLines
      };

    const normalizedBonusPensioners = normalizeImportedData(fileLines);
    const { error: insertError } = await createTemporaryImportedBonusPensioners(
      normalizedBonusPensioners
    );

    if (insertError) return { isError: true, error: insertError };

    return { isError: false, result: true };
  }
};

module.exports = service;

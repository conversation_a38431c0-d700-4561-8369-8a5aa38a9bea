const moment = require('moment');

const DISABILITY_PENSION_TYPE = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const RETIREMENT_VALIDITY = /Vigente hasta la jubilaci[oó]n/i;

const { FEMALE_END_OF_VALIDITY_DATE, MALE_END_OF_VALIDITY_DATE } = process.env;

const isDisabilityPensionType = pensionType =>
  DISABILITY_PENSION_TYPE.some(regex => regex.test(pensionType));
const isRetirement = retirement => RETIREMENT_VALIDITY.test(retirement);
const getEndDateOfValidity = (date, addYears) =>
  addYears > 0
    ? moment(new Date(date))
        .add(addYears, 'years')
        .subtract(1, 'day')
        .toDate()
    : null;

const setEndDateOfValidityByGender = (finalData, birth, gender) => {
  if (birth && gender) {
    return {
      ...finalData,
      endDateOfTheoricalValidity:
        gender === 'M'
          ? getEndDateOfValidity(birth, `${MALE_END_OF_VALIDITY_DATE}`)
          : getEndDateOfValidity(birth, `${FEMALE_END_OF_VALIDITY_DATE}`),
      endDateOfValidity:
        gender === 'M'
          ? getEndDateOfValidity(birth, `${MALE_END_OF_VALIDITY_DATE}`)
          : getEndDateOfValidity(birth, `${FEMALE_END_OF_VALIDITY_DATE}`)
    };
  }
  return finalData;
};

const setRetirement = (finalData, pensionType, validityType) => {
  const { dateOfBirth, gender } = finalData;
  let article41 = {};
  if (isDisabilityPensionType(pensionType)) {
    article41 = { article41: 0 };
  }
  if (isDisabilityPensionType(pensionType) && isRetirement(validityType)) {
    return setEndDateOfValidityByGender({ ...finalData, ...article41 }, dateOfBirth, gender);
  }
  return { ...finalData, ...article41 };
};

module.exports = {
  setRetirement,
  isDisabilityPensionType,
  isRetirement,
  getEndDateOfValidity,
  setEndDateOfValidityByGender
};

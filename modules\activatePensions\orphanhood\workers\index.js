const workerModule = require('./worker');
const logService = require('../../../sharedFiles/services/jobLog.service');
const service = require('../services/orphanhood.service');

module.exports = {
  name: 'reactivate-orphanhood',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  description: 'Reactivar pensiones de orfandad por certifacado de estudio vigente',
  endPoint: 'reactivateorphanhood',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const { check } = require('express-validator');
const { regRule, rutMatchRule, percentageMatchRule, codeRule } = require('./validator');

const validators = [
  check('afp.name')
    .notEmpty()
    .withMessage('el nombre no debe estar vacío')
    .isLength({ min: 1, max: 255 })
    .withMessage('la longitud máxima debe ser 255')
    .matches(regRule)
    .withMessage('el nombre debe coincidir con el formato'),
  check('afp.rut')
    .notEmpty()
    .withMessage('rut no debe estar vacío')
    .isLength({ max: 12 })
    .withMessage('la longitud máxima debe ser 12')
    .custom(rut => rutMatchRule(rut))
    .withMessage('rut debe coincidir con el formato'),
  check('afp.percentage')
    .notEmpty()
    .withMessage('el porcentaje no debe estar vacío')
    .isLength({ max: 5 })
    .withMessage('la longitud máxima debe ser 5')
    .custom(value => percentageMatchRule(value))
    .withMessage(
      'el porcentaje debe coincidir con el formato y los valores permitidos: [10.01 - 99.99]'
    ),
  check('afp.code')
    .notEmpty()
    .withMessage('el código no debe estar vacío')
    .isLength({ min: 2, max: 2 })
    .withMessage('la longitud mínima y máxima debe ser 2')
    .matches(codeRule)
    .withMessage('el código debe coincidir con el formato')
];

module.exports = validators;

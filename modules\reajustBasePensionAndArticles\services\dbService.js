const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  async reajustBasePensionAndArticles(modifierFn) {
    try {
      const pensions = await pensionService.findValidPensions();
      const modifiedPensions = pensions.map(p => modifierFn(p));
      const { completed, error } = await pensionService.updatePensions(modifiedPensions);
      return { completed, error };
    } catch (e) {
      return { completed: false, error: e };
    }
  }
};

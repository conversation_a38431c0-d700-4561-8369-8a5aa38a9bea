const pensionFields = require('../../../resources/pensionerFormatter/pensionerFormatter.json');

const getField = (path, data) =>
  path.split('.').reduce((object, nestedField) => object && object[nestedField], data);

const flattenPensionerFields = ({ liquidation, ...data }) => {
  const flattenedFields = pensionFields.reduce(
    (obj, { key, path }) => ({ ...obj, [key]: getField(path, data) }),
    {}
  );
  return { ...flattenedFields, ...liquidation };
};

module.exports = flattenPensionerFields;

const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'set-winter-bonus',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SET_WINTER_BONUS,
  description: 'Calculo de Bono invierno',
  endPoint: 'winterbonusassignment',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

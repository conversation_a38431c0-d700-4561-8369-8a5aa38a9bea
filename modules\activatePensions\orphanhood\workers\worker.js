/* eslint-disable consistent-return */

const alreadyExecutedMessage = 'This process was already executed for the current month.';
const cronMark = 'REACTIVATE_ORPANHOOD';
const successMessage = `Process ${cronMark} completed successfully.`;
const cronDescription = 'Reactivar orfandad';
const dependencyMark = '';

const workerFn = async ({ Logger, done, service, logService }) => {
  try {
    Logger.info(`Inicio proceso: ${cronDescription}`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED', alreadyExecuted: true };
    }
    await service.activate();
    await logService.saveLog(cronMark);
    Logger.info(`Fin proceso: ${cronDescription} `);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription} ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */

const appendSimpleFields = (data, simpleFields) => {
  return Object.keys(data)
    .filter(key => simpleFields.includes(key))
    .reduce((obj, key) => {
      obj[key] = data[key];
      return obj;
    }, {});
};

const filterData = (dataSets, simpleFields) =>
  dataSets.map(({ _doc }) => appendSimpleFields(_doc, simpleFields));

const formatter = {
  string: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).string(value || ''),
  number: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).number(+value || 0),
  currency: ({ sheet, currentRow, currentCol, value }) =>
    sheet
      .cell(currentRow, currentCol)
      .number(value || 0)
      .style({
        numberFormat: '$#,##0; ($#,##0.00); $0'
      }),
  date: ({ sheet, currentRow, currentCol, value }) =>
    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(value)
          .style({ numberFormat: 'dd-mm-yyyy' })
      : sheet.cell(currentRow, currentCol).string(''),
  monthYear: ({ sheet, currentRow, currentCol, value }) =>
    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(value)
          .style({ numberFormat: 'mm-yyyy' })
      : sheet.cell(currentRow, currentCol).string('')
};

module.exports = { filterData, formatter };

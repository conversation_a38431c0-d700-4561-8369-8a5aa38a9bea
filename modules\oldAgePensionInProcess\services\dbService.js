const PensionModel = require('../../../models/pension');
const pensionService = require('../../pensions/services/pension.service');

const PENSION_TYPES = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];
const VALIDITY_TYPE = /No vigente/i;
const NUMBER_OF_MONTHS = 6;

const service = {
  async filterPensionersAssignOne() {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + NUMBER_OF_MONTHS);

    return PensionModel.find(
      {
        enabled: true,
        validityType: { $not: VALIDITY_TYPE },
        pensionType: { $in: PENSION_TYPES },
        endDateOfTheoricalValidity: { $lte: endDate }
      },
      {
        beneficiary: 1,
        causant: 1,
        endDateOfTheoricalValidity: 1
      }
    ).lean();
  },

  async filterPensionersAssignZero() {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + NUMBER_OF_MONTHS);

    return PensionModel.find(
      {
        enabled: true,
        validityType: { $not: VALIDITY_TYPE },
        pensionType: { $in: PENSION_TYPES },
        endDateOfTheoricalValidity: { $gt: endDate }
      },
      {
        beneficiary: 1,
        causant: 1,
        endDateOfTheoricalValidity: 1
      }
    ).lean();
  },

  async listPensionersToRetire() {
    const pensioners = await this.filterPensionersAssignOne();

    return pensioners.map(pensioner => {
      return {
        ...pensioner,
        oldAgePensionInProcess: 1
      };
    });
  },

  async listPensionersNotToRetire() {
    const pensioners = await this.filterPensionersAssignZero();

    return pensioners.map(pensioner => {
      return {
        ...pensioner,
        oldAgePensionInProcess: 0
      };
    });
  },

  async markPensionersForRetirement() {
    try {
      const pensionersToRetire = await this.listPensionersToRetire();
      const pensionersNotToRetire = await this.listPensionersNotToRetire();
      const pensioners = pensionersToRetire.concat(pensionersNotToRetire);

      const { completed, error } = await pensionService.updatePensionsById(pensioners);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

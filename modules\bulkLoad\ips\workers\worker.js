const cronDescription = 'bulk load ips:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'BULK_LOAD_IPS_FILES';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const errorDownload = 'Error al descargar archivos IPS';
const emptyFileError = 'Verificacion terminada, Aun no se ha recibido el archivo.';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({
  Logger,
  done,
  ftpCredentials,
  ftpHelper,
  getParsedLinesFromIpsFiles,
  pensionService,
  service,
  logService,
  sftp,
  job
}) => {
  try {
    Logger.info(`Checkeando servidor SFTP. Verificando recepcion archivos IPS.`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    const client = new sftp.Client();
    const downloadResult = await ftpHelper.downloadIpsZipFiles({ client, sftp, ftpCredentials });
    const { patprDirPath, papsoDirPath, brsaludDirPath, downloadError } = downloadResult;
    if (downloadError) {
      Logger.error(`Error al descargar archivos IPS: ${downloadError}`);
      throw new Error(errorDownload);
    }

    const { files = [] } = await ftpHelper.extractDownloadedIpsZipFiles(
      patprDirPath,
      papsoDirPath,
      brsaludDirPath,
      ftpHelper.extractZip,
      ftpHelper.checkLocalFileExistence
    );
    if (!files.length) {
      Logger.error(`Verificacion terminada. Aun no se ha recibido el archivo.`);
      throw new Error(emptyFileError);
    }

    Logger.info('Archivos recibidos. Inicio procesamiento de los archivos.');

    const parsedLines = await getParsedLinesFromIpsFiles(...files);
    const { isError, error: err } = await service.updatePensions(pensionService, parsedLines);
    if (isError) throw new Error(err);

    await logService.saveLog(cronMark);
    Logger.info(`Fin procesamiento archivos IPS`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

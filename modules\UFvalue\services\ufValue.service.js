const moment = require('moment');
const mongoose = require('mongoose');

const Model = require('../models/ufValue');

module.exports = {
  async findPreviousMonthIPCS() {
    const previousMonth = moment()
      .endOf('month')
      .subtract(32, 'days');
    return mongoose.connection.db.collection('ipcs').findOne({
      $expr: {
        $and: [
          { $eq: [{ $month: `$date` }, previousMonth.month() + 1] },
          { $eq: [{ $year: `$date` }, previousMonth.year()] }
        ]
      }
    });
  },

  async createUfValue(data) {
    const { date, value } = data;
    return Model.update(
      {
        date: moment(date, 'DD-MM-YYYY').toDate(),
        period: moment().format('YYYY-MM')
      },
      {
        date: moment(date, 'DD-MM-YYYY').toDate(),
        period: moment().format('YYYY-MM'),
        value
      },
      {
        upsert: true
      }
    );
  },

  async getCurrentUfValue() {
    return Model.findOne()
      .sort('-period')
      .exec();
  }
};

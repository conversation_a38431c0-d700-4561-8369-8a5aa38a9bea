/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests, Logger } = require('../../../testsHelper');

process.env = Object.assign(process.env, {
  SENDGRID_API_KEY: 'SG.'
});

const PensionModel = require('../../../../models/pension');
const TemporaryHorphanhoodModel = require('../../../orphanhood/models/temporaryHorphanhood');
const service = require('./orphanhood.service');
const pensionsData = require('../../../../resources/pensions.json');
const orphanhoodData = require('../../../../resources/orphanhood.json');

describe('Temporary Family Allowance service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should disable and create new doc with inactivation criteria', async () => {
    await TemporaryHorphanhoodModel.create(orphanhoodData[0]);
    pensionsData[0].validityType = 'Vigente Orfandad';
    pensionsData[0].pensionType = 'Pensión por orfandad';
    await PensionModel.create(pensionsData[0]);
    await service.inactivate(Logger);
    const result = await PensionModel.find({}).lean();
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(true);
    expect(result[0].inactivationReason).toBe('Vencimiento de certificado de estudios');
  });

  it('should return on empty pension model for inactivate service', async () => {
    const result = await service.inactivate();
    expect(result).toBeUndefined();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await TemporaryHorphanhoodModel.deleteMany({});
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

{"apps": [{"name": "achs-pensiones-qa", "script": "./dist/index.js", "watch": true, "merge_logs": true, "cwd": "/home/<USER>/backend/achs-pensiones-qa", "env": {"NODE_ENV": "qa", "PORT": "8000", "MONGODB_DB_URL": "mongodb+srv://user-dev-achs:<EMAIL>/23people-dev?authSource=admin&replicaSet=Cluster0-shard-0&readPreference=primary&ssl=true", "ACHS_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "ACHS_FTP_OUTPUT_FOLDER": "/files/dev/Salida", "ACHS_FTP_PASS": "FTPAchs23people", "ACHS_FTP_PORT": "21", "ACHS_FTP_USER": "ftpuser", "ARTICLE_41_INCREASE_PERCENTAGE": "5", "BULKLOAD_CAJA18_FILES_FTP_FOLDER_PATH": "/files/Salida", "BULKLOAD_IPS_FILES_FTP_FOLDER_PATH": "/files/dev/haciamutual", "CAJA18_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "CAJA18_FTP_PASS": "FTPAchs23people", "CAJA18_FTP_PORT": "21", "CAJA18_FTP_USER": "ftpuser", "CAJA_LOSHEROES_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "CAJA_LOSHEROES_FTP_PASS": "FTPAchs23people", "CAJA_LOSHEROES_FTP_PORT": "21", "CAJA_LOSHEROES_FTP_USER": "ftpuser", "CIVIL_REGISTRATION_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "CIVIL_REGISTRATION_FTP_INPUT_FOLDER": "files/dev/Entrada/", "CIVIL_REGISTRATION_FTP_OUTPUT_FOLDER": "/files/dev/Salida", "CIVIL_REGISTRATION_FTP_PASS": "FTPAchs23people", "CIVIL_REGISTRATION_FTP_PORT": "21", "CIVIL_REGISTRATION_FTP_USER": "ftpuser", "CRON_BASE_MINIMUN_PENSION_MAX_AGE": "75", "CRON_BASE_MINIMUN_PENSION_WORKER": "3 minutes", "CRON_BULK_LOAD_LA_ARAUCANA": "4 minutes", "CRON_BULK_LOAD_LOS_HEROES": "2 minutes", "CRON_BULKLOAD_CAJA18_FREQUENCY": "1 minutes", "CRON_BULKLOAD_IPS_FREQUENCY": "4 minutes", "CRON_CALCULATE_PAYMENT_DATES_FREQUENCY": "1 minute", "CRON_CALCULATE_TAXABLE_PENSIONS": "2 minutes", "CRON_HISTORICAL_PENSION_REPORTS": "5 minutes", "CRON_INACTIVATE_BY_AGE_LIMIT": "0 0 7 6 *", "CRON_INACTIVATE_BYRETIREMENT_FREQUENCY": "*/3 * * * *", "CRON_INACTIVATE_FRECUENCY": "*/1 * * * *", "CRON_POST_INACTIVATE_BYRETIREMENT_FREQUENCY": "1 minute", "CRON_PRE_INACTIVATE_BY_TRANSIENTS": "3 minutes", "CRON_PRE_INACTIVATE_BYRETIREMENT_FREQUENCY": "30 seconds", "CRON_REACTIVATE_BY_TRANSIENTS": "*/1 * * * *", "CRON_SET_VALUE_TO_ZERO_PENSIONS": "1 month", "CRON_TO_INACTIVATE_SAP_AUTH_TYPE": "Ocp-Apim-Subscription-Key", "CRON_TO_INACTIVATE_SAP_KEY": "2edba90a2c3a4218b33e90be3c36a567", "CRON_TO_INACTIVATE_SAP_URL": "**************************************/SiniestroMiddleware/api/Siniestros/ObtenerDatosSiniestro", "CRON_UFVALUE_FREQUENCY": "2 minutes", "CRON_GENERATE_AND_UPLOAD_PREVIRED_FILE_FREQUENCY": "5 minutes", "DOCKER_CUSTOM_IMAGE_NAME": "pec23registrydev.azurecr.io/achs-app-nodejs:latest", "DOCKER_REGISTRY_SERVER_PASSWORD": "********************************", "DOCKER_REGISTRY_SERVER_URL": "https://pec23registrydev.azurecr.io/achs-app-nodejs:latest", "DOCKER_REGISTRY_SERVER_USERNAME": "************************************", "FEMALE_END_OF_VALIDITY_DATE": "60", "HOLIDAYS_URL": "https://apis.digital.gob.cl/fl/feriados", "IPS_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "IPS_FTP_PASS": "FTPAchs23people", "IPS_FTP_PORT": "21", "IPS_FTP_USER": "ftpuser", "LIFE_PENSION_LIMIT_YEARS": "110", "LOSHEROES_FTP_OUTPUT_FOLDER": "/files/Salida", "MALE_END_OF_VALIDITY_DATE": "65", "PASSWORD_IPC_SERVICE": "ZIr1MSDH9JoDQmz", "PASSWORD_UF_SOAP_SERVICE": "ZIr1MSDH9JoDQmz", "PREVIRED_ACHS_EMAIL": "<EMAIL>", "PREVIRED_LOGIN_PAGE_PASSWORD": "achs2020", "PREVIRED_LOGIN_PAGE_URL": "https://www.previred.com/wPortal/login/login.jsp", "PREVIRED_LOGIN_PAGE_USER": "18634442-1", "PREVIRED_PAYROLL_FILE_FORMAT": "2", "PREVIRED_PAYROLL_TYPE": "8", "SERIE_ID_IPC_SERVICE": "F074.IPC.IND.Z.EP18.C.M", "SERIE_ID_UF_SOAP_SERVICE": "F073.UFF.PRE.Z.D", "STORAGE_URL": "http://achs23peoplefiles.blob.core.windows.net/achs-storage-dev", "TEST": "", "TOTAL_DAYS_TO_ALLOW_ACTIONS": "30", "URL_API_IPC_SERVICE": "https://si3.bcentral.cl/SieteWS/SieteWS.asmx?wsdl", "URL_UF_SOAP_SERVICE": "https://si3.bcentral.cl/sietews/sietews.asmx?wsdl", "USER_IPC_SERVICE": "764587839", "USER_UF_SOAP_SERVICE": "764587839", "YEAR_WIDOW": "1"}}]}
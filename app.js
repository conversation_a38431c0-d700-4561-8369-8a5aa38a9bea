const express = require('express');
const passport = require('passport');
const { BearerStrategy } = require('passport-azure-ad');
const { createNamespace } = require('cls-hooked');
const kraken = require('kraken-js');
const cors = require('cors');

const helmet = require('helmet');
const logger = require('./lib/logger');
const options = require('./lib/start-options');
const bearerOptions = require('./lib/auth/bearerOptions');
const initWorkers = require('./workerManager');
const initDB = require('./modules/systems/db-init');

const ns = createNamespace('session');
const requestIdMiddleware = (req, res, next) => {
  ns.run(() => {
    next();
  });
};

const bearerStrategy = new BearerStrategy(bearerOptions, (token, done) => {
  done(null, {}, token);
});

const app = express();

app.use(kraken(options));
app.use(requestIdMiddleware);
app.use(passport.initialize());
passport.use(bearerStrategy);
app.use(helmet());
app.use(cors());

app.on('start', async () => {
  logger.info('Application ready to serve requests.');
  try {
    if (process.env.JEST_WORKER_ID !== '1' && process.env.NODE_ENV !== 'test') {
      await initWorkers();
      await initDB();
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
  }
  logger.info(`Environment: ${app.kraken.get('env:env')}`);
});

module.exports = app;

const mongoose = require('mongoose');

const { Schema } = mongoose;

const MotiveSchema = new Schema(
  {
    motive: { type: String, maxlength: 100, required: true },
    option: { type: String, enum: ['Haber', 'Descuento'], required: true },
    enabled: {
      type: Boolean,
      default: true
    },
    isDefault: {
      type: Boolean,
      default: false
    }
  },
  { timestamps: true }
);
MotiveSchema.index(
  { motive: 1, option: 1 },
  { unique: true, collation: { locale: 'es', strength: 1 } }
);

module.exports = mongoose.model('Motive', MotiveSchema);

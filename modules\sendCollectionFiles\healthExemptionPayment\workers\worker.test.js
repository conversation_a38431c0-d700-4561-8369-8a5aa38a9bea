/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./worker');

describe('Helth exemption payment worker test', () => {
  beforeAll(beforeAllTests);

  let Logger;
  let done;
  let logService;
  let service;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = {
      generateFile: jest.fn().mockResolvedValue('test_file.txt'),
      uploadFileToSftpServer: jest.fn().mockResolvedValue('test_file.zip')
    };
  });

  it('should execute worker correctly', async () => {
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      done,
      service,
      logService
    });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(service.generateFile).toHaveBeenCalled();
    expect(service.uploadFileToSftpServer).toHaveBeenCalled();
    expect(executionCompleted).toBe(true);
  });

  it('should return if process was previously executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    const { message } = await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.generateFile).not.toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(message).toBe('Este proceso ya fue ejecutado para el mes actual.');
    expect(done).toHaveBeenCalled();
  });

  it('should throw error if there is any in generateFile', async () => {
    service.generateFile = jest.fn(() => {
      throw new Error();
    });
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.generateFile).toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

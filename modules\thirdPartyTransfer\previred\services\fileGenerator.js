/* eslint-disable no-unused-expressions */
const moment = require('moment');
const { safeValue } = require('../../../../lib/utils/object-utils');
const { roundValue, escapeStringToRegex, removeAccents } = require('../../../sharedFiles/helpers');

const FONASA = escapeStringToRegex('fonasa');
const NO_AFILIATION = escapeStringToRegex('sin afiliacion');
const SI = escapeStringToRegex('Si');
const NO = escapeStringToRegex('No');
const RECEPTOR_TYPE = '2';

const formatText = (value = '', num = 0) => {
  return removeAccents(value)
    .toUpperCase()
    .padEnd(num, ' ')
    .slice(0, num);
};

const formatRut = rut => {
  return rut
    .replace('-', '')
    .toUpperCase()
    .padStart(12, '0');
};

const formatNumber = (value = '', num = 0) => {
  return value
    .toString()
    .padStart(num, '0')
    .slice(0, num);
};

const getPaymentPeriod = pension => {
  return moment(safeValue(pension, 'createdAt')).format('YYYYMM');
};

const getTaxableAmount = (pension, type, taxablePension) => {
  let taxableAmount = 0;
  if (type === 1 && taxablePension) {
    taxableAmount = roundValue(taxablePension, 0);
  } else if (type === 2) {
    taxableAmount = roundValue(
      safeValue(pension, 'reservedAmounts.forDisability', 0) +
        safeValue(pension, 'reservedAmounts.forSurvival', 0),
      0
    );
  }
  return taxableAmount;
};

const getAFPCode = (pension, afps) => {
  const nameRegex = escapeStringToRegex(safeValue(pension, 'afpAffiliation'));
  const afp = afps.find(x => nameRegex.test(x.name));
  return safeValue(afp, 'code', '00');
};

const getFonasaQuote = pension => {
  return FONASA.test(safeValue(pension, 'healthAffiliation'))
    ? formatNumber(roundValue(safeValue(pension, 'discounts.health', 0), 0), 8)
    : formatNumber('', 8);
};

const getIsapreCode = (pension, isapres) => {
  const nameRegex = escapeStringToRegex(safeValue(pension, 'healthAffiliation'));
  const isapre = isapres.find(x => nameRegex.test(x.name));
  return safeValue(isapre, 'code', '00');
};

const getCurrencyHealthPlan = pension => {
  return NO_AFILIATION.test(safeValue(pension, 'healthAffiliation')) ? '0' : '1';
};

const getAgreedQuote = pension => {
  let result = '';
  if (
    !NO_AFILIATION.test(safeValue(pension, 'healthAffiliation')) &&
    !FONASA.test(safeValue(pension, 'healthAffiliation'))
  ) {
    result = roundValue(safeValue(pension, 'discounts.health', 0), 0);
  }
  return formatNumber(result, 8);
};

const getMandatoryQuote = (pension, taxableAmount) => {
  let result = '';
  if (taxableAmount && !NO_AFILIATION.test(safeValue(pension, 'healthAffiliation'))) {
    result = roundValue(taxableAmount * 0.07, 0);
  }
  return formatNumber(result, 8);
};

const getAditionalQuote = (pension, taxableAmount) => {
  let result = '';
  if (safeValue(pension, 'discounts.healthUF', 0) > 0) {
    result = roundValue(safeValue(pension, 'discounts.health', 0) - taxableAmount * 0.07, 0);
    result = result > 0 ? result : 0;
  }
  return formatNumber(result, 8);
};

const getCajaCode = pension => {
  let result = '00';
  if (SI.test(safeValue(pension, 'discounts.onePercentLosAndes'))) {
    result = '01';
  } else if (SI.test(safeValue(pension, 'discounts.onePercentLaAraucana'))) {
    result = '02';
  } else if (SI.test(safeValue(pension, 'discounts.onePercentLosHeroes'))) {
    result = '03';
  } else if (SI.test(safeValue(pension, 'discounts.onePercent18'))) {
    result = '06';
  }
  return result;
};

const getSocialCredit = pension => {
  let result = '';
  if (SI.test(safeValue(pension, 'discounts.onePercentLosAndes'))) {
    result = roundValue(safeValue(pension, 'discounts.socialCreditsLosAndes'), 0);
  } else if (SI.test(safeValue(pension, 'discounts.onePercentLaAraucana'))) {
    result = roundValue(safeValue(pension, 'discounts.socialCreditsLaAraucana'), 0);
  } else if (SI.test(safeValue(pension, 'discounts.onePercentLosHeroes'))) {
    result = roundValue(safeValue(pension, 'discounts.socialCreditsLosHeroes'), 0);
  } else if (SI.test(safeValue(pension, 'discounts.onePercent18'))) {
    result = roundValue(safeValue(pension, 'discounts.socialCredits18'), 0);
  }
  return formatNumber(result, 8);
};

const getTextLine = ({
  pension,
  type,
  afps,
  isapres,
  taxablePension,
  extraLine = false,
  cajaCode = '00',
  cajaSocialCredits = '00000000'
}) => {
  const result = [];
  const taxableAmount = getTaxableAmount(pension, type, taxablePension);
  result.push(formatRut(safeValue(pension, 'beneficiary.rut')));
  result.push(formatText(safeValue(pension, 'beneficiary.lastName'), 30));
  result.push(formatText(safeValue(pension, 'beneficiary.mothersLastName'), 30));
  result.push(formatText(safeValue(pension, 'beneficiary.name'), 30));
  result.push(RECEPTOR_TYPE);
  result.push(type.toString());
  result.push(getPaymentPeriod(pension));
  !extraLine
    ? result.push(formatNumber(taxableAmount, 12))
    : result.push(formatNumber(roundValue(taxablePension, 0), 12));
  !extraLine ? result.push(getAFPCode(pension, afps)) : result.push(formatNumber('', 2));
  !extraLine
    ? result.push(formatNumber(roundValue(safeValue(pension, 'discounts.afp', 0), 0), 8))
    : result.push(formatNumber('', 8));
  result.push(formatNumber('', 8));
  result.push(formatText('', 3));
  result.push(formatText('', 20));
  result.push(formatNumber('', 1));
  result.push(formatNumber('', 8));
  result.push(formatNumber('', 4));
  result.push('00.00');
  result.push(formatNumber('', 8));
  result.push(formatNumber('', 8));
  result.push(formatNumber('', 4));
  result.push('00.00');
  result.push(formatNumber('', 8));
  !extraLine ? result.push(getFonasaQuote(pension)) : result.push(formatNumber('', 8));
  result.push(formatNumber('', 8));
  !extraLine ? result.push(getIsapreCode(pension, isapres)) : result.push(formatNumber('', 2));
  result.push(formatText('', 16));
  !extraLine ? result.push(getCurrencyHealthPlan(pension)) : result.push(formatNumber('', 1));
  !extraLine ? result.push(getAgreedQuote(pension)) : result.push(formatNumber('', 8));
  !extraLine
    ? result.push(getMandatoryQuote(pension, taxableAmount))
    : result.push(formatNumber('', 8));
  !extraLine
    ? result.push(getAditionalQuote(pension, taxableAmount))
    : result.push(formatNumber('', 8));
  !extraLine ? result.push(getCajaCode(pension)) : result.push(cajaCode);
  !extraLine
    ? result.push(
        formatNumber(roundValue(safeValue(pension, 'discounts.onePercentAdjusted', 0), 0), 8)
      )
    : result.push(formatNumber('', 8));
  !extraLine
    ? result.push(getSocialCredit(pension))
    : result.push(formatNumber(roundValue(cajaSocialCredits, 0), 8));
  result.push(formatNumber('', 8));
  result.push(formatNumber('', 8));
  !extraLine
    ? result.push(
        formatNumber(roundValue(safeValue(pension, 'discounts.othersLosHeroes', 0), 0), 8)
      )
    : result.push(formatNumber('', 8));
  !extraLine
    ? result.push(formatNumber(roundValue(safeValue(pension, 'discounts.othersLosAndes', 0), 0), 8))
    : result.push(formatNumber('', 8));
  return result.join('');
};

const getExtraTextLines = ({ pension, taxablePension }) => {
  const cajas = [];
  cajas.push({
    code: '01',
    onePercent: safeValue(pension, 'discounts.onePercentLosAndes'),
    socialCredits: safeValue(pension, 'discounts.socialCreditsLosAndes', 0)
  });
  cajas.push({
    code: '02',
    onePercent: safeValue(pension, 'discounts.onePercentLaAraucana'),
    socialCredits: safeValue(pension, 'discounts.socialCreditsLaAraucana', 0)
  });
  cajas.push({
    code: '03',
    onePercent: safeValue(pension, 'discounts.onePercentLosHeroes'),
    socialCredits: safeValue(pension, 'discounts.socialCreditsLosHeroes', 0)
  });
  cajas.push({
    code: '06',
    onePercent: safeValue(pension, 'discounts.onePercent18'),
    socialCredits: safeValue(pension, 'discounts.socialCredits18', 0)
  });

  const lines = [];
  cajas.forEach(caja => {
    if (NO.test(caja.onePercent) && caja.socialCredits > 0) {
      const result = getTextLine({
        pension,
        type: 1,
        taxablePension,
        extraLine: true,
        cajaCode: caja.code,
        cajaSocialCredits: caja.socialCredits
      });
      lines.push(result);
    }
  });
  return lines;
};

module.exports = { getTextLine, getExtraTextLines };

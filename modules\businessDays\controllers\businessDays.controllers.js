const moment = require('moment');

module.exports = ({
  HttpStatus,
  getNBusinessDaysService,
  getFirstNbusinessDays,
  getMonthHolidays,
  getDueDaysList,
  getRemainingDaysForProcesses,
  ErrorBuilder = { build: () => [501, 'not implemented'] },
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    isIsaprePortalProcessAvailable: async (req, res) => {
      try {
        Logger.info('Getting year and month');
        const nDays = process.env.TOTAL_DAYS_TO_ALLOW_ISAPRE_PORTAL;
        Logger.info(`Getting first ${nDays} business days`);
        const currentDate = new Date();

        const { businessDays, error } = await getNBusinessDaysService(
          currentDate,
          nDays,
          getFirstNbusinessDays,
          getMonthHolidays
        );

        if (error) manageError(res, error);

        res.status(HttpStatus.OK).json({
          nDays,
          processAvailable: businessDays.includes(moment(currentDate).format('YYYY-MM-DD'))
        });
      } catch (error) {
        manageError(res, error);
      }
    },
    isInactReactProcessAvailable: async (req, res) => {
      try {
        const nDays = process.env.TOTAL_DAYS_TO_ALLOW_REACTIVATION_INACTIVATION;
        Logger.info('Getting year and month');
        const currentDate = new Date();
        Logger.info(`Getting first ${nDays} business days`);
        const { businessDays, error } = await getNBusinessDaysService(
          currentDate,
          nDays,
          getFirstNbusinessDays,
          getMonthHolidays
        );

        if (error) {
          manageError(res, error);
        }

        res.status(HttpStatus.OK).json({
          processAvailable: businessDays.includes(moment(currentDate).format('YYYY-MM-DD')),
          nDays
        });
      } catch (e) {
        manageError(res, e);
      }
    },
    getDueDays: async (req, res) => {
      try {
        const nDays = process.env.BUSINESS_DAYS_IN_A_MONTH;

        Logger.info('Getting year and month');
        const currentDate = new Date();

        Logger.info(`Getting first ${nDays} business days`);
        const { businessDays, error } = await getNBusinessDaysService(
          currentDate,
          nDays,
          getFirstNbusinessDays,
          getMonthHolidays
        );

        if (error) {
          manageError(res, error);
        }

        const {
          dueDaysForProcesses,
          errorDueDaysForProcesses
        } = await getRemainingDaysForProcesses(getDueDaysList, businessDays, currentDate);

        if (errorDueDaysForProcesses) {
          manageError(res, error);
        }

        res.status(HttpStatus.OK).json({ dueDaysForProcesses });
      } catch (e) {
        manageError(res, e);
      }
    },
    daysToUpdatePensionerInfoAndPensionType: async (req, res) => {
      try {
        const { TOTAL_BUSINESS_DAYS } = process.env;
        const daysToModifyPensionerData = process.env.BUSINESS_DAYS_UPDATE_PENSIONER_DATA;
        const daysToModifyPensionType = process.env.BUSINESS_DAYS_UPDATE_PENSION_TYPE;

        Logger.info('Getting year and month');
        const currentDate = moment()
          .startOf('day')
          .toDate();

        Logger.info(`Getting remaining days for updating pensioner `);
        const { businessDays, error } = await getNBusinessDaysService(
          currentDate,
          TOTAL_BUSINESS_DAYS,
          getFirstNbusinessDays,
          getMonthHolidays
        );

        if (error) {
          return manageError(res, error);
        }

        const lastBusinessDayToUpdatePensioner = businessDays[daysToModifyPensionerData - 1];
        const lastBusinessDayToUpdatePensionType = businessDays[daysToModifyPensionType - 1];

        return res.status(HttpStatus.OK).json({
          updateTemporally:
            moment(lastBusinessDayToUpdatePensioner, 'YYYY-MM-DD').diff(
              moment(currentDate),
              'days'
            ) < 0,
          updatePensionTypeToTemporally:
            moment(lastBusinessDayToUpdatePensionType, 'YYYY-MM-DD').diff(
              moment(currentDate),
              'days'
            ) < 0
        });
      } catch (error) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ error: error.toString() });
      }
    },
    getCurrentDate: async (req, res) => {
      return res.status(HttpStatus.OK).json({
        currentDate: moment().format('DD-MM-YYYY')
      });
    }
  };
};

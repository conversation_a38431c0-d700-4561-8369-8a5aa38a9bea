const generateAndUploadBankFile = require('../../unifiedGenerateAndUploadBankFileCrons/workers');
const reservedAssetsAndDiscountsAmountCalculation = require('../../unifiedReservedAmountCalculationCrons/workers');
const basePensionWorker = require('../../basePension/workers');
const setPensionTypeChange = require('../../pensionTypeChange/workers');

const calculateArticle41 = require('../../articleFourtyOne/DisabilityPensioners/workers');
const reajustBasePensionAndArticles = require('../../reajustBasePensionAndArticles/workers');
const calculateDaysToPayWorker = require('../../calculateDaysToPay/workers');
const ipc = require('../../ipc/workers');
const historicalPensionReports = require('../../historicPensions/workers');
const taxablePension = require('../../taxablePensions/workers');
const monthlyExpenses = require('../../monthlyExpenses/workers');
const unifiedBulkLoadAndIps = require('../../bulkLoad/unifiedBulkLoadAndIpsCrons/workers');
const socialDiscountsCheckPoint = require('../../socialDiscountsCheckpoint/workers');
const postLiquidationCheckpoint = require('../../postLiquidationCheckpoint/workers');
const generateAndUploadPreviredFile = require('../../fileUpload/previred/workers');
const keyBuilder = require('../../keyBuilder/workers');
const {
  setFixedBasePensionAndArticlesValue,
  resetFixedBasePensionAndArticlesValue
} = require('../../fixedValues/workers');
const calculateCurrentCapital = require('../../calculateCurrentCapital/workers');
const calculatePaymentDatesWorker = require('../../paymentDate/workers');
const setZeroValuesPensionWorker = require('../../resetValuePensions/workers');
const daysOfTranstientPension = require('../../transients/workers');
const toCivilRegistration = require('../../pensions/workers');
const reactivateOrphanhood = require('../../activatePensions/orphanhood/workers');
const caja18BulkLoad = require('../../bulkLoad/caja18/workers');
const cajaLosHeroesBulkLoad = require('../../bulkLoad/cajaLosHeroes/workers');
const cajaLaAraucanaBulkLoad = require('../../bulkLoad/laAraucana/workers');
const ipsBulkLoad = require('../../bulkLoad/ips/workers');
const calculateReservedAssetsAndDiscounts = require('../../calculateAssetsBonusDiscounts/workers');
const inactivateOrReactivateAFCronsGroup = require('../../inactivateOrReactivateAFCronsGroup/workers');
const inactivateOrReactivateProcess = require('../../InactivateOrReactivateProcess/workers');
const InactivateOrReactivateOrphanhoodProcess = require('../../InactivateOrReactivateOrphanhoodProcess/workers');
const inactivateByAgeLimit = require('../../inactivatePensions/ageLimit/workers');
const inactivateByDeath = require('../../inactivatePensions/death/workers');
const inactivateByRetirement = require('../../inactivatePensions/retirement/workers');
const inactivateByMarriage = require('../../inactivatePensions/marriage/workers');
const inactivateByTransient = require('../../inactivatePensions/transients/workers');
const inactivateByWidowUnderFourtyFive = require('../../inactivatePensions/widowUnderFourtyFive/workers');
const netPensionsLiquidationReports = require('../../netPensionsLiquidationReports/workers');
const reactivateForRenewalOfStudyCertificate = require('../../reactivatePensions/renewalOfStudyCertificate/workers');
const reactivatePensionsByTransients = require('../../reactivatePensions/transients/workers');
const setReservedAmountForInstitutionalPatient = require('../../reservedAmountInstitutionalPatient/workers');
const reservedAmountAssetsDiscounts = require('../../reservedAmountOfNonFormulableAssetsDiscountsTotal/worker');
const calculateRetroactiveAmountForSurvival = require('../../retroactiveAmountsForSurvivalPension/worker');
const calculateRetroactiveAmountForInstitutianalPatient = require('../../retroactiveAmountsInstitutionalPatient/workers');
const calculateRetroactiveBank = require('../../retroactiveBank/workers');
const calculateRetroactiveDisabilityPension = require('../../retroactiveDisabilityPension/workers');
const schedulingCronjobs = require('../../scheduleCronExecution/workers');
const registerStartAndEndDateStudyCertificate = require('../../studyCertificateDateRegistration/workers');
const obtainUfValue = require('../../UFvalue/workers');
const widowhoodPayment = require('../../widowhoodPayment/worker');
const unifiedRetroactiveAmounts = require('../../unifiedRetroactiveAmountCalculationCrons/workers');
const analysisOfCurrentCapital = require('../../analysisOfCurrentCapital/workers');
const collectionDiscountHealth = require('../../sendCollectionFiles/collectionDiscountHealth/workers');
const unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport = require('../../unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCron/workers');
const updatePensionerInfo = require('../../queryPensions/worker');
const healthRejection = require('../../healthRejection/workers');
const sendCircularFile2480 = require('../../sendCollectionFiles/sendCircular2480/workers');
const healthExemptionPayment = require('../../sendCollectionFiles/healthExemptionPayment/workers');
const transferPensions = require('../../transferPensions/workers');
const rejectedReservedAmount = require('../../rejectedReservedAmount/workers');
const apsCollection = require('../../apsCollection/workers');
const notApprovedCheckpoint = require('../../notApprovedCheckpoint/workers');
const rejectedRetroactiveAmount = require('../../rejectedRetroactiveAmount/workers');
const manuallyInactivateMarkedPensions = require('../../manuallyInactivateMarkedPensions/workers');
const modifyCivilRegistryData = require('../../modifyCivilRegistryData/workers');
const winterBonusAssignment = require('../../winterBonusAssignment/workers');
const calculateReservedAmountBySurvival = require('../../calculateReservedAmountBySurvival/workers');
const totalAssetsAndDiscounts = require('../../totalAssetsAndDiscounts/workers');
const generateBonusAssignmentFile = require('../../generateBonusAssignmentFile/workers');
const expenseAccountingReport = require('../../expenseAccountingReport/workers');
const assignChristmasBonus = require('../../assignChristmasBonus/workers');
const assignNationalHolidaysBonus = require('../../assignNationalHolidaysBonus/workers');
const notificationEndOfValidity = require('../../notificationEndOfValidity/workers');
const oldAgePensionInProcess = require('../../oldAgePensionInProcess/workers');
const discountJudicialRetention = require('../../discountJudicialRetention/workers');
const calculationOfEarnedFields = require('../../calculationOfEarnedFields/workers');

const cronFilemarkAndFunction = {
  collectiondiscounthealth: {
    functionToExecute: collectionDiscountHealth.worker
  },
  widowhoodpayment: {
    functionToExecute: widowhoodPayment.worker
  },
  obtainufvalue: {
    functionToExecute: obtainUfValue.worker
  },
  registerstartandenddatestudycertificate: {
    functionToExecute: registerStartAndEndDateStudyCertificate.worker
  },
  schedulingcronjobs: {
    functionToExecute: schedulingCronjobs.worker
  },
  calculateretroactivedisabilitypension: {
    functionToExecute: calculateRetroactiveDisabilityPension.worker
  },
  calculateretroactivebank: {
    functionToExecute: calculateRetroactiveBank.worker
  },
  calculateretroactiveamountforinstitutianalpatient: {
    functionToExecute: calculateRetroactiveAmountForInstitutianalPatient.worker
  },
  calculateretroactiveamountforsurvival: {
    functionToExecute: calculateRetroactiveAmountForSurvival.worker
  },
  reservedamountassetsdiscounts: {
    functionToExecute: reservedAmountAssetsDiscounts.worker
  },
  setreservedamountforinstitutionalpatient: {
    functionToExecute: setReservedAmountForInstitutionalPatient.worker
  },
  reactivatepensionsbytransients: {
    functionToExecute: reactivatePensionsByTransients.worker
  },
  reactivateforrenewalofstudycertificate: {
    functionToExecute: reactivateForRenewalOfStudyCertificate.worker
  },
  netpensionsliquidationreports: {
    functionToExecute: netPensionsLiquidationReports.worker
  },
  inactivatebywidowunderfourtyfive: {
    functionToExecute: inactivateByWidowUnderFourtyFive.worker
  },
  inactivatebytransientpreworker: {
    functionToExecute: inactivateByTransient.inactivatePensionsByTransientsPreWorker.worker,
    dependencies: [
      'modifycivilregistrydata',
      'inactivatebydeath',
      'inactivatebytransientpostworker',
      'daysoftranstientpension',
      'inactivatebyretirementpostworker',
      'inactivatebymarriagepostworker',
      'inactivatebyretirementpreworker',
      'inactivatebymarriagepreworker'
    ]
  },
  inactivatebytransientpostworker: {
    functionToExecute: inactivateByTransient.inactivatePensionsByTransientsPostWorker.worker
  },
  inactivatebyretirementpostworker: {
    functionToExecute: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.worker
  },
  inactivatebyretirementpreworker: {
    functionToExecute: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.worker
  },
  inactivatebymarriagepostworker: {
    functionToExecute: inactivateByMarriage.marriagePostworker.worker
  },
  inactivatebymarriagepreworker: {
    functionToExecute: inactivateByMarriage.marriagePreworker.worker
  },
  inactivatebydeath: {
    functionToExecute: inactivateByDeath.worker
  },
  inactivatebyagelimit: {
    functionToExecute: inactivateByAgeLimit.worker
  },
  inactivateorreactivateprocess: {
    functionToExecute: inactivateOrReactivateProcess.worker
  },
  inactivateorreactivateorphanhoodprocess: {
    functionToExecute: InactivateOrReactivateOrphanhoodProcess.worker
  },
  inactivateorreactivateafcronsgroup: {
    functionToExecute: inactivateOrReactivateAFCronsGroup.worker
  },
  calculatereservedassetsanddiscounts: {
    functionToExecute: calculateReservedAssetsAndDiscounts.worker
  },
  ipsbulkload: {
    functionToExecute: ipsBulkLoad.worker
  },
  cajalosheroesbulkload: {
    functionToExecute: cajaLosHeroesBulkLoad.worker
  },
  cajalaaraucanabulkload: {
    functionToExecute: cajaLaAraucanaBulkLoad.worker
  },
  caja18bulkload: {
    functionToExecute: caja18BulkLoad.worker
  },
  calculatearticle41: {
    functionToExecute: calculateArticle41.worker
  },
  reactivateorphanhood: {
    functionToExecute: reactivateOrphanhood.worker
  },
  tocivilregistration: {
    functionToExecute: toCivilRegistration.worker
  },
  daysoftranstientpension: {
    functionToExecute: daysOfTranstientPension.worker
  },
  setzerovaluespensionworker: {
    functionToExecute: setZeroValuesPensionWorker.worker
  },
  calculatepaymentdatesworker: {
    functionToExecute: calculatePaymentDatesWorker.worker
  },
  generateanduploadbankfile: {
    functionToExecute: generateAndUploadBankFile.worker
  },
  reservedassetsanddiscountsamountcalculation: {
    functionToExecute: reservedAssetsAndDiscountsAmountCalculation.worker
  },
  basepensionworker: {
    functionToExecute: basePensionWorker.worker
  },
  setpensiontypechange: {
    functionToExecute: setPensionTypeChange.worker
  },
  monthlyexpenses: {
    functionToExecute: monthlyExpenses.worker
  },
  unifiedretroactiveamounts: {
    functionToExecute: unifiedRetroactiveAmounts.worker
  },
  unifiedbulkloadandips: {
    functionToExecute: unifiedBulkLoadAndIps.worker
  },
  reajustbasepensionandarticles: {
    functionToExecute: reajustBasePensionAndArticles.worker
  },
  calculatedaystopayworker: {
    functionToExecute: calculateDaysToPayWorker.worker
  },
  setfixedbasepensionandarticlesvalue: {
    functionToExecute: setFixedBasePensionAndArticlesValue.worker
  },
  resetfixedbasepensionandarticlesvalue: {
    functionToExecute: resetFixedBasePensionAndArticlesValue.worker
  },
  ipcservice: {
    functionToExecute: ipc.worker
  },
  historicalpensionreports: {
    functionToExecute: historicalPensionReports.worker
  },
  calculatetaxablepension: {
    functionToExecute: taxablePension.worker
  },
  generateanduploadpreviredfile: {
    functionToExecute: generateAndUploadPreviredFile.worker
  },
  keybuilder: {
    functionToExecute: keyBuilder.worker
  },
  ufvalue: {
    functionToExecute: obtainUfValue.worker
  },
  calculatecurrentcapital: {
    functionToExecute: calculateCurrentCapital.worker
  },
  socialdiscountscheckpoint: {
    functionToExecute: socialDiscountsCheckPoint.worker
  },
  postliquidationcheckpointreport: {
    functionToExecute: postLiquidationCheckpoint.worker
  },
  analysisofcurrentcapital: {
    functionToExecute: analysisOfCurrentCapital.worker
  },
  unifiedtotalassetsanddiscountswithnetpensionliquidationreport: {
    functionToExecute: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.worker
  },
  updatepensionerinfo: {
    functionToExecute: updatePensionerInfo.worker
  },
  sendcircularfile2480: {
    functionToExecute: sendCircularFile2480.worker
  },
  healthrejection: {
    functionToExecute: healthRejection.worker
  },
  healthexemptionpayment: {
    functionToExecute: healthExemptionPayment.worker
  },
  transferpensions: {
    functionToExecute: transferPensions.worker
  },
  rejectedreservedamount: {
    functionToExecute: rejectedReservedAmount.worker
  },
  generateanduploadapscollectionfile: {
    functionToExecute: apsCollection.worker
  },
  notapprovedcheckpoint: {
    functionToExecute: notApprovedCheckpoint.worker
  },
  rejectedretroactiveamount: {
    functionToExecute: rejectedRetroactiveAmount.worker
  },
  manuallyinactivatemarkedpensions: {
    functionToExecute: manuallyInactivateMarkedPensions.worker
  },
  modifycivilregistrydata: {
    functionToExecute: modifyCivilRegistryData.worker
  },
  winterbonusassignment: {
    functionToExecute: winterBonusAssignment.worker
  },
  calculatereservedamountbysurvival: {
    functionToExecute: calculateReservedAmountBySurvival.worker
  },
  totalassetsanddiscounts: {
    functionToExecute: totalAssetsAndDiscounts.worker
  },
  generatebonusassignmentfile: {
    functionToExecute: generateBonusAssignmentFile.worker
  },
  expenseaccountingreport: {
    functionToExecute: expenseAccountingReport.worker
  },
  assignchristmasbonus: {
    functionToExecute: assignChristmasBonus.worker
  },
  assignnationalholidaysbonus: {
    functionToExecute: assignNationalHolidaysBonus.worker
  },
  notificationendofvalidity: {
    functionToExecute: notificationEndOfValidity.worker
  },
  oldagepensioninprocess: {
    functionToExecute: oldAgePensionInProcess.worker
  },
  discountjudicialretention: {
    functionToExecute: discountJudicialRetention.worker
  },
  calculationofearnedfields: {
    functionToExecute: calculationOfEarnedFields.worker
  }
};

module.exports = cronFilemarkAndFunction;

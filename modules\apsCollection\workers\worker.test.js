/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');
const service = require('../services/apsCollection.service');
const serviceFile = require('../services/file.service');
const pensions = require('../../../resources/pensions.json');
const PensionModel = require('../../../models/pension');

const testFilePath = `${__dirname}/../../../resources/aps/test.txt`;

describe('APS Collection Worker', () => {
  beforeAll(beforeAllTests);
  // let service;
  let fileService;
  let myService;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();

    logService = {
      existsLog: jest.fn().mockImplementationOnce(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    myService = {
      ...service,
      getUnmatchedPensionLines: jest.fn().mockResolvedValue({})
    };

    fileService = {
      ...serviceFile,
      downloadAndExtractZipFile: jest.fn().mockResolvedValue(testFilePath),
      uploadDpasFileToSftpServer: jest.fn().mockResolvedValue(true),
      appendFileAsync: jest.fn(),
      compressFile: jest.fn(),
      uploadPprFileToSftpServer: jest.fn().mockResolvedValue({}),
      generatePprZipFile: jest.fn().mockResolvedValue({ pprZipFilePath: '' })
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  const composePension = rut => ({
    ...pensions[0],
    beneficiary: { ...pensions[0].beneficiary, rut },
    validityType: 'Vigente',
    createdAt: moment().subtract(1, 'month')
  });

  it('should execute worker download zip file, extract, read and generate lines for new file', async () => {
    await PensionModel.insertMany([
      composePension('4602712-4'),
      composePension('5247261-k'),
      composePension('4885593-8')
    ]);

    const result = await workerModule.workerFn({
      Logger,
      service: myService,
      logService,
      fileService,
      done
    });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalled();
    expect(result.dpapsFileData).toHaveLength(3);
    expect(result.executionCompleted).toBe(true);
    expect(Logger.info).toHaveBeenCalled();
    expect(fileService.downloadAndExtractZipFile).toHaveBeenCalled();
    expect(fileService.uploadDpasFileToSftpServer).toHaveBeenCalled();
    expect(fileService.appendFileAsync).toHaveBeenCalled();
    expect(fileService.compressFile).toHaveBeenCalled();
    expect(logService.saveLog).toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('should generate lines for pensions that are in file but not in BD', async () => {
    await PensionModel.insertMany([
      { ...composePension('4602712-4'), validityType: 'No Vigente', inactivationReason: '' },
      { ...composePension('5247261-k'), validityType: 'No Vigente', inactivationReason: '' },
      {
        ...composePension('4885593-8'),
        validityType: 'No Vigente',
        inactivationReason: 'Fallecimiento'
      }
    ]);

    const result = await workerModule.workerFn({ Logger, service, logService, fileService, done });

    expect(result.dpeapsFileData).toHaveLength(3);
  });

  it('should return if already cron is already executed', async () => {
    logService.existsLogAndRetry = jest
      .fn()
      .mockImplementationOnce(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, service, logService, fileService, done });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(fileService.downloadAndExtractZipFile).not.toHaveBeenCalled();
    expect(fileService.uploadDpasFileToSftpServer).not.toHaveBeenCalled();
    expect(fileService.appendFileAsync).not.toHaveBeenCalled();
    expect(fileService.compressFile).not.toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('should return if dependency is not already executed', async () => {
    logService.existsLog = jest.fn().mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, service, logService, fileService, done });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(fileService.downloadAndExtractZipFile).not.toHaveBeenCalled();
    expect(fileService.uploadDpasFileToSftpServer).not.toHaveBeenCalled();
    expect(fileService.appendFileAsync).not.toHaveBeenCalled();
    expect(fileService.compressFile).not.toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

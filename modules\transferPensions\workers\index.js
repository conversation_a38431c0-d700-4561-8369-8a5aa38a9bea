const service = require('../services/transfer.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'transferPensions',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_TRANSFER_PENSIONS_FREQUENCY,
  description: 'Traspaso de pensionados enlazados',
  endPoint: 'transferpensions',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

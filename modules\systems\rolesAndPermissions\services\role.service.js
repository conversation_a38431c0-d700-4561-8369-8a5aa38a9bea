const Model = require('../models/roles.model');
const utilsService = require('./utils.service');

const service = {
  ...utilsService,
  createRole: async ({ roleName, views }) => {
    try {
      const result = await Model.findOne({ roleName, enabled: false }).exec();

      if (result) {
        const savedUser = await Model.findOneAndUpdate(
          { roleName, enabled: true },
          { roleName, views, enabled: true },
          { new: true, runValidators: true }
        ).exec();

        return { completed: !!savedUser };
      }

      await Model.create({ roleName, views });

      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  readRole: async ({ roleName }) => {
    try {
      const roleNameRegex = new RegExp(roleName, 'i');
      const role = await Model.findOne({ roleName: roleNameRegex, enabled: true })
        .populate('views.view')
        .lean()
        .exec();
      return { role };
    } catch (error) {
      return { error };
    }
  },

  readAllRoles: async () => {
    try {
      const roles = await Model.find({ enabled: true })
        .populate('views.view')
        .lean()
        .exec();
      return { roles };
    } catch (error) {
      return { error };
    }
  },
  updateRole: async ({ roleName, view, newPermission }) => {
    try {
      const roleNameRegex = new RegExp(roleName, 'i');
      const role = await Model.findOne({ roleName: roleNameRegex, enabled: true })
        .lean()
        .exec();

      if (!role) throw new Error('role does not exist');

      const { _id, views = [] } = role;

      const unmodifiedPermissionView = views.filter(
        currentUserView => currentUserView.view.toString() !== view
      );
      const modifiedPermissionView = views.find(
        currentUserView => currentUserView.view.toString() === view
      );

      if (!modifiedPermissionView) throw new Error('view does not exist');

      const updatededRole = await Model.findOneAndUpdate(
        { _id },
        {
          $set: {
            views: [
              ...unmodifiedPermissionView,
              { ...modifiedPermissionView, permission: newPermission }
            ]
          }
        },
        { new: true }
      ).exec();

      return { completed: !!updatededRole };
    } catch (error) {
      return { error };
    }
  }
};

module.exports = { ...service, Model };

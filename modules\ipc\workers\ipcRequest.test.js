const { soap } = require('strong-soap');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const soapRequest = require('./ipcRequest');

describe('IPC request test', () => {
  beforeAll(beforeAllTests);
  let mocks;

  beforeEach(async () => {});
  it('fails soapRequest createClient', async () => {
    const reject = jest.fn();
    const resolve = jest.fn();
    mocks = {
      GetSeries: jest.fn().mockImplementation((msg, cb) => {
        cb('error', true);
      })
    };
    jest.spyOn(soap, 'createClient').mockImplementation((msg, json, cb) => {
      expect(msg).toBe('https://si3.bcentral.cl/SieteWS/SieteWS.asmx?wsdl');
      cb('error', mocks);
    });

    await soapRequest(resolve, reject);
  });

  it('success soapRequest createClient', async () => {
    mocks = {
      GetSeries: jest.fn().mockImplementation((msg, cb) => {
        cb(null, true);
      })
    };
    jest.spyOn(soap, 'createClient').mockImplementation((msg, json, cb) => {
      expect(msg).toBe('https://si3.bcentral.cl/SieteWS/SieteWS.asmx?wsdl');
      cb(null, mocks);
    });
    const reject = jest.fn();
    const resolve = jest.fn();
    await soapRequest(resolve, reject);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

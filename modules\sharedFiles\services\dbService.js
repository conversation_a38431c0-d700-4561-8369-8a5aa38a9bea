/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */
const processedCronJobModel = require('../models/processedJob');

const service = {
  async createProcessedFileMark(year, month, name) {
    return processedCronJobModel.create({ year, month, name }).catch(console.log);
  },

  async getProcessedFileMark(year, month, name) {
    const result = await processedCronJobModel.findOne({ year, month, name }).catch(console.log);
    return result ? result.alreadyProcessed : false;
  }
};

module.exports = {
  ...service
};

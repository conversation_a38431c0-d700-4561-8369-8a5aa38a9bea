const mongoose = require('mongoose');

const { Schema } = mongoose;

const InactivateTransientSchema = new Schema(
  {
    beneficiaryRut: { type: String, required: true },
    causantRut: { type: String, required: true },
    inactivationReason: { type: String, required: true },
    endDateOfValidity: { type: Date, required: true },
    dateToInactivate: { type: Date, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

InactivateTransientSchema.index({ createdAt: 1 }, { expires: '30d' });
InactivateTransientSchema.index({ idCode: 1 });

module.exports = mongoose.model('InactivatePension', InactivateTransientSchema);

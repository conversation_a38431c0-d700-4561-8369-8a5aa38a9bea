[{"totalTransitoryDaysToPay": 21, "enabled": true, "validityType": "<PERSON><PERSON>nte vital<PERSON>", "transient": "Si", "country": "CHI", "paymentInfo": {"paymentGateway": "otro", "accountNumber": "071-222-444-008", "bank": "Banco 1"}, "causant": {"rut": "********-4", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-5", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": "<PERSON><PERSON>. <PERSON>", "commune": "San miguel", "city": "Santiago"}, "beneficiary": {"rut": "********-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": "<EMAIL>"}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": [], "familyAssignment": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": [], "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": []}, "numberOfCharges": 0, "basePension": 99999999.0999999, "numberOfNonFormulableDiscounts": 1, "numberOfNetNonFormulableAssets": 1, "numberOfTaxableNonFormulableAssets": 0, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "article40": 1, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "discounts": {"healthUF": 12000, "onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": *********, "afp": 0, "totalNonFormulable": 0, "adjustedOnePercent": 0, "nonFormulableByReason": []}, "totalEstimatedDaysToPay": 0, "cun": "", "initialBasePension": 89000, "dateOfBirth": "1955-06-01T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "pensionType": "Pensión por enfermedad profesional", "disabilityDegree": 75, "disabilityType": "Invalidez parcial", "resolutionNumber": 91154119, "accidentNumber": 1288639, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2001-06-15T04:00:00.000Z", "pensionCodeId": "11135", "pensionStartDate": "2010-01-02T03:00:00.000Z", "createdAt": "2020-07-14T23:49:02.586Z", "updatedAt": "2020-07-14T23:59:17.380Z", "validatedStudyPeriod": "No", "taxablePension": 0, "netPension": 0, "discountsAndAssets": "5ed6cb684fc56fb0eeed4d4b", "linkedDate": "2020-06-02T21:58:00.613Z", "evaluationDate": "2020-08-01T04:00:00.000Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3"}]
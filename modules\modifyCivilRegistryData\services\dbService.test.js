/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const pensionModel = require('../../../models/pension');
const dataPensions = require('../../../resources/pensions.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./dbService');

describe('DBservice keyBuilder service Test', () => {
  beforeAll(beforeAllTests);

  let lines;

  beforeEach(() => {
    lines = [
      {
        rut: '19685004-K',
        resultFlag: '1',
        lastName: 'CARMONA',
        mothersLastName: 'PALOMINOS',
        name: 'HUMBERTO CARLOS',
        dateOfBirth: '1997-10-08T04:00:00.000Z',
        gender: 'M'
      }
    ];
  });

  it('should return modified civil registry data when there are no errors', async () => {
    await pensionModel.create(dataPensions[0]);
    const expectedLength = 1;
    const expectedlastName = lines[0].lastName;
    const expectedMothersLastName = lines[0].mothersLastName;
    const expectedName = lines[0].name;
    const expectedDateOfBirth = lines[0].dateOfBirth;
    const expectedGender = lines[0].gender;

    const modifiedPensions = await service.modifyCivilRegistryData(lines);

    expect(modifiedPensions).toBeDefined();
    expect(modifiedPensions.length).toBe(expectedLength);
    expect(modifiedPensions[0].beneficiary.lastName).toBe(expectedlastName);
    expect(modifiedPensions[0].beneficiary.mothersLastName).toBe(expectedMothersLastName);
    expect(modifiedPensions[0].beneficiary.name).toBe(expectedName);
    expect(modifiedPensions[0].dateOfBirth).toBe(expectedDateOfBirth);
    expect(modifiedPensions[0].gender).toBe(expectedGender);
  });

  afterEach(async () => {
    await pensionModel.deleteMany().catch(error => console.log(error));
  });
  afterAll(afterAllTests);
});

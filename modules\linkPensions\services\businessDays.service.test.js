const moxios = require('moxios');

const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const {
  isActionAllowedOnCurrentMonth,
  isActionAllowedOnCurrentDate,
  getMonthHolidays,
  getFirstNbusinessDays
} = require('./businessDays.service');

const holidayRequest = require('../../../resources/holidaysRequest.json');

describe('Temporary pensions validation', () => {
  beforeAll(beforeAllTests);
  beforeEach(() => {
    moxios.install();
  });

  process.env = Object.assign(process.env, {
    HOLIDAYS_URL: 'https://apis.digital.gob.cl/fl/feriados'
  });

  const getMonthHolidaysFn = jest.fn().mockResolvedValue(['20200101']);

  it('should allow action for the month without restrictions', async () => {
    const currentDate = new Date();
    const { actionIsAllowed, totalDays } = await isActionAllowedOnCurrentMonth(currentDate);
    expect(actionIsAllowed).toBe(true);
    expect(totalDays).toBe(moment(currentDate).daysInMonth());
  });
  it('should allow action if in day range', async () => {
    const { actionIsAllowed, inDayRange, totalDays } = await isActionAllowedOnCurrentDate(
      '20200106',
      5,
      getMonthHolidaysFn
    );
    expect(actionIsAllowed).toBe(true);
    expect(inDayRange).toBe(true);
    expect(totalDays).toBe(5);
  });

  it('should NOT allow action if NOT in day range', async () => {
    const { actionIsAllowed, inDayRange, totalDays } = await isActionAllowedOnCurrentDate(
      '20200106',
      3,
      getMonthHolidaysFn
    );
    expect(actionIsAllowed).toBe(false);
    expect(inDayRange).toBe(false);
    expect(totalDays).toBe(3);
  });

  it('action is not allowed for the selected date ', async () => {
    const { actionIsAllowed, inDayRange } = await isActionAllowedOnCurrentDate(
      '2020-03-06',
      4,
      getMonthHolidaysFn
    );
    expect({ actionIsAllowed, inDayRange }).toBeDefined();
    expect(actionIsAllowed).toBe(false);
    expect(inDayRange).toBe(false);
  });

  it('get current chilean holidays ', async () => {
    moxios.stubRequest('https://apis.digital.gob.cl/fl/feriados/2013/10', {
      status: 200,
      response: holidayRequest
    });

    const expected = await getMonthHolidays(2013, 10);

    expect(expected).toBeDefined();
    expect(expected[0]).toBe('2013-10-12');
    expect(expected[1]).toBe('2013-10-31');
  });

  it('get N first business days ', async () => {
    const expected = await getFirstNbusinessDays('2020-01-01', 4, getMonthHolidaysFn);

    expect(expected).toBeDefined();
    expect(expected.length).toBe(4);
  });
  afterEach(() => {
    moxios.uninstall();
  });
  afterAll(afterAllTests);
});

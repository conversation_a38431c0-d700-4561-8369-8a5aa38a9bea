/* eslint-disable no-underscore-dangle */
const { roundValue } = require('../../../sharedFiles/helpers');

const SI = /s[iíìîï]/i;
const VALIDITY_TYPE = /No\s+vigente/i;
const JOB_ACCIDENT = /Pensi[oó]n por accidente de trabajo/i;
const TRAJECT_ACCIDENT = /Pensi[oó]n por accidente de trayecto/i;
const DISEASE = /Pensi[oó]n por enfermedad profesional/i;
const CAJA_DIECIOCHO = /Caja 18/i;
const LA_ARAUCANA = /La Araucana/i;
const LOS_HEROES = /Los Heroes/i;
const LOS_ANDES = /Los Andes/i;

const getRebsal = ({ healthDiscount, taxablePension }) => {
  return SI.test(healthDiscount) ? roundValue(taxablePension * 0.07) : 0;
};

const getAdjustedHealthExemption = ({ healthExemption, taxablePension }) => {
  return SI.test(healthExemption) ? roundValue(taxablePension * 0.07) : 0;
};

const formatNameAfp = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[aáàâäãå]/gi, 'a')
    .replace(/[eéèêë]/gi, 'e')
    .replace(/[iíìîï]/gi, 'i')
    .replace(/[oóòôöõ]/gi, 'o')
    .replace(/[uúùûü]/gi, 'u')
    .toLowerCase()
    .trim();

const getPercentCompentation = (pensionPlusLaws, compentation) => {
  const onePercentAmount = pensionPlusLaws * (compentation.percentage / 100);
  return compentation.isMaxAmount && onePercentAmount > compentation.maxAmount
    ? roundValue(compentation.maxAmount)
    : roundValue(onePercentAmount);
};

const getPercentAdjusted = ({
  onePercentLaAraucana,
  onePercent18,
  onePercentLosAndes,
  onePercentLosHeroes,
  pensionPlusLaws,
  compentation
}) => {
  if (SI.test(onePercentLaAraucana)) {
    const laAraucana = compentation.find(element => LA_ARAUCANA.test(element.name));
    return getPercentCompentation(pensionPlusLaws, laAraucana);
  }

  if (SI.test(onePercent18)) {
    const caja18 = compentation.find(element => CAJA_DIECIOCHO.test(element.name));
    return getPercentCompentation(pensionPlusLaws, caja18);
  }

  if (SI.test(onePercentLosAndes)) {
    const losAndes = compentation.find(element => LOS_ANDES.test(element.name));
    return getPercentCompentation(pensionPlusLaws, losAndes);
  }

  if (SI.test(onePercentLosHeroes)) {
    const losHeroes = compentation.find(element => LOS_HEROES.test(element.name));
    return getPercentCompentation(pensionPlusLaws, losHeroes);
  }

  return 0;
};

const getHealthAmount = ({ healthUF, ufValue, taxablePension }) => {
  return healthUF > 0 ? roundValue(healthUF * ufValue) : roundValue(taxablePension * 0.07);
};

const afpFilter = (afp, name) => {
  const specialCharactersValidation = new RegExp(formatNameAfp(afp.name));
  return specialCharactersValidation.test(formatNameAfp(name));
};

const getAfpAmount = (pensionType, name, taxablePension, afps = []) => {
  if (
    JOB_ACCIDENT.test(pensionType) ||
    TRAJECT_ACCIDENT.test(pensionType) ||
    DISEASE.test(pensionType)
  ) {
    const afp = afps.find(x => afpFilter(x, name));
    if (afp) return roundValue((afp.percentage / 100) * taxablePension);
  }
  return 0;
};

const calculateNetPension = (
  pension = {},
  liquidation = {},
  afps = [],
  ufValue = 0,
  compentation = {}
) => {
  const {
    assets = {},
    discounts = {},
    pensionType = {},
    afpAffiliation = {},
    retroactiveAmounts = {},
    basePension = 0,
    article40 = 0,
    law19403 = 0,
    law19539 = 0,
    law19953 = 0
  } = pension;
  const {
    onePercentLaAraucana = 'No',
    onePercent18 = 'No',
    onePercentLosAndes = 'No',
    onePercentLosHeroes,
    healthUF = 0,
    socialCredits18 = 0,
    socialCreditsLaAraucana = 0,
    socialCreditsLosHeroes = 0,
    socialCreditsLosAndes = 0,
    othersLosAndes = 0,
    othersLosHeroes = 0,
    healthLoan = 0,
    totalNonFormulable = 0
  } = discounts;
  const {
    healthDiscount = 'No',
    healthExemption = 'No',
    netTotalNonFormulable = 0,
    nationalHolidaysBonus = 0,
    christmasBonus = 0,
    winterBonus = 0,
    forFamilyAssignment: assignmetForFamily = 0
  } = assets;

  const {
    forInstitutionalPatient = 0,
    forRejection = 0,
    forTotalNonFormulableDiscounts = 0,
    forNetTotalNonFormulableAssets = 0,
    forBonuses = 0,
    forPayCheck = 0,
    forFamilyAssignment = 0
  } = retroactiveAmounts;

  const { taxablePension = 0 } = liquidation;
  const pensionPlusLaws = basePension + article40 + law19403 + law19539 + law19953;

  const rebsal = getRebsal({ healthDiscount, taxablePension });
  const adjustedHealthExemption = getAdjustedHealthExemption({ healthExemption, taxablePension });
  const percentAdjusted = getPercentAdjusted({
    onePercentLaAraucana,
    onePercent18,
    onePercentLosAndes,
    onePercentLosHeroes,
    pensionPlusLaws,
    compentation
  });

  const health = getHealthAmount({ healthUF, ufValue, taxablePension });
  const afp = getAfpAmount(pensionType, afpAffiliation, taxablePension, afps);

  const netPension = roundValue(
    taxablePension +
      nationalHolidaysBonus +
      christmasBonus +
      winterBonus +
      forBonuses +
      rebsal +
      assignmetForFamily +
      forInstitutionalPatient +
      adjustedHealthExemption +
      forPayCheck +
      forFamilyAssignment +
      forRejection +
      forNetTotalNonFormulableAssets +
      netTotalNonFormulable -
      percentAdjusted -
      totalNonFormulable -
      afp -
      forTotalNonFormulableDiscounts -
      health -
      socialCredits18 -
      socialCreditsLaAraucana -
      socialCreditsLosHeroes -
      socialCreditsLosAndes -
      othersLosAndes -
      healthLoan -
      othersLosHeroes
  );

  if (netPension == null || Number.isNaN(netPension)) {
    throw new Error('NaN netPension');
  }

  return {
    pension: {
      ...pension,
      assets: {
        ...assets,
        rebsal,
        adjustedHealthExemption
      },
      discounts: {
        ...discounts,
        onePercentAdjusted: percentAdjusted,
        health,
        afp
      }
    },
    liquidation: {
      ...liquidation,
      taxablePension,
      netPension
    }
  };
};

const service = {
  async netPension(
    pensionService,
    liquidationService,
    afpService,
    ufService,
    cajaService,
    pensions
  ) {
    const { result: pensionList } =
      pensions && pensions.length
        ? { result: pensions }
        : await pensionService.getAllWithFilter(
            {
              enabled: true,
              validityType: { $not: VALIDITY_TYPE }
            },
            {
              beneficiary: 1,
              causant: 1,
              assets: 1,
              discounts: 1,
              pensionType: 1,
              afpAffiliation: 1,
              retroactiveAmounts: 1,
              basePension: 1,
              article40: 1,
              law19403: 1,
              law19539: 1,
              law19953: 1,
              validityType: 1,
              enabled: 1,
              pensionCodeId: 1
            }
          );
    const { result: afps } = await afpService.getAfpsWithFilters(
      { enabled: true },
      { name: 1, percentage: 1 }
    );
    const { value: ufValue } = (await ufService.getCurrentUfValue()) || {};
    if (ufValue == null || Number.isNaN(ufValue)) {
      return { completed: false, error: 'cannot get UF value' };
    }

    const calculatedLiquidations = [];
    const calculatedPensions = [];
    const { result: compentation } = await cajaService.getCajas();

    await Promise.all(
      pensionList.map(async item => {
        const { _id: id, ...pension } = item;
        const beneficiaryRut = pension.beneficiary.rut;
        const causantRut = pension.causant.rut;
        const idPension = pension.pensionCodeId;
        const { result: liquidationResults } = await liquidationService.getAllWithFilter(
          {
            enabled: true,
            beneficiaryRut,
            causantRut,
            pensionCodeId: idPension
          },
          {
            beneficiaryRut: 1,
            causantRut: 1,
            taxablePension: 1,
            netPension: 1,
            enabled: 1,
            pensionCodeId: 1
          }
        );

        if (liquidationResults.length > 0) {
          const { _id, ...liquidationObject } = liquidationResults[0];
          const { pension: pensionResult, liquidation: liquidationResult } = calculateNetPension(
            pension,
            liquidationObject,
            afps,
            ufValue,
            compentation
          );
          calculatedPensions.push(pensionResult);
          calculatedLiquidations.push(liquidationResult);
        }
      })
    );

    const {
      completed: completedPension,
      error: errorPension
    } = await pensionService.updatePensions(calculatedPensions);
    if (!completedPension) {
      return { completed: false, error: errorPension };
    }
    const {
      completed: completedLiquidation,
      error: errorLiquidation
    } = await liquidationService.createUpdateLiquidation(calculatedLiquidations);
    return { completed: completedLiquidation, error: errorLiquidation };
  }
};

module.exports = { calculateNetPension, ...service };

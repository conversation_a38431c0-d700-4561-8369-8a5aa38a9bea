/* eslint-disable consistent-return */
const Sftp = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const fileGenerationUtils = require('../services/fileGenerationUtils');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'collectionDiscountHealth',
  worker: async deps =>
    workerModule.workerFn({ service, logService, fileGenerationUtils, Sftp, ...deps }),
  repeatInterval: process.env.CRON_COLLECTION_DISCOUNT_HEALTH_FRECUENCY,
  description: 'Cron para generar archivo Rebaja Salud APS',
  endPoint: 'collectiondiscounthealth',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const moment = require('moment');
const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const service = require('../modules/quadrature/services/quadrature.service');
const FactoryController = require('../modules/quadrature/controllers/quadrature.controller');
const { concurrencyValidators, factorValidators } = require('../modules/quadrature/validators');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const quadratureController = FactoryController({
    HttpStatus,
    ErrorBuilder,
    moment,
    service,
    validationResult,
    Logger
  });

  router.get('/time', validateAccess(), quadratureController.getDate);
  router.get('/was-data-uploaded', validateAccess(), quadratureController.wasDataUploaded);
  router.post(
    '/insert-quadrature-data/factors',
    [validateAccess(), factorValidators],
    quadratureController.insertFactorsData
  );
  router.post(
    '/insert-quadrature-data/concurrencies',
    [validateAccess(), concurrencyValidators],
    quadratureController.insertConcurrenciesData
  );
  router.get('/get-quadrature-data/factors', validateAccess(), quadratureController.getFactors);
  router.get(
    '/get-quadrature-data/concurrencies',
    validateAccess(),
    quadratureController.getConcurrencies
  );
};

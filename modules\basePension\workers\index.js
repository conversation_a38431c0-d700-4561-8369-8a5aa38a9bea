const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/base.pension.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'basePensionWorker',
  worker: deps => workerModule.workerFn({ service, pensionService, logService, ...deps }),
  repeatInterval: process.env.CRON_BASE_MINIMUN_PENSION_WORKER,
  description: '<PERSON><PERSON><PERSON> mín<PERSON> (calculo de pensión minima y bonos )',
  endPoint: 'basepensionworker',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMarkOne
};

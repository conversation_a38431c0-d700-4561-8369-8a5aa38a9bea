const moment = require('moment');
const { removeAccents } = require('../../sharedFiles/helpers');

const cutAndAddSpaces = (word, size) => word.padEnd(size, ' ').substr(0, size);

const mapperData = item => {
  const { beneficiary = {}, dateOfBirth } = item;
  const { rut, lastName, mothersLastName, name } = beneficiary;
  const EÑE_REGEX = /ñ/gi;

  const [nationalNumber, dv = ''] = rut.replace(/\./g, '').split('-');
  const beneficiaryRut = nationalNumber.padStart(8, 0);
  const nationalId = `${beneficiaryRut}${dv}`.substr(0, 9);
  const beneficiaryName = cutAndAddSpaces(name.replace(EÑE_REGEX, '@'), 50);
  const beneficiarLastName = cutAndAddSpaces(lastName.replace(EÑE_REGEX, '@'), 30);
  const beneficiaryMothersLastName = cutAndAddSpaces(mothersLastName.replace(EÑE_REGEX, '@'), 30);
  const beneficiaryBirthDate = moment(dateOfBirth)
    .format('YYYYMMDD')
    .padEnd(20, ' ')
    .substr(0, 20);
  const flag = '11111';
  const finalLine = removeAccents(
    `${nationalId}${beneficiarLastName}${beneficiaryMothersLastName}${beneficiaryName}${beneficiaryBirthDate}${flag}`
  );
  return [finalLine];
};

module.exports = mapperData;

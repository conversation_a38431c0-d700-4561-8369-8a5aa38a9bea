const cronDescription = 'setting pension type change';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const cronMark = 'SET_PENSION_TYPE_CHANGE';
const successMessage = `Process ${cronMark} completed successfully.`;
const dependencyMark = 'INACTIVATE_OR_REACTIVATE_ORPHANHOOD_PROCESS';

const getMissingDependencyMessage = dep => `La dependencia ${dep} aun no se ha ejecutado`;

const workerFn = async ({ Logger, logService, pensionService, service, done }) => {
  try {
    Logger.info(`${cronDescription}: checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { executionCompleted: false, message: getMissingDependencyMessage(dependencyMark) };
    }

    Logger.info(`Inicio proceso: ${cronDescription}`);
    const { error } = await service.pensionTypesToChange(pensionService);

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`Fin proceso: ${cronDescription}`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

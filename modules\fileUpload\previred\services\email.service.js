const moment = require('moment');
const RolesModel = require('../../../systems/rolesAndPermissions/models/roles.model');
const UserModel = require('../../../../models/user');
const mailService = require('../../../sendMail/service/sendMail.service');
const { getMonth } = require('../../../sharedFiles/services/helpers.service');

const DEFAULT_FROM_EMAIL = '<EMAIL>';

const ROL_TYPE = [/Jefe de PEC/i];
const { TEMPLATE_ID_CRON_NOTIFICATION } = process.env;

const subject = `Generación de archivo Previred ${moment().format('MM_YYYY')} exitoso.`;
const info = {
  name: 'Cron que crea y envía archivo Previred',
  month: getMonth(moment().month()),
  year: moment().year(),
  status: 'OK',
  title:
    'Se ha generado el archivo Previred correspondiente a este mes y esta disponible para su descarga en el sistema',
  subject
};

const buildEmailContent = sendersList => ({
  to: sendersList,
  from: DEFAULT_FROM_EMAIL,
  cc: '',
  templateId: TEMPLATE_ID_CRON_NOTIFICATION,
  fromname: 'ACHS-PEC',
  dynamic_template_data: {
    ...info
  }
});

const filterUserByRol = async idRoles => {
  try {
    return UserModel.aggregate([
      {
        $addFields: { userRoleId: { $toString: '$role' } }
      },
      {
        $match: {
          userRoleId: { $in: idRoles }
        }
      }
    ]);
  } catch (error) {
    return [];
  }
};

const getRoles = async () => {
  try {
    return RolesModel.aggregate([
      {
        $addFields: { rolId: { $toString: '$_id' } }
      },
      {
        $match: {
          roleName: { $in: ROL_TYPE }
        }
      }
    ]);
  } catch (error) {
    return [];
  }
};

const service = {
  async destinationList() {
    const filteRoles = await getRoles();
    const idRoles = filteRoles.map(rol => {
      return rol.rolId;
    });

    const users = await filterUserByRol(idRoles);
    return users.map(user => {
      return user.email;
    });
  },
  async sendNotificationEmail() {
    try {
      const sendersList = await this.destinationList();
      const emailList = sendersList.toString();

      const content = buildEmailContent(emailList);
      const { error } = await mailService.sendEmail(content);
      if (error) throw new Error(error);

      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const CriteriaBuilder = require('../lib/builders/criteria-builder');

const afpFactoryController = require('../modules/nomenclators/afp/controllers/index.controller');
const afpService = require('../modules/nomenclators/afp/services/index.service');
const afpValidators = require('../modules/nomenclators/afp/validators');

const bankFactoryController = require('../modules/nomenclators/banks/controllers/index.controller');
const banksService = require('../modules/nomenclators/banks/services/index.service');
const bankValidators = require('../modules/nomenclators/banks/validators');

const IsapreFactoryController = require('../modules/nomenclators/isapre/controllers/index.controller');
const isapreService = require('../modules/nomenclators/isapre/services/index.service');
const {
  isapreValidators,
  isaprePortalValidators
} = require('../modules/nomenclators/isapre/validators');

const FactoryControllerMinimun = require('../modules/nomenclators/basePension/controllers/basePension.controller');
const minimunService = require('../modules/nomenclators/basePension/services/basePension.service');
const minimunValidators = require('../modules/nomenclators/basePension/validators/index');

const ServipagFactoryController = require('../modules/nomenclators/servipag/controllers/servipagBranchOffices.controller');
const servipagService = require('../modules/nomenclators/servipag/services/servipagBranchOffices.service');
const servipagValidators = require('../modules/nomenclators/servipag/validators');

const MotiveFactoryController = require('../modules/nomenclators/motive/controllers/index.controller');
const motiveService = require('../modules/nomenclators/motive/services/index.service');
const motiveValidators = require('../modules/nomenclators/motive/validators');
const validateAccess = require('../lib/auth/validate');

const CajaFactoryController = require('../modules/nomenclators/cajaCompensacion/controllers/index.controller');
const cajaService = require('../modules/nomenclators/cajaCompensacion/services/index.service');

module.exports = router => {
  const afpController = afpFactoryController(
    { HttpStatus, ErrorBuilder, Logger, CriteriaBuilder, afpService, validationResult } // deps injected
  );

  const isapreController = IsapreFactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    CriteriaBuilder,
    isapreService,
    validationResult
  });

  const bankController = bankFactoryController(
    { HttpStatus, ErrorBuilder, Logger, CriteriaBuilder, banksService, validationResult } // deps injected
  );
  const minimunController = FactoryControllerMinimun({
    HttpStatus,
    ErrorBuilder,
    Logger,
    CriteriaBuilder,
    minimunService,
    validationResult
  });

  const servipagController = ServipagFactoryController(
    { HttpStatus, ErrorBuilder, Logger, CriteriaBuilder, servipagService, validationResult } // deps injected
  );

  const motiveController = MotiveFactoryController(
    { HttpStatus, ErrorBuilder, Logger, CriteriaBuilder, motiveService, validationResult } // deps injected
  );

  const cajaController = CajaFactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    CriteriaBuilder,
    cajaService,
    validationResult
  });

  router.get('/afp', validateAccess(), afpController.getAfps);
  router.post('/afp', validateAccess(), afpValidators, afpController.createAfp);
  router.put('/afp', validateAccess(), afpValidators, afpController.updateAfp);
  router.delete('/afp/:id', validateAccess(), afpController.deleteAfp);

  router.get('/bank', validateAccess(), bankController.getBanks);
  router.post('/bank', validateAccess(), bankValidators, bankController.createBank);
  router.put('/bank', validateAccess(), bankValidators, bankController.updateBank);
  router.delete('/bank/:id', validateAccess(), bankController.deleteBank);

  router.get('/minimun-pensions-and-bonus', validateAccess(), minimunController.getAll);
  router.put(
    '/minimun-pensions-and-bonus',
    validateAccess(),
    minimunValidators,
    minimunController.update
  );

  router.get('/isapres', validateAccess(), isapreController.getIsapres);
  router.post('/isapres', validateAccess(), isapreValidators, isapreController.createIsapre);
  router.put('/isapres', validateAccess(), isapreValidators, isapreController.updateIsapre);
  router.delete('/isapres/:id', validateAccess(), isapreController.deleteIsapre);
  router.post(
    '/isapres/temporaryIsaprePortal/bulk',
    validateAccess(),
    isaprePortalValidators,
    isapreController.insertTemporaryIsaprePortal
  );
  router.post(
    '/isapres/temporaryIsaprePortal/bulkUpdate',
    validateAccess(),
    isapreController.bulkUpdate
  );
  router.get(
    '/isapres/temporaryIsaprePortal/was-executed',
    validateAccess(),
    isapreController.wasExecuted
  );

  router.get('/servipag', validateAccess(), servipagController.getAll);
  router.post('/servipag', validateAccess(), servipagValidators, servipagController.createServipag);
  router.put('/servipag', validateAccess(), servipagValidators, servipagController.updateServipag);
  router.delete('/servipag/:id', validateAccess(), servipagController.delete);

  router.get('/motive', validateAccess(), motiveController.getMotives);
  router.post('/motive', validateAccess(), motiveValidators, motiveController.createMotive);
  router.put('/motive', validateAccess(), motiveValidators, motiveController.updateMotive);
  router.delete('/motive/:id', validateAccess(), motiveController.deleteMotive);

  router.get('/caja', validateAccess(), cajaController.getCajas);
  router.put('/caja', validateAccess(), cajaController.updateCaja);
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('Analysis of Current Capital Worker Test', () => {
  beforeAll(beforeAllTests);

  let Logger;
  let done;
  let logService;
  let service;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      saveLog: jest.fn().mockResolvedValue(true),
      existsLog: jest.fn(mark => mark === 'CALCULATE_CURRENT_CAPITAL'),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = {
      generateValidPensionsInPrevAndCurrMonthReport: jest.fn(),
      generatePensionsLinkedAndReactivatedInCurrentMonthReport: jest.fn(),
      generatePensionsInactInCurrMonthReport: jest.fn(),
      generateValidPensionInCurrentMonthReport: jest.fn()
    };
  });

  it('should execute worker correctly', async () => {
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      done,
      service,
      logService
    });
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(Logger.error).not.toHaveBeenCalled();
    expect(service.generateValidPensionsInPrevAndCurrMonthReport).toHaveBeenCalledTimes(2);
    expect(service.generatePensionsLinkedAndReactivatedInCurrentMonthReport).toHaveBeenCalledTimes(
      1
    );
    expect(service.generatePensionsInactInCurrMonthReport).toHaveBeenCalledTimes(1);
    expect(executionCompleted).toBe(true);
  });

  it('should return if process was previously executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    const { message } = await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.generateValidPensionsInPrevAndCurrMonthReport).not.toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(message).toBe('This process was already executed for the current month.');
    expect(done).toHaveBeenCalled();
  });

  it('should throw error if there is any in generateValidPensionsInPrevAndCurrMonthReport', async () => {
    service.generateValidPensionsInPrevAndCurrMonthReport = jest.fn(() => {
      throw new Error();
    });
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.generateValidPensionsInPrevAndCurrMonthReport).toHaveBeenCalled();
    expect(service.generatePensionsLinkedAndReactivatedInCurrentMonthReport).not.toHaveBeenCalled();
    expect(service.generatePensionsInactInCurrMonthReport).not.toHaveBeenCalled();
    expect(service.generateValidPensionInCurrentMonthReport).not.toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('should throw error if there is any in generatePensionsLinkedAndReactivatedInCurrentMonthReport', async () => {
    service.generatePensionsLinkedAndReactivatedInCurrentMonthReport = jest.fn(() => {
      throw new Error();
    });
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.generateValidPensionsInPrevAndCurrMonthReport).toHaveBeenCalled();
    expect(service.generatePensionsLinkedAndReactivatedInCurrentMonthReport).toHaveBeenCalled();
    expect(service.generatePensionsInactInCurrMonthReport).not.toHaveBeenCalled();
    expect(service.generateValidPensionInCurrentMonthReport).not.toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

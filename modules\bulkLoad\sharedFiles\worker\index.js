/* eslint-disable consistent-return */
const sftp = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const pensionService = require('../../../pensions/services/pension.service');
const { downloadFilesFromSFTP } = require('../ftpFileDownloader');
const { getParsedLinesFromFiles } = require('../filesParser');

const workerFn = require('./worker');

module.exports = {
  name: 'shared-worker-bulkLoad',
  worker: deps =>
    workerFn({
      logService,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles,
      pensionService,
      ...deps
    })
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const { getDataToReactivate } = require('./sapRequests');

describe('sap request', () => {
  beforeAll(beforeAllTests);
  let axios;

  it('get data to reactivate', async () => {
    axios = jest.fn(() => Promise.resolve([]));
    const expected = await getDataToReactivate({ axios, accidentCode: '5165858' });
    expect(expected).toStrictEqual([]);
  });

  it('fail get data to reactivate', async () => {
    axios = jest.fn(() => Promise.reject(new Error('fail get Data')));

    const { data } = await getDataToReactivate({ axios, accidentCode: '5165858' });
    expect(data.incapacidad).toStrictEqual([]);
    expect(data.reposos).toStrictEqual([]);
  });
  afterAll(afterAllTests);
});

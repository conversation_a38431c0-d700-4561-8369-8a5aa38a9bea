const moment = require('moment');

const { encrypt } = require('../services/crypto');

const WORD_TRUE = /S[ií]/i;
const removeDashSpaceTrim = value => `${value}`.replace(/^\s*-\s*$|^\s+|\s+$/, '');

const formatters = {
  array: ({ defaultValue, value = {} }) => {
    if (value.v === '') return defaultValue;
    return [];
  },
  boolean: ({ defaultValue, value = {} }) => {
    if (value.v === '') return defaultValue;
    if (typeof value.v === 'boolean') return value.v;
    return WORD_TRUE.test(`${value.v}`);
  },
  date: ({ defaultValue, value = {} }) => {
    if (value.v === '') return moment(defaultValue, 'DD-MM-YYYY').toISOString();
    if (moment(value.w, 'DD-MM-YYYY').isValid()) return moment(value.w, 'DD-MM-YYYY').toISOString();
    if (moment(value.w, 'MM-DD-YYYY').isValid()) return moment(value.w, 'MM-DD-YYYY').toISOString();
    return undefined;
  },
  encrypt: ({ defaultValue, value = {} }) => {
    if (value.v === '') return encrypt(defaultValue);
    return encrypt(`${value.v}`);
  },
  number: ({ defaultValue, value = {}, minValue }) => {
    if (typeof value.v === 'number' && typeof minValue === 'number' && value.v < minValue)
      return minValue;
    if (value.v === '') return defaultValue;
    return +removeDashSpaceTrim(value.v);
  },
  word: ({ defaultValue, value = {} }) => {
    if (!value.w) return defaultValue;
    return removeDashSpaceTrim(value.w);
  },
  forcedBooleanValue: ({ defaultValue }) => defaultValue,
  forcedDateValue: ({ defaultValue }) => moment(defaultValue, 'DD-MM-YYYY').toISOString()
};

module.exports = formatters;

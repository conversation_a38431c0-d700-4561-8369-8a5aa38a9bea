const crypto = require('crypto');

const generateRandomName = () =>
  crypto
    .randomBytes(64)
    .toString('hex')
    .substring(0, 7);

const startingDummyRut = 40000000;
const rutSpacing = 100;

const generateDummyRut = pensionerIndex => {
  const rutWithoutDV = `${startingDummyRut + rutSpacing * pensionerIndex}`;
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const multipliersLength = multipliers.length;
  const sumOfElements = rutWithoutDV
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliersLength])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'K';
  }
  if (actualDV === module) actualDV = 0;
  return `${rutWithoutDV}-${actualDV}`;
};

const generateDummyObj = index => {
  return {
    rut: generateDummyRut(index),
    name: generateRandomName(),
    lastName: generateRandomName(),
    mothersLastName: generateRandomName()
  };
};

module.exports = { generateDummyObj };

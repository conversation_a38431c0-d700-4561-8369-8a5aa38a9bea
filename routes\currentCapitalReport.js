const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');
const service = require('../modules/analysisOfCurrentCapital/services/dbService');
const FactoryController = require('../modules/analysisOfCurrentCapital/controllers/index.controller');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const controller = FactoryController({ HttpStatus, service, Logger });

  router.get('/:reportType', validateAccess(), controller.getReport);
};

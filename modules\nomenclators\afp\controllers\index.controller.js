/* eslint-disable no-console */
const { rutFormatter, formatterPercent, codeFormatter } = require('../validators/validator');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  afpService,
  validationResult,
  Logger
}) => {
  return {
    updateAfp: async (req, res) => {
      Logger.info('update nomenclator Afp: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, name, rut, percentage, code } = req.body.afp;
      const { result, isError, error } = await afpService.updateAfp({
        id,
        name,
        rut: rutFormatter(rut),
        percentage: formatterPercent(percentage),
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Afps successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    createAfp: async (req, res) => {
      Logger.info('create nomenclator Afp: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, name, rut, percentage, code } = req.body.afp;
      const { result, isError, error } = await afpService.createAfp({
        id,
        name,
        rut: rutFormatter(rut),
        percentage: formatterPercent(percentage),
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.ALREADY_EXIST).json({ error, isError });
      } else {
        Logger.info('Operation on nomenclator Afps successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    deleteAfp: async (req, res) => {
      Logger.info('Delete nomenclator Afp: ', req.params.id);
      const { result, isError, error } = await afpService.deleteAfp(req.params.id);
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.NOT_FOUND).json({ error, isError });
      } else {
        Logger.info('Delete nomenclator Afp successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    getAfps: async (req, res) => {
      try {
        Logger.info('Get all Afps: ');
        const { result } = await afpService.getAfps();
        res.status(HttpStatus.OK).json({ result });
      } catch (error) {
        Logger.error(error);
      }
    }
  };
};

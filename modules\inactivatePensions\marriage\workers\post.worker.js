const cronMark = 'INACTIVATE_MARRIAGE_POST_WORKER';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronDescription = 'post worker de inactivar pensiones por matrimonio Nº6:';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'INACTIVATE_RETIREMENT_POST_WORKER';

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const logError = (logger, error) => {
  logger.error(`Error cron inactivacion por matrimonio: ${error},  Nº6`);
  return { message: `Error cron inactivacion por matrimonio: ${error}` };
};
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, service, done, logService, job }) => {
  try {
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }
    Logger.info('Inicio de worker inactivacion por matrimonio Nº6');
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage
      };
    }

    Logger.info(`${cronDescription}: process started`);
    const { inactivationError } = await service.createUpdateMarriagePension();
    if (inactivationError) throw new Error(inactivationError);

    Logger.info('Fin de worker inactivacion por matrimonio  Nº6');
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${logError(Logger, error)}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return logError(Logger, error);
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

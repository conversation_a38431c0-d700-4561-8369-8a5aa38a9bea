const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'set-pensioners-bonus',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_ASSING_BONUS,
  description:
    'Generar archivo txt para descargarlo, servirá para la asignación de aguinaldos de septiembre y diciembre',
  endPoint: 'generatebonusassignmentfile',
  cronMark: workerModule.markOfCron,
  dependencyMark: workerModule.markDependency
};

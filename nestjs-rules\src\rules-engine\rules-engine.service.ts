import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CacheService } from '../common/cache/cache.service';
import { RuleEvaluator } from './rule-evaluator.service';
import { FormulaCalculator } from './formula-calculator.service';
import { Pension, PensionRule, RuleScale } from '@prisma/client';

export interface RuleContext {
  pension: Pension & { [key: string]: any };
  currentDate: Date;
  referenceValues: Map<string, number>;
  calculationHistory: Map<string, any>;
}

export interface RuleResult {
  ruleId: string;
  ruleName: string;
  wasApplied: boolean;
  resultValue: number;
  executionTime: number;
  errorMessage?: string;
  metadata?: any;
}

export interface CalculationResult {
  basePension: Record<string, number>;
  assets: Record<string, number>;
  discounts: Record<string, number>;
  retroactive: Record<string, number>;
  netPension: number;
  appliedRules: RuleResult[];
  calculatedAt: Date;
}

/**
 * Motor de Reglas Principal para Cálculo de Pensiones
 * Ejecuta todas las reglas en orden de prioridad y categoría
 */
@Injectable()
export class RulesEngineService {
  private readonly logger = new Logger(RulesEngineService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly cache: CacheService,
    private readonly ruleEvaluator: RuleEvaluator,
    private readonly formulaCalculator: FormulaCalculator
  ) {}

  /**
   * Ejecuta todas las reglas para calcular una pensión completa
   */
  async calculatePension(pensionId: string): Promise<CalculationResult> {
    const startTime = Date.now();
    
    try {
      // 1. Cargar pensión con datos relacionados
      const pension = await this.loadPensionWithRelations(pensionId);
      
      // 2. Preparar contexto de ejecución
      const context = await this.prepareRuleContext(pension);
      
      // 3. Ejecutar reglas por categoría en orden
      const results = await this.executeRulesByCategory(context);
      
      // 4. Calcular pensión neta final
      const netPension = this.calculateNetPension(results);
      
      // 5. Registrar ejecución para auditoría
      await this.logRuleExecutions(pensionId, results);
      
      const executionTime = Date.now() - startTime;
      this.logger.log(`Pension ${pensionId} calculated in ${executionTime}ms`);
      
      return {
        basePension: results.basePension || {},
        assets: results.assets || {},
        discounts: results.discounts || {},
        retroactive: results.retroactive || {},
        netPension,
        appliedRules: results.appliedRules || [],
        calculatedAt: new Date()
      };
      
    } catch (error) {
      this.logger.error(`Error calculating pension ${pensionId}:`, error);
      throw error;
    }
  }

  /**
   * Ejecuta reglas por categoría en orden específico
   */
  private async executeRulesByCategory(context: RuleContext): Promise<any> {
    const results = {
      basePension: {},
      assets: {},
      discounts: {},
      retroactive: {},
      appliedRules: []
    };

    // Orden de ejecución de categorías
    const categories = [
      'BASE_PENSION',
      'MINIMUM_PENSION', 
      'ASSETS',
      'DISCOUNTS',
      'JUDICIAL_RETENTIONS',
      'RETROACTIVE',
      'VALIDATIONS'
    ];

    for (const category of categories) {
      const categoryResults = await this.executeRulesForCategory(category, context);
      
      // Merge results
      if (categoryResults.basePension) {
        Object.assign(results.basePension, categoryResults.basePension);
      }
      if (categoryResults.assets) {
        Object.assign(results.assets, categoryResults.assets);
      }
      if (categoryResults.discounts) {
        Object.assign(results.discounts, categoryResults.discounts);
      }
      if (categoryResults.retroactive) {
        Object.assign(results.retroactive, categoryResults.retroactive);
      }
      
      results.appliedRules.push(...(categoryResults.appliedRules || []));
      
      // Actualizar contexto con resultados parciales
      context.calculationHistory.set(category, categoryResults);
    }

    return results;
  }

  /**
   * Ejecuta todas las reglas de una categoría específica
   */
  private async executeRulesForCategory(category: string, context: RuleContext): Promise<any> {
    const rules = await this.getRulesForCategory(category);
    const results = {
      basePension: {},
      assets: {},
      discounts: {},
      retroactive: {},
      appliedRules: []
    };

    // Ordenar reglas por prioridad y orden de ejecución
    rules.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // Mayor prioridad primero
      }
      return a.executionOrder - b.executionOrder;
    });

    for (const rule of rules) {
      try {
        const ruleResult = await this.executeRule(rule, context);
        
        if (ruleResult.wasApplied) {
          // Aplicar resultado según subcategoría
          this.applyRuleResult(rule, ruleResult, results);
        }
        
        results.appliedRules.push(ruleResult);
        
      } catch (error) {
        this.logger.error(`Error executing rule ${rule.id}:`, error);
        results.appliedRules.push({
          ruleId: rule.id,
          ruleName: rule.name,
          wasApplied: false,
          resultValue: 0,
          executionTime: 0,
          errorMessage: error.message
        });
      }
    }

    return results;
  }

  /**
   * Ejecuta una regla individual
   */
  private async executeRule(rule: PensionRule, context: RuleContext): Promise<RuleResult> {
    const startTime = Date.now();
    
    try {
      // 1. Evaluar condiciones
      const conditionsMet = await this.ruleEvaluator.evaluateConditions(
        rule.conditions as any,
        context
      );
      
      if (!conditionsMet) {
        return {
          ruleId: rule.id,
          ruleName: rule.name,
          wasApplied: false,
          resultValue: 0,
          executionTime: Date.now() - startTime
        };
      }

      // 2. Calcular resultado
      let resultValue = 0;
      
      if (rule.formula) {
        // Usar fórmula matemática
        resultValue = await this.formulaCalculator.calculate(
          rule.formula,
          context,
          rule.parameters as any
        );
      } else if (rule.subcategory) {
        // Usar método específico por subcategoría
        resultValue = await this.calculateBySubcategory(
          rule.subcategory,
          context,
          rule.parameters as any
        );
      }

      return {
        ruleId: rule.id,
        ruleName: rule.name,
        wasApplied: true,
        resultValue,
        executionTime: Date.now() - startTime,
        metadata: {
          category: rule.category,
          subcategory: rule.subcategory,
          parameters: rule.parameters
        }
      };

    } catch (error) {
      return {
        ruleId: rule.id,
        ruleName: rule.name,
        wasApplied: false,
        resultValue: 0,
        executionTime: Date.now() - startTime,
        errorMessage: error.message
      };
    }
  }

  /**
   * Calcula valores específicos por subcategoría
   */
  private async calculateBySubcategory(
    subcategory: string,
    context: RuleContext,
    parameters: any
  ): Promise<number> {
    switch (subcategory) {
      case 'APS':
        return this.calculateAps(context, parameters);
      
      case 'FAMILY_ASSIGNMENT':
        return this.calculateFamilyAssignment(context, parameters);
      
      case 'CHRISTMAS_BONUS':
        return this.calculateSeasonalBonus(context, 'CHRISTMAS');
      
      case 'WINTER_BONUS':
        return this.calculateSeasonalBonus(context, 'WINTER');
      
      case 'NATIONAL_HOLIDAYS_BONUS':
        return this.calculateSeasonalBonus(context, 'NATIONAL_HOLIDAYS');
      
      case 'AFP_DISCOUNT':
        return this.calculateAfpDiscount(context, parameters);
      
      case 'HEALTH_DISCOUNT':
        return this.calculateHealthDiscount(context, parameters);
      
      case 'JUDICIAL_RETENTION':
        return this.calculateJudicialRetention(context, parameters);
      
      default:
        this.logger.warn(`Unknown subcategory: ${subcategory}`);
        return 0;
    }
  }

  /**
   * Métodos de cálculo específicos
   */
  private async calculateAps(context: RuleContext, parameters: any): Promise<number> {
    const pension = context.pension;
    const totalBasePension = Number(pension.basePension) + 
                           Number(pension.article40) + 
                           Number(pension.article41);

    // Obtener escalas APS desde base de datos
    const apsScales = await this.cache.getOrSet(
      'aps_scales',
      async () => {
        return this.prisma.ruleScale.findMany({
          where: {
            ruleName: 'APS',
            isActive: true,
            validFrom: { lte: context.currentDate },
            OR: [
              { validTo: null },
              { validTo: { gte: context.currentDate } }
            ]
          },
          orderBy: { minValue: 'asc' }
        });
      },
      3600
    );

    // Encontrar escala aplicable
    for (const scale of apsScales) {
      const minValue = Number(scale.minValue || 0);
      const maxValue = Number(scale.maxValue || Number.MAX_SAFE_INTEGER);
      
      if (totalBasePension >= minValue && totalBasePension <= maxValue) {
        return Number(scale.resultValue);
      }
    }

    return 0;
  }

  private async calculateFamilyAssignment(context: RuleContext, parameters: any): Promise<number> {
    const pension = context.pension;
    const numberOfCharges = pension.numberOfCharges || 0;
    
    if (numberOfCharges <= 0) {
      return 0;
    }

    const totalBasePension = Number(pension.basePension) + 
                           Number(pension.article40) + 
                           Number(pension.article41);

    // Obtener escalas de asignación familiar
    const familyScales = await this.cache.getOrSet(
      'family_assignment_scales',
      async () => {
        return this.prisma.ruleScale.findMany({
          where: {
            ruleName: 'FAMILY_ASSIGNMENT',
            isActive: true,
            validFrom: { lte: context.currentDate },
            OR: [
              { validTo: null },
              { validTo: { gte: context.currentDate } }
            ]
          },
          orderBy: { minValue: 'asc' }
        });
      },
      3600
    );

    // Encontrar escala aplicable
    for (const scale of familyScales) {
      const minValue = Number(scale.minValue || 0);
      const maxValue = Number(scale.maxValue || Number.MAX_SAFE_INTEGER);
      
      if (totalBasePension >= minValue && totalBasePension <= maxValue) {
        return Number(scale.resultValue) * numberOfCharges;
      }
    }

    return 0;
  }

  private async calculateSeasonalBonus(context: RuleContext, bonusType: string): Promise<number> {
    const currentMonth = context.currentDate.getMonth() + 1;
    
    // Obtener configuración del bono
    const bonusConfig = await this.cache.getOrSet(
      `seasonal_bonus_${bonusType}`,
      async () => {
        return this.prisma.seasonalBonus.findFirst({
          where: {
            bonusType,
            isActive: true,
            validFrom: { lte: context.currentDate },
            OR: [
              { validTo: null },
              { validTo: { gte: context.currentDate } }
            ]
          }
        });
      },
      3600
    );

    if (!bonusConfig) {
      return 0;
    }

    // Verificar si es el mes de pago
    const paymentMonths = bonusConfig.paymentMonths as number[];
    if (!paymentMonths.includes(currentMonth)) {
      return 0;
    }

    // Verificar elegibilidad
    const eligibility = bonusConfig.eligibility as any;
    if (eligibility.payBonus === 'NO') {
      return 0;
    }

    // Calcular monto según escalas
    const pension = context.pension;
    const totalBasePension = Number(pension.basePension) + 
                           Number(pension.article40) + 
                           Number(pension.article41);

    const scales = bonusConfig.scales as any[];
    for (const scale of scales) {
      if (totalBasePension >= scale.minPension && totalBasePension <= scale.maxPension) {
        return scale.bonusAmount;
      }
    }

    return 0;
  }

  private async calculateAfpDiscount(context: RuleContext, parameters: any): Promise<number> {
    const pension = context.pension;
    
    if (!pension.afpAffiliation) {
      return 0;
    }

    // Obtener configuración AFP
    const afpConfig = await this.cache.getOrSet(
      `afp_config_${pension.afpAffiliation}`,
      async () => {
        return this.prisma.afpConfiguration.findFirst({
          where: {
            afpName: pension.afpAffiliation,
            isActive: true,
            validFrom: { lte: context.currentDate },
            OR: [
              { validTo: null },
              { validTo: { gte: context.currentDate } }
            ]
          }
        });
      },
      3600
    );

    if (!afpConfig) {
      return 0;
    }

    // Calcular descuento AFP
    const taxablePension = this.getTaxablePension(context);
    const afpDiscount = taxablePension * Number(afpConfig.totalRate) / 100;
    const maxDiscount = Number(afpConfig.maxTaxableAmount) * Number(afpConfig.totalRate) / 100;

    return Math.min(afpDiscount, maxDiscount);
  }

  private async calculateHealthDiscount(context: RuleContext, parameters: any): Promise<number> {
    const pension = context.pension;
    
    if (!pension.healthAffiliation) {
      return 0;
    }

    // Obtener configuración de salud
    const healthConfig = await this.cache.getOrSet(
      `health_config_${pension.healthAffiliation}`,
      async () => {
        return this.prisma.healthConfiguration.findFirst({
          where: {
            healthProvider: pension.healthAffiliation,
            isActive: true,
            validFrom: { lte: context.currentDate },
            OR: [
              { validTo: null },
              { validTo: { gte: context.currentDate } }
            ]
          }
        });
      },
      3600
    );

    if (!healthConfig) {
      return 0;
    }

    // Calcular descuento según tipo
    if (healthConfig.providerType === 'PUBLIC') {
      // FONASA - 7% de pensión imponible
      const taxablePension = this.getTaxablePension(context);
      return taxablePension * 0.07;
    } else {
      // ISAPRE - según configuración específica
      const discounts = pension.discounts as any;
      const healthUF = discounts?.healthUF || 0;
      const ufValue = context.referenceValues.get('UF') || 35000;
      return healthUF * ufValue;
    }
  }

  private async calculateJudicialRetention(context: RuleContext, parameters: any): Promise<number> {
    const pension = context.pension;
    const discounts = pension.discounts as any;
    
    if (!discounts?.judicialRetentions) {
      return 0;
    }

    const activeRetentions = discounts.judicialRetentions.filter((r: any) => r.validity);
    if (activeRetentions.length === 0) {
      return 0;
    }

    const netPension = this.getNetPensionForRetention(context);
    const minimumSalary = context.referenceValues.get('MINIMUM_SALARY') || 350000;
    const exemptAmount = minimumSalary * 0.9;
    const maxRetentionBase = Math.max(0, netPension - exemptAmount);

    let totalRetention = 0;
    
    for (const retention of activeRetentions) {
      if (retention.type === 'percentage') {
        totalRetention += maxRetentionBase * (retention.amount / 100);
      } else {
        totalRetention += retention.amount;
      }
    }

    // Límite máximo del 50% de pensión líquida
    const maxAllowedRetention = netPension * 0.5;
    return Math.min(totalRetention, maxAllowedRetention);
  }

  // Métodos auxiliares
  private async loadPensionWithRelations(pensionId: string): Promise<any> {
    return this.prisma.pension.findUniqueOrThrow({
      where: { id: pensionId },
      include: {
        discountsAndAssets: true,
        liquidations: {
          where: { enabled: true },
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });
  }

  private async prepareRuleContext(pension: any): Promise<RuleContext> {
    const referenceValues = await this.loadReferenceValues();
    
    return {
      pension,
      currentDate: new Date(),
      referenceValues,
      calculationHistory: new Map()
    };
  }

  private async loadReferenceValues(): Promise<Map<string, number>> {
    const values = await this.cache.getOrSet(
      'reference_values',
      async () => {
        return this.prisma.referenceValue.findMany({
          where: {
            isActive: true,
            effectiveDate: { lte: new Date() },
            OR: [
              { expiryDate: null },
              { expiryDate: { gte: new Date() } }
            ]
          }
        });
      },
      1800 // 30 minutos
    );

    const map = new Map<string, number>();
    values.forEach(v => {
      map.set(v.name, Number(v.value));
    });

    return map;
  }

  private async getRulesForCategory(category: string): Promise<PensionRule[]> {
    return this.cache.getOrSet(
      `rules_${category}`,
      async () => {
        return this.prisma.pensionRule.findMany({
          where: {
            category,
            isActive: true,
            validFrom: { lte: new Date() },
            OR: [
              { validTo: null },
              { validTo: { gte: new Date() } }
            ]
          },
          orderBy: [
            { priority: 'desc' },
            { executionOrder: 'asc' }
          ]
        });
      },
      3600
    );
  }

  private applyRuleResult(rule: PensionRule, result: RuleResult, results: any): void {
    const category = rule.category.toLowerCase();
    const subcategory = rule.subcategory;
    
    if (category === 'base_pension') {
      results.basePension[subcategory] = result.resultValue;
    } else if (category === 'assets') {
      results.assets[subcategory] = result.resultValue;
    } else if (category === 'discounts') {
      results.discounts[subcategory] = result.resultValue;
    } else if (category === 'retroactive') {
      results.retroactive[subcategory] = result.resultValue;
    }
  }

  private calculateNetPension(results: any): number {
    const totalBasePension = Object.values(results.basePension).reduce((sum: number, val: any) => sum + Number(val), 0);
    const totalAssets = Object.values(results.assets).reduce((sum: number, val: any) => sum + Number(val), 0);
    const totalDiscounts = Object.values(results.discounts).reduce((sum: number, val: any) => sum + Number(val), 0);
    const totalRetroactive = Object.values(results.retroactive).reduce((sum: number, val: any) => sum + Number(val), 0);

    return Math.max(0, Math.round((totalBasePension + totalAssets - totalDiscounts + totalRetroactive) * 100) / 100);
  }

  private getTaxablePension(context: RuleContext): number {
    const pension = context.pension;
    return Number(pension.basePension) + Number(pension.article40) + Number(pension.article41);
  }

  private getNetPensionForRetention(context: RuleContext): number {
    const basePension = this.getTaxablePension(context);
    const assets = context.calculationHistory.get('ASSETS') || {};
    const discounts = context.calculationHistory.get('DISCOUNTS') || {};
    
    const totalAssets = Object.values(assets).reduce((sum: number, val: any) => sum + Number(val), 0);
    const totalDiscounts = Object.values(discounts).reduce((sum: number, val: any) => sum + Number(val), 0);
    
    return basePension + totalAssets - totalDiscounts;
  }

  private async logRuleExecutions(pensionId: string, results: any): Promise<void> {
    const executions = results.appliedRules.map((rule: RuleResult) => ({
      ruleId: rule.ruleId,
      pensionId,
      inputData: {},
      outputData: { resultValue: rule.resultValue },
      wasApplied: rule.wasApplied,
      resultValue: rule.resultValue,
      executionTime: rule.executionTime,
      errorMessage: rule.errorMessage
    }));

    await this.prisma.ruleExecution.createMany({
      data: executions
    });
  }
}

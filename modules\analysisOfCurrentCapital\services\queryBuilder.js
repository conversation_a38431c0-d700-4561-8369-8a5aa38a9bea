const { getCurrentYearAndMonth } = require('../../inactivatePensions/sharedFiles/filesHelper');

const buildAggregationQuery = ({
  month,
  year,
  matchQuery,
  preserveNullAndEmptyArrays = false,
  additionalLookupQuery = {}
}) => [
  {
    $match: { ...matchQuery }
  },
  {
    $lookup: {
      from: 'pensions',
      let: { beneficiary: '$beneficiary.rut', causant: '$causant.rut' },
      pipeline: [
        {
          $match: {
            ...additionalLookupQuery,
            $expr: {
              $and: [
                { $eq: ['$beneficiary.rut', '$$beneficiary'] },
                { $eq: ['$causant.rut', '$$causant'] },
                { $eq: ['$enabled', false] },
                { $eq: [{ $month: '$createdAt' }, month] },
                { $eq: [{ $year: '$createdAt' }, year] }
              ]
            }
          }
        },
        { $sort: { _id: -1 } },
        { $limit: 1 }
      ],
      as: 'previousMonthDoc'
    }
  },
  { $unwind: { path: '$previousMonthDoc', preserveNullAndEmptyArrays } }
];

const validPensionsWithPreviousMonthDocQuery = (month, year) =>
  buildAggregationQuery({
    month,
    year,
    preserveNullAndEmptyArrays: true,
    matchQuery: { enabled: true, validityType: { $not: /No vigente/i } }
  });

const pensionsWithValidPreviousMonthDocQuery = (month, year) =>
  buildAggregationQuery({
    month,
    year,
    matchQuery: { enabled: true },
    additionalLookupQuery: { validityType: { $not: /No vigente/i } }
  });

const pensionsInactInCurrMonthQuery = (month, year) => {
  const [currentYear, currentMonth] = getCurrentYearAndMonth();
  return buildAggregationQuery({
    month,
    year,
    matchQuery: {
      enabled: true,
      validityType: /No\s+vigente/i,
      $expr: {
        $and: [
          { $eq: [{ $month: `$inactivationDate` }, currentMonth] },
          { $eq: [{ $year: `$inactivationDate` }, currentYear] }
        ]
      }
    }
  });
};

const buildQuery = ({ dateField, month, year, additionalQuery = {} }) => ({
  ...additionalQuery,
  validityType: { $not: /No vigente/i },
  enabled: true,
  $expr: {
    $and: [
      { $eq: [{ $month: `$${dateField}` }, month] },
      { $eq: [{ $year: `$${dateField}` }, year] }
    ]
  }
});

module.exports = {
  buildAggregationQuery,
  validPensionsWithPreviousMonthDocQuery,
  pensionsWithValidPreviousMonthDocQuery,
  pensionsInactInCurrMonthQuery,
  buildQuery
};

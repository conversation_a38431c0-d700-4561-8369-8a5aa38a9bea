// Schema Prisma para Sistema de Reglas de Pensiones
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Tabla principal de reglas
model PensionRule {
  id                String   @id @default(cuid())
  name              String
  description       String?
  category          String   // 'BASE_PENSION', 'ASSETS', 'DISCOUNTS', 'RETROACTIVE'
  subcategory       String   // 'APS', 'FAMILY_ASSIGNMENT', 'AFP', etc.
  ruleType          String   // 'CALCULATION', 'VALIDATION', 'LIMIT'
  
  // Configuración de la regla
  conditions        Json     @db.JsonB // Condiciones para aplicar la regla
  formula           String?  // Fórmula de cálculo
  parameters        Json     @db.JsonB // Parámetros configurables
  
  // Vigencia
  validFrom         DateTime
  validTo           DateTime?
  isActive          Boolean  @default(true)
  
  // Prioridad y orden
  priority          Int      @default(0)
  executionOrder    Int      @default(0)
  
  // Metadatos
  version           String   @default("1.0")
  createdBy         String
  approvedBy        String?
  approvedAt        DateTime?
  
  // Timestamps
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  // Relaciones
  ruleExecutions    RuleExecution[]
  
  @@map("pension_rules")
  @@index([category, subcategory, isActive])
  @@index([validFrom, validTo])
  @@index([priority, executionOrder])
}

// Tabla para valores de referencia (UF, IPC, salarios mínimos, etc.)
model ReferenceValue {
  id          String   @id @default(cuid())
  name        String   // 'UF', 'IPC', 'MINIMUM_SALARY', etc.
  value       Decimal  @db.Decimal(15, 4)
  currency    String   @default("CLP")
  unit        String?  // 'PESOS', 'PERCENTAGE', 'UF'
  
  // Vigencia
  effectiveDate DateTime @map("effective_date")
  expiryDate    DateTime? @map("expiry_date")
  isActive      Boolean  @default(true)
  
  // Metadatos
  source        String?  // Fuente del valor (SII, INE, etc.)
  description   String?
  
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  @@map("reference_values")
  @@index([name, effectiveDate])
  @@index([effectiveDate, expiryDate])
}

// Tabla para rangos y escalas (pensiones mínimas, APS, asignación familiar)
model RuleScale {
  id            String   @id @default(cuid())
  ruleName      String   @map("rule_name")
  scaleType     String   @map("scale_type") // 'PENSION_RANGE', 'AGE_RANGE', 'DISABILITY_RANGE'
  
  // Rangos
  minValue      Decimal? @map("min_value") @db.Decimal(15, 2)
  maxValue      Decimal? @map("max_value") @db.Decimal(15, 2)
  
  // Resultado
  resultValue   Decimal  @map("result_value") @db.Decimal(15, 2)
  resultType    String   @map("result_type") // 'FIXED_AMOUNT', 'PERCENTAGE', 'FORMULA'
  
  // Condiciones adicionales
  conditions    Json?    @db.JsonB
  
  // Vigencia
  validFrom     DateTime @map("valid_from")
  validTo       DateTime? @map("valid_to")
  isActive      Boolean  @default(true)
  
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  @@map("rule_scales")
  @@index([ruleName, isActive])
  @@index([scaleType, validFrom])
}

// Tabla para configuración de AFP
model AfpConfiguration {
  id              String   @id @default(cuid())
  afpName         String   @unique @map("afp_name")
  commissionRate  Decimal  @map("commission_rate") @db.Decimal(5, 4) // Porcentaje
  insuranceRate   Decimal  @map("insurance_rate") @db.Decimal(5, 4)
  totalRate       Decimal  @map("total_rate") @db.Decimal(5, 4)
  
  // Límites
  maxTaxableAmount Decimal @map("max_taxable_amount") @db.Decimal(15, 2)
  
  // Vigencia
  validFrom       DateTime @map("valid_from")
  validTo         DateTime? @map("valid_to")
  isActive        Boolean  @default(true)
  
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  @@map("afp_configurations")
  @@index([afpName, validFrom])
}

// Tabla para configuración de ISAPRES
model HealthConfiguration {
  id              String   @id @default(cuid())
  healthProvider  String   @map("health_provider") // 'FONASA', 'ISAPRE_CONSALUD', etc.
  providerType    String   @map("provider_type") // 'PUBLIC', 'PRIVATE'
  
  // Configuración de descuentos
  baseRate        Decimal? @map("base_rate") @db.Decimal(5, 4)
  additionalRate  Decimal? @map("additional_rate") @db.Decimal(5, 4)
  
  // Límites y topes
  minAmount       Decimal? @map("min_amount") @db.Decimal(15, 2)
  maxAmount       Decimal? @map("max_amount") @db.Decimal(15, 2)
  
  // Configuración específica
  configuration   Json?    @db.JsonB
  
  validFrom       DateTime @map("valid_from")
  validTo         DateTime? @map("valid_to")
  isActive        Boolean  @default(true)
  
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  @@map("health_configurations")
  @@index([healthProvider, validFrom])
}

// Tabla para ejecución y auditoría de reglas
model RuleExecution {
  id              String   @id @default(cuid())
  ruleId          String   @map("rule_id")
  pensionId       String   @map("pension_id")
  
  // Contexto de ejecución
  inputData       Json     @map("input_data") @db.JsonB
  outputData      Json     @map("output_data") @db.JsonB
  
  // Resultado
  wasApplied      Boolean  @map("was_applied")
  resultValue     Decimal? @map("result_value") @db.Decimal(15, 2)
  
  // Metadatos de ejecución
  executionTime   Int      @map("execution_time") // Milisegundos
  errorMessage    String?  @map("error_message")
  
  // Timestamps
  executedAt      DateTime @default(now()) @map("executed_at")
  
  // Relaciones
  rule            PensionRule @relation(fields: [ruleId], references: [id])
  
  @@map("rule_executions")
  @@index([pensionId, executedAt])
  @@index([ruleId, executedAt])
}

// Tabla para configuración de bonos estacionales
model SeasonalBonus {
  id            String   @id @default(cuid())
  bonusType     String   @map("bonus_type") // 'CHRISTMAS', 'NATIONAL_HOLIDAYS', 'WINTER'
  bonusName     String   @map("bonus_name")
  
  // Configuración de pago
  paymentMonths Json     @map("payment_months") @db.JsonB // [12] para navidad
  
  // Escalas de pago
  scales        Json     @map("scales") @db.JsonB // Rangos de pensión y montos
  
  // Condiciones de elegibilidad
  eligibility   Json     @map("eligibility") @db.JsonB
  
  // Vigencia
  validFrom     DateTime @map("valid_from")
  validTo       DateTime? @map("valid_to")
  isActive      Boolean  @default(true)
  
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  @@map("seasonal_bonuses")
  @@index([bonusType, isActive])
}

// Tabla para límites y validaciones
model ValidationRule {
  id              String   @id @default(cuid())
  validationType  String   @map("validation_type") // 'LIMIT', 'FORMAT', 'BUSINESS'
  fieldName       String   @map("field_name")
  
  // Configuración de validación
  validationConfig Json    @map("validation_config") @db.JsonB
  
  // Mensaje de error
  errorMessage    String   @map("error_message")
  errorCode       String   @map("error_code")
  
  // Severidad
  severity        String   @default("ERROR") // 'ERROR', 'WARNING', 'INFO'
  
  isActive        Boolean  @default(true)
  
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  @@map("validation_rules")
  @@index([validationType, fieldName])
}

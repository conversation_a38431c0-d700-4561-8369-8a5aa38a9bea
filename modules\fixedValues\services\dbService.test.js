/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { resetFixedValues, setFixedValues } = require('../modifiers');
const PensionsModel = require('../../../models/pension');
const LogModel = require('../../sharedFiles/models/processedJob');
const service = require('./dbService');

const pensionsData = require('../../../resources/pensions.json');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionsModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should find pensions and set fixed field value', async () => {
    // create and save one  document
    const pension = await PensionsModel.create({
      ...pensionsData[0],
      pensionType: 'Pensión por accidente de trabajo',
      enabled: true,
      basePension: 142452,
      article40: 500,
      article41: 300
    });
    expect(pension._id).toBeDefined();

    const { error, completed } = await service.basePensionAndArticlesFixedValues(setFixedValues);
    expect(error).toBe(null);
    expect(completed).toBe(true);
  });

  it('should find pensions and reset fixed field value', async () => {
    // create and save one  document
    const pension = await PensionsModel.create({
      ...pensionsData[0],
      pensionType: 'Pensión por accidente de trabajo',
      enabled: true,
      basePension: 10000.05,
      article40: 0,
      article41: 0,
      fixedArticle40: 500,
      fixedArticle41: 300,
      fixedBasePension: 142452
    });
    expect(pension._id).toBeDefined();

    const { error, completed } = await service.basePensionAndArticlesFixedValues(resetFixedValues);
    expect(error).toBe(null);
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    await PensionsModel.deleteMany({}).catch(e => console.error(e));
    await LogModel.deleteMany({}).catch(e => console.error(e));
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

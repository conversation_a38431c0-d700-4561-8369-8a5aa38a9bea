const { formatter } = require('./formatFields');

const pensionFields = [
  { pensionCodeId: { name: 'Número de pensión', format: formatter.number, path: 'pensionCodeId' } },
  {
    beneficiaryRut: { name: 'Rut beneficiario', format: formatter.normal, path: 'beneficiary.rut' }
  },
  { pensionType: { name: 'Tipo de pensión', format: formatter.normal, path: 'pensionType' } },
  {
    validityType: {
      name: 'Tipo de vigencia',
      format: formatter.normal,
      path: 'validityType'
    }
  },
  {
    basePension: { name: 'Pensión base', format: formatter.amount, path: 'basePension' }
  },
  {
    fullname: { name: 'Nombre beneficiario', format: formatter.normal, path: 'fullname' }
  },
  {
    netPension: {
      name: 'Pensión líquida',
      format: formatter.amount,
      path: 'liquidation.netPension'
    }
  },

  {
    taxablePension: {
      name: 'Pensión imponible',
      format: formatter.amount,
      path: 'liquidation.taxablePension'
    }
  },
  {
    article40: { name: 'Artículo 40', format: formatter.amount, path: 'article40' }
  },
  {
    article41: { name: 'Artículo 41', format: formatter.amount, path: 'article41' }
  },
  {
    assetsFamilyAssignment: {
      name: 'Haber por asignación familiar',
      format: formatter.amount,
      path: 'assets.forFamilyAssignment'
    }
  },
  {
    assetsAPS: { name: 'APS', format: formatter.amount, path: 'assets.aps' }
  },
  {
    assetsREBSAL: {
      name: 'REBSAL (rebaja de salud ajustado)',
      format: formatter.amount,
      path: 'assets.rebsal'
    }
  },
  {
    assetsAdjustedHealthExemption: {
      name: 'Extención de salud (haber exención de salud ajustado)',
      format: formatter.amount,
      path: 'assets.adjustedHealthExemption'
    }
  },
  {
    assetsChristmasBonus: {
      name: 'Aguinaldo navidad',
      format: formatter.amount,
      path: 'assets.christmasBonus'
    }
  },
  {
    assetsNationalHolidaysBonus: {
      name: 'Aguinaldo fiestas patrias',
      format: formatter.amount,
      path: 'assets.nationalHolidaysBonus'
    }
  },
  {
    assetsWinterBonus: {
      name: 'Bono invierno',
      format: formatter.amount,
      path: 'assets.winterBonus'
    }
  },
  {
    assetsMarriageBonus: {
      name: 'Bono por matrimonio',
      format: formatter.amount,
      path: 'assets.marriageBonus'
    }
  },
  {
    retroactiveAmountsForBasePension: {
      name: 'Monto retroactivo por pensión base',
      format: formatter.amount,
      path: 'retroactiveAmounts.forBasePension'
    }
  },
  {
    retroactiveAmountsForArticle40: {
      name: 'Monto retroactivo por artículo 40',
      format: formatter.amount,
      path: 'retroactiveAmounts.forArticle40'
    }
  },
  {
    retroactiveAmountsForArticle41: {
      name: 'Monto retroactivo por artículo 41',
      format: formatter.amount,
      path: 'retroactiveAmounts.forArticle41'
    }
  },
  {
    retroactiveAmountsForBonuses: {
      name: 'Monto retroactivo por aguinaldos',
      format: formatter.amount,
      path: 'retroactiveAmounts.forBonuses'
    }
  },
  {
    retroactiveAmountsForFamilyAssignment: {
      name: 'Monto retroactivo por asignación familiar',
      format: formatter.amount,
      path: 'retroactiveAmounts.forFamilyAssignment'
    }
  },
  {
    retroactiveAmountsForRejection: {
      name: 'Retroactivo por rechazo',
      format: formatter.amount,
      path: 'retroactiveAmounts.forRejection'
    }
  },
  {
    retroactiveAmountsForInstitutionalPatient: {
      name: 'Retroactivo por paciente institucional',
      format: formatter.amount,
      path: 'retroactiveAmounts.forInstitutionalPatient'
    }
  },
  {
    totalAssets: {
      name: 'Total haberes',
      format: formatter.amount,
      path: 'liquidation.totalAssets'
    }
  },
  {
    totalOnePercentDiscounts: {
      name: 'Descuento 1%',
      format: formatter.amount,
      path: 'liquidation.totalOnePercentDiscounts'
    }
  },
  {
    totalSocialCreditDiscounts: {
      name: 'Descuentos créditos sociales',
      format: formatter.amount,
      path: 'liquidation.totalSocialCreditDiscounts'
    }
  },
  {
    othersDiscountsCompensationBoxes: {
      name: 'Otros descuentos cajas de compensación ',
      format: formatter.amount,
      path: 'othersDiscountsCompensationBoxes'
    }
  },
  {
    discountsHealthLoan: {
      name: 'Descuento por préstamo de salud',
      format: formatter.amount,
      path: 'discounts.healthLoan'
    }
  },
  {
    discountsHealth: {
      name: 'Descuento salud',
      format: formatter.amount,
      path: 'discounts.health'
    }
  },
  {
    discountsAfp: {
      name: 'Descuento AFP',
      format: formatter.amount,
      path: 'discounts.afp'
    }
  },
  {
    totalDiscounts: {
      name: 'Total descuentos',
      format: formatter.amount,
      path: 'liquidation.totalDiscounts'
    }
  },
  {
    assetsNetTotalNonFormulable: {
      name: 'Haberes no formulables totales líquidos',
      format: formatter.amount,
      path: 'assets.netTotalNonFormulable'
    }
  },
  {
    assetsTaxableTotalNonFormulable: {
      name: 'Haberes no formulables totales imponibles',
      format: formatter.amount,
      path: 'assets.taxableTotalNonFormulable'
    }
  },
  {
    discountsTotalNonFormulable: {
      name: 'Descuentos no formulables totales',
      format: formatter.amount,
      path: 'discounts.totalNonFormulable'
    }
  },
  {
    retroactiveAmountsForTaxableTotalNonFormulableAssets: {
      name: 'Monto retroactivo por haberes no formulables totales imponibles',
      format: formatter.amount,
      path: 'retroactiveAmounts.forTaxableTotalNonFormulableAssets'
    }
  },
  {
    retroactiveAmountsForNetTotalNonFormulableAssets: {
      name: 'Monto retroactivo por haberes no formulables totales líquidos',
      format: formatter.amount,
      path: 'retroactiveAmounts.forNetTotalNonFormulableAssets'
    }
  },
  {
    retroactiveAmountsForTotalNonFormulableDiscounts: {
      name: 'Monto retroactivo por descuentos no formulables totales',
      format: formatter.amount,
      path: 'retroactiveAmounts.forTotalNonFormulableDiscounts'
    }
  }
];

const simpleFields = pensionFields.map(x => Object.keys(x)[0]);

module.exports = { pensionFields, simpleFields };

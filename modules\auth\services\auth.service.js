/* eslint-disable no-underscore-dangle */
const jwt = require('jsonwebtoken');

const TokenModel = require('../../../models/token');
const { getToken } = require('../../../lib/auth/token');

const service = {
  async login(user) {
    try {
      const { name, email, role } = user;
      const safeUser = { name, email, role };
      const { JWTsecret, JWTexpires } = process.env;
      const token = jwt.sign(safeUser, JWTsecret, {
        expiresIn: JWTexpires || '1d',
        algorithm: 'HS256'
      });

      await TokenModel.remove({ user: user.id }).exec();

      await TokenModel.create({
        token,
        user: user.id
      });
      return {
        complete: true,
        error: null,
        data: { success: true, token: `Bearer ${token}` }
      };
    } catch (error) {
      throw new Error(error);
    }
  },
  async logout(req) {
    const { JWTsecret, JWTexpires } = process.env;
    try {
      const token = getToken(req);
      await jwt.verify(
        token,
        JWTsecret,
        { expiresIn: JWTexpires || '1d', algorithms: ['HS256'] },
        async function dec(err) {
          if (err) throw new Error('Error verifying the token on signout');
          await TokenModel.deleteOne({ token }).exec();
        }
      );
      return { complete: true };
    } catch (error) {
      throw new Error(error);
    }
  },
  async logoutUserById(userId) {
    try {
      await TokenModel.remove({ user: userId }).exec();
      return { complete: true };
    } catch (error) {
      throw new Error(error);
    }
  }
};

module.exports = service;

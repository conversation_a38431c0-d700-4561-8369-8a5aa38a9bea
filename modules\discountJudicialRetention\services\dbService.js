/* eslint no-underscore-dangle: 0 */
/* eslint no-param-reassign: "error" */
const DiscountsModel = require('../../../models/discountsAndAssets');
const PensionModel = require('../../../models/pension');
const CollectorModel = require('../../../models/collectorretentions');
const minimunSalaryModel = require('../../../models/minimunsalarys');
const ipcService = require('../../ipc/services/ipc.service');
const ipcHelper = require('../../ipc/workers/ipcHelper');
const ufService = require('../../UFvalue/services/ufValue.service');
const afpService = require('../../nomenclators/afp/services/index.service');
const { roundValue } = require('../../sharedFiles/helpers');

const JOB_ACCIDENT = /Pensi[oó]n por accidente de trabajo/i;
const TRAJECT_ACCIDENT = /Pensi[oó]n por accidente de trayecto/i;
const DISEASE = /Pensi[oó]n por enfermedad profesional/i;
const TYPE_MONTO = /Monto/i;
const TYPE_VITAL = /SueldoVital/i;
const TYPE_IMR = /ReajusteIMR/i;
const TYPE_IPC = /ReajusteIPC/i;
const TYPE_PORCENTAGE = /Porcentaje/i;
const REASON = /retenci[oó]n judicial/i;
const LABEL = /Descuento/i;
const IMR_DESCRIPCION = /Ingreso Mínimo Remuneracional/i;
const INMR_DESCRIPCION = /Ingreso Mínimo No Remuneracional/i;
const VITAL_POCENTAJE = 0.222757;

const NOT_VALID = /No vigente/i;

let retentionsCollector = [];

const formatNameAfp = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[aáàâäãå]/gi, 'a')
    .replace(/[eéèêë]/gi, 'e')
    .replace(/[iíìîï]/gi, 'i')
    .replace(/[oóòôöõ]/gi, 'o')
    .replace(/[uúùûü]/gi, 'u')
    .toLowerCase()
    .trim();

const getHealthAmount = ({ healthUF, ufValue, taxablePension }) => {
  return healthUF > 0 ? roundValue(healthUF * ufValue) : roundValue(taxablePension * 0.07);
};

const collectorRetencion = (paymentInfo, collector, amount, beneficiaryRut, causantRut) => {
  const { paymentGateway, accountNumber, bank, branchOffice } = paymentInfo;
  const { rut, name, lastName, mothersLastName, address, commune, city } = collector;

  return {
    beneficiaryRutOrigen: beneficiaryRut,
    causantRutOrigen: causantRut,
    collectorRut: rut,
    name,
    lastName,
    mothersLastName,
    address,
    commune,
    city,
    paymentGateway,
    accountNumber,
    bank,
    branchOffice,
    amount: roundValue(amount, 2)
  };
};

const afpFilter = (afp, name) => {
  const specialCharactersValidation = new RegExp(formatNameAfp(afp.name));
  return specialCharactersValidation.test(formatNameAfp(name));
};

const getAfpAmount = (pensionType, name, taxablePension, afps = []) => {
  if (
    JOB_ACCIDENT.test(pensionType) ||
    TRAJECT_ACCIDENT.test(pensionType) ||
    DISEASE.test(pensionType)
  ) {
    const afp = afps.find(x => afpFilter(x, name));
    if (afp) return roundValue((afp.percentage / 100) * taxablePension);
  }
  return 0;
};

const readjustAmount = (amount, creationDate, ipcLastVariation) => {
  const date = new Date();
  const currentMonth = new Date(date.getFullYear(), date.getMonth(), 1);
  const retentionMonth = new Date(creationDate.getFullYear(), creationDate.getMonth(), 1);
  if (ipcLastVariation.isLastPercentageChange && retentionMonth < currentMonth) {
    return ipcHelper.calculateField(amount, ipcLastVariation.percentage);
  }
  return amount;
};

const calculateRetention = (nonFormulable, calculationObject) => {
  const {
    amountMinimoRemuneracional,
    amountMinimoNoRemuneracional,
    ipcLastVariation,
    taxablePension,
    ufValue,
    afps,
    pensionType,
    afpAffiliation,
    healthUF,
    beneficiaryRut,
    causantRut
  } = calculationObject;

  const { judicialRetention, creationDate } = nonFormulable;
  const { retention, paymentInfo, collector } = judicialRetention;
  const { type, amount, validity } = retention;
  if (validity) {
    if (TYPE_MONTO.test(type)) {
      const newcollector = collectorRetencion(
        paymentInfo,
        collector,
        amount,
        beneficiaryRut,
        causantRut
      );
      retentionsCollector.push(newcollector);
      return { ...nonFormulable, amount };
    }
    if (TYPE_VITAL.test(type)) {
      const livingSalary = VITAL_POCENTAJE * amountMinimoNoRemuneracional;
      const amountIMR = livingSalary * (amount / 100);
      const newcollector = collectorRetencion(
        paymentInfo,
        collector,
        amountIMR,
        beneficiaryRut,
        causantRut
      );
      retentionsCollector.push(newcollector);
      return { ...nonFormulable, amount: roundValue(amountIMR, 2) };
    }
    if (TYPE_IMR.test(type)) {
      const ReadjustmentIMR = amountMinimoRemuneracional * (amount / 100);
      const newcollector = collectorRetencion(
        paymentInfo,
        collector,
        ReadjustmentIMR,
        beneficiaryRut,
        causantRut
      );
      retentionsCollector.push(newcollector);
      return { ...nonFormulable, amount: roundValue(ReadjustmentIMR, 2) };
    }
    if (TYPE_IPC.test(type)) {
      const ReadjustmentIPC = readjustAmount(amount, creationDate, ipcLastVariation);
      const newcollector = collectorRetencion(
        paymentInfo,
        collector,
        ReadjustmentIPC,
        beneficiaryRut,
        causantRut
      );
      retentionsCollector.push(newcollector);
      // Actualizo el monto de la retención ingresada para no perder el incremento del ipc en los meses venideros
      return {
        ...nonFormulable,
        amount: roundValue(ReadjustmentIPC, 2),
        judicialRetention: {
          ...judicialRetention,
          retention: { ...retention, amount: ReadjustmentIPC }
        }
      };
    }
    if (TYPE_PORCENTAGE.test(type)) {
      const health = getHealthAmount({ healthUF, ufValue, taxablePension });
      const afp = getAfpAmount(pensionType, afpAffiliation, taxablePension, afps);
      const amountDiscount = (taxablePension - health - afp) * (amount / 100);
      const newcollector = collectorRetencion(
        paymentInfo,
        collector,
        amountDiscount,
        beneficiaryRut,
        causantRut
      );
      retentionsCollector.push(newcollector);
      return { ...nonFormulable, amount: roundValue(amountDiscount, 2) };
    }
  }

  return { ...nonFormulable, amount: 0 };
};

const calculateDiscountNonFormulable = calculationObject => {
  const { discountsNonFormulable } = calculationObject;

  retentionsCollector = [];
  return discountsNonFormulable.map(nonFormulable => {
    const { reason, label } = nonFormulable;

    if (REASON.test(reason) && LABEL.test(label)) {
      return calculateRetention(nonFormulable, calculationObject);
    }
    return nonFormulable;
  });
};

const getTotalAmountRetention = discountsNonFormulable => {
  const retentions = discountsNonFormulable.filter(retention => REASON.test(retention.reason));
  return retentions.reduce(
    (sum, value) => (typeof value.amount === 'number' ? sum + value.amount : sum),
    0
  );
};

const service = {
  async getDiscounts() {
    return DiscountsModel.aggregate([
      {
        $lookup: {
          from: 'liquidations',
          let: {
            beneficiary: '$beneficiaryRut',
            causant: '$causantRut',
            pensionCode: '$pensionCodeId'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$beneficiaryRut', '$$beneficiary']
                    },
                    {
                      $eq: ['$causantRut', '$$causant']
                    },
                    {
                      $eq: ['$pensionCodeId', '$$pensionCode']
                    }
                  ]
                }
              }
            }
          ],
          as: 'liquidation'
        }
      },
      {
        $lookup: {
          from: 'pensions',
          let: {
            beneficiaryP: '$beneficiaryRut',
            causantP: '$causantRut',
            pensionCodeP: '$pensionCodeId'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$beneficiary.rut', '$$beneficiaryP']
                    },
                    {
                      $eq: ['$causant.rut', '$$causantP']
                    },
                    {
                      $eq: ['$pensionCodeId', '$$pensionCodeP']
                    }
                  ]
                }
              }
            }
          ],
          as: 'pension'
        }
      },
      {
        $match: {
          'pension.validityType': {
            $nin: [NOT_VALID]
          },
          pension: {
            $size: 1
          },
          liquidation: {
            $size: 1
          }
        }
      },
      {
        $project: {
          _id: 1,
          beneficiaryRut: 1,
          causantRut: 1,
          __v: 1,
          assetsNonFormulable: 1,
          createdAt: 1,
          discountsNonFormulable: 1,
          updatedAt: 1,
          'liquidation.taxablePension': 1,
          'liquidation._id': 1,
          'pension._id': 1,
          'pension.pensionType': 1,
          'pension.afpAffiliation': 1,
          'pension.discounts.healthUF': 1,
          'pension.discounts.nonFormulableByReason': 1
        }
      }
    ]);
  },

  async getMinimunSalary() {
    return minimunSalaryModel.find({}).lean();
  },

  async updateDiscountsAndAssetsById(discountsAndAssets) {
    if (!discountsAndAssets.length) {
      await CollectorModel.deleteMany({}).exec();
      return { completed: true, error: null };
    }
    const session = await DiscountsModel.startSession();
    session.startTransaction();
    try {
      const bulk = DiscountsModel.collection.initializeOrderedBulkOp();
      const bulkPension = PensionModel.collection.initializeOrderedBulkOp();
      await CollectorModel.deleteMany({}).exec();
      discountsAndAssets.forEach(({ _id, ...discountsAndAsset }) => {
        const { discountsNonFormulable, pension, retentionsCollectorAux } = discountsAndAsset;
        bulk
          .find({
            _id
          })
          .updateOne({
            $set: {
              discountsNonFormulable,
              updatedAt: new Date()
            }
          });

        const { discounts } = pension;
        const { idPension, nonFormulableByReason, totalNonFormulable } = discounts;
        bulkPension
          .find({
            _id: idPension
          })
          .updateOne({
            $set: {
              'discounts.nonFormulableByReason': nonFormulableByReason,
              'discounts.totalNonFormulable': totalNonFormulable,
              updatedAt: new Date()
            }
          });

        CollectorModel.insertMany(retentionsCollectorAux);
      });
      await bulk.execute();
      await bulkPension.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async calculateAmountDiscounts() {
    const discountsPension = await this.getDiscounts();
    const minimunSalary = await this.getMinimunSalary();
    const minimoRemuneracional = minimunSalary.find(element => IMR_DESCRIPCION.test(element.name));
    const minimoNoRemuneracional = minimunSalary.find(element =>
      INMR_DESCRIPCION.test(element.name)
    );
    const { amount: amountMinimoRemuneracional } = minimoRemuneracional;
    const { amount: amountMinimoNoRemuneracional } = minimoNoRemuneracional;
    const ipcLastVariation = await ipcService.getLastPercentage();
    const { value: ufValue } = await ufService.getCurrentUfValue();

    const { result: afps } = await afpService.getAfpsWithFilters(
      { enabled: true },
      { name: 1, percentage: 1 }
    );

    return discountsPension.map(discount => {
      const { liquidation, pension, discountsNonFormulable, beneficiaryRut, causantRut } = discount;
      const { taxablePension } = liquidation.find(element => element._id);
      const { pensionType, afpAffiliation, discounts, _id: idPension } = pension.find(
        element => element._id
      );
      const { healthUF, nonFormulableByReason } = discounts;

      const calculationObject = {
        beneficiaryRut,
        causantRut,
        discountsNonFormulable,
        amountMinimoRemuneracional,
        amountMinimoNoRemuneracional,
        ipcLastVariation,
        taxablePension,
        ufValue,
        afps,
        pensionType,
        afpAffiliation,
        healthUF
      };

      const newDiscountsNonFormulable = calculateDiscountNonFormulable(calculationObject);
      const totalAmountRetention = getTotalAmountRetention(newDiscountsNonFormulable);

      const newNonFormulableByReason = nonFormulableByReason.map(element => {
        if (REASON.test(element.reason)) {
          element.amount = roundValue(totalAmountRetention, 2);
        }
        return element;
      });

      const totalNonFormulable = newDiscountsNonFormulable.reduce(
        (acc, current) => acc + current.amount,
        0
      );

      return {
        ...discount,
        discountsNonFormulable: newDiscountsNonFormulable,
        pension: {
          ...pension,
          discounts: {
            ...discounts,
            idPension,
            nonFormulableByReason: newNonFormulableByReason,
            totalNonFormulable
          }
        },
        updateRecord: totalAmountRetention > 0,
        retentionsCollector
      };
    });
  },

  async setAmountRetention() {
    try {
      const discountsAndAssets = await this.calculateAmountDiscounts();
      const discountsUpdate = discountsAndAssets.filter(
        retention => retention.updateRecord === true
      );
      const { completed, error } = await this.updateDiscountsAndAssetsById(discountsUpdate);
      return { completed, error, discountsAndAssets };
    } catch (error) {
      return { completed: false, error, discountsAndAssets: [] };
    }
  }
};

module.exports = { ...service };

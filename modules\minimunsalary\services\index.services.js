/* eslint-disable new-cap */
const minimunSalaryModel = require('../../../models/minimunsalarys');

const service = {
  async updateMinimunSalary({ name, ...minimunSalaryData }) {
    try {
      const newim = new minimunSalaryModel({
        name,
        ...minimunSalaryData
      });
      const err = newim.validateSync();
      if (err === undefined) {
        const data = await minimunSalaryModel
          .findOneAndUpdate(
            { name },
            { $set: { ...minimunSalaryData } },
            { returnNewDocument: true, upsert: true, new: true }
          )
          .exec();
        return { result: data };
      }

      return { error: err.message, isError: true };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getMinimunSalarys() {
    return minimunSalaryModel
      .find({})
      .lean()
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

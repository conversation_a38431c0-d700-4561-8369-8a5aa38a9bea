/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const TemporaryPensionerModel = require('../models/temporaryPensioner');
const DiscountsModel = require('../../../models/discountsAndAssets');
const ProcessedJobsModel = require('../../sharedFiles/models/processedJob');
const service = require('./link.service');

const pensioner = require('../../../resources/pensions.json')[0];

describe('Pensioner Model Test', () => {
  beforeAll(beforeAllTests);
  let mocks;

  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn()
    };
    jest.spyOn(TemporaryPensionerModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  const actionPermisionFn = jest
    .fn()
    .mockResolvedValue({ actionIsAllowed: true, inDayRange: true, totalDays: 4 });

  it('error because no data to insert', async () => {
    const { isError } = await service.linkPensions();

    expect(isError).toBe(true);
  });

  it('success in link data', async () => {
    // create temporary pensioner
    await TemporaryPensionerModel.create(pensioner);

    const { isError } = await service.linkPensions();

    const count = await TemporaryPensionerModel.countDocuments();

    expect(isError).toBe(false);
    expect(count).toBe(1);
  });

  it('could not link cause there is linked on current month', async () => {
    // create temporary pensioner
    await TemporaryPensionerModel.create(pensioner);
    // first linked. Therefore linked on current month
    await service.linkPensions();
    // second link in same month
    const { isError, result } = await service.linkPensions();

    jest.restoreAllMocks();
    expect(isError).toBe(true);
    expect(result).toBe(0);
  });

  it('should not allow cancellation on non-working days', async () => {
    // Date format "YYYY-MM-DD"
    const date = new Date('2020-01-04');
    const response = await service.cancelLinkedPensions(date, actionPermisionFn);
    expect(response).toMatchObject({ isError: false, isCancelled: false });
  });

  it('should not allow cancellation, no linked data for the current month', async () => {
    await TemporaryPensionerModel.create(pensioner);
    // first linked. Therefore linked on current month
    await service.linkPensions();
    // first cancellation in same month
    let data = await service.cancelLinkedPensions(new Date(), actionPermisionFn);
    expect(mocks.startTransaction).toBeCalled();
    expect(mocks.commitTransaction).toBeCalled();
    expect(data.isError).toBe(false);
    // second cancellation in same month
    data = await service.cancelLinkedPensions(new Date(), actionPermisionFn);
  });

  it('should abort the transaction if there is any error', async () => {
    mocks.commitTransaction = jest.fn().mockImplementationOnce(() => {
      throw new Error();
    });
    await TemporaryPensionerModel.create(pensioner);
    await service.linkPensions();
    await service.cancelLinkedPensions(new Date(), actionPermisionFn);
    expect(mocks.abortTransaction).toBeCalled();
  });

  it('should abort the transaction if there is any error when cancelling link', async () => {
    mocks.commitTransaction = jest.fn().mockImplementationOnce(() => {
      throw new Error();
    });
    service.alreadyLinked = jest.fn().mockImplementationOnce(() => true);
    await TemporaryPensionerModel.create(pensioner);
    await service.cancelLinkedPensions(new Date(), actionPermisionFn);
    expect(mocks.abortTransaction).toBeCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await DiscountsModel.deleteMany({});
    await TemporaryPensionerModel.deleteMany({});
    await ProcessedJobsModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

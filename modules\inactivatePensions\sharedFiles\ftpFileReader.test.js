/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const readSFTPFile = require('./sftpFileReader');

describe('Ftp file Reader Test', () => {
  beforeAll(beforeAllTests);
  let sftpClient;
  let filesHelper;
  let connectToSFTPServer;
  let tmp;
  beforeEach(() => {
    sftpClient = {
      access: jest.fn(() => Promise.resolve()),
      downloadTo: jest.fn(() => Promise.resolve()),
      stat: jest.fn(() => Promise.resolve({ name: 'Achs_2020_out.txt', size: 10 })),
      close: jest.fn(() => Promise.resolve()),
      ftp: { verbose: false }
    };
    filesHelper = {
      getSFTPFileName: jest.fn(),
      readLines: jest.fn(() => Promise.resolve([])),
      getFormatedRut: jest.fn(),
      getFormatedDate: jest.fn()
    };
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true, error: false }));
    tmp = {
      fileSync: jest.fn(() => {
        return {
          name: 'xFile'
        };
      })
    };
  });

  it('success ftp get list', async () => {
    await readSFTPFile({ sftpClient, connectToSFTPServer, filesHelper, tmp });

    expect(sftpClient.stat).toHaveBeenCalled();
  });

  it('success ftp download', async () => {
    await readSFTPFile({ sftpClient, connectToSFTPServer, filesHelper, tmp });

    expect(sftpClient.downloadTo).toHaveBeenCalled();
  });

  it('error ftp connection', async () => {
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: false, error: true }));
    await readSFTPFile({ sftpClient, connectToSFTPServer, filesHelper, tmp });

    expect(sftpClient.stat).not.toHaveBeenCalled();
  });

  it('success ftp get list', async () => {
    filesHelper.getFormatedRut = jest.fn(() => '19023819-9');
    filesHelper.getFormatedDate = jest.fn(() => '20200303');
    filesHelper.readLines = jest.fn(() => Promise.resolve([['190238199', '    20200303']]));

    const { lines = [], error } = await readSFTPFile({
      sftpClient,
      connectToSFTPServer,
      filesHelper,
      tmp
    });
    const [rut, date] = lines[0];
    expect(error).toBeUndefined();
    expect(rut).toBe('19023819-9');
    expect(date).toBe('20200303');
  });

  afterAll(afterAllTests);
});

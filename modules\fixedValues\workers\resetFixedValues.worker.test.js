/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./resetFixedValues.worker');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);

  const resetFixedValues = jest.fn();
  const done = jest.fn();
  const Logger = {
    error: jest.fn(),
    info: jest.fn()
  };
  const logService = {
    saveLog: jest.fn().mockResolvedValue(true),
    existsLog: jest.fn(mark => mark === 'GENERATE_AND_UPLOAD_BANK_FILE'),
    retryLog: jest.fn(() => Promise.resolve()),
    existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
    addRetryTimeToJob: jest.fn(() => Promise.resolve())
  };

  const service = {
    basePensionAndArticlesFixedValues: jest.fn().mockResolvedValue({ error: null })
  };

  it('should run resetFixedValues worker', async () => {
    await workerModule.workerFn({ Logger, done, service, logService, resetFixedValues });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(service.basePensionAndArticlesFixedValues).toHaveBeenCalledWith(resetFixedValues);
    expect(done).toHaveBeenCalled();
  });

  it('should log error if there is any', async () => {
    service.basePensionAndArticlesFixedValues = jest.fn().mockResolvedValue({ error: true });
    logService.saveLog = jest.fn().mockImplementationOnce(() => {
      throw new Error();
    });
    await workerModule.workerFn({ Logger, done, service, logService, resetFixedValues });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.basePensionAndArticlesFixedValues).toHaveBeenCalledWith(resetFixedValues);
    expect(logService.saveLog).toThrow();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

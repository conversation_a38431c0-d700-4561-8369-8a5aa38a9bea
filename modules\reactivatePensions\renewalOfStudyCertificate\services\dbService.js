const PensionModel = require('../../../../models/pension');
const reactivate = require('../pipeline');
const { createUpdatePension } = require('../../../pensions/services/pension.service');

const VALIDITY_TYPE = /^No\s+vigente$/i;
const PENSION_TYPES = [
  /Pensi[oó]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+sin\s+hijos/i
];

const getPensions = async () => {
  return PensionModel.find({
    enabled: true,
    numberOfCharges: { $gte: 1 },
    validityType: VALIDITY_TYPE,
    pensionType: { $in: PENSION_TYPES },
    manuallyReactivated: { $ne: true },
    inactivateManually: { $ne: true }
  }).lean();
};

module.exports = {
  async reactivatePensions() {
    try {
      const pensions = await getPensions();
      const reactivatedPensions = pensions.map(({ _id, ...pension }) => reactivate(pension));
      const { completed, error } = await createUpdatePension(reactivatedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

const cronDescription = 'Cantidades reservadas paciente institucional:';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const cronMark = 'SET_RESERVED_AMOUNT_FOR_INSTITUTIONAL_PATIENT';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({ Logger, done, logService, service, pensionService, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    Logger.info(`${cronDescription} process started`);
    const { error } = await service.updatePensions(pensionService);
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');
const FactoryController = require('../modules/discountsAndAssets/controllers/discountsAndAssets.controller');
const validateAccess = require('../lib/auth/validate');

const { setUserAndEndpointInfo } = require('../lib/middleware/setUserAndEndpointInfo');

const SEE_MORE_ENDPOINT = '/pensionados/consulta-pensionados/ver-mas';

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    Logger
  });

  router.post(
    '/update',
    validateAccess(),
    setUserAndEndpointInfo(SEE_MORE_ENDPOINT),
    controller.update
  );
  router.post(
    '/create',
    validateAccess(),
    setUserAndEndpointInfo(SEE_MORE_ENDPOINT),
    controller.create
  );
  router.get('/isReadyToUpdate', validateAccess(), controller.isReadyToUpdate);
  router.get(
    '/isReadyToUpdateNonFormulable',
    validateAccess(),
    controller.isReadyToUpdateNonFormulable
  );
};

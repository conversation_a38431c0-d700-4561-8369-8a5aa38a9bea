const moment = require('moment');
const PensionModel = require('../../../../models/pension');
const PensionHistoricModel = require('../../../../models/pensionHistoric');

const getQuery = () => {
  const startOfYear = moment().startOf('year');
  const endOfYear = moment().endOf('year');
  return {
    enabled: true,
    validityType: { $regex: /Vigente\s+orfandad/i },
    endDateOfValidity: { $gte: startOfYear, $lte: endOfYear },
    $or: [
      { pensionType: { $regex: /Pensi[oó]n\s+por\s+orfandad/i } },
      { pensionType: { $regex: /Pensi[oó]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i } }
    ]
  };
};

const service = {
  async inactivatePensionsByAgeLimitCompliance() {
    const query = getQuery();
    const session = await PensionHistoricModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionHistoricModel.collection.initializeOrderedBulkOp();
      const bulkUpdate = PensionModel.collection.initializeOrderedBulkOp();
      const pensions = await PensionModel.find(query)
        .lean()
        .exec();

      pensions.forEach(pensionData => {
        const { _id, ...pensionOld } = pensionData;
        bulk.insert({
          ...pensionOld,
          enabled: false
        });
      });

      pensions.forEach(({ _id, ...pension }) => {
        bulkUpdate
          .find({
            _id
          })
          .updateOne({
            $set: {
              ...pension,
              inactivationDate: new Date(),
              validityType: 'No vigente',
              inactivationReason: 'Por cumplimiento de edad límite',
              enabled: true,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });
      });
      await bulk.execute();
      await bulkUpdate.execute();
      await session.commitTransaction();
      return { completed: true, hasError: false, inactivationError: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: true, hasError: true, inactivationError: error };
    }
  }
};

module.exports = service;

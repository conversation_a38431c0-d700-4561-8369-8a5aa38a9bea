/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests, Logger } = require('../../../testsHelper');
const { createMarkToReactivate, reactivateTransient } = require('./transients.service');
const pensionService = require('../../../pensions/services/pension.service');
const pensionToReactivate = require('../../../../resources/pensionToReactivate.json');
const responseSap = require('../../../../resources/responseSapRequest.json');
const result = require('../../../../resources/marksToReactivate.json');
const transientHelper = require('./transientHelper');

describe('transient rules', () => {
  beforeAll(beforeAllTests);
  let axios;
  let linkService;
  let sapRequests;

  it('create mark with result empty', async () => {
    const expected = await createMarkToReactivate([]);

    expect(expected.completed).toBe(true);
  });

  it('create mark with result ', async () => {
    const expected = await createMarkToReactivate(result);
    expect(expected.completed).toBe(true);
  });

  it('reactivate transient', async () => {
    axios = jest.fn(() => Promise.resolve([]));
    linkService = {
      getAllAndFilter: jest.fn(() => ({ result: [pensionToReactivate[0]] }))
    };

    sapRequests = { getDataToReactivate: jest.fn(() => Promise.resolve({ data: responseSap })) };

    await reactivateTransient({
      linkService,
      sapRequests,
      axios,
      pensionService,
      transientHelper,
      Logger
    });
    expect(linkService.getAllAndFilter).toHaveBeenCalled();
    expect(sapRequests.getDataToReactivate).toHaveBeenCalled();
  });

  afterAll(afterAllTests);
});

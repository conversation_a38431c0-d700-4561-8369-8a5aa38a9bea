const cronDescription =
  'unified Total Assets And Discounts with Net Pensions Liquidations Report cron:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual.';
const cronMark = 'UNIFIED_TOTAL_ASSETS_AND_DISCOUNTS_WITH_NET_PENSION_LIQUIDATIONS_REPORT';
const successMessage = `Proceso ${cronMark} completado correctamente.`;
const depsMarks = ['UF_VALUE', 'TAXABLE_PENSION'];
/* const getMissingDepsMsg = arr =>
  `No se ha ejecutado una o más de las dependencias ${arr.join(', ')} `; */
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, logService, netPensionsLiquidationReports, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    /*     if (!(await logService.allMarksExists(depsMarks))) {
      Logger.info(`${cronMark} ${getMissingDepsMsg(depsMarks)}`);
      return { message: getMissingDepsMsg(depsMarks), status: 'UNAUTHORIZED' };
    } */

    Logger.info(`${cronDescription} process started`);

    const {
      executionCompleted: netPensionLiquidationReportCompleted,
      message: netPensionLiquidationReportMessage,
      alreadyExecuted: netPensionLiquidationReportExecuted
    } = await netPensionsLiquidationReports({ done, Logger });

    const messageOutput = {
      netPensionLiquidationReportMessage
    };

    const allCompleted =
      netPensionLiquidationReportCompleted || netPensionLiquidationReportExecuted;

    if (!allCompleted) throw new Error(JSON.stringify(messageOutput));

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  }
};

module.exports = { cronMark, depsMarks, workerFn };

const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const LiquidationSchema = new Schema(
  {
    beneficiaryRut: { type: String },
    causantRut: { type: String },
    pensionCodeId: { type: String, required: true },
    taxablePension: { type: Number, min: 0, default: 0 },
    taxablePensionDate: { type: Date },
    totalAssets: { type: Number, min: 0, default: 0 },
    totalOnePercentDiscounts: { type: Number, min: 0, default: 0 },
    totalSocialCreditDiscounts: { type: Number, min: 0, default: 0 },
    totalDiscounts: { type: Number, min: 0, default: 0 },
    numberOfAssets: { type: Number, min: 0, default: 0 },
    numberOfDiscounts: { type: Number, min: 0, default: 0 },
    liquidationMonth: { type: Number },
    liquidationYear: { type: Number },
    netPension: { type: Number, min: 0, default: 0 },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

LiquidationSchema.plugin(paginate);
LiquidationSchema.index({ beneficiaryRut: 1, causantRut: 1, pensionCodeId: 1 });
LiquidationSchema.index({ beneficiaryRut: 1, causantRut: 1, pensionCodeId: 1, enabled: 1 });

module.exports = mongoose.model('Liquidation', LiquidationSchema);

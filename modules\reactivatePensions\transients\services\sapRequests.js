const getDataToReactivate = ({ axios, accidentCode }) => {
  const key = process.env.CRON_TO_INACTIVATE_SAP_KEY;
  const url = process.env.CRON_TO_INACTIVATE_SAP_URL;
  const authType = process.env.CRON_TO_INACTIVATE_SAP_AUTH_TYPE;

  const options = {
    method: 'GET',
    headers: { [authType]: key },
    url: `${url}?idSiniestro=${accidentCode.padStart(10, 0)}`
  };

  return axios(options).catch(err => ({ data: { reposos: [], incapacidad: [], error: err } }));
};

module.exports = {
  getDataToReactivate
};

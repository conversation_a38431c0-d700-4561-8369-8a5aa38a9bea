/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('Manually Inactivate Marked Pensions', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let done;
  let logService;
  let service;
  let pensionService;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = {
      inactivatePensions: jest.fn().mockResolvedValue({ error: null }),
      notReactivatePensions: jest.fn().mockResolvedValue({ error: null }),
      updateMarkedPensions: jest.fn().mockResolvedValue({ error: null }),
      inactivateAndNotReactivatePensions: jest.fn().mockResolvedValue({ error: null })
    };
    pensionService = {
      updatePensions: jest.fn().mockResolvedValue({ error: null })
    };
  });

  it('should call updateMarkedPensions', async () => {
    await workerModule.workerFn({ Logger, done, logService, service, pensionService });

    expect(logService.existsLog).toBeCalled();
    expect(service.inactivatePensions).toBeCalled();
    expect(service.notReactivatePensions).toBeCalled();
    expect(service.inactivateAndNotReactivatePensions).toBeCalled();
    expect(service.updateMarkedPensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('should fail updateMarkedPensions', async () => {
    jest.spyOn(service, 'updateMarkedPensions').mockImplementationOnce(() => {
      throw new Error();
    });

    await workerModule.workerFn({ Logger, done, logService, service, pensionService });

    expect(logService.existsLog).toBeCalled();
    expect(service.inactivatePensions).toBeCalled();
    expect(service.notReactivatePensions).toBeCalled();
    expect(service.inactivateAndNotReactivatePensions).toBeCalled();
    expect(service.updateMarkedPensions).toBeCalled();
    expect(Logger.error).toBeCalled();
  });

  it('should exist cron mark', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, done, logService, service, pensionService });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(Logger.info).toBeCalled();
  });

  it('Do not exist cron dependency mark', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(true))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, done, logService, service, pensionService });

    expect(logService.existsLog).toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(3);
  });

  it('should call inactivatePensions', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(true));

    await workerModule.workerFn({ Logger, done, logService, service, pensionService });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(4);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

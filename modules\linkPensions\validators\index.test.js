const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionsModel = require('../../../models/pension');
const { decimalValidator, rutValidator, heavyDutyValidator } = require('./index');
const pensions = require('../../../resources/pensions.json');

describe('Temporary pensions validation', () => {
  beforeAll(beforeAllTests);

  it('it should check a valid value for heavyDuty by pension type ', async () => {
    const pension = { ...pensions[0], heavyDuty: 'No' };

    const pensionData = await PensionsModel.create(pension);

    expect(pensionData.heavyDuty).toBe('No');
  });
  it('it should check a invalid value for heavyDuty by pension type ', async () => {
    const heavyDuty = 'Noo';
    const pensionType = 'Pensión por orfandad';

    expect(heavyDutyValidator.validator(heavyDuty, pensionType)).toBe(false);
  });

  it('it should check a invalid value for parentRut is valid and pensionType is one of SURVIVORS_PENSIONS_TYPE', async () => {
    const parentRUT = '';
    const pensionType = 'Pensión por orfandad';

    expect(rutValidator.validator(parentRUT, pensionType)).toBe(true);
  });

  it('it should check a invalid value for increasingInLaw19578 is valid and pensionType is one of SURVIVORS_PENSIONS_TYPE', async () => {
    const increasingInLaw19578 = 10.9956;

    expect(decimalValidator.validator(increasingInLaw19578)).toBe(false);
  });

  it('it should check an empty valid value for heavyDuty by pension type that allows an empty value ', async () => {
    const heavyDuty = '';
    const pensionType = 'Pensión de viudez con hijos';

    expect(heavyDutyValidator.validator(heavyDuty, pensionType)).toBe(true);
  });

  it('it should check an empty valid value for parentRUT by pension type that allows an empty value ', async () => {
    const parentRUT = '';
    const pensionType = 'Pensión de viudez con hijos';

    expect(rutValidator.validator(parentRUT, pensionType)).toBe(true);
  });

  it('it should check an correct value for increasingInLaw19578 is valid ', async () => {
    const increasingInLaw19578 = 10.99;

    expect(decimalValidator.validator(increasingInLaw19578)).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionsModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TemporaryFamilyAssignment = new Schema(
  {
    chargeId: { type: String, required: true },
    causantId: { type: String, required: true },
    collectorId: { type: String, required: true },
    chargeValidityType: { type: String, required: true },
    typeOfAssocietedPension: { type: String, required: true },
    dateOfChargeInactivation: { type: Date },
    startDateOfCertificationValidity: { type: Date },
    endDateOfCertificationValidity: { type: Date },
    invalid: { type: String },
    relationship: { type: String },
    familyAssignment: { type: Number, required: true },
    retroactiveFamilyAssignment: { type: Number, required: true }
  },

  { timestamps: true }
);
TemporaryFamilyAssignment.index({ createdAt: 1 }, { expires: '180d' });
TemporaryFamilyAssignment.plugin(paginate);
TemporaryFamilyAssignment.index({ idCode: 1 });

module.exports = mongoose.model('TemporaryFamilyAssignment', TemporaryFamilyAssignment);

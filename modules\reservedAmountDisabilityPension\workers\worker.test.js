/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const worker = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker set reserved amount to Taxable Pension Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      reservedDisabilityPension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {
      updatePensions: jest.fn().mockResolvedValue({ completed: true, error: null })
    };
  });

  it('success worker', async () => {
    await worker({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.reservedDisabilityPension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await worker({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.reservedDisabilityPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await worker({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.reservedDisabilityPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

const moment = require('moment');

const DiscountsAndAssetsModel = require('../../../models/discountsAndAssets');
const pensionService = require('../../pensions/services/pension.service');

const IMPONIBLE = /imponible/i;
const LIQUIDO = /l[ií]quido/i;

const VALIDITY_TYPE = /No\s+vigente/i;
const toLowerCase = (text = '') => text.toLowerCase();

function calculateSumByReason(asset, sumArray) {
  const assetSum = sumArray.find(item => toLowerCase(item.reason) === toLowerCase(asset.reason));
  if (assetSum) {
    assetSum.amount += asset.amount;
  } else {
    sumArray.push({ reason: toLowerCase(asset.reason), amount: asset.amount });
  }
}

const service = {
  async updateDiscountAndAssetsTotals(id) {
    const discountsAndAssets = id && (await DiscountsAndAssetsModel.findById(id).exec());
    const { assetsNonFormulable = [], discountsNonFormulable = [] } = discountsAndAssets || {};
    const assetsNonFormulableTaxableTotalsByReason = [];
    const assetsNonFormulableNetTotalsByReason = [];
    const discountsNonFormulableTotalsByReason = [];
    assetsNonFormulable.forEach(asset => {
      if (IMPONIBLE.test(asset.assetType)) {
        calculateSumByReason(asset, assetsNonFormulableTaxableTotalsByReason);
      } else if (LIQUIDO.test(asset.assetType)) {
        calculateSumByReason(asset, assetsNonFormulableNetTotalsByReason);
      }
    });
    discountsNonFormulable.forEach(asset => {
      calculateSumByReason(asset, discountsNonFormulableTotalsByReason);
    });

    return {
      assetsNonFormulableTaxableTotalsByReason: assetsNonFormulableTaxableTotalsByReason.filter(
        item => item.reason
      ),
      assetsNonFormulableNetTotalsByReason: assetsNonFormulableNetTotalsByReason.filter(
        item => item.reason
      ),
      discountsNonFormulableTotalsByReason: discountsNonFormulableTotalsByReason.filter(
        item => item.reason
      )
    };
  },
  async calculateAssetsBonusDiscounts() {
    const { result: pensionList } = await pensionService.getAllAndFilter({
      enabled: true,
      validityType: VALIDITY_TYPE,
      createdAt: {
        $gte: moment()
          .startOf('month')
          .startOf('day')
          .toDate()
      }
    });

    const updatedPensions = await Promise.all(
      pensionList.map(async ({ _doc: { _id, ...pension } }) => {
        const {
          basePension = 0,
          article40 = 0,
          article41 = 0,
          assets = {},
          reservedAmounts = {}
        } = pension;
        const { winterBonus = 0, nationalHolidaysBonus = 0, christmasBonus = 0 } = assets;
        const forBonuses = winterBonus + nationalHolidaysBonus + christmasBonus;

        const {
          assetsNonFormulableTaxableTotalsByReason,
          assetsNonFormulableNetTotalsByReason,
          discountsNonFormulableTotalsByReason
        } = await this.updateDiscountAndAssetsTotals(pension.discountsAndAssets);

        return {
          ...pension,
          reservedAmounts: {
            ...reservedAmounts,
            forBasePension: basePension,
            forArticle40: article40,
            forArticle41: article41,
            forBonuses,
            assetsNonFormulableTaxableTotalsByReason,
            assetsNonFormulableNetTotalsByReason,
            discountsNonFormulableTotalsByReason
          }
        };
      })
    );

    const { completed, error } = await pensionService.updatePensions(updatedPensions);

    return { completed, error };
  }
};

module.exports = { ...service };

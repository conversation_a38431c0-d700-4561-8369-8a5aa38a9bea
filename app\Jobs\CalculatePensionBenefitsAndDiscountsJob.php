<?php

namespace App\Jobs;

use App\Domain\Pension\Models\Pension;
use App\Domain\Pension\Services\PensionCalculationService;
use App\Domain\Pension\Events\PensionCalculated;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

class CalculatePensionBenefitsAndDiscountsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutos
    public $tries = 3;
    public $maxExceptions = 3;

    public function __construct(
        private Collection $pensionIds,
        private string $batchId,
        private array $options = []
    ) {
        $this->onQueue('pension-calculations');
    }

    public function handle(PensionCalculationService $calculationService): void
    {
        Log::info('Starting pension calculations batch', [
            'batch_id' => $this->batchId,
            'pension_count' => $this->pensionIds->count()
        ]);

        $startTime = microtime(true);
        $processed = 0;
        $errors = 0;

        // Cargar pensiones con relaciones necesarias
        $pensions = Pension::with([
            'discountsAndAssets',
            'currentLiquidation'
        ])->whereIn('id', $this->pensionIds)->get();

        foreach ($pensions as $pension) {
            try {
                $this->processPension($pension, $calculationService);
                $processed++;
                
                // Actualizar progreso cada 50 pensiones
                if ($processed % 50 === 0) {
                    $this->updateProgress($processed, $this->pensionIds->count());
                }
                
            } catch (\Exception $e) {
                $errors++;
                Log::error('Error calculating pension', [
                    'pension_id' => $pension->id,
                    'batch_id' => $this->batchId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);

                // Si hay demasiados errores, fallar el job
                if ($errors > 10) {
                    throw new \Exception("Too many errors in batch {$this->batchId}");
                }
            }
        }

        $executionTime = microtime(true) - $startTime;

        Log::info('Completed pension calculations batch', [
            'batch_id' => $this->batchId,
            'processed' => $processed,
            'errors' => $errors,
            'execution_time' => round($executionTime, 2)
        ]);

        // Limpiar cache de progreso
        Cache::forget("batch_progress_{$this->batchId}");
    }

    private function processPension(Pension $pension, PensionCalculationService $service): void
    {
        // Calcular beneficios y descuentos
        $calculationResult = $service->calculatePension($pension);

        // Actualizar pensión con resultados
        $pension->update([
            'assets' => $calculationResult->assets,
            'discounts' => $calculationResult->discounts,
            'retroactive_amounts' => $calculationResult->retroactive,
            'calculated_at' => $calculationResult->calculatedAt,
        ]);

        // Crear/actualizar liquidación
        $this->updateLiquidation($pension, $calculationResult);

        // Disparar evento
        event(new PensionCalculated($pension, $calculationResult));
    }

    private function updateLiquidation(Pension $pension, $calculationResult): void
    {
        $liquidation = $pension->currentLiquidation ?? $pension->liquidations()->make();

        $liquidation->fill([
            'pension_code_id' => $pension->pension_code_id,
            'beneficiary_rut' => $pension->beneficiary_rut,
            'causant_rut' => $pension->causant_rut,
            'taxable_pension' => $this->calculateTaxablePension($calculationResult),
            'total_assets' => array_sum($calculationResult->assets),
            'total_discounts' => array_sum($calculationResult->discounts),
            'net_pension' => $calculationResult->netPension,
            'liquidation_month' => now()->month,
            'liquidation_year' => now()->year,
            'enabled' => true,
        ]);

        $liquidation->save();
    }

    private function calculateTaxablePension($calculationResult): float
    {
        // Pensión imponible = pensión base + algunos beneficios específicos
        $taxableComponents = [
            'base_pension',
            'article_40', 
            'article_41',
            'law_19403',
            'law_19539', 
            'law_19953',
            'non_formulable_taxable'
        ];

        $taxablePension = 0;
        
        foreach ($taxableComponents as $component) {
            $taxablePension += $calculationResult->assets[$component] ?? 0;
        }

        return round($taxablePension, 2);
    }

    private function updateProgress(int $processed, int $total): void
    {
        $progress = [
            'processed' => $processed,
            'total' => $total,
            'percentage' => round(($processed / $total) * 100, 2),
            'updated_at' => now()->toISOString()
        ];

        Cache::put("batch_progress_{$this->batchId}", $progress, 3600);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Pension calculation batch failed', [
            'batch_id' => $this->batchId,
            'pension_ids' => $this->pensionIds->toArray(),
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);

        // Marcar progreso como fallido
        Cache::put("batch_progress_{$this->batchId}", [
            'status' => 'failed',
            'error' => $exception->getMessage(),
            'failed_at' => now()->toISOString()
        ], 3600);
    }

    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(30);
    }
}

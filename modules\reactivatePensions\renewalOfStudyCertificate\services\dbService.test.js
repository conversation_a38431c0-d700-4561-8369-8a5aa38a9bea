/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const PensionModel = require('../../../../models/pension');
const service = require('./dbService');
const pensionsData = require('../../../../resources/pensions.json');

describe('Reactivate Pensioner For Renewal Of study Certificate', () => {
  beforeAll(beforeAllTests);

  it('should reactivate pensioners under 45 years old setting validityType to  "Vigente viudez"', async () => {
    const thirtyYearsAgo = new Date().getFullYear() - 30;

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No Vigente',
      dateOfBirth: `${thirtyYearsAgo}-03-03`,
      numberOfCharges: 1
    };

    await PensionModel.create(pension);
    await service.reactivatePensions();
    const dbDocs = await PensionModel.find({});
    expect(dbDocs.length).toBe(1);
    expect(dbDocs[0].enabled).toBe(true);
    expect(dbDocs[0].validityType).toBe('Vigente viudez');
    expect(moment(dbDocs[0].endDateOfValidity).format('YYYY-MM-DD')).toBe(
      moment()
        .endOf('year')
        .format('YYYY-MM-DD')
    );
  });

  it('should reactivate pensioners greater than 45 years old setting validityType to  "Vigente vitalicia"', async () => {
    const fourtySevenYearsAgo = new Date().getFullYear() - 47;

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No Vigente',
      dateOfBirth: `${fourtySevenYearsAgo}-03-03`,
      numberOfCharges: 10
    };

    await PensionModel.create(pension);
    await service.reactivatePensions();
    const dbDocs = await PensionModel.find({});
    expect(dbDocs.length).toBe(1);
    expect(dbDocs[0].enabled).toBe(true);
    expect(dbDocs[0].validityType).toBe('Vigente vitalicia');
    expect(moment(dbDocs[0].endDateOfValidity).format('YYYY-MM-DD')).toBe(
      moment(new Date(dbDocs[0].dateOfBirth))
        .add(110, 'years')
        .format('YYYY-MM-DD')
    );
  });

  afterEach(async () => {
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./reportGeneratorService');
const expenseAccountingData = require('../../../resources/expenseAccountingReport.json');

describe('generator test service', () => {
  beforeAll(beforeAllTests);

  it('should get the xlsx file', async () => {
    const { workbook, error } = await service.getWorkBook(expenseAccountingData);

    expect(error).toBe(undefined);
    expect(workbook).not.toBe(undefined);
  });

  it('should fail to get xlsx', async () => {
    const { workbook, error } = await service.getWorkBook([]);

    expect(error).not.toBe(undefined);
    expect(workbook).toBe(undefined);
  });

  afterAll(afterAllTests);
});

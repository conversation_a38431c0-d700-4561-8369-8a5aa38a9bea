/* eslint-disable consistent-return */
const Sftp = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const fileGenerationUtils = require('../services/fileGenerationUtils');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'healthExemptionPayment',
  worker: async deps =>
    workerModule.workerFn({ service, logService, fileGenerationUtils, Sftp, ...deps }),
  repeatInterval: process.env.CRON_HEALTH_EXEMPTION_PAYMENT_FREQUENCY,
  description: 'Cron para generar archivo Exención Salud APS',
  endPoint: 'healthexemptionpayment',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

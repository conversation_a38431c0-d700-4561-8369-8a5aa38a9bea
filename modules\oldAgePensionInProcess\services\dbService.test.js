const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const oldAgePensioners = require('../../../resources/oldAgePensionInProcess.json');

describe('old-age-pension-in-process', () => {
  beforeAll(beforeAllTests);

  it('old-age-pension', async () => {
    service.filterPensionersAssignOne = jest.fn(() => Promise.resolve(oldAgePensioners));
    service.filterPensionersAssignZero = jest.fn(() => Promise.resolve(oldAgePensioners));

    const { completed } = await service.markPensionersForRetirement();
    expect(completed).toBe(true);
  });

  afterAll(afterAllTests);
});

const { soap } = require('strong-soap');

const { getPreviousMonthDate } = require('../../sharedFiles/helpers');

const USER_IPC_SERVICE = process.env.USER_IPC_SERVICE || 764587839;
const PASSWORD_IPC_SERVICE = process.env.PASSWORD_IPC_SERVICE || 'ZIr1MSDH9JoDQmz';
const SERIE_ID_IPC_SERVICE = process.env.SERIE_ID_IPC_SERVICE || 'F074.IPC.IND.Z.EP18.C.M';
const URL_API_IPC_SERVICE =
  process.env.URL_API_IPC_SERVICE || 'https://si3.bcentral.cl/SieteWS/SieteWS.asmx?wsdl';

const requestArgs = {
  user: USER_IPC_SERVICE,
  password: PASSWORD_IPC_SERVICE,
  firstDate: getPreviousMonthDate('01'),
  seriesIds: { string: SERIE_ID_IPC_SERVICE }
};

const soapRequest = (resolve, reject) =>
  soap.createClient(URL_API_IPC_SERVICE, {}, (err, client) => {
    if (err) {
      reject(new Error(err));
    }
    const method = client.GetSeries;
    method(requestArgs, async (errMethod, result) => {
      if (errMethod) {
        reject(new Error(errMethod));
      }
      resolve(result);
    });
  });

module.exports = soapRequest;

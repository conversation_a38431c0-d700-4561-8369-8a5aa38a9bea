const cronMark = 'REACTIVATE_TRANSIENT_PRE_WORKER';
const dependencyMark = 'INACTIVATE_PENSIONS_WORKER';
const getMissingDependencyMessage = `No se ha ejecutado la dependencia ${dependencyMark}`;

const cronDescription = 'reactivation by transient.';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const workerFn = async ({
  Logger,
  transientService,
  linkService,
  pensionService,
  axios,
  sapRequests,
  transientHelper,
  logService,
  job,
  done
}) => {
  try {
    Logger.info(`Inicio de ejecucion worker por reactivar`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage);
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    await transientService.reactivateTransient({
      Logger,
      linkService,
      pensionService,
      axios,
      sapRequests,
      transientHelper
    });

    await logService.saveLog(cronMark);

    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

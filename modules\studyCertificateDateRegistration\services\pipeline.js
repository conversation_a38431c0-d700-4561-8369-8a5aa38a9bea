const matchAggregationStage = [
  {
    $match: {
      chargeValidityType: {
        $in: [/Interna\s+activa/i, /Activa\s+para\s+art[íi]culo\s+41/i]
      }
    }
  },
  {
    $addFields: {
      startDate: {
        $dateFromParts: {
          year: { $year: '$startDateOfCertificationValidity' },
          month: { $month: '$startDateOfCertificationValidity' },
          day: 1,
          hour: 0
        }
      },
      endDate: {
        $dateFromParts: {
          year: { $year: '$endDateOfCertificationValidity' },
          month: { $month: '$endDateOfCertificationValidity' },
          day: 31,
          hour: 23,
          minute: 59,
          second: 59
        }
      }
    }
  }
];

const buildLookupAggregationStage = ({ idField, pensionTypes, alias }) => ({
  $lookup: {
    from: 'pensions',
    let: { [idField]: `$${idField}`, startDate: '$startDate', endDate: '$endDate' },
    pipeline: [
      {
        $match: {
          validatedStudyPeriod: /No/i,
          validityType: { $not: /No vigente/i },
          pensionType: {
            $in: pensionTypes
          },
          $expr: {
            $and: [
              { $eq: ['$beneficiary.rut', `$$${idField}`] },
              { $gte: ['$createdAt', '$$startDate'] },
              { $lte: ['$createdAt', '$$endDate'] }
            ]
          }
        }
      }
    ],
    as: alias
  }
});

module.exports = { matchAggregationStage, buildLookupAggregationStage };

const buildAggregation = (year, month, queryField) => [
  {
    $match: {
      enabled: true,
      validityType: {
        $not: new RegExp('No vigente', 'i')
      },
      [queryField]: new RegExp('s[ií]', 'i'),
      $expr: {
        $and: [
          {
            $eq: [
              {
                $month: '$createdAt'
              },
              month
            ]
          },
          {
            $eq: [
              {
                $year: '$createdAt'
              },
              year
            ]
          }
        ]
      }
    }
  },
  {
    $lookup: {
      from: 'pensions',
      let: {
        beneficiary: '$beneficiary.rut',
        causant: '$causant.rut'
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ['$beneficiary.rut', '$$beneficiary']
                },
                {
                  $eq: ['$causant.rut', '$$causant']
                },
                {
                  $eq: ['$enabled', false]
                }
              ]
            }
          }
        },
        {
          $sort: {
            _id: -1
          }
        }
      ],
      as: 'matchedPensions'
    }
  },
  {
    $addFields: {
      matchedPensions: {
        $reduce: {
          input: '$matchedPensions',
          initialValue: {
            result: [],
            status: true
          },
          in: {
            $cond: [
              {
                $and: [
                  {
                    $eq: ['$$this.rejectionIPS', true]
                  },
                  {
                    $eq: ['$$value.status', true]
                  }
                ]
              },
              {
                result: {
                  $concatArrays: ['$$value.result', ['$$this']]
                },
                status: '$$value.status'
              },
              {
                result: '$$value.result',
                status: false
              }
            ]
          }
        }
      }
    }
  }
];

module.exports = { buildAggregation };

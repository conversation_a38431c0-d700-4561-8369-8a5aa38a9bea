<?php

namespace App\Domain\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Modelo que representa las reglas de bonos (navidad, fiestas patrias, invierno, etc.)
 */
class BonusRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'bonus_type',
        'name',
        'description',
        'min_pension',
        'max_pension',
        'bonus_amount',
        'calculation_method',
        'payment_months',
        'pension_types',
        'enabled',
        'valid_from',
        'valid_to'
    ];

    protected $casts = [
        'min_pension' => 'decimal:2',
        'max_pension' => 'decimal:2',
        'bonus_amount' => 'decimal:2',
        'payment_months' => 'array',
        'pension_types' => 'array',
        'enabled' => 'boolean',
        'valid_from' => 'date',
        'valid_to' => 'date'
    ];

    // Scopes
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeByType($query, string $bonusType)
    {
        return $query->where('bonus_type', $bonusType);
    }

    public function scopeForCurrentMonth($query)
    {
        $currentMonth = now()->month;
        return $query->whereJsonContains('payment_months', $currentMonth);
    }

    // Business Logic
    public function shouldPayThisMonth(): bool
    {
        if (!$this->payment_months) {
            return true; // Si no hay restricción de meses, se paga siempre
        }

        return in_array(now()->month, $this->payment_months);
    }

    public function appliesTo($pension): bool
    {
        // Verificar tipo de pensión
        if ($this->pension_types && !in_array($pension->pension_type, $this->pension_types)) {
            return false;
        }

        // Verificar rango de pensión
        $totalPension = $pension->base_pension + $pension->article_40 + $pension->article_41;
        
        if ($this->min_pension && $totalPension < $this->min_pension) {
            return false;
        }

        if ($this->max_pension && $totalPension > $this->max_pension) {
            return false;
        }

        return true;
    }
}

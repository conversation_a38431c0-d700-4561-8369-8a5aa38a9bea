const operator = '(=|>|<|>=|<|<=|<>)';
const space = '(\\s)*';
const age = '\\d{1,2}';
const expr = `^(${age}${space}${operator}${space})?Edad${space}${operator}${space}${age}$`;
const regexp = new RegExp(expr, 'i');

module.exports = {
  validator: {
    $and: [
      {
        age: {
          $in: [null, regexp]
        }
      },
      { type: { $in: ['bonus', 'pension'] } },
      {
        label: {
          $in: [
            /Pensi[oó]n por accidente de trabajo/i,
            /Pensi[oó]n por accidente de trayecto/i,
            /Pensi[oó]n por enfermedad profesional/i,
            /Pensi[oó]n de viudez con hijos/i,
            /Pensi[oó]n de viudez sin hijos/i,
            /Pensi[oó]n de madre de hijo de filiación no matrimonial con hijos/i,
            /Pensi[oó]n de madre de hijo de filiación no matrimonial sin hijos/i,
            /Pensi[oó]n por orfandad/i,
            /Aguinaldo de fiestas patrias/i,
            /Aguinaldo de navidad/i,
            /Bono Invierno/i,
            /Aguinaldo fiestas patrias por carga familiar/i,
            /Aguinaldo navidad por carga familiar/i
          ]
        }
      }
    ]
  }
};

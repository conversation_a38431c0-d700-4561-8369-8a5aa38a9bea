/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker  reseted values Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;

  beforeEach(() => {
    done = jest.fn();
    service = {
      resetPensionValues: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {};

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(service.resetPensionValues).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('alreay worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.resetPensionValues).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.resetPensionValues).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker dependency mark not founded', async () => {
    logService = {
      existsLog: jest
        .fn(() => Promise.resolve(false))
        .mockImplementationOnce(() => Promise.resolve(false))
        .mockImplementationOnce(() => Promise.resolve(false)),
      saveLog: jest.fn(() => Promise.resolve())
    };

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(service.resetPensionValues).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(2);
  });

  afterAll(afterAllTests);
});

const axios = require('axios');
const transientService = require('../services/transients.service');
const linkService = require('../../../linkPensions/services/link.service');
const pensionService = require('../../../pensions/services/pension.service');
const transientHelper = require('../services/transientsHelper');
const preWorkerModule = require('./pre.worker');
const postWorkerModule = require('./post.worker');
const logService = require('../../../sharedFiles/services/jobLog.service');

module.exports = {
  inactivatePensionsByTransientsPreWorker: {
    name: 'inactivatePensionsByTransientsPreWorker',
    dependencies: [
      'InactivateByMarriage-preworker',
      'inactivatePensionsByRetirementPreWorker',
      'InactivateByMarriage-postworker',
      'inactivatePensionsByRetirementPostWorker',
      'daysOfTranstientPension',
      'inactivatePensionsByTransientsPostWorker',
      'inactivatePensionsDeath',
      'modifyCivilRegistryData'
    ],
    worker: deps =>
      preWorkerModule.workerFn({
        pensionService,
        transientService,
        linkService,
        axios,
        transientHelper,
        logService,
        ...deps
      }),
    repeatInterval: process.env.CRON_PRE_INACTIVATE_BY_TRANSIENTS,
    description:
      'Marcar pensiones transitorias a inactivar el mes siguiente al actual por alta medica o Resolución definitiva',
    endPoint: 'inactivatebytransientpreworker',
    cronMark: preWorkerModule.cronMark,
    dependencyMark: preWorkerModule.dependencyMark
  },
  inactivatePensionsByTransientsPostWorker: {
    name: 'inactivatePensionsByTransientsPostWorker',
    worker: deps => postWorkerModule.workerFn({ transientService, logService, ...deps }),
    description: 'Inactivar pensiones transitorias marcadas en el mes anterior al actual',
    endPoint: 'inactivatebytransientpostworker',
    cronMark: postWorkerModule.cronMark,
    dependencyMark: postWorkerModule.dependencyMark
  }
};

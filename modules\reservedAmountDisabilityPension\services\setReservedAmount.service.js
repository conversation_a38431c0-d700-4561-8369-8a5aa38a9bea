const PensionModel = require('../../../models/pension');

const PENSION_TYPE = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];
const setReservedAmountPension = ({ _id, liquidation, ...pension }) => {
  const { taxablePension } = liquidation;
  const { reservedAmounts } = pension;
  const roundValues = Math.round((taxablePension + Number.EPSILON) * 100) / 100;

  return {
    ...pension,
    reservedAmounts: { ...reservedAmounts, forDisability: roundValues }
  };
};

const reservedDisabilityPension = async pensionService => {
  const pensions = await PensionModel.aggregate([
    {
      $match: {
        pensionType: { $in: PENSION_TYPE },
        enabled: true,
        validityType: new RegExp('no vigente', 'i'),
        transient: new RegExp('no', 'i')
      }
    },
    {
      $lookup: {
        from: 'liquidations',
        let: {
          pensionBeneficiaryRut: '$beneficiary.rut',
          pensionCausantRut: '$causant.rut',
          pensionCodeIdP: '$pensionCodeId'
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: ['$beneficiaryRut', '$$pensionBeneficiaryRut']
                  },
                  {
                    $eq: ['$causantRut', '$$pensionCausantRut']
                  },
                  {
                    $eq: ['$pensionCodeId', '$$pensionCodeIdP']
                  }
                ]
              }
            }
          }
        ],
        as: 'liquidation'
      }
    },
    {
      $unwind: {
        path: '$liquidation'
      }
    }
  ]).exec();

  const setTaxablePensions = pensions.map(pension => setReservedAmountPension(pension));

  const { completed, error } = await pensionService.updatePensions(setTaxablePensions);
  return { completed, error };
};

module.exports = { setReservedAmountPension, reservedDisabilityPension };

const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const ConcurrentModel = require('../models/concurrency');
const FactorModel = require('../models/factor');
const service = require('./quadrature.service');

describe('test quadrature services', () => {
  beforeAll(beforeAllTests);

  const correctConcurrencies = [
    {
      beneficiaryRut: '17176146-8',
      concurrencyPercentage: 1.15,
      concurrencyReceivable: 10.15,
      mutualPercentage: 11.15,
      istPercentage: 12.15,
      islPercentage: 13.15
    }
  ];
  const correctFactors = [{ key: 'M01Jubilacion9', factor: '51.1' }];

  it('should find no data was uploaded', async () => {
    const [month, year] = moment()
      .format('MM-YYYY')
      .split('-');
    const {
      wasDataUploaded: { factors, concurrencies }
    } = await service.wasDataUploaded({ month, year });

    expect(concurrencies).toBe(false);
    expect(factors).toBe(false);
  });

  it('should throw an error', async () => {
    const [month, year] = moment()
      .format('MM-YYYY')
      .split('-');
    jest.spyOn(ConcurrentModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });
    const {
      wasDataUploaded: { factors, concurrencies },
      error
    } = await service.wasDataUploaded({ month, year });

    expect(error).toBeDefined();
    expect(concurrencies).toBeUndefined();
    expect(factors).toBeUndefined();
  });

  it('should insert concurrencies', async () => {
    const { completed, error } = await service.insertConcurrencies(correctConcurrencies);

    expect(error).toBeUndefined();
    expect(completed).toBe(true);
  });

  it('should not insert concurrencies without the mandatory fields', async () => {
    const { completed, error } = await service.insertConcurrencies([{}]);

    expect(error).toBeDefined();
    expect(completed).toBeUndefined();
  });

  it('should insert factors', async () => {
    const { completed, error } = await service.insertFactors(correctFactors);

    expect(error).toBeUndefined();
    expect(completed).toBe(true);
  });

  it('should not insert factors without the mandatory fields', async () => {
    const { completed, error } = await service.insertFactors([{}]);

    expect(error).toBeDefined();
    expect(completed).toBeUndefined();
  });

  it('should check csvData is a string composed of llave,factor', async () => {
    const { completed } = await service.insertFactors(correctFactors);

    expect(completed).toBe(true);

    const { csvData, error } = await service.generateFactorCSV();

    expect(csvData).toBe('Llave,factor\nM01Jubilacion9,51.1');
    expect(error).toBeUndefined();
  });

  it('should fail searching factors', async () => {
    jest.spyOn(FactorModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });
    const { csvData, error } = await service.generateFactorCSV();

    expect(csvData).toBe('');
    expect(error).toBeDefined();
  });
  it('should check csvData is a string composed of rutbeneficiario,% concurrencias', async () => {
    const { completed } = await service.insertConcurrencies(correctConcurrencies);

    expect(completed).toBe(true);

    const [month, year] = moment()
      .format('MM-YYYY')
      .split('-');

    const { csvData, error } = await service.generateConcurrencyCSV({ month, year });

    expect(csvData).toBe(
      'Rut beneficiario,% Concurrencias,concurr por cobrar,porcent mutual,porcent ist,porcent isl\n17176146-8,1.15,10.15,11.15,12.15,13.15'
    );
    expect(error).toBeUndefined();
  });
  it('should fail searching concurrencies', async () => {
    jest.spyOn(ConcurrentModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });

    const { csvData, error } = await service.generateConcurrencyCSV({ year: 2000, month: 1 });

    expect(csvData).toBe('');
    expect(error).toBeDefined();
  });

  afterEach(() => {
    FactorModel.deleteMany({});
    ConcurrentModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

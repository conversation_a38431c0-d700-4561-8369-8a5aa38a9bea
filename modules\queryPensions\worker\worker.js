const cronDescription = 'Información de transferencia de pension desde tablas temporales:';
const cronDependency = 'TRANSFER_PENSIONS';
const cronMark = 'CRON_TRANSFER_PENSIONER_INFO';
const successMessage = ` El proceso ${cronDescription} se completó correctamente`;
const alreadyExecutedMessage = `El proceso ${cronMark} fue ejecutado para el mes actual`;
const getMissingDependencyMessage = `No se ha ejecutado la dependencia ${cronDependency}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const workerFn = async ({ Logger, logService, pensionService, service, job, done }) => {
  try {
    Logger.info('transfer pensioner info: revisando ejecucion dependencia cron transfer pensions');
    if (!(await logService.existsLog(cronDependency))) {
      Logger.info('Fin proceso: Dependencia cron transfer pensions aun no ejecutado.');
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    const { error, pensionersToUpdate } = await service.transferPensionerData();

    if (error) throw new Error(error);

    const { error: errorAtUpdating } = await pensionService.createUpdatePension(pensionersToUpdate);

    if (errorAtUpdating) throw new Error(errorAtUpdating);

    await service.deleteTemporalData(Logger);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process completed`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, cronDependency, workerFn };

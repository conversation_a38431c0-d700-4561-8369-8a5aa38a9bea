const defaultService = {
  getPrimaryKeysFields() {
    const results = [];
    Object.entries(this.Model.prototype.schema.tree).forEach(entry => {
      const [key, value] = entry;
      if (value.primary) {
        results.push(key);
      }
    });

    return results;
  },
  async bulkUpdateOrCreateUnordered(arr) {
    const primaryKeys = this.getPrimaryKeysFields();
    const bulks = this.Model.collection.initializeUnorderedBulkOp();

    arr.forEach(elem => {
      const criteria = {};
      primaryKeys.forEach(pk => {
        criteria[pk] = elem[pk];
      });

      bulks
        .find(criteria)
        .upsert()
        .replaceOne({
          ...elem
        });
    });
    return bulks.execute();
  }
};

module.exports = defaultService;

const fsClient = require('fs');
const split = require('split');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const service = require('../modules/historicalData/services/historicalData.service');
const Logger = require('../lib/logger');
const FactoryController = require('../modules/historicalData/controllers/historicalData.controller');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const historicalDataController = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    fsClient,
    service,
    split
  });

  router.post('/bulk', [validateAccess()], historicalDataController.insertAll);
};

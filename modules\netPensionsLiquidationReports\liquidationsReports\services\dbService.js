const moment = require('moment');
const liquidationService = require('../../../liquidation/services/liquidation.service');
const pensionService = require('../../../pensions/services/pension.service');
const sumFields = require('../pipelines/pipeline');

const VALIDITY_TYPE = /^No\s+vigente$/i;

module.exports = {
  async createLiquidationsReports() {
    try {
      const { result: pensions } = await pensionService.getAllWithFilter(
        {
          validityType: { $not: VALIDITY_TYPE },
          enabled: true
        },
        {
          beneficiary: 1,
          causant: 1,
          liquidation: 1,
          article40: 1,
          article41: 1,
          assets: 1,
          basePension: 1,
          retroactiveAmounts: 1,
          discounts: 1,
          law19403: 1,
          law19539: 1,
          law19953: 1,
          pensionCodeId: 1
        }
      );
      const liquidations = pensions.map(pension => ({
        ...sumFields(pension).liquidation,
        beneficiaryRut: pension.beneficiary.rut,
        pensionCodeId: pension.pensionCodeId,
        causantRut: pension.causant.rut,
        liquidationMonth: moment().month() + 1,
        liquidationYear: moment().year()
      }));
      const { completed, error } = await liquidationService.createUpdateLiquidation(liquidations);
      return { completedLiquidationReport: completed, errorLiquidationReport: error };
    } catch (error) {
      return { completedCreationReport: false, errorLiquidationReport: error };
    }
  },
  async createLiquidationReport(pension) {
    const liquidation = {
      ...sumFields(pension).liquidation,
      beneficiaryRut: pension.beneficiary.rut,
      causantRut: pension.causant.rut,
      pensionCodeId: pension.pensionCodeId,
      liquidationMonth: moment().month() + 1,
      liquidationYear: moment().year()
    };
    const { error } = await liquidationService.createUpdateLiquidation([liquidation]);
    if (error) throw new Error(error);
  }
};

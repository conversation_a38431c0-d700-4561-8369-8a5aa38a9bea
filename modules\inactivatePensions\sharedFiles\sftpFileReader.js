const readSFTPFile = async ({
  sftpClient,
  connectToSFTPServer,
  sftpCredentials,
  filesHelper,
  dataType,
  tmp
}) => {
  let lines = [];
  let errorMessage = '';
  try {
    const remoteFilePath = filesHelper.getSFTPFileName();
    const { connected, error } = await connectToSFTPServer(sftpClient, sftpCredentials);

    if (!connected) return { lines, error };

    const stats = await sftpClient.stat(remoteFilePath).catch(() => {
      errorMessage = 'Archivo no encontrado';
    });
    if (errorMessage) throw new Error(errorMessage);
    if (!stats.size) return { lines };

    const outputFilePath = tmp.fileSync().name;
    await sftpClient.downloadTo(remoteFilePath, outputFilePath);
    lines = await filesHelper.readLines(outputFilePath, dataType);
    if (lines.length)
      lines = lines.map(([rut, marriageDate, country]) => [
        filesHelper.getFormatedRut(rut, country),
        filesHelper.getFormatedDate(marriageDate)
      ]);

    return { lines };
  } catch (error) {
    return { lines: [], error };
  } finally {
    sftpClient.close();
  }
};

module.exports = readSFTPFile;

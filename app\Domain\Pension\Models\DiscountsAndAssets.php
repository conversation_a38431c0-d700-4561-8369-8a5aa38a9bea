<?php

namespace App\Domain\Pension\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DiscountsAndAssets extends Model
{
    use HasFactory;

    protected $table = 'discounts_and_assets';

    protected $fillable = [
        'pension_code_id',
        'beneficiary_rut',
        'causant_rut',
        'assets_non_formulable',
        'discounts_non_formulable',
    ];

    protected $casts = [
        'assets_non_formulable' => 'array',
        'discounts_non_formulable' => 'array',
    ];

    // Relationships
    public function pension(): BelongsTo
    {
        return $this->belongsTo(Pension::class, 'pension_code_id', 'pension_code_id');
    }

    // Business Logic Methods
    public function getTotalAssetsNonFormulable(): float
    {
        $assets = $this->assets_non_formulable ?? [];
        return collect($assets)->sum('amount');
    }

    public function getTotalDiscountsNonFormulable(): float
    {
        $discounts = $this->discounts_non_formulable ?? [];
        return collect($discounts)->sum('amount');
    }

    public function getAssetsByType(string $type): array
    {
        $assets = $this->assets_non_formulable ?? [];
        return collect($assets)->where('asset_type', $type)->toArray();
    }

    public function getDiscountsByReason(string $reason): array
    {
        $discounts = $this->discounts_non_formulable ?? [];
        return collect($discounts)->where('reason', 'like', "%{$reason}%")->toArray();
    }

    public function hasJudicialRetentions(): bool
    {
        $discounts = $this->discounts_non_formulable ?? [];
        return collect($discounts)->contains(function ($discount) {
            return str_contains(strtolower($discount['reason'] ?? ''), 'retención judicial');
        });
    }
}

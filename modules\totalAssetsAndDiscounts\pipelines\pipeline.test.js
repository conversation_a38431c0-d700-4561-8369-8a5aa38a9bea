const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const pensionsData = require('../../../resources/pensionObjectForLiquidation.json');
const pipe = require('./pipeline');
const discountsAndAssets = require('../../../resources/discountsAndAssets.json');
const countUniqueKeyValue = require('./countUniqueKeyValue');

describe('Total assets and discounts pipeline', () => {
  beforeAll(beforeAllTests);

  it('should go through functions pipeline and calculate fields', async () => {
    const pension = { ...pensionsData, discountsAndAssets };
    const result = pipe(pension);
    const {
      discounts: { totalNonFormulable, nonFormulableByReason },
      assets: {
        netTotalNonFormulable,
        taxableTotalNonFormulable,
        netNonFormulableByReason,
        taxableNonFormulableByReason
      },
      numberOfNonFormulableDiscounts,
      numberOfNetNonFormulableAssets,
      numberOfTaxableNonFormulableAssets
    } = result;

    const [nonFormulableByReasonObj] = nonFormulableByReason;
    const { reason, amount } = nonFormulableByReasonObj;

    const [netNonFormulableByReasonObj] = netNonFormulableByReason;
    const { reason: netReason, amount: netAmount } = netNonFormulableByReasonObj;

    const [taxableNonFormulableByReasonObj] = taxableNonFormulableByReason;
    const { reason: taxableReason, amount: taxableAmount } = taxableNonFormulableByReasonObj;

    expect(taxableReason).toBe('comida');
    expect(taxableAmount).toBe(10);

    expect(netReason).toBe('movilizacion');
    expect(netAmount).toBe(20);

    expect(reason).toBe('comida');
    expect(amount).toBe(30);

    expect(totalNonFormulable).toBe(60);
    expect(netTotalNonFormulable).toBe(60);
    expect(taxableTotalNonFormulable).toBe(40);
    expect(numberOfNonFormulableDiscounts).toBe(2);
    expect(numberOfNetNonFormulableAssets).toBe(2);
    expect(numberOfTaxableNonFormulableAssets).toBe(2);
  });

  it('Should initialize assets and discounsts as an empty array', () => {
    const pension = { ...pensionsData, discountsAndAssets: {} };
    const result = pipe(pension);
    const {
      discounts: { totalNonFormulable, nonFormulableByReason },
      assets: {
        netTotalNonFormulable,
        taxableTotalNonFormulable,
        netNonFormulableByReason,
        taxableNonFormulableByReason
      },
      numberOfNonFormulableDiscounts,
      numberOfNetNonFormulableAssets,
      numberOfTaxableNonFormulableAssets
    } = result;
    expect(nonFormulableByReason.length).toBe(0);
    expect(netNonFormulableByReason.length).toBe(0);
    expect(taxableNonFormulableByReason.length).toBe(0);
    expect(totalNonFormulable).toBe(0);
    expect(netTotalNonFormulable).toBe(0);
    expect(taxableTotalNonFormulable).toBe(0);
    expect(numberOfNonFormulableDiscounts).toBe(0);
    expect(numberOfNetNonFormulableAssets).toBe(0);
    expect(numberOfTaxableNonFormulableAssets).toBe(0);
  });

  it('Should return 0 if taxableAssets is not defined or empty', () => {
    const result1 = countUniqueKeyValue(undefined);
    const result2 = countUniqueKeyValue([]);
    expect(result1).toBe(0);
    expect(result2).toBe(0);
  });

  afterAll(afterAllTests);
});

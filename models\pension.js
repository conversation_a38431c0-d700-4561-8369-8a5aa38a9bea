const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');
const { basePensionValidator, isNumeric } = require('../modules/linkPensions/validators');

const { Schema } = mongoose;

const totalsByReasonSchema = new Schema({
  amount: { type: Number, default: 0 },
  reason: { type: String, default: '', lowercase: true }
});

const PensionSchema = new Schema(
  {
    discountsAndAssets: { type: mongoose.Schema.Types.ObjectId, ref: 'DiscountsAndAsset' },
    basePension: { type: Number, min: 10000.0, max: ********.99, required: true },
    country: { type: String },
    transient: { type: String },
    cun: { type: String },
    initialBasePension: {
      type: Number,
      min: 10000.0,
      max: ********.99,
      required: true,
      validate: basePensionValidator
    },
    dateOfBirth: { type: Date, required: true },
    gender: { type: String, enum: ['M', 'F'], required: true, trim: true },
    afpAffiliation: { type: String },
    healthAffiliation: { type: String },
    linkedDate: { type: Date },
    bankRejected: { type: String, default: 'No' },
    paycheckRefunded: { type: String, default: 'No' },
    manuallyReactivated: { type: Boolean, default: false },
    paymentInfo: {
      paymentGateway: { type: String, maxlength: 60 },
      accountNumber: { type: String, maxlength: 30 },
      bank: { type: String, maxlength: 60 },
      branchOffice: { type: String, maxlength: 80 }
    },
    retirementAge: { type: Number, min: 18, max: 120 },
    causant: {
      rut: { type: String, maxlength: 12, required: true },
      name: { type: String, maxlength: 60 },
      lastName: { type: String, maxlength: 60 },
      mothersLastName: { type: String, maxlength: 60 }
    },
    collector: {
      rut: { type: String, maxlength: 12, required: true },
      name: { type: String, maxlength: 60 },
      lastName: { type: String, maxlength: 60 },
      mothersLastName: { type: String, maxlength: 60 },
      address: { type: String, maxlength: 1000 },
      commune: { type: String, maxlength: 1000 },
      city: { type: String, maxlength: 1000 }
    },
    beneficiary: {
      rut: { type: String, maxlength: 12, required: true },
      name: { type: String, maxlength: 60 },
      lastName: { type: String, maxlength: 60 },
      mothersLastName: { type: String, maxlength: 60 },
      email: { type: String, maxlength: 120 },
      phone: { type: String, maxlength: 20 }
    },
    capital: { type: Number, min: 0, max: ******** },
    currentCapital: { type: Number, min: 0, max: ******** },
    validityType: { type: String, maxlength: 160 },
    pensionType: { type: String, required: true },
    ChangeOfPensionTypeDueToCharges: { type: Boolean, default: false, required: true },
    disabilityDegree: { type: Number, min: 0, max: 999, required: true },
    disabilityType: { type: String, maxlength: 20, trim: true },
    resolutionNumber: { type: Number, min: 0, required: true },
    accidentNumber: { type: Number, min: 0, required: true },
    numberOfCharges: { type: Number, min: 0, default: 0 },
    numberOfNonFormulableDiscounts: { type: Number, min: 0, default: 0 },
    numberOfNetNonFormulableAssets: { type: Number, min: 0, default: 0 },
    numberOfTaxableNonFormulableAssets: { type: Number, min: 0, default: 0 },
    resolutionDate: { type: Date, required: true },
    disabilityStartDate: { type: Date, required: true },
    accidentDate: { type: Date, required: true },
    pensionCodeId: { type: String, required: true },
    institutionalPatient: { type: Boolean, default: false },
    validatedStudyPeriod: { type: String, maxlength: 160 },
    inactivationReason: { type: String },
    article40: { type: Number, required: true },
    article41: { type: Number, default: 0 },
    inactivationDate: { type: Date },
    marriageDate: { type: Date },
    pensionStartDate: { type: Date, required: true, validate: v => v instanceof Date },
    deathDate: { type: Date },
    endDateOfValidity: { type: Date },
    previousEndDateOfValidity: { type: Date },
    endDateOfTheoricalValidity: { type: Date },
    evaluationDate: { type: Date },
    reactivationDate: { type: Date },
    fixedBasePension: { type: Number, min: 0, default: 0 },
    fixedArticle40: { type: Number, min: 0, default: 0 },
    fixedArticle41: { type: Number, min: 0, default: 0 },
    daysToPay: { type: Number, min: 0, default: 0 },
    inactivateManually: { type: Boolean, default: false },
    familyGroup: { type: Number, required: true, default: 1 },
    increasingInLaw19578: { type: Number, required: true, default: 0 },
    increasingInLaw19953: { type: Number, required: true, default: 0 },
    increasingInLaw20102: { type: Number, required: true, default: 0 },
    law19403: { type: Number, default: 0 },
    law19539: { type: Number, default: 0 },
    law19953: { type: Number, default: 0 },
    payBonus: { type: String, default: 'SI' },
    basePensionWithoutIncreases: {
      type: Number,
      required: true,
      default: 0
    },
    heavyDuty: { type: String, minlength: 0, trim: true },
    parentRUT: { type: String, minlength: 0, maxlength: 12 },
    reservedAmounts: {
      assetsNonFormulableTaxableTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      assetsNonFormulableNetTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      discountsNonFormulableTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      forSurvival: { type: Number, min: 0, default: 0 },
      forDisability: { type: Number, min: 0, default: 0 },
      forInstitutionalPatient: { type: Number, min: 0, default: 0 },
      forRejection: { type: Number, min: 0, default: 0 },
      forBasePension: { type: Number, min: 0, default: 0 },
      forArticle40: { type: Number, min: 0, default: 0 },
      forArticle41: { type: Number, min: 0, default: 0, validate: isNumeric },
      forTaxableTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forTotalNonFormulableDiscounts: { type: Number, min: 0, default: 0 },
      forBonuses: { type: Number, min: 0, default: 0 },
      forPayCheck: { type: Number, default: 0 }
    },
    assets: {
      aps: { type: Number, min: 0, default: 0 },
      healthDiscount: { type: String, default: 'No' },
      healthExemption: { type: String, default: 'No' },
      forFamilyAssignment: { type: Number, min: 0, default: 0 },
      marriageBonus: { type: Number, min: 0, default: 0 },
      christmasBonus: { type: Number, min: 0, default: 0 },
      nationalHolidaysBonus: { type: Number, min: 0, default: 0 },
      winterBonus: { type: Number, min: 0, default: 0 },
      nonFormulable: { type: Number, min: 0, default: 0 },
      rebsal: { type: Number, min: 0, default: 0 },
      adjustedHealthExemption: { type: Number, min: 0, default: 0 },
      netTotalNonFormulable: { type: Number, min: 0, default: 0 },
      taxableTotalNonFormulable: { type: Number, min: 0, default: 0 },
      netNonFormulableByReason: [{ reason: { type: String, lowercase: true }, amount: Number }],
      taxableNonFormulableByReason: [{ reason: { type: String, lowercase: true }, amount: Number }]
    },
    retroactiveAmounts: {
      forSurvival: { type: Number, min: 0, default: 0 },
      forDisability: { type: Number, min: 0, default: 0 },
      forInstitutionalPatient: { type: Number, min: 0, default: 0 },
      forRejection: { type: Number, min: 0, default: 0 },
      forBonuses: { type: Number, min: 0, default: 0 },
      forTaxableTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forTotalNonFormulableDiscounts: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssetsByReason: {
        type: [totalsByReasonSchema],
        default: []
      },
      forTaxableTotalNonFormulableAssetsByReason: {
        type: [totalsByReasonSchema],
        default: []
      },
      forTotalNonFormulableDiscountsByReason: {
        type: [totalsByReasonSchema],
        default: []
      },
      forBasePension: { type: Number, min: 0, default: 0 },
      forArticle40: { type: Number, min: 0, default: 0 },
      forArticle41: { type: Number, min: 0, default: 0 },
      forFamilyAssignment: { type: Number, min: 0, default: 0 },
      forPayCheck: { type: Number, default: 0 }
    },
    discounts: {
      type: Schema.Types.Mixed,
      default: {
        onePercentLaAraucana: 'No',
        socialCreditsLaAraucana: 0,
        onePercent18: 'No',
        socialCredits18: 0,
        onePercentLosAndes: 'No',
        socialCreditsLosAndes: 0,
        othersLosAndes: 0,
        onePercentLosHeroes: 'No',
        socialCreditsLosHeroes: 0,
        othersLosHeroes: 0,
        healthLoan: 0,
        health: 0,
        afp: 0,
        onePercentAdjusted: 0,
        totalNonFormulable: 0,
        nonFormulableByReason: [{}]
      },
      healthUF: {
        type: Number,
        min: 0,
        required: true
      }
    },
    currentCapitalKeys: { type: Object },
    currentCapitalCalculation: {
      basePensionCapital: { type: Number },
      capitalLaw19578: { type: Number },
      capitalLaw19953: { type: Number },
      capitalLaw20102: { type: Number },
      capitalBonusLaw19403: { type: Number },
      capitalBonusLaw19953: { type: Number },
      capitalBonusLaw19539: { type: Number },
      basePensionWorkingCapital: { type: Number },
      basePensionNotWorkingCapital: { type: Number },
      workingCapitalLaw19578: { type: Number },
      notWorkingCapitalLaw19578: { type: Number },
      workingCapitalLaw19953: { type: Number },
      notWorkingCapitalLaw19953: { type: Number },
      workingCapitalLaw20102: { type: Number },
      notWorkingCapitalLaw20102: { type: Number },
      workingCapitalBonusLaw19403: { type: Number },
      notWorkingCapitalBonusLaw19403: { type: Number },
      workingCapitalBonusLaw19953: { type: Number },
      notWorkingCapitalBonusLaw19953: { type: Number },
      workingCapitalBonusLaw19539: { type: Number },
      notWorkingCapitalBonusLaw19539: { type: Number },
      workingCapitalBonus: { type: Number },
      notWorkingCapitalBonus: { type: Number },
      capitalPBIpc: { type: Number },
      capitalLaw19578Ipc: { type: Number },
      capitalLaw19953Ipc: { type: Number },
      capitalLaw20102Ipc: { type: Number },
      capitalBonusLaw19403Ipc: { type: Number },
      capitalBonusLaw19539Ipc: { type: Number },
      capitalBonusLaw19953Ipc: { type: Number },
      totalCapital: { type: Number },
      totalCapitalIpc: { type: Number },
      capitalTotalBonus: { type: Number },
      capitalTotalBonusIpc: { type: Number }
    },
    totalEstimatedDaysToPay: { type: Number },
    totalTransitoryDaysToPay: { type: Number },
    checkPoint: { type: Boolean, default: false },
    capitalStatus: { type: String, default: 'Nuevo' },
    totalCapital: { type: Number, default: 0 },
    apsInfo: {
      apsResolutionNumber: { type: String, default: '' },
      apsResolutionDate: { type: String, default: '' },
      apsPaymentUniqueId: { type: String, default: '' },
      apsTransferCode: { type: String, default: '' },
      apsOrigin: { type: String, default: '' }
    },
    rejectionHealthExemptionAmount: { type: Number, default: 0 },
    rejectionHealthReductionAmount: { type: Number, default: 0 },
    rejectionIPS: { type: Boolean, default: false },
    enabled: {
      type: Boolean,
      default: true
    },
    oldAgePensionInProcess: { type: Number, default: 0 },
    maritalStatus: { type: String, maxlength: 1, default: 'S' },
    otherPension: { type: String, maxlength: 2, default: 'No' },
    regimenOtherPension: { type: Number, default: 0 },
    startAnotherPension: { type: Date },
    amountOtherPension: { type: Number, default: 0 },
    baseIncome: { type: Number, default: 0 },
    retroactiveConstitution: {
      totalPensionAccrued: { type: Number, min: 0, default: 0 },
      indemnityDiscount: { type: Number, min: 0, default: 0 },
      strennaRetroConstitution: { type: Number, min: 0, default: 0 },
      otherLink: { type: Number, min: 0, default: 0 },
      settlement: { type: Number, min: 0, default: 0 },
      healthDiscountAccrued: { type: Number, min: 0, default: 0 },
      afpDiscountAccrued: { type: Number, min: 0, default: 0 }
    },
    dl1026: { type: Number, default: 0 },
    retirement: { type: Boolean, default: false },
    numberOfChargesExternal: { type: Number, min: 0, default: 0 },
    numberOfChargesArticle41: { type: Number, min: 0, default: 0 }
  },
  { timestamps: true }
);

PensionSchema.plugin(paginate);
PensionSchema.index({ beneficiary: 1 });
PensionSchema.index({ causant: 1 });
PensionSchema.index({ pensionType: 1, enabled: 1, validityType: 1 });
PensionSchema.index({ 'beneficiary.rut': 1, 'causant.rut': 1, enabled: 1, deathDate: 1 });
PensionSchema.index({ pensionCodeId: 1 });

module.exports = mongoose.model('Pension', PensionSchema);

const {
  BUSINESS_DAYS_TO_PAY_WORKER,
  BUSINESS_DAY_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS,
  BUSINESS_DAY_NUMBER_FOR_ASSETS_DISCOUNTS_RETROACTIVE_CRON_EXECUTION,
  TIME_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS
} = process.env;

const scheduleCronJobList = [
  {
    name: 'calculateDaysToPayWorker',
    businessDaysToExecute: BUSINESS_DAYS_TO_PAY_WORKER,
    fileMarks: ['CALCULATE_DAYS_TO_PAY_WORKER']
  },
  {
    name: 'inactivateOrReactivateFamilyAssignmentProcess',
    businessDaysToExecute: BUSINESS_DAY_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS,
    timeToExecute: TIME_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS,
    fileMarks: ['INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNEMENT_PROCESS']
  },
  {
    name: 'calculateTotalAssetsDiscountsAndRetroactiveAmounts',
    businessDaysToExecute: BUSINESS_DAY_NUMBER_FOR_ASSETS_DISCOUNTS_RETROACTIVE_CRON_EXECUTION,
    fileMarks: ['UNIFIED_RETROACTIVE_AMOUNT_CALCULATION']
  }
];

const jobFields = {
  data: null,
  type: 'normal',
  priority: 0,
  lastModifiedBy: null,
  lockedAt: null,
  lastFinishedAt: null,
  lastRunAt: null
};

module.exports = { scheduleCronJobList, jobFields };

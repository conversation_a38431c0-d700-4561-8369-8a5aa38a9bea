/* eslint-disable func-names */
const mongoose = require('mongoose');
const {
  previousSave,
  trakingPostSave
} = require('../../trackingUserActivity/services/trackingUserActivity.service');

const ACTION_CREATE = 'ejecutar proceso';
const MODEL_NAME = 'processedjobs';

const { Schema } = mongoose;

const processedJobsSchema = new Schema(
  {
    alreadyProcessed: { type: Boolean, default: true },
    year: { type: Number, required: true },
    month: { type: Number, required: true },
    name: { type: String, required: true },
    attempts: { type: Number, default: 0 },
    status: { type: String, enum: ['OK', 'WARNING', 'ERROR'], default: 'WARNING' }
  },
  { timestamps: true }
);

processedJobsSchema.pre('save', previousSave);
processedJobsSchema.post('save', async function(doc) {
  return trakingPostSave(doc, MODEL_NAME)(ACTION_CREATE);
});

processedJobsSchema.index({ year: 1, month: 1, name: 1 }, { unique: true });
processedJobsSchema.index({ createdAt: 1 }, { expires: '90d' });

module.exports = mongoose.model('processedJobs', processedJobsSchema);

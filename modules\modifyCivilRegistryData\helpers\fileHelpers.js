const moment = require('moment');

const { readLines } = require('./fileReader');
const { readSFTPFile } = require('./sftpFileReader');

const getCivilRegistryFileName = FOLDER_PATH =>
  `${FOLDER_PATH}/Achs_${moment().format('YYYYMM')}_out.txt`;

const checkRemoteFileExistence = async (client, remoteFilePath) => {
  try {
    const exist = await client.exists(remoteFilePath);
    return !!exist;
  } catch (error) {
    throw new Error(error);
  }
};

const downloadCivilRegistryFileFromSFTP = async ({
  sftpClient,
  connectToSFTPServer,
  sftpCredentials,
  FOLDER_PATH,
  tmp,
  fileHelpers
}) => {
  try {
    const { connected, error } = await connectToSFTPServer(sftpClient, { ...sftpCredentials });
    if (!connected) return { file: null, error };

    const remoteFilePath = fileHelpers.getCivilRegistryFileName(FOLDER_PATH);
    const filesExist = await fileHelpers.checkRemoteFileExistence(sftpClient, remoteFilePath);
    if (!filesExist) return { file: null, error: null };

    const outputFilePath = tmp.fileSync().name;
    await sftpClient.downloadTo(remoteFilePath, outputFilePath);

    return { file: outputFilePath };
  } catch (error) {
    return { file: null, error };
  } finally {
    sftpClient.close();
  }
};

module.exports = {
  getCivilRegistryFileName,
  checkRemoteFileExistence,
  downloadCivilRegistryFileFromSFTP,
  readLines,
  readSFTPFile
};

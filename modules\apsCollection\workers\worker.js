const alreadyExecutedMessage = 'Este proceso ya fue realizado el mes actual';
const cronMark = 'CRON_GENERATE_AND_UPLOAD_APS_COLLECTION_FILE';
const successMessage = `Proceso ${cronMark}: completado correctamente`;
const dependencyMark = 'UNIFIED_BULKLOAD_AND_IPS';
const missingDepsMsg = `No se ha ejecutado la dependencia ${dependencyMark}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, logService, service, fileService, done, job }) => {
  try {
    Logger.info(`${cronMark}: Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: Checking if dependency ${dependencyMark} was previously executed...`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`${cronMark}:${missingDepsMsg}`);
      return { message: missingDepsMsg, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: Getting patpr.ips.703601006 zip file from SFTP...`);
    const extractedAIIItapsFilePath = await fileService.downloadAndExtractZipFile({});
    Logger.info(`${cronMark}: Zip File extracted succesfully to ${extractedAIIItapsFilePath}.`);

    Logger.info(`${cronMark}: Reading extractedFile ${extractedAIIItapsFilePath}...`);
    const fileLines = await fileService.readFile(extractedAIIItapsFilePath);
    Logger.info(`${cronMark}: File  ${extractedAIIItapsFilePath} read succesfully.`);

    Logger.info(`${cronMark}:Generating dpaps File From read File ${extractedAIIItapsFilePath}`);
    const rutToLineMapperObj = fileService.mapRutToLine(fileLines);
    const dpapsFileData = await service.getMatchedPensionLines(rutToLineMapperObj);
    const { filePath, destinationPath } = fileService.getPathForCompressFile();
    await fileService.appendFileAsync(filePath, dpapsFileData.join('\n'));
    fileService.compressFile(filePath, destinationPath);
    Logger.info(`${cronMark}: dpaps File generated succesfully at ${filePath}`);

    Logger.info(`${cronMark}: Uploading dpaps File To SFTP server...`);
    await fileService.uploadDpasFileToSftpServer({ fileToUpload: destinationPath, Logger });
    Logger.info(`${cronMark}: File uploaded succesfully To SFTP server.`);

    Logger.info(`${cronMark}: Generating ppr.703601006.zip with rctaps and dpeaps files...`);
    const unmatchedData = await service.getUnmatchedPensionLines(rutToLineMapperObj);
    const { dpeapsFileData, rctapsFileData } = unmatchedData;
    const fileInfo = await fileService.generatePprZipFile(unmatchedData);
    Logger.info(`${cronMark}: ppr.703601006.zip created succesfully at ${fileInfo.pprZipFilePath}`);

    Logger.info(`${cronMark}: Uploading ppr.703601006.zip to SFTP server`);
    await fileService.uploadPprFileToSftpServer({ fileInfo });

    await logService.saveLog(cronMark);
    Logger.info(`Fin procesamiento`);
    return {
      executionCompleted: true,
      message: successMessage,
      dpapsFileData,
      dpeapsFileData,
      rctapsFileData
    };
  } catch (error) {
    Logger.error(`${cronMark} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronMark} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

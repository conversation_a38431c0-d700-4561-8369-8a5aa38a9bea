const { filterPensionsTypesChange } = require('../../orphanhood/services/orphanhood.service');

const PENSION_TYPES = [
  /Pensi[óo]n de viudez con hijos/i,
  /Pensi[óo]n de viudez sin hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i
];

const PENSION_TYPES_WITHOUT_CHILD_MAPPPER = [
  { regex: /Pensi[óo]n de viudez con hijos/i, value: 'Pensión de viudez sin hijos' },
  {
    regex: /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i,
    value: 'Pensión de madre de hijo de filiación no matrimonial sin hijos'
  }
];
const PENSION_TYPES_WITH_CHILD_MAPPPER = [
  { regex: /Pensi[óo]n de viudez sin hijos/i, value: 'Pensión de viudez con hijos' },
  {
    regex: /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i,
    value: 'Pensión de madre de hijo de filiación no matrimonial con hijos'
  }
];
const findInRegex = (array, toSearch) => {
  const parsedValue = toSearch.trim();
  const result = array.find(({ regex }) => regex.test(parsedValue));
  return result ? result.value : parsedValue;
};

const setHourZero = dateToSet => {
  try {
    dateToSet.setHours(0);
    dateToSet.setMinutes(0);
    dateToSet.setSeconds(0);
    dateToSet.setMilliseconds(0);
    return dateToSet;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
    return dateToSet;
  }
};

const majorToLastDayPreviousMonth = endDateOfTheoricalValidity => {
  const currentDate = new Date();
  currentDate.setDate(0);
  const lastDayPreviousMonth = setHourZero(currentDate);
  const endDateOfTheorical = setHourZero(endDateOfTheoricalValidity);
  return endDateOfTheorical > lastDayPreviousMonth;
};

const minorThanLastDayOfMonth = endDateOfTheoricalValidity => {
  const currentDate = new Date();
  const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
  const lastDayMonth = setHourZero(lastDay);
  const endDateOfTheorical = setHourZero(endDateOfTheoricalValidity);
  return endDateOfTheorical <= lastDayMonth;
};

const getHorphanhood = (temporaryhorphanhoodsMadre, pensionsHorphanhood) => {
  const horphanhoodPensions = [];

  temporaryhorphanhoodsMadre.forEach(reg => {
    const { beneficiaryRut, causantRut, pensionId, validity } = reg;

    const pensionHorphanhoodAux = pensionsHorphanhood.filter(item => item.pension);
    const pensionHorphanhood = pensionHorphanhoodAux.find(
      item =>
        item.pension.beneficiary.rut === beneficiaryRut &&
        item.pension.causant.rut === causantRut &&
        item.pension.pensionCodeId === pensionId.toString()
    );

    if (pensionHorphanhood) {
      horphanhoodPensions.push({
        beneficiaryRut: pensionHorphanhood.pension.beneficiary.rut,
        causantRut: pensionHorphanhood.pension.causant.rut,
        pensionCodeId: pensionHorphanhood.pension.pensionCodeId,
        validity,
        endDateOfTheoricalValidity: pensionHorphanhood.pension.endDateOfTheoricalValidity,
        inactivationReason: pensionHorphanhood.pension.inactivationReason
      });
    }
  });

  return horphanhoodPensions;
};

const setPensionsTypesChange = (pension, horphanhood) => {
  const { pensionType, pensionCodeId, beneficiary, causant, enabled } = pension;

  if (
    horphanhood.find(
      row =>
        row.validity === true ||
        (row.validity === false &&
          majorToLastDayPreviousMonth(row.endDateOfTheoricalValidity) &&
          !/Fallecimiento/i.test(row.inactivationReason))
    )
  ) {
    return {
      beneficiary,
      causant,
      pensionCodeId,
      pensionType: findInRegex(PENSION_TYPES_WITH_CHILD_MAPPPER, pensionType),
      ChangeOfPensionTypeDueToCharges: true,
      enabled
    };
  }
  if (
    horphanhood.find(
      row =>
        minorThanLastDayOfMonth(row.endDateOfTheoricalValidity) ||
        (row.validity === false && /Fallecimiento/i.test(row.inactivationReason))
    )
  ) {
    return {
      beneficiary,
      causant,
      pensionCodeId,
      pensionType: findInRegex(PENSION_TYPES_WITHOUT_CHILD_MAPPPER, pensionType),
      enabled
    };
  }

  return {
    beneficiary,
    causant,
    pensionCodeId,
    pensionType,
    enabled
  };
};

const pensionTypesToChange = async pensionService => {
  try {
    const pensionsHorphanhoodMother = await pensionService.filterPensionsHorphanhoodMother(
      PENSION_TYPES
    );
    const pensionHorphanhood = await filterPensionsTypesChange();

    const setPensionsTypeToChange = pensionsHorphanhoodMother.map(pension => {
      const {
        temporaryhorphanhoodsMadre,
        beneficiary,
        causant,
        pensionCodeId,
        pensionType,
        enabled
      } = pension;

      if (!temporaryhorphanhoodsMadre.length) {
        return {
          beneficiary,
          causant,
          pensionCodeId,
          pensionType,
          enabled
        };
      }

      const horphanhood = getHorphanhood(temporaryhorphanhoodsMadre, pensionHorphanhood);

      return setPensionsTypesChange(pension, horphanhood);
    });

    const { completed, error } = await pensionService.updatePensions(setPensionsTypeToChange);
    return { completed, error };
  } catch (error) {
    return { completed: false, error };
  }
};

module.exports = { setPensionsTypesChange, pensionTypesToChange };

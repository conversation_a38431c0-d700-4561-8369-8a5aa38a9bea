const { check } = require('express-validator');
const { textFieldRegRule } = require('./validator');

const validators = [
  check('motive.motive')
    .notEmpty()
    .withMessage('motive should not be empty')
    .isLength({ min: 1, max: 100 })
    .withMessage('max length should be 100')
    .matches(textFieldRegRule)
    .withMessage('motive should match the format'),
  check('motive.option')
    .notEmpty()
    .withMessage('option should not be empty')
    .isLength({ min: 5, max: 9 })
    .withMessage('option max length should be 9')
    .matches(value => value.toLowercase() === 'descuento' || value.toLowercase() === 'haber')
    .withMessage('option should match the format')
];

module.exports = validators;

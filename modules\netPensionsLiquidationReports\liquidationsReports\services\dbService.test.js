/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const PensionModel = require('../../../../models/pension');
const LiquidationModel = require('../../../../models/liquidation');
const pensionData = require('../../../../resources/pensionObjectForLiquidation.json');
const service = require('./dbService');

describe('Liquidation reports service', () => {
  beforeAll(beforeAllTests);
  it('should get pensions and create liquidation reports', async () => {
    await PensionModel.create(pensionData).catch(e => console.error(e));
    const { completedLiquidationReport } = await service.createLiquidationsReports();
    expect(completedLiquidationReport).toBe(true);
    const liquidation = await LiquidationModel.findOne({
      liquidationYear: new Date().getFullYear()
    }).catch(e => console.log(e));
    const {
      totalAssets,
      totalOnePercentDiscounts,
      totalSocialCreditDiscounts,
      totalDiscounts,
      numberOfAssets,
      numberOfDiscounts
    } = liquidation;
    expect(totalAssets).toBeDefined();
    expect(totalOnePercentDiscounts).toBeDefined();
    expect(totalSocialCreditDiscounts).toBeDefined();
    expect(totalDiscounts).toBeDefined();
    expect(numberOfAssets).toBeDefined();
    expect(numberOfDiscounts).toBeDefined();
  });

  afterAll(afterAllTests);
});

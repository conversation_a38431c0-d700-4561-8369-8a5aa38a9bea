/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const { textFieldRegRule } = require('./validator');

const matchRule = (value = '') => textFieldRegRule.test(value);
describe('Banks validators service Test', () => {
  beforeAll(beforeAllTests);

  it('should allow', () => {
    const rule1 = '10000jjjj';
    const rule2 = '00000111202020djfjg  fjhgfjgjgjgjgjgj';
    const rule3 = 'Hola Cómo estás';
    const rule4 = null;
    const rule5 = undefined;
    expect(matchRule(rule1)).toBe(true);
    expect(matchRule(rule2)).toBe(false);
    expect(matchRule(rule3)).toBe(true);
    expect(matchRule(rule4)).toBe(true);
    expect(matchRule(rule5)).toBe(false);
  });

  afterEach(async () => {});

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const rulesValidator = require('../../../../models/basePensionRules');
const rule1 = require('./rule1');
const rule2 = require('./rule2');
const rule3 = require('./rule3');
const data = require('../../../../resources/pensions.json');

const createCollection = name => mongoose.connection.db.createCollection(name, rulesValidator);
const clone = d => JSON.parse(JSON.stringify(d));
describe('Rules Test', () => {
  beforeAll(beforeAllTests);

  it('should execute basic rule 1', async () => {
    const basePensionModel = await createCollection('basePensionRules');
    const pensionsData = clone(data);
    const workAccident = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión por accidente de trabajo',
      age,
      type: 'pension',
      value: { minimun: '1000.56', law19403: null, law19539: null, law19953: null }
    }));

    const orphandRule = {
      label: 'Pensión por orfandad',
      age: null,
      type: 'pension',
      value: { minimun: '1000.68', law19403: null, law19539: null, law19953: null }
    };

    await basePensionModel.insertMany([orphandRule, ...workAccident]);

    pensionsData[0].pensionType = 'Pensión por orfandad';
    pensionsData[0].basePension = 10;

    pensionsData[1].pensionType = 'Pensión por accidente de trabajo';
    pensionsData[1].basePension = 100;

    pensionsData[2].pensionType = 'Pensión por accidente de trabajo';
    pensionsData[2].dateOfBirth = moment('1950-01-01', 'YYYY-MM-DD');
    pensionsData[2].basePension = 100;

    const [pensionOrphand] = await rule1(pensionsData[0]);
    const [pensionWorkAccident] = await rule1(pensionsData[1]);
    const [pensionWorkAccident2] = await rule1(pensionsData[2]);

    expect(pensionOrphand).toBeDefined();
    expect(pensionWorkAccident).toBeDefined();
    expect(pensionWorkAccident2).toBeDefined();
    expect(pensionOrphand.basePension).toBe(21951.19);
    expect(pensionWorkAccident.basePension).toBe(146341.28);
    expect(pensionWorkAccident2.basePension).toBe(160012.95);
  });

  it('should execute rule 2 cause age < 75', async () => {
    const basePensionModel = await createCollection('basePensionRules');
    const pensionsData = clone(data);
    const widowhoodWithChildrens = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez con hijos',
      age,
      type: 'pension',
      value: { minimun: '100.10', law19403: '1.00', law19539: null, law19953: null }
    }));

    const childlessWidowhood = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez sin hijos',
      age,
      type: 'pension',
      value: { minimun: '10.10', law19403: '50.50', law19539: '150.15', law19953: null }
    }));

    await basePensionModel.insertMany([...widowhoodWithChildrens, ...childlessWidowhood]);

    /**
     * pension -> a1
     *  age < 75
     *  basePension < law19539
     *  basePension > law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].dateOfBirth = moment('1970-01-01', 'YYYY-MM-DD');
    pensionsData[0].basePension = 100;
    const [a1] = await rule2(pensionsData[0]);

    expect(a1).toBeDefined();
    expect(a1.basePension).toBe(94965.97);
    expect(a1.law19403).toBe(16727.34);
    expect(a1.law19539).toBe(34648.03);
    expect(a1.law19953).toBe(0);

    /**
     * pension -> a2
     *  age < 75
     *  basePension < law19539
     *  minimun <= basePension < law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 20;
    const [a2] = await rule2(pensionsData[0]);

    expect(a2).toBeDefined();
    expect(a2.basePension).toBe(94965.97);
    expect(a2.law19403).toBe(16727.34);
    expect(a2.law19539).toBe(34648.03);
    expect(a2.law19953).toBe(0);

    /**
     * pension -> a3
     *  age < 75
     *  basePension < law19539
     *  basePension < minimun
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 5;
    const [a3] = await rule2(pensionsData[0]);

    expect(a3).toBeDefined();
    expect(a3.basePension).toBe(94965.97);
    expect(a3.law19403).toBe(16727.34);
    expect(a3.law19539).toBe(34648.03);
    expect(a3.law19953).toBe(0);

    /**
     * pension -> a
     *  age < 75
     *  basePension > law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 200;
    const [a] = await rule2(pensionsData[0]);

    expect(a).toBeDefined();
    expect(a.basePension).toBe(94965.97);
    expect(a.law19403).toBe(16727.34);
    expect(a.law19539).toBe(34648.03);
    expect(a.law19953).toBe(0);
  });

  it('should execute rule 3 cause age >= 75', async () => {
    const basePensionModel = await createCollection('basePensionRules');
    const pensionsData = clone(data);
    const widowhoodWithChildrens = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez con hijos',
      age,
      type: 'pension',
      value: { minimun: '100.00', law19403: '1.00', law19539: null, law19953: null }
    }));

    const childlessWidowhood = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez sin hijos',
      age,
      type: 'pension',
      value: { minimun: '10.10', law19403: '50.50', law19539: '150.00', law19953: '250.00' }
    }));

    await basePensionModel.insertMany([...widowhoodWithChildrens, ...childlessWidowhood]);

    /**
     * pension -> b1
     *  age > 75
     *  basePension >= law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].dateOfBirth = moment('1935-01-01', 'YYYY-MM-DD');
    pensionsData[0].basePension = 150;
    const [b1] = await rule3(pensionsData[0]);

    expect(b1).toBeDefined();
    expect(b1.basePension).toBe(118493.82);
    expect(b1.law19403).toBe(16635.74);
    expect(b1.law19539).toBe(24883.37);
    expect(b1.law19953).toBe(10715.48);

    /**
     * pension -> b2
     *  age > 75
     *  law19403 <= basePension < law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 100;
    const [b2] = await rule3(pensionsData[0]);

    expect(b2).toBeDefined();
    expect(b2.basePension).toBe(118493.82);
    expect(b2.law19403).toBe(16635.74);
    expect(b2.law19539).toBe(24883.37);
    expect(b2.law19953).toBe(10715.48);

    /**
     * pension -> b3
     *  age > 75
     *  minimun <= basePension < law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 20;
    const [b3] = await rule3(pensionsData[0]);

    expect(b3).toBeDefined();
    expect(b3.basePension).toBe(118493.82);
    expect(b3.law19403).toBe(16635.74);
    expect(b3.law19539).toBe(24883.37);
    expect(b3.law19953).toBe(10715.48);

    /**
     * pension -> b4
     *  age > 75
     *  basePension < minimun
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 5;
    const [b4] = await rule3(pensionsData[0]);

    expect(b4).toBeDefined();
    expect(b4.basePension).toBe(118493.82);
    expect(b4.law19403).toBe(16635.74);
    expect(b4.law19539).toBe(24883.37);
    expect(b4.law19953).toBe(10715.48);

    /**
     * pension -> b
     *  age > 75
     *  basePension > law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 300;
    const [b] = await rule3(pensionsData[0]);

    expect(b).toBeDefined();
    expect(b.basePension).toBe(118493.82);
    expect(b.law19403).toBe(16635.74);
    expect(b.law19539).toBe(24883.37);
    expect(b.law19953).toBe(10715.48);
  });

  it('should execute rule 2 dl1026-amountOtherPension-retirement cause age < 75', async () => {
    const basePensionModel = await createCollection('basePensionRules');
    const pensionsData = clone(data);
    const widowhoodWithChildrens = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez con hijos',
      age,
      type: 'pension',
      value: { minimun: '100.10', law19403: '1.00', law19539: null, law19953: null }
    }));

    const childlessWidowhood = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez sin hijos',
      age,
      type: 'pension',
      value: { minimun: '10.10', law19403: '50.50', law19539: '150.15', law19953: null }
    }));

    await basePensionModel.insertMany([...widowhoodWithChildrens, ...childlessWidowhood]);

    /**
     * pension -> a1
     *  age < 75
     *  basePension < law19539
     *  basePension > law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].dateOfBirth = moment('1970-01-01', 'YYYY-MM-DD');
    pensionsData[0].basePension = 100;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 0;
    pensionsData[0].retirement = false;
    const [a1] = await rule2(pensionsData[0]);

    expect(a1).toBeDefined();
    expect(a1.basePension).toBe(94965.97);
    expect(a1.law19403).toBe(16727.34);
    expect(a1.law19539).toBe(34648.03);
    expect(a1.law19953).toBe(0);

    /**
     * pension -> a2
     *  age < 75
     *  basePension < law19539
     *  minimun <= basePension < law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 20;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [a2] = await rule2(pensionsData[0]);

    expect(a2).toBeDefined();
    expect(a2.basePension).toBe(94965.97);
    expect(a2.law19403).toBe(0);
    expect(a2.law19539).toBe(0);
    expect(a2.law19953).toBe(0);

    /**
     * pension -> a3
     *  age < 75
     *  basePension < law19539
     *  basePension < minimun
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 5;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [a3] = await rule2(pensionsData[0]);

    expect(a3).toBeDefined();
    expect(a3.basePension).toBe(94965.97);
    expect(a3.law19403).toBe(0);
    expect(a3.law19539).toBe(0);
    expect(a3.law19953).toBe(0);

    /**
     * pension -> a
     *  age < 75
     *  basePension > law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 200;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [a] = await rule2(pensionsData[0]);

    expect(a).toBeDefined();
    expect(a.basePension).toBe(94965.97);
    expect(a.law19403).toBe(0);
    expect(a.law19539).toBe(0);
    expect(a.law19953).toBe(0);
  });

  it('should execute rule 3 dl1026-amountOtherPension-retirement cause age >= 75', async () => {
    const basePensionModel = await createCollection('basePensionRules');
    const pensionsData = clone(data);
    const widowhoodWithChildrens = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez con hijos',
      age,
      type: 'pension',
      value: { minimun: '100.00', law19403: '1.00', law19539: null, law19953: null }
    }));

    const childlessWidowhood = [
      {
        age: 'Edad < 70'
      },
      {
        age: '70 <= Edad < 75'
      },
      {
        age: 'Edad >= 75'
      }
    ].map(({ age }) => ({
      label: 'Pensión de viudez sin hijos',
      age,
      type: 'pension',
      value: { minimun: '10.10', law19403: '50.50', law19539: '150.00', law19953: '250.00' }
    }));

    await basePensionModel.insertMany([...widowhoodWithChildrens, ...childlessWidowhood]);

    /**
     * pension -> b1
     *  age > 75
     *  basePension >= law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].dateOfBirth = moment('1935-01-01', 'YYYY-MM-DD');
    pensionsData[0].basePension = 150;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 0;
    pensionsData[0].retirement = false;
    const [b1] = await rule3(pensionsData[0]);

    expect(b1).toBeDefined();
    expect(b1.basePension).toBe(118493.82);
    expect(b1.law19403).toBe(16635.74);
    expect(b1.law19539).toBe(24883.37);
    expect(b1.law19953).toBe(10715.48);

    /**
     * pension -> b2
     *  age > 75
     *  law19403 <= basePension < law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 100;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [b2] = await rule3(pensionsData[0]);

    expect(b2).toBeDefined();
    expect(b2.basePension).toBe(118493.82);
    expect(b2.law19403).toBe(0);
    expect(b2.law19539).toBe(0);
    expect(b2.law19953).toBe(0);

    /**
     * pension -> b3
     *  age > 75
     *  minimun <= basePension < law19403
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 20;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [b3] = await rule3(pensionsData[0]);

    expect(b3).toBeDefined();
    expect(b3.basePension).toBe(118493.82);
    expect(b3.law19403).toBe(0);
    expect(b3.law19539).toBe(0);
    expect(b3.law19953).toBe(0);

    /**
     * pension -> b4
     *  age > 75
     *  basePension < minimun
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 5;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [b4] = await rule3(pensionsData[0]);

    expect(b4).toBeDefined();
    expect(b4.basePension).toBe(118493.82);
    expect(b4.law19403).toBe(0);
    expect(b4.law19539).toBe(0);
    expect(b4.law19953).toBe(0);

    /**
     * pension -> b
     *  age > 75
     *  basePension > law19539
     */
    pensionsData[0].pensionType = 'Pensión de viudez sin hijos';
    pensionsData[0].basePension = 300;
    pensionsData[0].dl1026 = 0;
    pensionsData[0].amountOtherPension = 100;
    pensionsData[0].retirement = true;
    const [b] = await rule3(pensionsData[0]);

    expect(b).toBeDefined();
    expect(b.basePension).toBe(118493.82);
    expect(b.law19403).toBe(0);
    expect(b.law19539).toBe(0);
    expect(b.law19953).toBe(0);
  });

  afterEach(async () => {
    await mongoose.connection.db
      .collection('basePensionRules')
      .drop()
      .catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

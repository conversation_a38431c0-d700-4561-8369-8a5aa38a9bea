/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const dataPension = require('../../../resources/keyBuilder.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./dbService');

describe('DBservice keyBuilder service Test', () => {
  beforeAll(beforeAllTests);

  let pensionService;

  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() =>
        Promise.resolve({
          result: dataPension.map(item => ({
            ...item,
            _doc: { ...item._doc, dateOfBirth: new Date(item._doc.dateOfBirth) }
          }))
        })
      )
    };
  });

  it('success test case orphandhood pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() =>
      Promise.resolve({
        result: [dataPension[0]].map(item => ({
          ...item,
          _doc: { ...item._doc, dateOfBirth: new Date(item._doc.dateOfBirth) }
        }))
      })
    );

    const { result, error, completed } = await service.generateKeys(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(undefined);
    expect(result).toBeDefined();
  });

  it('success test no matches first condition', async () => {
    pensionService.getAllAndFilter = jest.fn(() =>
      Promise.resolve({
        result: [dataPension[1]].map(item => ({
          ...item,
          _doc: { ...item._doc, dateOfBirth: new Date(item._doc.dateOfBirth) }
        }))
      })
    );

    const { result, error, completed } = await service.generateKeys(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(undefined);
    expect(result).toBeDefined();
  });

  it('success test widow pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() =>
      Promise.resolve({
        result: [dataPension[2]].map(item => ({
          ...item,
          _doc: { ...item._doc, dateOfBirth: new Date(item._doc.dateOfBirth) }
        }))
      })
    );

    const { result, error, completed } = await service.generateKeys(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(undefined);
    expect(result).toBeDefined();
  });

  it('success test lower age orphanhood', async () => {
    pensionService.getAllAndFilter = jest.fn(() =>
      Promise.resolve({
        result: [dataPension[3], dataPension[4]].map(item => ({
          ...item,
          _doc: { ...item._doc, dateOfBirth: new Date(item._doc.dateOfBirth) }
        }))
      })
    );

    const { result, error, completed } = await service.generateKeys(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(undefined);
    expect(result).toBeDefined();
  });

  it('fail general error test', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.reject(new Error('error')));

    const { err, completed } = await service.generateKeys(pensionService);
    expect(completed).toBe(false);
    expect(err.message).toBe('error');
  });

  afterAll(afterAllTests);
});

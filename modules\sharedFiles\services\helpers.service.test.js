/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./helpers.service');

describe('helpers Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;

  it('processed taxable pensions success', async () => {
    const date = new Date();
    const [year, month] = await service.getCurrentYearAndMonth(pensionService);
    expect(year).toBe(date.getFullYear());
    expect(month).toBe(date.getMonth());
  });

  afterAll(afterAllTests);
});

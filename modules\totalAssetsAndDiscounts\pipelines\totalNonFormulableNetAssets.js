const calculate = pension => {
  const netAssetTypeRegex = /l[iíì<PERSON>]qu[íìïi]do/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const netAssets = assetsNonFormulable.filter(({ assetType }) =>
    netAssetTypeRegex.test(assetType)
  );
  const netTotalNonFormulable = netAssets.reduce((acc, current) => acc + current.amount, 0);
  return { ...pension, assets: { ...pension.assets, netTotalNonFormulable } };
};

module.exports = calculate;

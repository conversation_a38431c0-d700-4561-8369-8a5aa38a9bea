const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TemporaryInactivationPensionSchema = new Schema(
  {
    pensionCodeId: { type: String, required: true },
    pensionType: { type: String, required: true },
    validityType: { type: String, maxlength: 160, required: true },
    basePension: { type: Number, min: 0, max: 99999999, required: true },
    beneficiaryName: { type: String, required: true, uppercase: true },
    endDateOfValidity: { type: Date, required: true },
    endDateOfTheoricalValidity: { type: Date, required: true },
    inactivationReason: { type: String, required: true },
    paymentEndDate: { type: Date, required: true },
    transient: { type: String },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

TemporaryInactivationPensionSchema.plugin(paginate);
TemporaryInactivationPensionSchema.index({ beneficiaryName: 1 });

module.exports = mongoose.model('TemporaryInactivationPension', TemporaryInactivationPensionSchema);

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const TemporaryFamilyAssignment = require('../../familyAssignment/models/temporaryFamilyAssignment');
const LiquidationModel = require('../../../models/liquidation');
const liquidationHistoric = require('../../../models/liquidationHistoric');
const discountsAndAssetsModel = require('../../../models/discountsAndAssets');
const UpdatePensionerInfo = require('../models/UpdatePensionerInfo');
const UpdatePensionType = require('../models/UpdatePensionType');
const pensionService = require('./queryPensions.services');
const pensionData = require('../../../resources/queryPensions.json');
const pensionData1 = require('../../../resources/queryPensioner.json');
const discountsAndAssetsData = require('../../../resources/discountsAndAssets.json');
const ModelRol = require('../../systems/rolesAndPermissions/models/roles.model');
const ModelUser = require('../../../models/user');
const serviceMail = require('../../sendMail/service/sendMail.service');

const { checkDigitValidation, RUT_PATTERN, PENSION_PATTERN } = require('../validators');

describe('test service', () => {
  beforeAll(beforeAllTests);
  beforeEach(async () => {
    await discountsAndAssetsModel.create({
      ...discountsAndAssetsData,
      pensionCodeId: '100107',
      beneficiaryRut: '17176176-8',
      causantRut: '17175175-8'
    });
  });
  it('should retrieve data by beneficiary rut', async done => {
    await PensionModel.insertMany([pensionData]).catch(err => console.error(err));
    const query = { beneficiary: { rut: '5537843-6' } };
    const { error, result } = await pensionService
      .queryPensions(query)
      .catch(err => console.log(err));

    expect(error).toBe(null);
    expect(result[0].causant.rut).toBe('********-2');
    expect(result[0].collector.rut).toBe('19685004-K');
    expect(result[0].beneficiary.rut).toBe('5537843-6');
    expect(result[0].beneficiary.name).toBe('ALEX');
    expect(result[0].beneficiary.lastName).toBe('RAMOS');
    expect(result[0].validityType).toBe('Vigente hasta la jubilación');
    expect(result[0].pensionCodeId).toBe('13136');
    expect(result[0].endDateOfValidity).toStrictEqual(new Date('2042-08-10T04:00:00.000Z'));

    done();
  });

  it('should retrieve data by causant rut', async done => {
    await PensionModel.insertMany([pensionData]).catch(err => console.error(err));
    const query = { causant: { rut: '********-2' } };
    const { error, result } = await pensionService
      .queryPensions(query)
      .catch(err => console.log(err));

    expect(error).toBe(null);
    expect(result[0].causant.rut).toBe('********-2');
    expect(result[0].collector.rut).toBe('19685004-K');
    expect(result[0].beneficiary.rut).toBe('5537843-6');
    expect(result[0].beneficiary.name).toBe('ALEX');
    expect(result[0].beneficiary.lastName).toBe('RAMOS');
    expect(result[0].validityType).toBe('Vigente hasta la jubilación');
    expect(result[0].pensionCodeId).toBe('13136');
    expect(result[0].endDateOfValidity).toStrictEqual(new Date('2042-08-10T04:00:00.000Z'));

    done();
  });

  it('should retrieve data by collector rut', async done => {
    await PensionModel.insertMany([pensionData]).catch(err => console.error(err));
    const query = { collector: { rut: '19685004-K' } };
    const { error, result } = await pensionService
      .queryPensions(query)
      .catch(err => console.log(err));

    expect(error).toBe(null);
    expect(result[0].causant.rut).toBe('********-2');
    expect(result[0].collector.rut).toBe('19685004-K');
    expect(result[0].beneficiary.rut).toBe('5537843-6');
    expect(result[0].beneficiary.name).toBe('ALEX');
    expect(result[0].beneficiary.lastName).toBe('RAMOS');
    expect(result[0].validityType).toBe('Vigente hasta la jubilación');
    expect(result[0].pensionCodeId).toBe('13136');
    expect(result[0].endDateOfValidity).toStrictEqual(new Date('2042-08-10T04:00:00.000Z'));

    done();
  });

  it('should retrieve data by pensionCodeId', async done => {
    await PensionModel.insertMany([pensionData]).catch(err => console.error(err));
    const query = { pensionCodeId: '13136' };
    const { error, result } = await pensionService
      .queryPensions(query)
      .catch(err => console.log(err));

    expect(error).toBe(null);
    expect(result[0].causant.rut).toBe('********-2');
    expect(result[0].collector.rut).toBe('19685004-K');
    expect(result[0].beneficiary.rut).toBe('5537843-6');
    expect(result[0].beneficiary.name).toBe('ALEX');
    expect(result[0].beneficiary.lastName).toBe('RAMOS');
    expect(result[0].validityType).toBe('Vigente hasta la jubilación');
    expect(result[0].pensionCodeId).toBe('13136');
    expect(result[0].endDateOfValidity).toStrictEqual(new Date('2042-08-10T04:00:00.000Z'));

    done();
  });

  it('should retrieve data by pensionCodeId & collector rut', async done => {
    await PensionModel.insertMany([pensionData]).catch(err => console.error(err));
    const query = { pensionCodeId: '13136', collector: { rut: '19685004-K' } };
    const { error, result } = await pensionService
      .queryPensions(query)
      .catch(err => console.log(err));

    expect(error).toStrictEqual(new Error('More than one parameter supplied'));
    expect(result).toStrictEqual([]);

    done();
  });

  it('should test validation library', async done => {
    const invalidPensionCode = RUT_PATTERN.test('123456');
    const validPensionCode = PENSION_PATTERN.test('12345');
    const invalidRutPattern = RUT_PATTERN.test('55378-43-7');
    const invalidCheckDigit = checkDigitValidation('5537843-7');
    const rutWithK = checkDigitValidation('1.515.151-K');
    const validRut = checkDigitValidation('5537843-6');
    const rutWith0 = checkDigitValidation('19.779.407-0');

    expect(invalidPensionCode).toBe(false);
    expect(validPensionCode).toBe(true);
    expect(invalidRutPattern).toBe(false);
    expect(invalidCheckDigit).toBe(false);
    expect(validRut).toBe(true);
    expect(rutWithK).toBe(true);
    expect(rutWith0).toBe(true);

    done();
  });

  it('should return pensions linked in a time interval of one month', async done => {
    const query = {
      rutBeneficiary: '17176176-8',
      rutCausant: '17175175-8',
      pensionCodeId: '17153'
    };

    await discountsAndAssetsModel.create({
      ...discountsAndAssetsData,
      pensionCodeId: '17153',
      beneficiaryRut: '17176176-8',
      causantRut: '17175175-8'
    });

    const pension = [pensionData1[1], pensionData1[2]];
    await PensionModel.insertMany(pension).catch(err => console.error(err));
    const liquidation = pensionData1[0];
    await LiquidationModel.insertMany([liquidation]).catch(err => console.error(err));

    const { isError, result, error } = await pensionService
      .findExtendedPensioner(query)
      .catch(err => console.log(err));

    expect(error).toBe('');
    expect(isError).toBe(false);
    expect(result.totalAssets).toBe(26000);
    expect(result.totalOnePercentDiscounts).toBe(27000);
    expect(result.totalDiscounts).toBe(29000);
    expect(result.cun).toBe('1');
    expect(result.gender).toBe('M');

    done();
  });

  it('should return nextPaymentDate', async done => {
    const info = {
      rutBeneficiary: '17176176-8',
      rutCausant: '17175175-8'
    };

    const { errorNextPaymentDate } = await pensionService
      .getNextPaymentDate(info)
      .catch(err => console.log(err));

    expect(errorNextPaymentDate).toBeDefined();
    done();
  });

  it('should return values only comming from a single pension', async done => {
    const query = {
      rutBeneficiary: '17176176-8',
      rutCausant: '17175175-8',
      pensionCodeId: '17153'
    };

    await discountsAndAssetsModel.create({
      ...discountsAndAssetsData,
      pensionCodeId: '17153',
      beneficiaryRut: '17176176-8',
      causantRut: '17175175-8'
    });

    const pension = [pensionData1[1]];
    await PensionModel.insertMany(pension).catch(err => console.error(err));

    const liquidation = pensionData1[0];
    await LiquidationModel.insertMany([liquidation]).catch(err => console.error(err));

    const { isError, result, error } = await pensionService
      .findExtendedPensioner(query)
      .catch(err => console.log(err));

    expect(error).toBe('');
    expect(isError).toBe(false);
    expect(result.totalAssets).toBe(26000);
    expect(result.totalOnePercentDiscounts).toBe(27000);
    expect(result.totalDiscounts).toBe(29000);
    expect(result.cun).toBe('1');
    expect(result.gender).toBe('M');

    done();
  });

  it('should return only values having null as pensioner does not exist', async done => {
    const query = {
      rutBeneficiary: '17176176-7',
      rutCausant: '17175175-7'
    };

    const pension = [pensionData1[1]];
    await PensionModel.insertMany(pension).catch(err => console.error(err));

    const { isError, result, error } = await pensionService
      .findExtendedPensioner(query)
      .catch(err => console.log(err));

    expect(error).toBe('');
    expect(isError).toBe(false);
    expect(result.totalAssets).toBe(undefined);
    expect(result.totalOnePercentDiscounts).toBe(undefined);
    expect(result.totalDiscounts).toBe(undefined);
    done();
  });

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await discountsAndAssetsModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

describe('Throw error if function agreggate or find fails', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {
    jest.spyOn(PensionModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });
    jest.spyOn(PensionModel, 'aggregate').mockImplementationOnce(() => {
      throw new Error();
    });
  });
  it('Should return an error if the query is corrupted', async done => {
    const query = {
      rutBeneficiary: '17176176-8',
      rutCausant: ''
    };
    await LiquidationModel.insertMany(pensionData1[0]).catch(err => console.error(err));
    await PensionModel.insertMany(pensionData1[1]).catch(err => console.error(err));

    const { error, result } = await pensionService.findExtendedPensioner(query);

    expect(error).toStrictEqual(new Error());
    expect(result).toStrictEqual({});

    done();
  });

  it('Should return an error if find query returns an error', async done => {
    const query = { collector: { rut: '19685004-K' } };

    const { error, result } = await pensionService.queryPensions(query);

    expect(result).toStrictEqual([]);
    expect(error).toStrictEqual(new Error());
    done();
  });
  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await LiquidationModel.deleteMany({}).catch(err => console.error(err));
  });
  afterAll(afterAllTests);
});

describe('test storage logic', () => {
  beforeAll(beforeAllTests);

  let storageService;

  beforeEach(() => {
    storageService = {
      listFiles: jest.fn()
    };
  });

  it('should test functions called on check file existence on storage', async done => {
    const beneficiaryRut = '17.176.146-8';
    const causantRut = '17.176.146-8';
    const pensionCodeId = '13136';
    const nameAndRegex = { court: { regex: 'Dictamen_de_tribunales.pdf' }, neurological: {} };

    storageService.findFilesByPrefix = jest.fn(() =>
      Promise.resolve({ result: [{ virtualPath: '17176146/17176146/Dictamen_de_tribunales.pdf' }] })
    );

    const { storageError, fileExistenceDetails } = await pensionService.checkPensionerDocument({
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      nameAndRegex,
      storageService
    });

    expect(storageError).toBe(false);
    expect(fileExistenceDetails).toStrictEqual({
      court: { regex: 'Dictamen_de_tribunales.pdf', existence: true },
      neurological: { existence: false }
    });

    done();
  });

  it('should test error on check file existence on storage', async done => {
    const beneficiaryRut = '17.176.146-8';
    const causantRut = '17.176.146-8';
    const pensionCodeId = '13136';
    const nameAndRegex = { court: { regex: 'documento_tribunales.pdf' }, neurological: {} };

    storageService.findFilesByPrefix = jest.fn(() => Promise.reject(new Error()));

    const { storageError, fileExistenceDetails } = await pensionService.checkPensionerDocument({
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      nameAndRegex,
      storageService
    });

    expect(storageError).toStrictEqual(new Error());
    expect(fileExistenceDetails).toStrictEqual(nameAndRegex);

    done();
  });

  it('should test functions called on upload file to storage', async done => {
    process.env.PENSIONER_AFP_CERTIFICATE_TARGET_FILE_NAME = 'Certificado_de_afiliacion_AFP.pdf';
    process.env.PENSIONER_NOUROLOGIC_CERTIFICATE_TARGET_FILE_NAME = 'Certificado_neurologico.pdf';
    process.env.PENSIONER_NOTARIAL_TARGET_FILE_NAME = 'Poder_notarial_para_cambio_de_cobrante.pdf';
    process.env.PENSIONER_COURTS_OPINION_TARGET_FILE_NAME = 'Dictamen_de_tribunales.pdf';

    const fileName = '10.784.480-5 7.435.631-1 13136 Poder_notarial_para_cambio_de_cobrante.pdf';
    const path =
      'C:\\Users\\<USER>\\AppData\\Local\\Temp\\upload_3a2b2ce28190e1e7dfdafc25fd13f4e9.pdf';

    const {
      beneficiaryRutNumber,
      causantRutNumber,
      pensionCodeId,
      targetName
    } = pensionService.resolveStorageTargetProps(fileName);

    const virtualPath = `${beneficiaryRutNumber}/${causantRutNumber}/${targetName}`;

    storageService.uploadFileFromLocal = jest.fn(() =>
      Promise.resolve({ status: 200, data: '2443' })
    );
    storageService.saveFileRegistry = jest.fn(() => Promise.resolve({ result: 'qwerty' }));

    const { status, data } = await storageService.uploadFileFromLocal(path, targetName);
    const { result } = await storageService.saveFileRegistry(virtualPath, data);

    expect(beneficiaryRutNumber).toBe('10784480');
    expect(causantRutNumber).toBe('7435631');
    expect(pensionCodeId).toBe('13136');
    expect(targetName).toBe('Poder_notarial_para_cambio_de_cobrante.pdf');
    expect(status).toBe(200);
    expect(data).toBe('2443');
    expect(result).toBe('qwerty');

    done();
  });

  it('should test functions called on download file from storage', async done => {
    const filenameRegex = '10.784.480-5 7.435.631-1 13136 Certificado_neurologico.pdf';

    const {
      beneficiaryRutNumber,
      causantRutNumber,
      pensionCodeId,
      targetName
    } = pensionService.resolveStorageTargetProps(filenameRegex);

    const targetBlobName = `${beneficiaryRutNumber}/${causantRutNumber}/${targetName}`;

    storageService.findFileRegistry = jest.fn(() => Promise.resolve({ result: { uuid: '84466' } }));

    storageService.downloadFile = jest.fn(() => Promise.resolve({ status: 200, data: '%PDF-1.5' }));

    const { result } = await storageService.findFileRegistry(targetBlobName);
    const { status, data } = await storageService.downloadFile(result.uuid);

    expect(beneficiaryRutNumber).toBe('10784480');
    expect(causantRutNumber).toBe('7435631');
    expect(pensionCodeId).toBe('13136');

    expect(targetName).toBe('Certificado_neurologico.pdf');
    expect(result).toStrictEqual({ uuid: '84466' });
    expect(status).toBe(200);
    expect(data).toBe('%PDF-1.5');

    done();
  });

  afterEach(async () => {});
  afterAll(afterAllTests);
});

describe('test ftp logic', () => {
  beforeAll(beforeAllTests);

  let client;
  let fs;
  let connectToFTPServer;
  beforeEach(() => {
    client = {
      list: jest.fn(() => Promise.resolve([{ name: 'some file.pdf', isFile: true }])),
      close: jest.fn(() => Promise.resolve(false)),
      ftp: jest.fn(() => Promise.resolve({ verbose: false })),
      downloadTo: jest.fn(() => Promise.resolve(true)),
      uploadFrom: jest.fn(() => Promise.resolve())
    };
    fs = {
      writeFile: jest.fn(() => Promise.resolve()),
      unlink: jest.fn(() => Promise.resolve())
    };
    connectToFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
  });

  it('should test functions called on upload file to ftp', async done => {
    const { ftpError } = await pensionService.uploadToFTP({
      filename: 'a simple file.pdf',
      file: '',
      client,
      fs,
      connectToFTPServer
    });

    expect(ftpError).toBe(false);
    expect(fs.writeFile).toBeCalled();
    expect(client.uploadFrom).toBeCalled();
    expect(fs.unlink).toBeCalled();
    expect(client.close).toBeCalled();

    done();
  });

  it('should throw an error if something at uploadFile fails', async done => {
    fs.writeFile = jest.fn(() => Promise.reject(new Error()));
    const { ftpError } = await pensionService.uploadToFTP({
      filename: 'a simple file.pdf',
      file: '',
      client,
      fs,
      connectToFTPServer
    });

    expect(ftpError).toStrictEqual(new Error());
    expect(fs.writeFile).toBeCalled();
    expect(client.uploadFrom).not.toBeCalled();
    expect(fs.unlink).not.toBeCalled();
    expect(client.close).toBeCalled();

    done();
  });

  it('should throw an error if connected is false', async done => {
    connectToFTPServer = jest.fn(() => Promise.resolve({ connected: false }));
    const { ftpError } = await pensionService.uploadToFTP({
      filename: 'a simple file.pdf',
      file: '',
      client,
      fs,
      connectToFTPServer
    });

    expect(ftpError).toStrictEqual(new Error('Could not connect to FTP'));
    expect(fs.writeFile).not.toBeCalled();
    expect(client.uploadFrom).not.toBeCalled();
    expect(fs.unlink).not.toBeCalled();
    expect(client.close).toBeCalled();

    done();
  });

  it('should test functions called on get file from ftp', async done => {
    const beneficiaryRut = '17.176.146-8';
    const causantRut = '17.176.146-8';
    const filenameRegex = `${beneficiaryRut} ${causantRut} documento tribunales.pdf`;

    client.list = jest.fn(() =>
      Promise.resolve([
        { name: `${beneficiaryRut} ${causantRut} documento tribunales.pdf`, isFile: true }
      ])
    );

    const { ftpError, filename } = await pensionService.getPensionerDocument({
      filenameRegex,
      client,
      connectToFTPServer
    });

    expect(ftpError).toBe(false);
    expect(filename).toBe(filenameRegex);
    expect(client.list).toBeCalled();
    expect(client.downloadTo).toBeCalled();
    expect(client.close).toBeCalled();

    done();
  });

  it('should test functions throwing error on get file from ftp', async done => {
    const beneficiaryRut = '17.176.146-8';
    const causantRut = '17.176.146-8';
    const filenameRegex = `${beneficiaryRut} ${causantRut} documento tribunales.pdf`;

    client.list = jest.fn(() => Promise.reject(new Error()));

    const { ftpError, filename } = await pensionService.getPensionerDocument({
      filenameRegex,
      client,
      connectToFTPServer
    });

    expect(ftpError).toStrictEqual(new Error());
    expect(filename).toBeUndefined();
    expect(client.list).toBeCalled();
    expect(client.downloadTo).not.toBeCalled();
    expect(client.close).toBeCalled();

    done();
  });

  afterAll(afterAllTests);
});

describe('test update pension to pensioner or temporally collection', () => {
  beforeAll(beforeAllTests);

  it('should create a registry in the temporary table', async done => {
    const beneficiaryRut = '17176146-8';
    const causantRut = '17176146-8';
    const pensionerInfo = { institutionalPatient: true };

    const { error, completed } = await pensionService.updateToTemporalTable({
      beneficiaryRut,
      causantRut,
      pensionerInfo,
      user: {}
    });

    const countDocuments = await UpdatePensionerInfo.find({})
      .estimatedDocumentCount()
      .exec();

    expect(error).toBe(false);
    expect(completed).toBe(true);
    expect(countDocuments).toBe(1);

    done();
  });

  it('should not create a registry in the temporary table due to error', async done => {
    const causantRut = '17176146-8';
    const pensionerInfo = { institutionalPatient: true };

    const { error, completed } = await pensionService.updateToTemporalTable({
      causantRut,
      pensionerInfo,
      user: {}
    });

    const countDocuments = await UpdatePensionerInfo.find({})
      .estimatedDocumentCount()
      .exec();

    expect(error).toBeDefined();
    expect(completed).toBe(true);
    expect(countDocuments).toBe(1);

    done();
  });

  it('should update a registry in the pensioner', async done => {
    const beneficiaryRut = '5537843-6';
    const causantRut = '********-2';
    const pensionCodeId = '13136';
    const pensionerInfo = { institutionalPatient: true, bank: 'Banco de Chile' };

    await PensionModel.insertMany(pensionData).catch(err => console.error(err));

    const parameterUpdate = {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionerInfo,
      user: {}
    };

    const { error, completed } = await pensionService.updatePension({
      parameterUpdate
    });

    const countDocuments = await PensionModel.find({})
      .estimatedDocumentCount()
      .exec()
      .catch(err => console.error(err));

    const { institutionalPatient, paymentInfo } = await PensionModel.findOne({
      'beneficiary.rut': { $eq: beneficiaryRut },
      'causant.rut': { $eq: causantRut },
      pensionCodeId,
      enabled: { $eq: true }
    }).catch(err => console.error(err));

    expect(error).toBe(false);
    expect(completed).toBe(true);
    expect(institutionalPatient).toBe(true);
    expect(paymentInfo.bank).toBe('Banco de Chile');
    expect(countDocuments).toBe(1);

    done();
  });

  it('should do nothing with a registry doesnot exist', async done => {
    const beneficiaryRut = '5537843-6';
    const causantRut = '********-2';
    const pensionCodeId = '';
    const pensionerInfo = { institutionalPatient: true };

    const parameterUpdate = {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionerInfo,
      user: {}
    };

    const { error, completed } = await pensionService.updatePension({
      parameterUpdate
    });

    const countDocuments = await PensionModel.find({})
      .estimatedDocumentCount()
      .exec()
      .catch(err => console.error(err));

    expect(error).toBe(false);
    expect(completed).toBe(false);
    expect(countDocuments).toBe(0);

    done();
  });

  it('should do nothing as pension model throws error', async done => {
    jest.spyOn(PensionModel, 'findOne').mockImplementationOnce(() => {
      throw new Error();
    });
    const beneficiaryRut = '5537843-6';
    const causantRut = '********-2';
    const pensionerInfo = { institutionalPatient: true };

    const { error, completed } = await pensionService.updatePension({
      causantRut,
      beneficiaryRut,
      pensionerInfo
    });

    const countDocuments = await PensionModel.find({})
      .estimatedDocumentCount()
      .exec()
      .catch(err => console.error(err));

    expect(error).toBeDefined();
    expect(completed).toBe(false);
    expect(countDocuments).toBe(0);

    done();
  });

  it('should look pensioners in the temporal table', async () => {
    const { result } = await pensionService.findPensionerTemporalTable({
      rutBeneficiary: '1',
      rutCausant: '2'
    });
    expect(result).toStrictEqual({});
  });

  it('should look pensioners in the temporal table', async () => {
    jest.spyOn(UpdatePensionerInfo, 'findOne').mockImplementationOnce(() => {
      throw new Error();
    });

    const { result, error } = await pensionService.findPensionerTemporalTable({
      rutBeneficiary: '1',
      rutCausant: '2'
    });
    expect(error).toBeDefined();
    expect(result).toStrictEqual({});
  });

  it('should create a registry in the temporary pensionType table', async done => {
    const beneficiaryRut = '17176146-8';
    const causantRut = '17176146-8';
    const pensionCodeId = '13136';
    const pensionType = 'this is a pensionType';

    const { error, completed } = await pensionService.updateTemporalPensionType({
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      pensionType
    });

    const countDocuments = await UpdatePensionType.find({})
      .estimatedDocumentCount()
      .exec();

    expect(error).toBe(false);
    expect(completed).toBe(true);
    expect(countDocuments).toBe(1);

    done();
  });

  it('should fail to create a registry in the temporary pensionType table', async done => {
    const beneficiaryRut = '17176146-8';
    const causantRut = '17176146-8';
    const pensionCodeId = '13136';

    const { error, completed } = await pensionService.updateTemporalPensionType({
      beneficiaryRut,
      causantRut,
      pensionCodeId
    });

    const countDocuments = await UpdatePensionType.find({})
      .estimatedDocumentCount()
      .exec();

    expect(error).toBeDefined();
    expect(completed).toBe(true);
    expect(countDocuments).toBe(1);

    done();
  });

  it('should look pensioners in the temporal pension type table', async () => {
    const { result } = await pensionService.findTemporalPensionType({
      rutBeneficiary: '1',
      rutCausant: '2'
    });
    expect(result).toStrictEqual({});
  });

  it('should look pensioners in the temporal pension type table', async () => {
    jest.spyOn(UpdatePensionType, 'findOne').mockImplementationOnce(() => {
      throw new Error();
    });

    const { result, error } = await pensionService.findTemporalPensionType({
      rutBeneficiary: '1',
      rutCausant: '2'
    });
    expect(error).toBeDefined();
    expect(result).toStrictEqual({});
  });

  it('should look liquidations through different months ', async () => {
    const liquidation = {
      beneficiaryRut: '1',
      causantRut: '2',
      pensionCodeId: '123456',
      taxablePension: 3,
      enabled: true
    };

    const lowerDate = new Date(2020, 0, 25);
    const upperDate = new Date(2020, 4, 25);

    const liquidation20200105 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 0, 5)
    };
    const liquidation20200108 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 0, 30)
    };
    const liquidation20200208 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 1, 8)
    };
    const liquidation20200209 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 1, 9)
    };
    const liquidation20200211 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 1, 11)
    };
    const liquidation20200308 = {
      ...liquidation,
      pensionCodeId: '123456',
      updatedAt: new Date(2020, 2, 8)
    };

    await liquidationHistoric.insertMany([
      liquidation20200105,
      liquidation20200108,
      liquidation20200208,
      liquidation20200209,
      liquidation20200211,
      liquidation20200308
    ]);

    const { historicalSettlements, error } = await pensionService.getHistoricalSettlementsByDate({
      beneficiaryRut: '1',
      causantRut: '2',
      pensionCodeId: '123456',
      lowerDate,
      upperDate
    });

    expect(error).toBeUndefined();
    expect(historicalSettlements.length).toBe(3);
    expect(historicalSettlements[0].updatedAt).toStrictEqual(new Date(2020, 0, 30));
    expect(historicalSettlements[1].updatedAt).toStrictEqual(new Date(2020, 1, 11));
    expect(historicalSettlements[2].updatedAt).toStrictEqual(new Date(2020, 2, 8));
  });

  it('should return pension and liquidation, case with 0 numberOfCharges ', async () => {
    const beneficiaryRut = '40000000-K';
    const causantRut = '40000000-K';
    const pensionCodeId = '13136';

    const liquidation1 = {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      taxablePension: 3,
      enabled: true
    };

    const pension1 = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: beneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 0
    };

    await LiquidationModel.insertMany([liquidation1]);
    await PensionModel.insertMany([pension1]);

    const { result, error } = await pensionService.getInfoForPensionCertificate({
      beneficiaryRut: beneficiaryRut.toLowerCase(),
      causantRut,
      pensionCodeId
    });

    expect(error).toBeUndefined();
    expect(result.liquidation.beneficiaryRut).toBe(beneficiaryRut);
    expect(result.liquidation.causantRut).toBe(causantRut);
    expect(result.liquidation.taxablePension).toBe(3);
  });

  it('should return info por pdf, case with numberOfCharges>0 and equal beneficiary and causant rut ', async () => {
    const beneficiaryRut = '40000000-K';
    const causantRut = '40000000-K';
    const pensionCodeId = '13136';

    const charge1 = {
      chargeId: beneficiaryRut.toLowerCase(),
      causantId: beneficiaryRut,
      collectorId: beneficiaryRut,
      chargeValidityType: 'vigente vitalicia',
      typeOfAssocietedPension: 'hola ke ase',
      familyAssignment: 3000,
      retroactiveFamilyAssignment: 1000
    };

    const liquidation1 = {
      beneficiaryRut,
      causantRut,
      pensionCodeId,
      taxablePension: 3,
      enabled: true
    };

    const pension1 = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: beneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 1
    };

    await LiquidationModel.insertMany([liquidation1]);
    await PensionModel.insertMany([pension1]);
    await TemporaryFamilyAssignment.insertMany([charge1]);

    const { result, error } = await pensionService.getInfoForPensionCertificate({
      beneficiaryRut: beneficiaryRut.toLowerCase(),
      causantRut,
      pensionCodeId
    });

    expect(error).toBeUndefined();
    expect(result.liquidation.beneficiaryRut).toBe(beneficiaryRut);
    expect(result.liquidation.causantRut).toBe(causantRut);
    expect(result.liquidation.taxablePension).toBe(3);
    expect(result.chargesOfPensioner[0].chargeId).toBe(beneficiaryRut.toLowerCase());
  });

  it('should return info por pdf, case with numberOfCharges>0 and different beneficiary and causant rut ', async () => {
    const widowBeneficiaryRut = '40000100-6';
    const causantRut = '40000000-K';
    const pensionCodeId = '13136';
    const sonBeneficiaryRut = '40000300-9';
    const otherGroupBeneficiaryRut = '18180181-K';

    const liquidation1 = {
      beneficiaryRut: widowBeneficiaryRut,
      causantRut,
      pensionCodeId,
      taxablePension: 3,
      enabled: true
    };

    const widowPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: widowBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 1
    };

    const sonPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: sonBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 1
    };

    const otherGroupPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: otherGroupBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 2
    };

    await LiquidationModel.insertMany([liquidation1]);
    await PensionModel.insertMany([widowPension, sonPension, otherGroupPension]);

    const { result, error } = await pensionService.getInfoForPensionCertificate({
      beneficiaryRut: widowBeneficiaryRut.toLowerCase(),
      causantRut,
      pensionCodeId
    });

    expect(error).toBeUndefined();
    expect(result.liquidation.beneficiaryRut).toBe(widowBeneficiaryRut);
    expect(result.liquidation.causantRut).toBe(causantRut);
    expect(result.liquidation.taxablePension).toBe(3);
    expect(result.chargesOfPensioner.length).toBe(1);
    expect(result.chargesOfPensioner[0].beneficiary.rut).toBe(sonBeneficiaryRut.toLowerCase());
    expect(result.chargesOfPensioner[0].collector).toBeUndefined();
  });

  it('should return info por pdf even without a liquidation to match', async () => {
    const widowBeneficiaryRut = '40000100-6';
    const causantRut = '40000000-K';
    const pensionCodeId = '13136';
    const sonBeneficiaryRut = '40000300-9';
    const otherGroupBeneficiaryRut = '18180181-K';

    const widowPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: widowBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 1
    };

    const sonPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: sonBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 1
    };

    const otherGroupPension = {
      ...pensionData,
      beneficiary: { ...pensionData.beneficiary, rut: otherGroupBeneficiaryRut },
      causant: { ...pensionData.causant, rut: causantRut },
      numberOfCharges: 2,
      familyGroup: 2
    };

    await PensionModel.insertMany([widowPension, sonPension, otherGroupPension]);

    const { result, error } = await pensionService.getInfoForPensionCertificate({
      beneficiaryRut: widowBeneficiaryRut.toLowerCase(),
      causantRut,
      pensionCodeId
    });

    expect(error).toBeUndefined();
    expect(result.liquidation).toBeUndefined();
    expect(result.chargesOfPensioner.length).toBe(1);
    expect(result.chargesOfPensioner[0].beneficiary.rut).toBe(sonBeneficiaryRut.toLowerCase());
    expect(result.chargesOfPensioner[0].collector).toBeUndefined();
  });

  it('Obetener roles', async () => {
    const rol = {
      roleName: 'Jefe de PEC',
      views: [
        {
          permission: 'ReadWrite',
          view: '601ab72c287abf7c7ef9f324'
        }
      ],
      enabled: true,
      createdAt: '2022-02-18T19:27:03.244Z',
      updatedAt: '2022-02-18T19:27:03.244Z'
    };
    await ModelRol.insertMany([rol]);
    const listRoles = await pensionService.getRolesUsers();
    expect(listRoles.length).toBe(1);
  });

  it('Obetener users', async () => {
    const userA = {
      _id: '620ff7657be0bea14cf15419',
      name: 'MIGUEL ANGEL BECERRA TAIBA',
      email: '<EMAIL>',
      role: '601ac916287abf7c7efa6967'
    };
    await ModelUser.insertMany([userA]);
    const listUserRoles = await pensionService.filteUserByRole(['601ac916287abf7c7efa6967']);
    expect(listUserRoles.length).toBe(1);
  });

  it('Obetener mensaje correo', async () => {
    const infoBase = {
      rutBeneficiary: '11111111-1',
      pensionCodeId: '123456',
      basePension: '$156.888,45',
      newBasePension: '$159.888,45',
      email: '<EMAIL>'
    };
    const msj = pensionService.infoMessage(infoBase);
    expect(msj.name).toBe('Modificación pensión base N° 123456');
  });

  it('informar cambio de pension base', async () => {
    const infoBase = {
      rutBeneficiary: '11111111-1',
      pensionCodeId: '123456',
      basePension: '$156.888,45',
      newBasePension: '$159.888,45',
      email: '<EMAIL>'
    };

    const listEmail = ['<EMAIL>'];
    serviceMail.sendEmail = jest.fn(() => Promise.resolve({ completed: true, err: null }));
    pensionService.getListDestinations = jest.fn(() => Promise.resolve(listEmail));
    const { completed: successful } = await pensionService.sendNotificationEmail(infoBase);

    expect(successful).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({});
    await TemporaryFamilyAssignment.deleteMany({});
    await LiquidationModel.deleteMany({});
    await liquidationHistoric.deleteMany({});
    await UpdatePensionerInfo.deleteMany({});
    await UpdatePensionType.deleteMany({});
    await ModelUser.deleteMany({});
    await ModelRol.deleteMany({});
    await discountsAndAssetsModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

const mongoose = require('mongoose');
const PensionModel = require('../../../models/pension');
const pensionService = require('../../pensions/services/pension.service');

const RULER_PENSION_TYPES = [/Pensi[oó]n por accidente de trabajo/i, /Bono Invierno/i];
const WORK_ACCIDENT_PENSION = 'Pensión por accidente de trabajo';
const AGE_LIMIT_RULER = 'Edad >= 75';
const WINTER_BONUS_TYPE = 'Bono Invierno';
const AGE_LIMIT = 65;
const MILLISECONDS_YEAR = 365 * 24 * 60 * 60 * 1000;
const YES_PAY_WINTER_BONUS = 'Si';
const NOT_PAY_WINTER_BONUS = 'No';
const NOT_PAY_WINTER_BONUS_TEST = /No/i;
const INSTITUTION_CODE = '34';

const formatType = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .replace(/[èé]/gi, 'e')
    .replace(/[áà]/gi, 'a')
    .toLowerCase()
    .trim();

const reduceRulesToObj = rules => {
  return rules.reduce((rulesObj, rule) => {
    const { label, value } = rule;
    const key = formatType(label);
    const valueMinimun = key === formatType(WINTER_BONUS_TYPE) ? value : value.minimun;
    return { ...rulesObj, [key]: valueMinimun };
  }, {});
};

const assignWinterBonus = (pensioner, winterBonusAmount, minimumPensionAmount) => {
  const {
    basePension,
    law19403,
    law19539,
    law19953,
    article40,
    amountOtherPension,
    dl1026,
    assets,
    temporaryimportedbonuspensioners
  } = pensioner;
  const pensionBaseSum =
    basePension + law19403 + law19539 + law19953 + article40 + amountOtherPension + dl1026;

  const pensionIPS = temporaryimportedbonuspensioners.find(
    row => row.institutionCode === INSTITUTION_CODE && NOT_PAY_WINTER_BONUS_TEST.test(row.payBonus)
  );

  if (pensionIPS) {
    return {
      ...pensioner,
      payBonus: NOT_PAY_WINTER_BONUS,
      assets: { ...assets, winterBonus: 0 }
    };
  }

  if (pensionBaseSum <= minimumPensionAmount) {
    return {
      ...pensioner,
      payBonus: YES_PAY_WINTER_BONUS,
      assets: { ...assets, winterBonus: winterBonusAmount }
    };
  }

  return {
    ...pensioner,
    payBonus: NOT_PAY_WINTER_BONUS,
    assets: { ...assets, winterBonus: 0 }
  };
};

const service = {
  async filterPensionersWinterBonus() {
    return PensionModel.aggregate([
      {
        $addFields: {
          age: {
            $divide: [
              {
                $subtract: [new Date(), '$dateOfBirth']
              },
              MILLISECONDS_YEAR
            ]
          }
        }
      },
      {
        $match: {
          enabled: true
        }
      },
      {
        $lookup: {
          from: 'temporaryimportedbonuspensioners',
          localField: 'beneficiary.rut',
          foreignField: 'pensionerRut',
          as: 'temporaryimportedbonuspensioners'
        }
      },
      {
        $project: {
          'beneficiary.rut': 1,
          'causant.rut': 1,
          assets: 1,
          payBonus: 1,
          basePension: 1,
          article40: 1,
          law19403: 1,
          law19539: 1,
          law19953: 1,
          amountOtherPension: 1,
          dl1026: 1,
          age: 1,
          temporaryimportedbonuspensioners: 1
        }
      }
    ]);
  },

  async getRules() {
    try {
      return mongoose.connection.db
        .collection('basePensionRules')
        .find({
          $or: [
            { age: AGE_LIMIT_RULER },
            {
              label: {
                $in: RULER_PENSION_TYPES
              }
            }
          ]
        })
        .toArray();
    } catch (e) {
      return [];
    }
  },

  async calculateBasePension() {
    const pensioners = await this.filterPensionersWinterBonus();
    const rules = await this.getRules();
    const rulesObj = reduceRulesToObj(rules);
    const winterBonusAmount = +rulesObj[formatType(WINTER_BONUS_TYPE)];
    const minimumPensionAmount = +rulesObj[formatType(WORK_ACCIDENT_PENSION)];

    return pensioners.map(pensioner => {
      const { age, assets } = pensioner;
      if (age >= AGE_LIMIT) {
        return assignWinterBonus(pensioner, winterBonusAmount, minimumPensionAmount);
      }

      return {
        ...pensioner,
        payBonus: NOT_PAY_WINTER_BONUS,
        assets: { ...assets, winterBonus: 0 }
      };
    });
  },

  async setWinterBonus() {
    try {
      const pensioners = await this.calculateBasePension();
      const { completed, error } = await pensionService.updatePensionsByIdBonus(pensioners);
      return { completed, error, pensioners };
    } catch (error) {
      return { completed: false, error, pensioners: [] };
    }
  }
};

module.exports = { ...service };

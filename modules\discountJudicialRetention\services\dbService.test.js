const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const resourcePensions = require('../../../resources/retention/pensionRetention.json');
const resourceDiscounts = require('../../../resources/retention/discountsandassetsRetention.json');
const resourceCollector = require('../../../resources/retention/collectorRetention.json');
const minimunSalarCollector = require('../../../resources/retention/minimunSalaryRetention.json');
const ufCollector = require('../../../resources/retention/ufvaluesRetention.json');
const afpsCollector = require('../../../resources/retention/afpsRetention.json');
const ipcCollector = require('../../../resources/retention/ipcsRetention.json');
const liquidationCollector = require('../../../resources/retention/liquidationRetention.json');

const DiscountsModel = require('../../../models/discountsAndAssets');
const PensionModel = require('../../../models/pension');
const CollectorModel = require('../../../models/collectorretentions');
const minimunSalaryModel = require('../../../models/minimunsalarys');
const ufModel = require('../../UFvalue/models/ufValue');
const AfpsModel = require('../../nomenclators/afp/models/afp');
const ipcModel = require('../../../models/ipcs');
const liquidationModel = require('../../../models/liquidation');

const INDEX_MONTO = 0;
const INDEX_OTROS = 1;
const INDEX_VITAL = 2;
const INDEX_PORCENTAGE = 3;
const INDEX_IMR = 4;
const INDEX_IPC = 5;
const SIN_TIPO = 6;

describe('set amount retention service test', () => {
  beforeAll(beforeAllTests);

  it('get rules for calculation', async () => {
    await PensionModel.insertMany(resourcePensions);
    await DiscountsModel.insertMany(resourceDiscounts);
    await CollectorModel.insertMany(resourceCollector);
    await minimunSalaryModel.insertMany(minimunSalarCollector);
    await ufModel.insertMany(ufCollector);
    await AfpsModel.insertMany(afpsCollector);
    await ipcModel.insertMany(ipcCollector);
    await liquidationModel.insertMany(liquidationCollector);

    const { completed, discountsAndAssets } = await service.setAmountRetention();
    expect(completed).toBe(true);
    const { discountsNonFormulable, retentionsCollector } = discountsAndAssets.find(
      element => element.beneficiaryRut === '7972538-2'
    );
    expect(discountsNonFormulable[INDEX_MONTO].amount).toBe(70000);
    expect(discountsNonFormulable[INDEX_OTROS].amount).toBe(15000);
    expect(discountsNonFormulable[INDEX_VITAL].amount).toBe(14942.4);
    expect(discountsNonFormulable[INDEX_PORCENTAGE].amount).toBe(68503.54);
    expect(discountsNonFormulable[INDEX_IMR].amount).toBe(168500.44);
    expect(discountsNonFormulable[INDEX_IPC].amount).toBe(46418.99);
    expect(discountsNonFormulable[INDEX_IPC].judicialRetention.retention.amount).toBe(46418.99);
    expect(discountsNonFormulable[SIN_TIPO].amount).toBe(0);

    expect(retentionsCollector.length).toBe(5);
  });

  it('obtain rules for the calculation without IPC variation', async () => {
    await PensionModel.insertMany(resourcePensions[1]);
    await DiscountsModel.insertMany(resourceDiscounts[1]);
    await CollectorModel.insertMany(resourceCollector);
    await minimunSalaryModel.insertMany(minimunSalarCollector);
    await ufModel.insertMany(ufCollector);
    await AfpsModel.insertMany(afpsCollector);
    await ipcModel.insertMany(ipcCollector[0]);
    await ipcModel.insertMany(ipcCollector[1]);
    await liquidationModel.insertMany(liquidationCollector);

    const {
      completed,
      discountsAndAssets: discountsAndAssetsAux
    } = await service.setAmountRetention();
    expect(completed).toBe(true);
    const { discountsNonFormulable, retentionsCollector } = discountsAndAssetsAux.find(
      element => element.beneficiaryRut === '7972538-2'
    );
    expect(discountsNonFormulable[INDEX_IPC].amount).toBe(45000);
    expect(discountsNonFormulable[INDEX_IPC].judicialRetention.retention.amount).toBe(45000);
    expect(retentionsCollector.length).toBe(5);
  });

  afterEach(async () => {
    try {
      await PensionModel.deleteMany({});
      await DiscountsModel.deleteMany({});
      await CollectorModel.deleteMany({});
      await minimunSalaryModel.deleteMany({});
      await ufModel.deleteMany({});
      await AfpsModel.deleteMany({});
      await ipcModel.deleteMany({});
      await liquidationModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

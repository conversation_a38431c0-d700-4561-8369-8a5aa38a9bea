/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionsModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const service = require('./pension.service');

const pensionsData = require('../../../resources/pensions.json');
const pensionsHistoryData = require('../../../resources/pensionHistoric/pensions.json');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionsModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should find one and update', async () => {
    // create and save one  document
    const pensions = await PensionsModel.create({ ...pensionsData[0], enabled: true });
    expect(pensions._id).toBeDefined();

    const { error } = await service.findOnePensionAndUpdate(pensions._id);
    const { result, isError } = await service.getAllAndFilter();

    expect(error).not.toBeDefined();
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(false);
  });

  it('should create new and update old pension defined by BENEFICIARY RUT and CAUSANT RUT', async () => {
    const {
      _doc: { _id, __v, ...pension }
    } = await PensionsModel.create({
      ...pensionsData[0],
      enabled: true,
      validityType: 'No Vigente'
    });
    const before = await service.getAllAndFilter();
    expect(before.result.length).toBe(1);

    await service.createUpdatePension([
      { ...pension, validityType: 'Vigente hasta la jubilación' }
    ]);

    const after = await service.getAllAndFilter();
    expect(after.result.length).toBe(1);
    expect(after.result[0].validityType).toBe('Vigente hasta la jubilación');
  });

  it('should update The same pension defined by BENEFICIARY RUT and CAUSANT RUT', async () => {
    const {
      _doc: { _id, __v, ...pension }
    } = await PensionsModel.create({
      ...pensionsData[0],
      enabled: true,
      validityType: 'No Vigente'
    });
    const before = await service.getAllAndFilter();
    expect(before.result.length).toBe(1);

    await service.updatePensions([{ ...pension, validityType: 'Vigente hasta la jubilación' }]);

    const after = await service.getAllAndFilter();
    expect(after.result.length).toBe(1);
  });

  it('should get pensions with specific fields', async () => {
    const {
      _doc: { _id, validityType }
    } = await PensionsModel.create({
      ...pensionsData[0],
      enabled: true,
      validityType: 'No Vigente'
    });
    const { result } = await service.getAllWithFilter(
      {
        enabled: true
      },
      { enabled: 1, validityType: 1 }
    );

    expect(result.length).toBe(1);
    expect(result[0]._id).toStrictEqual(_id);
    expect(result[0].gender).not.toBeDefined();
    expect(result[0].enabled).toBe(true);
    expect(result[0].validityType).toBe(validityType);
  });

  it('should throw an error', async () => {
    await service.createUpdatePension(null);
    expect(mocks.abortTransaction).not.toBeCalled();
  });

  it('should update discount and assets ', async () => {
    const pension = await PensionsModel.create({ ...pensionsData[0], enabled: true });
    const updatedPension = {
      beneficiaryRut: pension.beneficiary.rut,
      causantRut: pension.causant.rut,
      pensionCodeId: '17153',
      assets: {
        forFamilyAssignment: 1200
      },
      article40: 3000,
      discounts: { health: 3000 }
    };
    const { completed, error } = await service.updateAssetsAndDiscountOfPension(updatedPension);

    expect(error).toBe(null);
    expect(completed).toBe(true);
  });
  it('should get the history of a pensioner by month', async () => {
    await PensionHistoricModel.insertMany(pensionsHistoryData);
    const result = await service.getPensionHistoryByMonth('23422439-5', '18098779-7');
    expect(result.length).toBe(2);
    expect(result[0].beneficiary.rut).toBe('23422439-5');
    expect(result[0].causant.rut).toBe('18098779-7');
    expect(result[0].enabled).toBe(false);
  });

  it('should complete updateDisableByDeath', async () => {
    const { completed } = await service.updateDisableByDeath();
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    await PensionsModel.deleteMany({}).catch(e => console.error(e));
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');

module.exports = {
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'reservedAssetsAndDiscountsAmountCalculation',
  description: 'Calcular la cantidades reservadas por supervivencia',
  endPoint: 'calculatereservedamountbysurvival',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

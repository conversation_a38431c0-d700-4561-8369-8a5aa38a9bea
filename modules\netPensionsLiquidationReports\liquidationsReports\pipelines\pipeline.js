const calculateTotalAssets = require('./calculateTotalAssets');
const countNumberOfAssets = require('./countNumberOfAssets');
const calculateTotalDiscounts = require('./calculateTotalDiscounts');
const countNumberOfDiscounts = require('./countNumberOfDiscounts');
const calculateTotalOnePercentDiscount = require('./calculateTotalOnePercentDiscount');
const calculateTotalSocialCreditDiscounts = require('./calculateTotalSocialCreditDiscounts');
const { pipe } = require('../../../sharedFiles/helpers');

const sumFields = obj =>
  pipe(
    calculateTotalAssets,
    calculateTotalSocialCreditDiscounts,
    calculateTotalOnePercentDiscount,
    countNumberOfAssets,
    countNumberOfDiscounts,
    // Should always be at last position because it depends of previous calculation
    calculateTotalDiscounts
  )(obj);

module.exports = sumFields;

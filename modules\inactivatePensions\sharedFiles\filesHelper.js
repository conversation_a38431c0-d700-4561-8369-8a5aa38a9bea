const moment = require('moment');

const { readLines } = require('./fileReader');
const readSFTPFile = require('./sftpFileReader');

const { CIVIL_REGISTRATION_SFTP_OUTPUT_FOLDER } = process.env;

const getCurrentDateTime = () => moment().format('DD-MM-YYYY HH:mm:ss');

const getCurrentYearAndMonth = () => {
  const year = moment().year();
  const month = moment().month() + 1;
  return [year, month];
};

const getSplitedDate = () => {
  const [year, month] = moment()
    .format('YYYY-MM')
    .split('-');
  return [year, month];
};
const getSFTPFileName = () =>
  `${CIVIL_REGISTRATION_SFTP_OUTPUT_FOLDER}Achs_${moment().format('YYYYMM')}_out.txt`;

const getFormatedRut = (rut, country) => {
  if (country && country.toUpperCase() === 'C') {
    const cleanedRut = rut && rut.replace(/[^0-9kK]+/gi, '').replace(/^0+/, '');
    const { length } = cleanedRut;
    const dv = rut && cleanedRut.substr(length - 1);
    const rutWithoutDV = cleanedRut.slice(0, length - 1);
    return (cleanedRut && `${rutWithoutDV}-${dv}`) || null;
  }
  return rut.trim();
};

const getFormatedDate = date => {
  const stringDate = date.toString();
  const cleanDate = stringDate && stringDate.replace(/[^0-9]+/gi, '');
  if (!cleanDate) return null;
  const formatedDate = moment(cleanDate, 'YYYYMMDD');
  if (!formatedDate.isValid()) return null;
  if (moment().diff(formatedDate) < 0) return null;
  return new Date(formatedDate.format());
};

module.exports = {
  getFormatedRut,
  getFormatedDate,
  getCurrentDateTime,
  getSFTPFileName,
  getCurrentYearAndMonth,
  getSplitedDate,
  readLines,
  readSFTPFile
};

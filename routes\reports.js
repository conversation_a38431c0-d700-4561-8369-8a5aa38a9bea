const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const CriteriaBuilder = require('../lib/builders/criteria-builder');
const service = require('../modules/reports/services/inactivationReactivation.service');
const excelService = require('../modules/reports/services/excel.service');
const storageService = require('../modules/fileStorage/services');
const FactoryController = require('../modules/reports/controllers/report.controller');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const reportController = FactoryController({
    HttpStatus,
    service,
    excelService,
    storageService,
    ErrorBuilder,
    Logger,
    CriteriaBuilder
  });

  router.get(
    '/inactivation-reactivation',
    validateAccess(),
    reportController.inactivationAndReactivationReport
  );
  router.get('/inactivation-reactivation/time', validateAccess(), reportController.getDate);
  router.get('/check-latest-bank-file', validateAccess(), reportController.checkLatestBankFile);
  router.get('/download-bank-file/:uuid', validateAccess(), reportController.downloadBankFile);
  router.get(
    '/check-latest-previred-file',
    validateAccess(),
    reportController.checkLatestPreviredFile
  );
  router.get(
    '/download-previred-file/:uuid',
    validateAccess(),
    reportController.downloadPreviredFile
  );
};

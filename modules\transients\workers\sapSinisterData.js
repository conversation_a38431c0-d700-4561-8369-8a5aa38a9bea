const Axios = require('axios');
const moment = require('moment');

const formatSinisterResponse = (reposos, idSiniestro, format) =>
  reposos.map(({ fechaInicioReposo, fechaAlta, tipoAlta }) => {
    if (!fechaInicioReposo || !fechaAlta || !Number.isInteger(+tipoAlta)) {
      throw Error('Pension has non valid data');
    }
    return {
      fechaInicioReposo: moment(fechaInicioReposo).format(format),
      fechaAlta: moment(fechaAlta).format(format),
      tipoAlta,
      idSiniestro
    };
  });

const getSAPSinisterData = async (
  accidentNumber,
  axios = Axios,
  formatDataFn = formatSinisterResponse,
  format = 'YYYY-MM-DD'
) => {
  const key = process.env.CRON_TO_INACTIVATE_SAP_KEY;
  const url = process.env.CRON_TO_INACTIVATE_SAP_URL;
  const authType = process.env.CRON_TO_INACTIVATE_SAP_AUTH_TYPE;

  const options = {
    method: 'GET',
    headers: { [authType]: key },
    url: `${url}?idSiniestro=${accidentNumber.padStart(10, 0)}`
  };

  return axios(options)
    .then(({ data: { reposos = [], idSiniestro } }) =>
      reposos && reposos.length ? formatDataFn(reposos, idSiniestro, format) : []
    )
    .catch(err => {
      console.log('err', err);
      return [];
    });
};

module.exports = { getSAPSinisterData, formatSinisterResponse };

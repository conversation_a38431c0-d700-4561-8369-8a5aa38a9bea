const { isDisabilityPensionType } = require('./retirement');
const { isWidow } = require('./widowhood');

const isValid = validityType => !/No\s+vigente/i.test(validityType);

const setNumberOfCharges = (data, pensionType, validityType) => {
  const isOfTypeWidowOrDisability = isWidow(pensionType) || isDisabilityPensionType(pensionType);
  const isMatchedCriteria = isValid(validityType) && isOfTypeWidowOrDisability;
  return isMatchedCriteria ? { ...data, numberOfCharges: 0 } : { ...data };
};

module.exports = setNumberOfCharges;

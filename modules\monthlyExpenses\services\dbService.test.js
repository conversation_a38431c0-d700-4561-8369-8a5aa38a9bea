/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const service = require('./dbService');
const PensionModel = require('../../../models/pension');
const pensions = require('../../../resources/pensionMonthlyExpense.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});

  it('success calculateMonthlyExpense', async () => {
    jest.spyOn(PensionModel, 'aggregate').mockImplementationOnce(() => [pensions]);

    const { error, completed } = await service.calculateMonthlyExpense();

    expect(completed).toBe(true);
    expect(error).toBe(null);
  });

  it('success getStats', async () => {
    const { error, result } = await service.getStats();

    expect(error).toBe(false);
    expect(result).toHaveLength(6);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

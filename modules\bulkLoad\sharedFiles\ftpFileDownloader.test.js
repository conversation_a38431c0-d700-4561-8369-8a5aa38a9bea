/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const ftpFileDownloader = require('./ftpFileDownloader');

const testFile1Path = `${__dirname}/../../../resources/bulkLoadCaja18/test1.txt`;
const testFile2Path = `${__dirname}/../../../resources/bulkLoadCaja18/test2.txt`;
const testZipFile1 = `${__dirname}/../../../resources/zipFiles`;
const testZipFile2 = `${__dirname}/../../../resources/zipFiles`;
const testZipFile3 = `${__dirname}/../../../resources/zipFiles`;

const LESS_A_MONTH = 1;
const YY_MM = moment().format('YYMM');
const brsaludDate = new Date();
brsaludDate.setMonth(brsaludDate.getMonth() - LESS_A_MONTH);
const YY_MM_LESS_A_MONTH = moment(brsaludDate).format('YYMM');
const patprZipFileName = `patpr.ips.703601006_${YY_MM}_${YY_MM}08_9876.zip`;
const papsoeZipFileName = `papsoe.ips.703601006_${YY_MM}_${YY_MM}09_6568.zip`;
const brsaludZipFileName = `brsalud.ips.703601006_${YY_MM_LESS_A_MONTH}_${YY_MM_LESS_A_MONTH}09_5686.zip`;

describe('File parser', () => {
  beforeAll(beforeAllTests);

  const connectToFTPServerSpy = jest.spyOn(ftpFileDownloader, 'connectToFTPServer');
  const getRemoteFileNamesSpy = jest.spyOn(ftpFileDownloader, 'getRemoteFileNames');
  const checkRemoteFilesExistenceSpy = jest.spyOn(ftpFileDownloader, 'checkRemoteFilesExistence');
  connectToFTPServerSpy.mockReturnValue({ connected: true, eror: null });
  getRemoteFileNamesSpy.mockReturnValue([testFile1Path, testFile2Path]);
  checkRemoteFilesExistenceSpy.mockReturnValue(true);

  const client = {
    downloadTo: jest.fn(),
    downloadToSpecial: jest.fn(),
    _downloadToFile: jest.fn(),
    close: jest.fn(),
    access: jest.fn(),
    connectToSFTPServer: jest.fn(() => Promise.resolve({ connected: true })),
    exists: jest.fn(() => Promise.resolve(true))
  };

  const sftp = {
    Client: jest.fn(),
    connectToSFTPServer: jest.fn(() => Promise.resolve({ connected: true }))
  };

  it('should download caja18 files to a tmp location', async () => {
    const { files, error } = await ftpFileDownloader.downloadFilesFromSFTP({
      client,
      sftp
    });
    expect(error).toBeUndefined();
    expect(files[0]).toMatch(/tmp/);
    expect(files[1]).toMatch(/tmp/);
  });

  it('should download caja18 files with special get a tmp location', async () => {
    const { files, error } = await ftpFileDownloader.downloadFilesFromSFTP({
      client,
      sftp,
      useSpecialGet: true
    });
    expect(error).toBeUndefined();
    expect(files[0]).toMatch(/tmp/);
    expect(files[1]).toMatch(/tmp/);
  });

  it('should download IPS files to a tmp location', async () => {
    client.list = jest
      .fn()
      .mockResolvedValue([
        { name: patprZipFileName },
        { name: papsoeZipFileName },
        { name: brsaludZipFileName }
      ]);
    const {
      patprDirPath,
      papsoDirPath,
      brsaludDirPath,
      downloadError
    } = await ftpFileDownloader.downloadIpsZipFiles({ client, sftp });
    expect(downloadError).toBe(null);
    expect(patprDirPath).toMatch(/tmp/);
    expect(papsoDirPath).toMatch(/tmp/);
    expect(brsaludDirPath).toMatch(/tmp/);
  });

  it('should extract IPS zip files and return the extraction path with correct file name', async () => {
    const extractZip = jest.fn().mockReturnValue('/extraction/path');
    const checkLocalFileExistence = jest.fn().mockReturnValue(true);
    const { files } = await ftpFileDownloader.extractDownloadedIpsZipFiles(
      testZipFile1,
      testZipFile2,
      testZipFile3,
      extractZip,
      checkLocalFileExistence
    );
    const [extractedFile1, extractedFile2] = files;
    expect(extractedFile1).toMatch(/aIII_taps/);
    expect(extractedFile2).toMatch(/papsoe/);
  });

  it('should return null if file does not exist in extracted folder', async () => {
    const extractZip = jest.fn().mockReturnValue('/extraction/path');
    const checkLocalFileExistence = jest.fn().mockReturnValue(false);
    const { files } = await ftpFileDownloader.extractDownloadedIpsZipFiles(
      testZipFile1,
      testZipFile2,
      testZipFile3,
      extractZip,
      checkLocalFileExistence
    );

    expect(files).toStrictEqual([]);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

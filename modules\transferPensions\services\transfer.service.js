/* eslint-disable no-underscore-dangle */
const PensionModel = require('../../../models/pension');
const DiscountsAndAssetModal = require('../../../models/discountsAndAssets');
const TemporaryPensionModel = require('../../linkPensions/models/temporaryPensioner');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const modifyData = require('../../linkPensions/services/modifiers/index');

const service = {
  async transferPensions() {
    await PensionModel.updateMany(
      {},
      {
        $set: {
          'retroactiveConstitution.totalPensionAccrued': 0,
          'retroactiveConstitution.indemnityDiscount': 0,
          'retroactiveConstitution.strennaRetroConstitution': 0,
          'retroactiveConstitution.otherLink': 0,
          'retroactiveConstitution.settlement': 0,
          'retroactiveConstitution.healthDiscountAccrued': 0,
          'retroactiveConstitution.afpDiscountAccrued': 0,
          payBonus: 'SI'
        }
      }
    ).exec();
    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();
      const bulkHistory = PensionHistoricModel.collection.initializeOrderedBulkOp();
      const temporaryPensions = await TemporaryPensionModel.find().lean();

      // eslint-disable-next-line no-restricted-syntax
      for await (const doc of temporaryPensions) {
        const { _id, __v, ...data } = doc;
        const {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut },
          pensionType,
          validityType,
          pensionCodeId
        } = data;

        const modifiedData = {
          ...(await modifyData(data, pensionType, validityType)),
          linkedDate: new Date()
        };

        const discountsAndAssets = await DiscountsAndAssetModal.findOneAndUpdate(
          { beneficiaryRut, causantRut },
          { $set: { beneficiaryRut, causantRut } },
          { upsert: true, new: true }
        );

        const dbPension = await PensionModel.findOne({
          enabled: true,
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          pensionCodeId
        }).lean();

        if (dbPension) {
          const { _id: id, ...pensionData } = dbPension;
          bulkHistory.insert({
            ...pensionData,
            updatedAt: new Date(),
            enabled: false
          });

          bulk
            .find({
              'beneficiary.rut': beneficiaryRut,
              'causant.rut': causantRut,
              enabled: true,
              pensionCodeId
            })
            .updateOne({
              $set: {
                ...pensionData,
                ...modifiedData,
                payBonus: 'SI',
                discountsAndAssets: discountsAndAssets._id,
                updatedAt: new Date()
              }
            });
        } else {
          bulk.insert({
            ...modifiedData,
            payBonus: 'No',
            discountsAndAssets: discountsAndAssets._id,
            oldAgePensionInProcess: 0
          });
        }
      }
      if (bulk.length) await bulk.execute();
      if (bulkHistory.length) await bulkHistory.execute();
      await TemporaryPensionModel.collection.deleteMany();
      await session.commitTransaction();
      return { isError: false, error: null };
    } catch (e) {
      await session.abortTransaction();
      return { isError: true, error: e };
    }
  }
};

module.exports = service;

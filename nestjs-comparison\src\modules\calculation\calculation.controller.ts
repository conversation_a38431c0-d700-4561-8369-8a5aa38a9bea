import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  UseInterceptors,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CalculationService, PensionCalculationResult } from './calculation.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CacheInterceptor } from '../common/interceptors/cache.interceptor';
import { LoggingInterceptor } from '../common/interceptors/logging.interceptor';
import { CalculatePensionDto, BatchCalculationDto } from './dto/calculation.dto';
import { PensionCalculationResponseDto } from './dto/calculation-response.dto';
import { Role } from '../auth/enums/role.enum';

@ApiTags('Pension Calculations')
@ApiBearerAuth()
@Controller('calculations')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(LoggingInterceptor)
export class CalculationController {
  private readonly logger = new Logger(CalculationController.name);

  constructor(private readonly calculationService: CalculationService) {}

  @Post('calculate')
  @Roles(Role.ADMIN, Role.CALCULATOR)
  @ApiOperation({ 
    summary: 'Calcula beneficios y descuentos de una pensión específica',
    description: 'Realiza el cálculo completo de una pensión incluyendo beneficios, descuentos y montos retroactivos'
  })
  @ApiBody({ type: CalculatePensionDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cálculo realizado exitosamente',
    type: PensionCalculationResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Datos de entrada inválidos',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Pensión no encontrada',
  })
  async calculate(
    @Body() calculateDto: CalculatePensionDto,
  ): Promise<PensionCalculationResponseDto> {
    try {
      const result = await this.calculationService.calculatePension(
        calculateDto.pensionId,
      );

      return {
        success: true,
        data: result,
        message: 'Cálculo realizado exitosamente',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error calculating pension ${calculateDto.pensionId}`, error);
      throw error;
    }
  }

  @Post('batch-calculate')
  @Roles(Role.ADMIN, Role.BATCH_PROCESSOR)
  @ApiOperation({ 
    summary: 'Inicia cálculo masivo de pensiones',
    description: 'Procesa múltiples pensiones en lotes para optimizar el rendimiento'
  })
  @ApiBody({ type: BatchCalculationDto })
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description: 'Cálculo masivo iniciado exitosamente',
  })
  async batchCalculate(
    @Body() batchDto: BatchCalculationDto,
  ): Promise<{ success: boolean; batchId: string; totalPensions: number }> {
    try {
      // Obtener IDs de pensiones que cumplen los filtros
      const pensionIds = await this.getPensionIdsByFilters(batchDto);

      if (pensionIds.length === 0) {
        throw new Error('No se encontraron pensiones que cumplan los criterios');
      }

      // Iniciar procesamiento en background
      const batchId = await this.calculationService.startBatchCalculation(
        pensionIds,
        batchDto,
      );

      return {
        success: true,
        batchId,
        totalPensions: pensionIds.length,
      };
    } catch (error) {
      this.logger.error('Error starting batch calculation', error);
      throw error;
    }
  }

  @Get('batch-progress/:batchId')
  @Roles(Role.ADMIN, Role.CALCULATOR, Role.BATCH_PROCESSOR)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Consulta el progreso de un cálculo masivo' })
  @ApiParam({ name: 'batchId', description: 'ID del lote de cálculo' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Progreso del lote obtenido exitosamente',
  })
  async getBatchProgress(@Param('batchId') batchId: string) {
    const progress = await this.calculationService.getBatchProgress(batchId);
    
    if (!progress) {
      throw new Error('Batch no encontrado o expirado');
    }

    return {
      success: true,
      data: progress,
    };
  }

  @Post('recalculate-outdated')
  @Roles(Role.ADMIN)
  @ApiOperation({ 
    summary: 'Recalcula pensiones desactualizadas',
    description: 'Identifica y recalcula pensiones que no han sido actualizadas en los últimos 30 días'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Recálculo iniciado exitosamente',
  })
  async recalculateOutdated(): Promise<{ success: boolean; processed: number }> {
    try {
      const processed = await this.calculationService.recalculateOutdatedPensions();

      return {
        success: true,
        processed,
      };
    } catch (error) {
      this.logger.error('Error recalculating outdated pensions', error);
      throw error;
    }
  }

  @Get('statistics')
  @Roles(Role.ADMIN, Role.VIEWER)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Obtiene estadísticas de cálculos' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Estadísticas obtenidas exitosamente',
  })
  async getStatistics() {
    const stats = await this.calculationService.getCalculationStatistics();

    return {
      success: true,
      data: stats,
    };
  }

  @Post('simulate')
  @Roles(Role.ADMIN, Role.CALCULATOR)
  @ApiOperation({ 
    summary: 'Simula cálculo sin guardar cambios',
    description: 'Permite probar cambios en una pensión sin persistir los resultados'
  })
  @ApiBody({ type: CalculatePensionDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Simulación realizada exitosamente',
  })
  async simulate(
    @Body() simulateDto: CalculatePensionDto & { temporaryChanges?: any },
  ) {
    try {
      const result = await this.calculationService.simulateCalculation(
        simulateDto.pensionId,
        simulateDto.temporaryChanges,
      );

      return {
        success: true,
        data: result,
        message: 'Simulación realizada exitosamente',
      };
    } catch (error) {
      this.logger.error(`Error simulating pension ${simulateDto.pensionId}`, error);
      throw error;
    }
  }

  @Get('validate/:pensionId')
  @Roles(Role.ADMIN, Role.CALCULATOR, Role.VALIDATOR)
  @ApiOperation({ 
    summary: 'Valida cálculos de una pensión',
    description: 'Verifica que los cálculos actuales sean correctos según las reglas vigentes'
  })
  @ApiParam({ name: 'pensionId', description: 'ID de la pensión a validar' })
  async validateCalculation(@Param('pensionId') pensionId: string) {
    try {
      const validation = await this.calculationService.validatePensionCalculation(pensionId);

      return {
        success: true,
        data: validation,
        message: validation.isValid ? 'Cálculo válido' : 'Cálculo requiere corrección',
      };
    } catch (error) {
      this.logger.error(`Error validating pension ${pensionId}`, error);
      throw error;
    }
  }

  // Método auxiliar para obtener IDs por filtros
  private async getPensionIdsByFilters(filters: BatchCalculationDto): Promise<string[]> {
    // Implementación usando Prisma con filtros type-safe
    return [];
  }
}

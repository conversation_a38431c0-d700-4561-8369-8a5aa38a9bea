/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const LiquidationModel = require('../../../models/liquidation');
const service = require('./dbService');
const pensionsData = require('../../../resources/pensions.json');
const UFValueModel = require('../../UFvalue/models/ufValue');

describe('Post Liquidation checkpoint Tests', () => {
  beforeAll(beforeAllTests);

  const ufValueObj = {
    period: `${new Date().getFullYear()}-${new Date().getMonth()}`,
    date: new Date(),
    value: 28990.7
  };

  const pension = {
    ...pensionsData[0],
    discounts: {
      socialCreditsLaAraucana: 5000,
      socialCredits18: 5000,
      socialCreditsLosAndes: 5000,
      othersLosAndes: 5000,
      socialCreditsLosHeroes: 5000,
      othersLosHeroes: 5000
    }
  };

  const liquidation = {
    taxablePension: 200000,
    totalAssets: 80000,
    totalOnePercentDiscounts: 0,
    totalSocialCreditDiscounts: 0,
    totalDiscounts: 60,
    numberOfAssets: 2,
    numberOfDiscounts: 1,
    netPension: 150000,
    enabled: true,
    beneficiaryRut: pensionsData[0].beneficiary.rut,
    causantRut: pensionsData[0].causant.rut,
    pensionCodeId: pensionsData[0].pensionCodeId,
    taxablePensionDate: '2020-06-25T22:23:02.679Z',
    createdAt: '2020-06-25T22:24:02.736Z',
    updatedAt: '2020-06-25T22:24:02.736Z',
    liquidationMonth: 6,
    liquidationYear: 2020
  };

  const pension1 = {
    ...pension,
    beneficiary: {
      ...pension.beneficiary,
      rut: '88888888-8'
    },
    causant: {
      ...pension.causant,
      rut: '99999999-9'
    },
    discounts: {
      socialCreditsLaAraucana: 0,
      socialCredits18: 0,
      socialCreditsLosAndes: 50000,
      othersLosAndes: 0,
      socialCreditsLosHeroes: 0,
      othersLosHeroes: 0
    }
  };

  const liquidation1 = {
    ...liquidation,
    beneficiaryRut: pension1.beneficiary.rut,
    causantRut: pension1.causant.rut,
    taxablePension: 80000,
    netPension: 15000
  };

  const pension2 = {
    ...pension,
    beneficiary: {
      ...pension.beneficiary,
      rut: '12345678-9'
    },
    causant: {
      ...pension.causant,
      rut: '98765432-1'
    },
    discounts: {
      socialCreditsLosAndes: 4000
    }
  };

  const liquidation2 = {
    ...liquidation,
    beneficiaryRut: pension2.beneficiary.rut,
    causantRut: pension2.causant.rut,
    taxablePension: 80000,
    netPension: 15000
  };

  it('should set checkpoint field to true if checkpoint is approved', async () => {
    await PensionModel.create(pension).catch(e => console.error(e));
    await LiquidationModel.create(liquidation).catch(e => console.error(e));
    const { completed, updatedFailedPensions } = await service.updateCheckPointPensions();
    const pensionsCount = await PensionModel.countDocuments();
    const enabledPensions = await PensionModel.find({ enabled: true }).catch(e => console.error(e));
    expect(completed).toBe(true);
    expect(pensionsCount).toBe(1);
    expect(enabledPensions.length).toBe(1);
    expect(updatedFailedPensions.length).toBe(0);
    expect(enabledPensions[0].checkPoint).toBe(true);
  });

  it('should decrease discounts and update pensions if conditions are met', async () => {
    await PensionModel.create(pension1).catch(e => console.error(e));
    await LiquidationModel.create(liquidation1).catch(e => console.error(e));
    const { error, updatedFailedPensions } = await service.updateCheckPointPensions();
    const pensions = await PensionModel.find({}).catch(e => console.error(e));
    const [enabledPension] = pensions.filter(p => p.enabled);
    expect(updatedFailedPensions.length).toBe(1);
    expect(pensions.length).toBe(1);
    expect(enabledPension.discounts.socialCreditsLosAndes).toBe(45000);
    expect(error).toBe(null);
  });

  it('should recalculate net pension value if conditions are met', async () => {
    await PensionModel.create(pension1).catch(e => console.error(e));
    await LiquidationModel.create(liquidation1).catch(e => console.error(e));
    const { error, updatedFailedPensions } = await service.updateCheckPointPensions();
    await UFValueModel.create(ufValueObj).catch(e => console.error(e));
    const recalculateResult = await service.recalculateNetPension(updatedFailedPensions);
    const updatedLiquidation = await LiquidationModel.findOne({
      beneficiaryRut: '88888888-8'
    }).catch(e => console.error(e));
    expect(recalculateResult.error).toBe(null);
    expect(recalculateResult.completed).toBe(true);
    expect(updatedLiquidation.netPension).toBe(29400);
    expect(error).toBe(null);
  });

  it('should reevaluate checkpoint and update pensions if conditions are met', async () => {
    await PensionModel.insertMany([pension1, pension2]).catch(e => console.error(e));
    await LiquidationModel.insertMany([liquidation1, liquidation2]).catch(e => console.error(e));
    const { updatedFailedPensions } = await service.updateCheckPointPensions();
    await UFValueModel.create(ufValueObj).catch(e => console.error(e));
    await service.recalculateNetPension(updatedFailedPensions);
    const [updatedPension] = await service.reevaluateCheckPointPensions();
    const pensionsInBd = await PensionModel.estimatedDocumentCount();
    expect(updatedPension.checkPoint).toBe(false);
    expect(pensionsInBd).toBe(2);
  });

  afterEach(async () => {
    try {
      await PensionModel.deleteMany({}).catch(e => console.error(e));
      await LiquidationModel.deleteMany({}).catch(e => console.error(e));
      await UFValueModel.deleteMany({}).catch(e => console.error(e));
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

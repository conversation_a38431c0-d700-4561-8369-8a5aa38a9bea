/* eslint-disable consistent-return */
const service = require('../services/temporary.service');
const linkService = require('../services/link.service');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger,
  validationResult
}) => {
  //
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    getAll: async (req, res) => {
      const { isError, error, result } = await service.getAll();
      Logger.info('Get temporary pensions: ', req.details);
      if (isError) {
        Logger.error(`Get selection error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    },
    insertAll: async (req, res) => {
      if (await linkService.alreadyLinked()) {
        return res
          .status(HttpStatus.UNAUTHORIZED)
          .json({ error: true, message: 'Este proceso ya se ejecutó en el mes actual.' });
      }
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { isError, error, result } = await service.bulkAndDelete(req.body, service);
      Logger.info('Bulk creation:', req.details);

      if (isError) {
        Logger.error(`Bulk insertion error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.CREATED).json(result);
      }
    }
  };
};

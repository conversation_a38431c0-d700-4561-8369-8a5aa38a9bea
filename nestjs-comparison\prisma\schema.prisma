// Prisma Schema para Sistema de Pensiones
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Pension {
  id                    String   @id @default(cuid())
  pensionCodeId         String   @unique @map("pension_code_id")
  beneficiaryRut        String   @map("beneficiary_rut")
  causantRut           String   @map("causant_rut")
  basePension          Decimal  @map("base_pension") @db.Decimal(12, 2)
  initialBasePension   Decimal  @map("initial_base_pension") @db.Decimal(12, 2)
  pensionType          String   @map("pension_type")
  validityType         String?  @map("validity_type")
  enabled              Boolean  @default(true)
  
  // Artículos y leyes
  article40            Decimal  @default(0) @db.Decimal(12, 2)
  article41            Decimal  @default(0) @db.Decimal(12, 2)
  law19403             Decimal  @default(0) @db.Decimal(12, 2)
  law19539             Decimal  @default(0) @db.Decimal(12, 2)
  law19953             Decimal  @default(0) @db.Decimal(12, 2)
  
  // Información personal
  dateOfBirth          DateTime @map("date_of_birth")
  gender               String   @db.Char(1)
  disabilityDegree     Int      @default(0) @map("disability_degree")
  numberOfCharges      Int      @default(0) @map("number_of_charges")
  familyGroup          Int      @default(1) @map("family_group")
  
  // Fechas importantes
  pensionStartDate     DateTime @map("pension_start_date")
  endDateOfValidity    DateTime? @map("end_date_of_validity")
  
  // Afiliaciones
  afpAffiliation       String?  @map("afp_affiliation")
  healthAffiliation    String?  @map("health_affiliation")
  
  // JSON fields para flexibilidad
  assets               Json?    @default("{}")
  discounts            Json?    @default("{}")
  retroactiveAmounts   Json?    @default("{}") @map("retroactive_amounts")
  reservedAmounts      Json?    @default("{}") @map("reserved_amounts")
  paymentInfo          Json?    @default("{}") @map("payment_info")
  
  // Timestamps
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")
  calculatedAt         DateTime? @map("calculated_at")
  
  // Relaciones
  discountsAndAssets   DiscountsAndAssets?
  liquidations         Liquidation[]
  historics            PensionHistoric[]
  
  @@map("pensions")
  @@index([beneficiaryRut, causantRut])
  @@index([pensionType, enabled, validityType])
  @@index([enabled, updatedAt])
}

model DiscountsAndAssets {
  id                      String @id @default(cuid())
  pensionCodeId           String @unique @map("pension_code_id")
  beneficiaryRut          String @map("beneficiary_rut")
  causantRut             String @map("causant_rut")
  assetsNonFormulable     Json   @default("[]") @map("assets_non_formulable")
  discountsNonFormulable  Json   @default("[]") @map("discounts_non_formulable")
  
  createdAt              DateTime @default(now()) @map("created_at")
  updatedAt              DateTime @updatedAt @map("updated_at")
  
  // Relación
  pension                Pension @relation(fields: [pensionCodeId], references: [pensionCodeId])
  
  @@map("discounts_and_assets")
}

model Liquidation {
  id                        String   @id @default(cuid())
  pensionCodeId             String   @map("pension_code_id")
  beneficiaryRut            String   @map("beneficiary_rut")
  causantRut               String   @map("causant_rut")
  taxablePension           Decimal  @default(0) @map("taxable_pension") @db.Decimal(12, 2)
  totalAssets              Decimal  @default(0) @map("total_assets") @db.Decimal(12, 2)
  totalDiscounts           Decimal  @default(0) @map("total_discounts") @db.Decimal(12, 2)
  netPension               Decimal  @default(0) @map("net_pension") @db.Decimal(12, 2)
  liquidationMonth         Int      @map("liquidation_month")
  liquidationYear          Int      @map("liquidation_year")
  enabled                  Boolean  @default(true)
  
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")
  
  // Relación
  pension                  Pension  @relation(fields: [pensionCodeId], references: [pensionCodeId])
  
  @@map("liquidations")
  @@index([pensionCodeId, enabled])
  @@index([liquidationMonth, liquidationYear])
}

model BasePensionRule {
  id                String    @id @default(cuid())
  name              String
  description       String?
  type              String
  pensionTypes      Json?     @map("pension_types")
  minAmount         Decimal?  @map("min_amount") @db.Decimal(12, 2)
  maxAmount         Decimal?  @map("max_amount") @db.Decimal(12, 2)
  adjustmentFactor  Decimal?  @map("adjustment_factor") @db.Decimal(8, 4)
  adjustmentType    String?   @map("adjustment_type")
  validFrom         DateTime? @map("valid_from")
  validTo           DateTime? @map("valid_to")
  priority          Int       @default(0)
  enabled           Boolean   @default(true)
  conditions        Json?     @default("[]")
  formula           String?
  
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  
  @@map("base_pension_rules")
  @@index([type, enabled])
  @@index([validFrom, validTo])
}

model BonusRule {
  id              String    @id @default(cuid())
  bonusType       String    @map("bonus_type")
  name            String
  description     String?
  minPension      Decimal   @map("min_pension") @db.Decimal(12, 2)
  maxPension      Decimal   @map("max_pension") @db.Decimal(12, 2)
  bonusAmount     Decimal   @map("bonus_amount") @db.Decimal(12, 2)
  calculationMethod String? @map("calculation_method")
  paymentMonths   Json?     @map("payment_months")
  pensionTypes    Json?     @map("pension_types")
  enabled         Boolean   @default(true)
  validFrom       DateTime? @map("valid_from")
  validTo         DateTime? @map("valid_to")
  
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  
  @@map("bonus_rules")
  @@index([bonusType, enabled])
}

model PensionHistoric {
  id              String   @id @default(cuid())
  pensionCodeId   String   @map("pension_code_id")
  changeType      String   @map("change_type")
  previousValues  Json     @map("previous_values")
  newValues       Json     @map("new_values")
  changedBy       String?  @map("changed_by")
  reason          String?
  
  createdAt       DateTime @default(now()) @map("created_at")
  
  // Relación
  pension         Pension  @relation(fields: [pensionCodeId], references: [pensionCodeId])
  
  @@map("pension_historics")
  @@index([pensionCodeId, createdAt])
}

model JobQueue {
  id          String   @id @default(cuid())
  name        String
  data        Json
  status      String   @default("pending")
  attempts    Int      @default(0)
  maxAttempts Int      @default(3) @map("max_attempts")
  error       String?
  processedAt DateTime? @map("processed_at")
  
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  @@map("job_queue")
  @@index([status, createdAt])
}

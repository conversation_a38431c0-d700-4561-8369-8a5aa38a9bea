const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const minimunSalaryModel = require('../../../models/minimunsalarys');
const service = require('./index.services');

describe('Minimun salarys service Test', () => {
  beforeAll(beforeAllTests);

  const imr = {
    name: 'Ingreso minimo remuneracional',
    amount: 120000,
    publishDate: new Date('2021-03-01'),
    validityDate: new Date('2021-02-01')
  };

  minimunSalaryModel.create(imr);

  it('should find one and update', async () => {
    const newDateTest = new Date('2020-01-01');
    const { error } = await service.updateMinimunSalary({
      name: 'Ingreso minimo remuneracional',
      amount: 120000,
      publishDate: new Date('2021-03-01'),
      validityDate: newDateTest
    });
    const { result, isError } = await service.getMinimunSalarys();

    expect(isError).not.toBe(true);
    expect(error).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].validityDate).toStrictEqual(newDateTest);
  });

  it('should dont update', async () => {
    const { error, isError } = await service.updateMinimunSalary({
      name: 'Ingreso minimo remuneracional',
      amount: 120000,
      publishDate: new Date('2021-03-01')
    });

    expect(isError).toBe(true);
    expect(error).toBeDefined();
    expect(error).toBe(
      'minimunsalarys validation failed: validityDate: Path `validityDate` is required.'
    );
  });

  afterAll(afterAllTests);
});

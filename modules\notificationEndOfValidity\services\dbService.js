const moment = require('moment');
const PensionModel = require('../../../models/pension');
const RolesModel = require('../../systems/rolesAndPermissions/models/roles.model');
const UserModel = require('../../../models/user');
const mailService = require('../../sendMail/service/sendMail.service');
const { excelService } = require('./excel.service');
const { pensionFields, simpleFields } = require('../formatters/fields');
const { getMonth } = require('../../sharedFiles/services/helpers.service');

const DEFAULT_FROM_EMAIL = '<EMAIL>';
const PENSION_TYPES = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const ROL_TYPE = [/Administrador/i, /Analista de control pensiones/i, /Jefe de PEC/i];
const NUMBER_OF_MONTHS_VALIDATION = 7;
const { TEMPLATE_ID_CRON_NOTIFICATION } = process.env;

const defaultValue = value => {
  return typeof value === 'undefined' ? '' : value;
};

const subject = `${moment().format('YYYYMM')} Control de Pensiones que están prontos a expirar.`;
const info = {
  name: 'Cron de notificacion de pensionados prontos a expirar',
  month: getMonth(moment().month()),
  year: moment().year(),
  status: 'OK',
  title: 'Se adjunta archivo con pensionados que están prontos a expirar',
  subject
};

const buildMailAttachment = (excelAttachment, sendersList) => {
  const msg = {
    to: sendersList,
    from: DEFAULT_FROM_EMAIL,
    cc: '',
    templateId: TEMPLATE_ID_CRON_NOTIFICATION,
    fromname: 'ACHS-PEC',
    dynamic_template_data: {
      ...info
    }
  };
  const attachments = [
    {
      content: excelAttachment,
      filename: `Proximas Jubilaciones ${moment().format('YYYYMM')}.xlsx`,
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      disposition: 'attachment'
    }
  ];

  return { msg, attachments };
};

const service = {
  async filterPensioners() {
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + NUMBER_OF_MONTHS_VALIDATION);
    return PensionModel.aggregate([
      {
        $match: {
          $and: [
            { pensionType: { $in: PENSION_TYPES } },
            { enabled: true },
            { endDateOfTheoricalValidity: { $lte: endDate } }
          ]
        }
      }
    ]);
  },

  async filteUser(idRoles) {
    try {
      return UserModel.aggregate([
        {
          $addFields: { userRoleId: { $toString: '$role' } }
        },
        {
          $match: {
            userRoleId: { $in: idRoles }
          }
        }
      ]);
    } catch (error) {
      return [];
    }
  },

  async getRoles() {
    try {
      return RolesModel.aggregate([
        {
          $addFields: { rolId: { $toString: '$_id' } }
        },
        {
          $match: {
            roleName: { $in: ROL_TYPE }
          }
        }
      ]);
    } catch (error) {
      return [];
    }
  },

  async destinationList() {
    const filteRoles = await this.getRoles();
    const idRoles = filteRoles.map(rol => {
      return rol.rolId;
    });

    const filteUser = await this.filteUser(idRoles);
    return filteUser.map(user => {
      return user.email;
    });
  },

  async listPensionersEndOfValidity() {
    const pensioners = await this.filterPensioners();

    return pensioners.map(pensioner => {
      const {
        pensionCodeId,
        pensionType,
        endDateOfTheoricalValidity,
        beneficiary,
        collector
      } = pensioner;
      const { name, lastName, mothersLastName, phone, email } = beneficiary;
      const { address, commune, city } = collector;

      return {
        namePensioner: defaultValue(name),
        lastName: defaultValue(lastName),
        mothersLastName: defaultValue(mothersLastName),
        pensionCodeId: defaultValue(pensionCodeId),
        pensionType: defaultValue(pensionType),
        endDateOfTheoricalValidity: defaultValue(endDateOfTheoricalValidity),
        phone: defaultValue(phone),
        email: defaultValue(email),
        address: defaultValue(address),
        commune: defaultValue(commune),
        city: defaultValue(city)
      };
    });
  },

  async sendNotificationEmail() {
    try {
      const sendersList = await this.destinationList();
      const result = await this.listPensionersEndOfValidity();
      const emailList = sendersList.toString();
      const wb = excelService(result, {
        headers: pensionFields,
        simpleFields,
        sheetName: 'Pensionados'
      });

      const excelAttachment = (await wb.writeToBuffer()).toString('base64');
      const { msg, attachments } = buildMailAttachment(excelAttachment, emailList);
      const { error } = await mailService.sendEmail(msg, attachments);
      if (error) throw new Error(error);

      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

const cronMark = 'INACTIVATE_PENSIONS_WORKER';
const dependencyMark = 'DATA_TO_CIVIL_REGISTRATION';
const missingDependencyMsg = `No se ha ejecutado la dependencia ${dependencyMark}`;
const notValid = 'No vigente';
const PENSION_TYPES = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const cronDescription = 'Inactivar por pensiones transitorias pre-worker';
const successMessage = ` El proceso ${cronDescription} se completó correctamente`;
const alreadyExecutedMessage = `El proceso ${cronMark} fue ejecutado para el mes actual`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  linkService,
  transientService,
  axios,
  pensionService,
  transientHelper,
  logService,
  done,
  job
}) => {
  try {
    Logger.info(`Inicio de ejecucion worker por inactivar, Nº 8`);

    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`${cronMark}: dependency verification started...`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDependencyMsg);
      return { message: missingDependencyMsg, status: 'UNAUTHORIZED' };
    }
    const { result } = await linkService.getAllAndFilter({
      validityType: { $not: new RegExp(notValid, 'i') },
      transient: /s[ií]/i,
      pensionType: { $in: PENSION_TYPES },
      manuallyReactivated: { $ne: true },
      inactivateManually: { $ne: true },
      enabled: true
    });

    const {
      pensionsToInactivate,
      pensionsToEvaluate
    } = await transientService.getAllPensionsToInactivate(result, transientHelper, axios, Logger);

    const { inactivationError } = await transientService.createTransientMarkPension(
      pensionsToInactivate
    );
    if (inactivationError) throw new Error(inactivationError);

    const { error } = await pensionService.createUpdatePension(pensionsToEvaluate);
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} proceso terminado`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

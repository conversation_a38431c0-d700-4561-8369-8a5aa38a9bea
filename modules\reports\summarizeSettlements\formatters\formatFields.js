/* eslint-disable no-unused-expressions */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */
const moment = require('moment');

const formatRut = rut =>
  rut
    .replace(/[^\dkK-]/g, '')
    .replace(/^(\d{1,2})(\d{3})(\d{3})-([0-9kK])$/, '$1.$2.$3-$4')
    .toUpperCase();

const getField = (path, data) =>
  path.split('.').reduce((object, nestedField) => (object && object[nestedField]) || null, data);

const appendSimpleFields = (fieldDetails, fields, data) => {
  return fields.reduce((obj, key, index) => {
    obj[key] = getField(fieldDetails[index][key].path, data);
    return obj;
  }, {});
};

const appendFullName = (
  data,
  {
    pension: {
      beneficiary: { name, lastName, mothersLastName = '' }
    }
  }
) => {
  const fullname = `${name} ${lastName} ${mothersLastName}`.trim();
  return { ...data, fullname };
};

const formattingRut = data => {
  if (!data.beneficiaryRut) return data;
  return { ...data, beneficiaryRut: formatRut(data.beneficiaryRut) };
};

const formatter = {
  normal: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).string(value || ''),
  number: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).number(+value || 0),
  currency: ({ sheet, currentRow, currentCol, value }) =>
    sheet
      .cell(currentRow, currentCol)
      .number(value || 0)
      .style({
        numberFormat: '$#,##0; -$#,##0; $0;'
      }),
  date: ({ sheet, currentRow, currentCol, value }) => {
    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(value)
          .style({ numberFormat: 'dd-mm-yyyy' })
      : sheet.cell(currentRow, currentCol).string('');
  },
  year: ({ sheet, currentRow, currentCol, value }) => {
    const date = moment(new Date(value)).format('YYYY-MM');

    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(date)
          .style({ numberFormat: 'yyyy' })
      : sheet.cell(currentRow, currentCol).string('');
  },
  month: ({ sheet, currentRow, currentCol, value }) => {
    const date = moment(new Date(value)).format('YYYY-MM');
    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(date)
          .style({ numberFormat: 'mm' })
      : sheet.cell(currentRow, currentCol).string('');
  }
};

const dynamicFieldsDefinition = {
  forNetTotalNonFormulableAssetsByReason: {
    namePrefix: 'Monto retroactivo por haber no formulable líquido',
    path: 'pension.retroactiveAmounts.forNetTotalNonFormulableAssetsByReason'
  },
  forTaxableTotalNonFormulableAssetsByReason: {
    namePrefix: 'Monto retroactivo imponible por haber no formulable',
    path: 'pension.retroactiveAmounts.forTaxableTotalNonFormulableAssetsByReason'
  },
  forTotalNonFormulableDiscountsByReason: {
    namePrefix: 'Monto retroactivo por descuento total no formulable',
    path: 'pension.retroactiveAmounts.forTotalNonFormulableDiscountsByReason'
  },
  assetsTaxableNonFormulable: {
    namePrefix: 'Haber no formulable imponible',
    path: 'pension.assets.taxableNonFormulableByReason'
  },
  assetsNetNonFormulable: {
    namePrefix: 'Haber no formulable líquido',
    path: 'pension.assets.netNonFormulableByReason'
  },
  discountsNonFormulable: {
    namePrefix: 'Descuento no formulable',
    path: 'pension.discounts.nonFormulableByReason'
  }
};

const generateDynamicObjectDetails = (field = '', reasons = []) => {
  try {
    const arrayOfFields = reasons.map(reason => {
      const obj = {};
      const objProperty = dynamicFieldsDefinition[field].path.replace(/\./g, '');
      obj[`${objProperty}${reason}`] = {
        name: `${dynamicFieldsDefinition[field].namePrefix} ${reason}`,
        path: dynamicFieldsDefinition[field].path,
        reason,
        format: formatter.currency
      };
      return obj;
    });
    return arrayOfFields;
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(`error at getting path for dynamic fields`);
    return [];
  }
};

const listOfDynamicFields = Object.keys(dynamicFieldsDefinition);
const flattenReasons = fields => fields.reduce((acc, curr) => [...acc, ...curr], []);
const getDynamicField = (path, data) =>
  path.split('.').reduce((object, nestedField) => (object && object[nestedField]) || null, data) ||
  [];

const getReasonsForField = (field, data = []) => {
  const reasonsForField =
    flattenReasons(
      data.map(x => getDynamicField(dynamicFieldsDefinition[field].path, x).map(y => y.reason))
    ) || [];
  const uniqueFields = reasonsForField.reduce((obj, currReason) => {
    obj[`${currReason}`] = 1;
    return obj;
  }, {});
  return Object.keys(uniqueFields);
};

const dynamicObjFields = (listOfFields, data = []) =>
  flattenReasons(
    listOfFields.map(field => generateDynamicObjectDetails(field, getReasonsForField(field, data)))
  );

const specificValueForRecord = (data, path, reasonToFind) => {
  const record = getDynamicField(path, data).filter(({ reason }) => reason === reasonToFind)[0];
  const valueFromRecord = record ? record.amount : 0;
  return valueFromRecord;
};

const appendDynamicFields = (fields, data) => {
  return fields.reduce((obj, field) => {
    const key = Object.keys(field)[0];
    obj[key] = specificValueForRecord(data, field[key].path, field[key].reason);
    return obj;
  }, data);
};

const filterData = (fieldDetails, fields, listDynamicFields, dataSets = []) => {
  if (dataSets.length === 0) return [];

  const dataWithFullname = dataSets.map(data => appendFullName(data, data));
  const simpleData = dataWithFullname.map(data => appendSimpleFields(fieldDetails, fields, data));
  const formattedFields = simpleData.map(data => formattingRut(data));

  const dynamicFields = dynamicObjFields(listDynamicFields, dataSets);
  const dataWithDynamicFields = dataWithFullname.map(data =>
    appendDynamicFields(dynamicFields, data)
  );

  const mergeFixedWithDynamic = formattedFields.map((data, index) => ({
    ...dataWithDynamicFields[index],
    ...data
  }));

  return { processedData: mergeFixedWithDynamic, excelFields: [...fieldDetails, ...dynamicFields] };
};

module.exports = { filterData, formatter, generateDynamicObjectDetails, listOfDynamicFields };

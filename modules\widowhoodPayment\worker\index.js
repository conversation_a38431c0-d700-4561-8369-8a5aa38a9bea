const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/widowhood.pensions.services');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'widowhoodPayment',
  worker: deps => workerModule.workerFn({ service, pensionService, logService, ...deps }),
  repeatInterval: process.env.CRON_WIDOWHOOD_PAYMENT,
  description: 'Pago de bono matrimonio a viudas',
  endPoint: 'widowhoodpayment',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependency
};

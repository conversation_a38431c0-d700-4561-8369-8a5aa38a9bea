const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const mailService = require('../../sendMail/service/sendMail.service');

module.exports = {
  name: 'not-approved-checpoint',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      mailService,
      ...deps
    }),
  repeatInterval: process.env.CRON_NOT_APPROVED_CHECKPOINT,
  description:
    'Envía correo con excel adjunto con los pensionados que no cumplieron pto de control',
  endPoint: 'notapprovedcheckpoint',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

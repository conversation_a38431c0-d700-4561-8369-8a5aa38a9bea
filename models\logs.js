const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const LogsSchema = new Schema(
  {
    meta: { type: Object },
    message: { type: String, required: true },
    level: { type: String, required: true }
  },
  { timestamps: true }
);

LogsSchema.plugin(paginate);
LogsSchema.index({ createdAt: 1 }, { expires: '180d' });
LogsSchema.index({ message: 'text' });

module.exports = mongoose.model('logs', LogsSchema);

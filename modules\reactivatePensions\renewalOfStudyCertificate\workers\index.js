/* eslint-disable consistent-return */
const logService = require('../../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');

const workerModule = require('./worker');

module.exports = {
  name: 'renewalOfStudyCertificate',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  description: 'Reactivar pensiones de viudez por certificados de estudio vigentes de las cargas',
  endPoint: 'reactivateforrenewalofstudycertificate',
  cronMark: workerModule.markOfCron,
  dependencyMark: workerModule.dependencyMark
};

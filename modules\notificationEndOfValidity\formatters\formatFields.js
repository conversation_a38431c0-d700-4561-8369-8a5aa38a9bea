/* eslint-disable no-unused-expressions */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */

const getField = (path, data) =>
  path.split('.').reduce((object, nestedField) => object && object[nestedField], data);
const formatNationalId = rut =>
  rut
    .replace(/[^\dkK-]/g, '')
    .replace(/^(\d{1,2})(\d{3})(\d{3})-([0-9kK])$/, '$1.$2.$3-$4')
    .toUpperCase();

const formattingNationalId = data => {
  if (!data.beneficiaryRut) return data;
  return { ...data, beneficiaryRut: formatNationalId(data.beneficiaryRut) };
};
const appendSimpleFields = (fieldDetails, fields, data) => {
  return fields.reduce((obj, key, index) => {
    obj[key] = getField(fieldDetails[index][key].path, data);
    return obj;
  }, {});
};

const formatter = {
  date: ({ sheet, currentRow, currentCol, value }) => {
    value
      ? sheet
          .cell(currentRow, currentCol)
          .date(value)
          .style({ numberFormat: 'dd-mm-yyyy' })
      : sheet.cell(currentRow, currentCol).string('');
  },
  normal: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).string(value || ''),

  number: ({ sheet, currentRow, currentCol, value }) =>
    sheet.cell(currentRow, currentCol).number(+value || 0)
};

const filterData = (fieldDetails, fields, dataSets = []) => {
  if (dataSets.length === 0) return [];
  const fullData = dataSets;
  const simpleData = fullData.map(data => appendSimpleFields(fieldDetails, fields, data));
  const formattedFields = simpleData.map(data => formattingNationalId(data));

  const mergeFixedWithDynamic = formattedFields.map(data => ({
    ...data
  }));

  return { processedData: mergeFixedWithDynamic, excelFields: [...fieldDetails] };
};

module.exports = { filterData, formatter };

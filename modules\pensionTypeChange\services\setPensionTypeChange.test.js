/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const pensionData2 = require('../../../resources/PensionTypeChange.json');
const service = require('./setPensionTypeChange');
const pensionService = require('../../pensions/services/pension.service');
const orphanhoodData = require('../../../resources/orphanhood.json');
const TemporaryHorphanhoodModel = require('../../orphanhood/models/temporaryHorphanhood');

describe('Pension Types Changes service', () => {
  beforeAll(beforeAllTests);
  it('debe obtener pensiones y actualizar el tipo de pensión a con hijos para validez igual true', async () => {
    const orphanhoodUno = {
      ...orphanhoodData[0],
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19007761-6',
      motherRut: '14285700-6',
      validity: true
    };

    const orphanhoodDos = {
      ...orphanhoodData[1],
      motherRut: '14285700-6',
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19997619-2',
      validity: true
    };

    await TemporaryHorphanhoodModel.insertMany([orphanhoodUno, orphanhoodDos]);
    const pension = {
      ...pensionData2[0],
      pensionType: 'Pension de viudez sin hijos',
      validityType: 'Vigente Vitalicia'
    };

    await PensionModel.insertMany([pension, pensionData2[1], pensionData2[2]]);
    const { completed, error } = await service.pensionTypesToChange(pensionService);
    const updatedPension = await PensionModel.findOne({
      'beneficiary.rut': '14285700-6',
      'causant.rut': '11763389-6',
      pensionCodeId: '303349',
      enabled: true
    }).lean();

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.pensionType).toBe('Pensión de viudez con hijos');
  });

  it('actualizar el tipo de pensión a con hijos para validez igual false fecha fecha de fin de vigencia teórica de la orfandad mayor al último día del mes anterior', async () => {
    const orphanhoodUno = {
      ...orphanhoodData[0],
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19007761-6',
      motherRut: '14285700-6',
      validity: false
    };

    const orphanhoodDos = {
      ...orphanhoodData[1],
      motherRut: '14285700-6',
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19997619-2',
      validity: false
    };

    await TemporaryHorphanhoodModel.insertMany([orphanhoodUno, orphanhoodDos]);
    const pension = {
      ...pensionData2[0],
      pensionType: 'Pension de viudez sin hijos',
      validityType: 'Vigente Vitalicia'
    };

    const pensionDos = {
      ...pensionData2[1],
      endDateOfTheoricalValidity: '2099-01-01T04:42:46.000Z'
    };

    await PensionModel.insertMany([pension, pensionDos, pensionData2[2]]);
    const { completed, error } = await service.pensionTypesToChange(pensionService);
    const updatedPension = await PensionModel.findOne({
      'beneficiary.rut': '14285700-6',
      'causant.rut': '11763389-6',
      pensionCodeId: '303349',
      enabled: true
    }).lean();

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.pensionType).toBe('Pensión de viudez con hijos');
  });

  it('debe obtener pensiones y actualizar el tipo de pensión a filiación no matrimonial con hijos para validez igual true', async () => {
    const orphanhoodUno = {
      ...orphanhoodData[0],
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19007761-6',
      motherRut: '14285700-6',
      validity: true
    };

    const orphanhoodDos = {
      ...orphanhoodData[1],
      motherRut: '14285700-6',
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19997619-2',
      validity: true
    };

    await TemporaryHorphanhoodModel.insertMany([orphanhoodUno, orphanhoodDos]);
    const pension = {
      ...pensionData2[0],
      pensionType: 'Pension de madre de hijo de filiacion no matrimonial sin hijos',
      validityType: 'Vigente Vitalicia'
    };

    await PensionModel.insertMany([pension, pensionData2[1], pensionData2[2]]);
    const { completed, error } = await service.pensionTypesToChange(pensionService);
    const updatedPension = await PensionModel.findOne({
      'beneficiary.rut': '14285700-6',
      'causant.rut': '11763389-6',
      pensionCodeId: '303349',
      enabled: true
    }).lean();

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.pensionType).toBe(
      'Pensión de madre de hijo de filiación no matrimonial con hijos'
    );
  });

  it('actualizar el tipo de pensión a sin hijos para fecha de fin de vigencia teórica menor o igual al fin del mes actual', async () => {
    const orphanhoodUno = {
      ...orphanhoodData[0],
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19007761-6',
      motherRut: '14285700-6',
      validity: false
    };

    const orphanhoodDos = {
      ...orphanhoodData[1],
      motherRut: '14285700-6',
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19997619-2',
      validity: false
    };

    await TemporaryHorphanhoodModel.insertMany([orphanhoodUno, orphanhoodDos]);
    const pension = {
      ...pensionData2[0],
      pensionType: 'Pension de viudez con hijos',
      validityType: 'Vigente Vitalicia'
    };

    const pensionDos = {
      ...pensionData2[1],
      endDateOfTheoricalValidity: '2022-05-29T04:42:46.000Z'
    };

    await PensionModel.insertMany([pension, pensionDos, pensionData2[2]]);
    const { completed, error } = await service.pensionTypesToChange(pensionService);
    const updatedPension = await PensionModel.findOne({
      'beneficiary.rut': '14285700-6',
      'causant.rut': '11763389-6',
      pensionCodeId: '303349',
      enabled: true
    }).lean();

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.pensionType).toBe('Pensión de viudez sin hijos');
  });

  it('pensionType no encontrado no debe modificarse', async () => {
    const orphanhoodUno = {
      ...orphanhoodData[0],
      pensionId: 303349,
      causantRut: '11763389-6',
      collectorRut: '14285700-6',
      beneficiaryRut: '19007761-6',
      motherRut: '14285701-6',
      validity: false
    };

    await TemporaryHorphanhoodModel.insertMany([orphanhoodUno]);
    const pension = {
      ...pensionData2[0],
      pensionType: 'Pension de viudez con hijos',
      validityType: 'Vigente Vitalicia'
    };

    await PensionModel.insertMany([pension, pensionData2[1], pensionData2[2]]);
    const { completed, error } = await service.pensionTypesToChange(pensionService);
    const updatedPension = await PensionModel.findOne({
      'beneficiary.rut': '14285700-6',
      'causant.rut': '11763389-6',
      pensionCodeId: '303349',
      enabled: true
    }).lean();

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.pensionType).toBe('Pension de viudez con hijos');
  });
  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.log(err));
    await TemporaryHorphanhoodModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const UpdatePensionerInfo = require('../models/UpdatePensionerInfo');
const UpdatePensionType = require('../models/UpdatePensionType');

const pensionData = require('../../../resources/queryPensions.json');
const { transferPensionerData, deleteTemporalData } = require('./transferPensionerInfo.services');
const service = require('./transferPensionerInfo.services');

describe('transfer of pensioner data from temporal tables to pensioner table', () => {
  beforeAll(beforeAllTests);
  beforeEach(async () => {});

  it('should get pensioner with fields updated', async done => {
    const pensioner1 = {
      ...pensionData,
      beneficiary: {
        ...pensionData.beneficiary,
        rut: '********-1'
      },
      causant: {
        ...pensionData.causant,
        rut: '********-1'
      },
      collector: {
        ...pensionData.collector,
        rut: '********-1'
      }
    };

    const pensionerWithNoModification = {
      ...pensionData,
      beneficiary: {
        ...pensionData.beneficiary,
        rut: '12345678-9'
      },
      causant: {
        ...pensionData.causant,
        rut: '12345678-9'
      },
      collector: {
        ...pensionData.collector,
        rut: '12345678-9'
      }
    };

    const temporalPensionerInfo1 = {
      beneficiaryRut: '********-1',
      causantRut: '********-1',
      pensionCodeId: '13136',
      beneficiaryEmail: '<EMAIL>',
      beneficiaryPhone: '*********',
      collectorRut: '9999999-9',
      collectorName: 'pedro pablo',
      collectorLastName: 'von cuicos',
      collectorMothersLastName: 'del rosal',
      collectorAddress: 'a brand new address',
      collectorCommune: 'lo barnetchea',
      collectorCity: 'santiago',
      cun: 'What is cun? baby dont hurt me',
      paymentGateway: 'bancaria',
      bank: 'scotiabank',
      branchOffice: 'none',
      accountNumber: '24500-03',
      institutionalPatient: true,
      afpAffiliation: 'capital',
      accidentDate: '2000-01-01T04:00:00.000Z',
      country: 'DE'
    };

    const temporalPensionType1 = {
      beneficiaryRut: '********-1',
      causantRut: '********-1',
      pensionCodeId: '13136',
      pensionType: 'an incredibly new pensionType',
      ChangeOfPensionTypeDueToCharges: true
    };

    await PensionModel.insertMany([pensioner1, pensionerWithNoModification]).catch(err =>
      console.error(err)
    );
    await UpdatePensionerInfo.insertMany([temporalPensionerInfo1]).catch(err => console.error(err));
    await UpdatePensionType.insertMany([temporalPensionType1]).catch(err => console.error(err));

    const { pensionersToUpdate, error } = await transferPensionerData().catch(err =>
      console.log(err)
    );

    expect(error).toBeUndefined();
    expect(pensionersToUpdate.length).toBe(1);

    expect(pensionersToUpdate[0].pensionType).toBe('an incredibly new pensionType');
    expect(pensionersToUpdate[0].ChangeOfPensionTypeDueToCharges).toBe(true);

    expect(pensionersToUpdate[0].beneficiary.email).toBe('<EMAIL>');
    expect(pensionersToUpdate[0].beneficiary.phone).toBe('*********');

    expect(pensionersToUpdate[0].collector.rut).toBe('9999999-9');
    expect(pensionersToUpdate[0].collector.name).toBe('pedro pablo');
    expect(pensionersToUpdate[0].collector.lastName).toBe('von cuicos');
    expect(pensionersToUpdate[0].collector.mothersLastName).toBe('del rosal');
    expect(pensionersToUpdate[0].collector.address).toBe('a brand new address');
    expect(pensionersToUpdate[0].collector.commune).toBe('lo barnetchea');
    expect(pensionersToUpdate[0].collector.city).toBe('santiago');

    expect(pensionersToUpdate[0].paymentInfo.paymentGateway).toBe('bancaria');
    expect(pensionersToUpdate[0].paymentInfo.accountNumber).toBe('24500-03');
    expect(pensionersToUpdate[0].paymentInfo.bank).toBe('scotiabank');
    expect(pensionersToUpdate[0].paymentInfo.branchOffice).toBe('none');

    expect(pensionersToUpdate[0].institutionalPatient).toBe(true);
    expect(pensionersToUpdate[0].afpAffiliation).toBe('capital');
    expect(pensionersToUpdate[0].accidentDate).toStrictEqual(new Date('2000-01-01T04:00:00.000Z'));
    expect(pensionersToUpdate[0].country).toBe('DE');
    expect(pensionersToUpdate[0].cun).toBe('What is cun? baby dont hurt me');

    done();
  });

  it('should get pensioner with only a change in pensionType', async done => {
    const pensioner1 = {
      ...pensionData,
      beneficiary: {
        ...pensionData.beneficiary,
        rut: '********-1'
      },
      causant: {
        ...pensionData.causant,
        rut: '********-1'
      },
      collector: {
        ...pensionData.collector,
        rut: '********-1'
      }
    };

    const temporalPensionType1 = {
      beneficiaryRut: '********-1',
      causantRut: '********-1',
      pensionCodeId: '13136',
      pensionType: 'an incredibly new pensionType',
      ChangeOfPensionTypeDueToCharges: true
    };

    await PensionModel.insertMany([pensioner1]).catch(err => console.error(err));
    await UpdatePensionType.insertMany([temporalPensionType1]).catch(err => console.error(err));

    const { pensionersToUpdate, error } = await transferPensionerData().catch(err =>
      console.log(err)
    );

    expect(error).toBeUndefined();
    expect(pensionersToUpdate.length).toBe(1);

    expect(pensionersToUpdate[0].pensionType).toBe('an incredibly new pensionType');
    expect(pensionersToUpdate[0].ChangeOfPensionTypeDueToCharges).toBe(true);

    done();
  });

  it('should get pensioner only with paymentInfo updated', async done => {
    const pensioner1 = {
      ...pensionData,
      beneficiary: {
        ...pensionData.beneficiary,
        rut: '********-1'
      },
      causant: {
        ...pensionData.causant,
        rut: '********-1'
      },
      collector: {
        ...pensionData.collector,
        rut: '********-1'
      }
    };

    const temporalPensionerInfo1 = {
      beneficiaryRut: '********-1',
      causantRut: '********-1',
      pensionCodeId: '13136',
      paymentGateway: 'bancaria',
      bank: 'scotiabank',
      branchOffice: 'none',
      accountNumber: '24500-03'
    };

    await PensionModel.insertMany([pensioner1]).catch(err => console.error(err));
    await UpdatePensionerInfo.insertMany([temporalPensionerInfo1]).catch(err => console.error(err));

    const { pensionersToUpdate, error } = await transferPensionerData().catch(err =>
      console.log(err)
    );

    expect(error).toBeUndefined();
    expect(pensionersToUpdate.length).toBe(1);

    expect(pensionersToUpdate[0].paymentInfo.paymentGateway).toBe('bancaria');
    expect(pensionersToUpdate[0].paymentInfo.accountNumber).toBe('24500-03');
    expect(pensionersToUpdate[0].paymentInfo.bank).toBe('scotiabank');
    expect(pensionersToUpdate[0].paymentInfo.branchOffice).toBe('none');

    done();
  });

  it('should fail at finding pensioners', async done => {
    jest
      .spyOn(service, 'transferPensionerData')
      .mockImplementationOnce(() => Promise.reject(new Error('prueba error')));

    const { pensionersToUpdate, error } = await service
      .transferPensionerData()
      .catch(err => ({ error: err }));

    expect(error).toBeDefined();
    expect(pensionersToUpdate).toBeUndefined();

    done();
  });

  it('should delete all the info in temporal tables', async done => {
    const Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    await deleteTemporalData(Logger).catch(err => console.log(err));

    expect(Logger.error).toHaveBeenCalledTimes(0);

    done();
  });

  it('should fail at deleting all the info in temporal tables', async done => {
    jest
      .spyOn(UpdatePensionerInfo, 'deleteMany')
      .mockImplementationOnce(() => Promise.reject(new Error('error')));
    jest
      .spyOn(UpdatePensionType, 'deleteMany')
      .mockImplementationOnce(() => Promise.reject(new Error('error')));
    const Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    await deleteTemporalData(Logger).catch(err => console.log(err));

    expect(Logger.error).toHaveBeenCalledTimes(2);

    done();
  });

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await UpdatePensionerInfo.deleteMany({}).catch(err => console.error(err));
    await UpdatePensionType.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

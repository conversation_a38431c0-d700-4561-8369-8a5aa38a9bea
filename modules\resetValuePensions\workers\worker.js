const cronMark = 'SET_VALUES_TO_ZERO';
const cronDependency = 'CALCULATE_RETROACTIVE_AMOUNT_BY_REJECTED';

const cronDescription = 'set values to zero:';
const alreadyExecutedMessage = `El proceso ${cronMark} fue ejecutado para el mes actual`;
const successMessage = ` El proceso ${cronDescription} se completó correctamente`;
const getMissingDependencyMessage = `No se ha ejecutado la dependencia ${cronDependency}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const workerFn = async ({ Logger, pensionService, service, logService, done, job }) => {
  try {
    Logger.info('set values to zero: revisando ejecucion dependencia cron transfer pensions');
    if (!(await logService.existsLog(cronDependency))) {
      Logger.info(`Fin proceso: Dependencia cron  ${cronDependency} aun no ejecutado.`);
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`Inicio procesamiento cron SetValuesToZero`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    const { completed, err } = await service.resetPensionValues(pensionService);
    if (completed && !err) {
      await logService.saveLog(cronMark);
    }

    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, cronDependency, workerFn };

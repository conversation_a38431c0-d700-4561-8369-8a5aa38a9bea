/* eslint-disable no-console */
// const { codeFormatter } = require('../validators/validator');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  motiveService,
  validationResult,
  Logger
}) => {
  return {
    updateMotive: async (req, res) => {
      Logger.info('Add or Update nomenclator motive: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { result, isError, error } = await motiveService.updateMotive(req.body.motive);
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator motive successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    createMotive: async (req, res) => {
      Logger.info('create nomenclator Motive: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { result, isError, error } = await motiveService.createMotive(req.body.motive);
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Motive successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    deleteMotive: async (req, res) => {
      Logger.info('Delete nomenclator Motive: ', req.params.id);
      const { result, isError, error } = await motiveService.deleteMotive(req.params.id);
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Delete nomenclator motive successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    getMotives: async (req, res) => {
      Logger.info('Get all Motives: ');
      const { result } = await motiveService.getMotives();
      res.status(HttpStatus.OK).json({ result });
    }
  };
};

import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiConsumes,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FilesInterceptor } from '@nestjs/platform-express';
import { DeathManagementService, DeathNotificationData } from '../services/death-management.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/role.enum';

@ApiTags('Death Management')
@ApiBearerAuth()
@Controller('death-management')
@UseGuards(JwtAuthGuard, RolesGuard)
export class DeathManagementController {

  constructor(
    private readonly deathManagementService: DeathManagementService
  ) {}

  @Post('notify-death')
  @Roles(Role.ADMIN, Role.DEATH_PROCESSOR)
  @ApiOperation({ 
    summary: 'Notifica fallecimiento de pensionado',
    description: 'Inicia el proceso de fallecimiento, suspende pagos y crea notificación para verificación'
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('documents'))
  async notifyDeath(
    @Body() notificationData: any,
    @UploadedFiles() documents: Express.Multer.File[]
  ) {
    try {
      const deathData: DeathNotificationData = {
        ...notificationData,
        deathDate: new Date(notificationData.deathDate),
        documents: documents.map(doc => ({
          filename: doc.filename,
          originalName: doc.originalname,
          size: doc.size,
          mimetype: doc.mimetype,
          path: doc.path
        }))
      };

      const notificationId = await this.deathManagementService.processDeathNotification(deathData);

      return {
        success: true,
        data: { notificationId },
        message: 'Notificación de fallecimiento procesada. Pagos suspendidos automáticamente.'
      };

    } catch (error) {
      return {
        success: false,
        message: `Error procesando notificación: ${error.message}`
      };
    }
  }

  @Put('verify-death/:notificationId')
  @Roles(Role.ADMIN, Role.DEATH_VERIFIER)
  @ApiOperation({ 
    summary: 'Verifica y procesa fallecimiento',
    description: 'Verifica documentos y ejecuta proceso de transición de pensión'
  })
  @ApiParam({ name: 'notificationId', description: 'ID de la notificación de fallecimiento' })
  async verifyDeath(
    @Param('notificationId') notificationId: string,
    @Body() verificationData: { verifiedBy: string; observations?: string }
  ) {
    try {
      await this.deathManagementService.verifyAndProcessDeath(
        notificationId, 
        verificationData.verifiedBy
      );

      return {
        success: true,
        message: 'Fallecimiento verificado y proceso iniciado exitosamente'
      };

    } catch (error) {
      return {
        success: false,
        message: `Error verificando fallecimiento: ${error.message}`
      };
    }
  }

  @Get('notifications')
  @Roles(Role.ADMIN, Role.DEATH_PROCESSOR, Role.DEATH_VERIFIER)
  @ApiOperation({ summary: 'Obtiene notificaciones de fallecimiento pendientes' })
  @ApiResponse({ status: HttpStatus.OK, description: 'Lista de notificaciones obtenida' })
  async getPendingNotifications(
    @Query('status') status?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50
  ) {
    const where: any = {};
    if (status) where.status = status;

    const notifications = await this.prisma.deathNotification.findMany({
      where,
      include: {
        pension: {
          select: {
            pensionCodeId: true,
            beneficiaryRut: true,
            pensionType: true,
            basePension: true
          }
        }
      },
      orderBy: { notificationDate: 'desc' },
      skip: (page - 1) * limit,
      take: limit
    });

    const total = await this.prisma.deathNotification.count({ where });

    return {
      success: true,
      data: notifications,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  @Get('process/:processId')
  @Roles(Role.ADMIN, Role.DEATH_PROCESSOR, Role.VIEWER)
  @ApiOperation({ summary: 'Obtiene detalles de proceso de fallecimiento' })
  async getDeathProcess(@Param('processId') processId: string) {
    const process = await this.prisma.deathProcess.findUniqueOrThrow({
      where: { id: processId },
      include: {
        deathNotification: {
          include: {
            pension: true
          }
        }
      }
    });

    return {
      success: true,
      data: process
    };
  }

  @Get('survival-pensions')
  @Roles(Role.ADMIN, Role.DEATH_PROCESSOR, Role.VIEWER)
  @ApiOperation({ summary: 'Obtiene pensiones de supervivencia creadas' })
  async getSurvivalPensions(
    @Query('status') status?: string,
    @Query('originalPensionId') originalPensionId?: string
  ) {
    const where: any = {};
    if (status) where.status = status;
    if (originalPensionId) where.originalPensionId = originalPensionId;

    const survivalPensions = await this.prisma.survivalPension.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    return {
      success: true,
      data: survivalPensions
    };
  }

  @Put('survival-pension/:id/approve')
  @Roles(Role.ADMIN, Role.DEATH_PROCESSOR)
  @ApiOperation({ 
    summary: 'Aprueba pensión de supervivencia',
    description: 'Aprueba documentos y activa nueva pensión de supervivencia'
  })
  async approveSurvivalPension(
    @Param('id') survivalPensionId: string,
    @Body() approvalData: { approvedBy: string; observations?: string }
  ) {
    try {
      // Aprobar pensión de supervivencia
      const survivalPension = await this.prisma.survivalPension.update({
        where: { id: survivalPensionId },
        data: {
          status: 'APPROVED',
          approvedAt: new Date(),
          approvedBy: approvalData.approvedBy
        }
      });

      // Activar pensión en el sistema principal
      await this.prisma.pension.update({
        where: { pensionCodeId: survivalPension.newPensionCodeId },
        data: {
          enabled: true,
          updatedAt: new Date()
        }
      });

      return {
        success: true,
        data: survivalPension,
        message: 'Pensión de supervivencia aprobada y activada'
      };

    } catch (error) {
      return {
        success: false,
        message: `Error aprobando pensión: ${error.message}`
      };
    }
  }

  @Get('suspended-payments')
  @Roles(Role.ADMIN, Role.PAYMENT_PROCESSOR)
  @ApiOperation({ summary: 'Obtiene pagos suspendidos por fallecimiento' })
  async getSuspendedPayments(
    @Query('pensionCodeId') pensionCodeId?: string,
    @Query('dateFrom') dateFrom?: string,
    @Query('dateTo') dateTo?: string
  ) {
    const where: any = { suspensionReason: 'DEATH' };
    
    if (pensionCodeId) where.pensionCodeId = pensionCodeId;
    if (dateFrom) where.suspensionDate = { gte: new Date(dateFrom) };
    if (dateTo) where.suspensionDate = { ...where.suspensionDate, lte: new Date(dateTo) };

    const suspensions = await this.prisma.paymentSuspension.findMany({
      where,
      orderBy: { suspensionDate: 'desc' }
    });

    return {
      success: true,
      data: suspensions
    };
  }

  @Get('refund-processes')
  @Roles(Role.ADMIN, Role.REFUND_PROCESSOR)
  @ApiOperation({ summary: 'Obtiene procesos de reembolso por fallecimiento' })
  async getRefundProcesses(
    @Query('status') status?: string,
    @Query('pensionCodeId') pensionCodeId?: string
  ) {
    const where: any = { refundReason: 'OVERPAYMENT_DEATH' };
    
    if (status) where.recoveryStatus = status;
    if (pensionCodeId) where.pensionCodeId = pensionCodeId;

    const refunds = await this.prisma.refundProcess.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    });

    return {
      success: true,
      data: refunds
    };
  }

  @Put('refund/:id/process')
  @Roles(Role.ADMIN, Role.REFUND_PROCESSOR)
  @ApiOperation({ summary: 'Procesa recuperación de sobrepago' })
  async processRefund(
    @Param('id') refundId: string,
    @Body() refundData: { 
      recoveryMethod: string; 
      recoveredAmount: number; 
      processedBy: string 
    }
  ) {
    try {
      const refund = await this.prisma.refundProcess.update({
        where: { id: refundId },
        data: {
          recoveryMethod: refundData.recoveryMethod,
          recoveredAmount: refundData.recoveredAmount,
          recoveryStatus: 'COMPLETED',
          recoveredAt: new Date()
        }
      });

      return {
        success: true,
        data: refund,
        message: 'Proceso de reembolso completado'
      };

    } catch (error) {
      return {
        success: false,
        message: `Error procesando reembolso: ${error.message}`
      };
    }
  }

  @Get('statistics')
  @Roles(Role.ADMIN, Role.VIEWER)
  @ApiOperation({ summary: 'Obtiene estadísticas de fallecimientos' })
  async getDeathStatistics(
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    const currentYear = year || new Date().getFullYear();
    const dateFilter: any = {
      gte: new Date(`${currentYear}-01-01`),
      lt: new Date(`${currentYear + 1}-01-01`)
    };

    if (month) {
      dateFilter.gte = new Date(`${currentYear}-${month.toString().padStart(2, '0')}-01`);
      dateFilter.lt = new Date(`${currentYear}-${(month + 1).toString().padStart(2, '0')}-01`);
    }

    const stats = await this.prisma.$queryRaw`
      SELECT 
        COUNT(*) as total_notifications,
        COUNT(*) FILTER (WHERE status = 'VERIFIED') as verified_notifications,
        COUNT(*) FILTER (WHERE status = 'PENDING') as pending_notifications,
        
        -- Procesos por tipo
        COUNT(dp.id) FILTER (WHERE dp.process_type = 'CONVERT_TO_SURVIVAL') as survival_conversions,
        COUNT(dp.id) FILTER (WHERE dp.process_type = 'TERMINATE') as terminations,
        
        -- Montos involucrados
        SUM(dp.final_payment_amount) as total_final_payments,
        SUM(rp.refund_amount) FILTER (WHERE rp.recovery_status = 'COMPLETED') as total_recovered,
        
        -- Pensiones de supervivencia
        COUNT(sp.id) as survival_pensions_created,
        COUNT(sp.id) FILTER (WHERE sp.status = 'APPROVED') as survival_pensions_approved
        
      FROM death_notifications dn
      LEFT JOIN death_processes dp ON dn.id = dp.death_notification_id
      LEFT JOIN refund_processes rp ON dn.pension_code_id = rp.pension_code_id
      LEFT JOIN survival_pensions sp ON dp.id = sp.original_pension_id
      WHERE dn.death_date >= ${dateFilter.gte}
        AND dn.death_date < ${dateFilter.lt}
    `;

    return {
      success: true,
      data: stats[0],
      period: { year: currentYear, month }
    };
  }

  // Inyección de PrismaService para consultas directas
  constructor(
    private readonly deathManagementService: DeathManagementService,
    private readonly prisma: PrismaService
  ) {}
}

const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const descriptionOfCron = 'old-age-pension-in-process';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual.';
const markOfCron = 'OLD_AGE_PENSION_IN_PROCESS';
const successMessage = `El proceso ${markOfCron} se completó correctamente`;
const dependencyMark = '';

const workerFn = async ({ Logger, logService, service, job, done }) => {
  try {
    Logger.info(
      `${descriptionOfCron} checking whether this process was previously executed or not`
    );
    const { existsLog } = await logService.existsLogAndRetry(markOfCron);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${descriptionOfCron} process started`);
    const { error } = await service.markPensionersForRetirement();
    if (error) throw new Error(error);

    await logService.saveLog(markOfCron);
    Logger.info(`${descriptionOfCron} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${descriptionOfCron} ${error}`);
    await logService.retryLog(markOfCron);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${descriptionOfCron} ${error}` };
  } finally {
    done();
  }
};

module.exports = { markOfCron, dependencyMark, workerFn };

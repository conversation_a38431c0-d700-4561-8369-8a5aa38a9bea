const service = require('../services/link.service');
const pensionService = require('../../pensions/services/pension.service');
const { isActionAllowedOnCurrentMonth } = require('../services/businessDays.service');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
  // CriteriaBuilder @todo
}) => {
  function manageError(res, error) {
    const { code, message = 'Internal server error' } = error;
    res.status(code || HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }

  return {
    getAll: async (req, res) => {
      const { isError, error, result } = await service.getAll();
      Logger.info('Get pensions: ', req.details);
      if (isError) {
        Logger.error(`Get error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    },
    actionIsAllowed: async (req, res) => {
      try {
        const result = await isActionAllowedOnCurrentMonth(new Date());
        res.status(HttpStatus.OK).json(result);
      } catch (error) {
        Logger.error(`Current date status error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      }
    },
    alreadyLinked: async (req, res) => {
      try {
        const linked = await service.alreadyLinked();
        res.status(HttpStatus.OK).json({ linked });
      } catch (error) {
        Logger.error(`Current linked status error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      }
    },
    link: async (req, res) => {
      const { isError, error, result } = await service.linkPensions();
      if (isError) {
        Logger.error(`Get error: ${error.message}`, req.details);
        res
          .status(error.code)
          .json({ error: true, message: error.message })
          .end();
      } else {
        Logger.info(`Linked data pensions rows: ${result}`);
        res.status(HttpStatus.OK).json({ linked: result });
      }
    },

    updateAssetsAndDiscountOfPension: async (req, res) => {
      const { user = {} } = req;
      const pension = req.body;
      const { error, completed } = await pensionService.updateAssetsAndDiscountOfPension(
        pension,
        user
      );
      if (error) {
        Logger.error(`Get error: ${JSON.stringify(error.errmsg)}`, req.details);
        manageError(res, error);
      } else {
        Logger.info(`update Pensions data: ${JSON.stringify(completed)}`);
        res.status(HttpStatus.OK).json({ completed });
      }
    },
    cancel: async (req, res) => {
      const {
        isError,
        error,
        isCancelled,
        inDayRange,
        message
      } = await service.cancelLinkedPensions(new Date(), isActionAllowedOnCurrentMonth);
      if (isError) {
        Logger.error(`Delete error: ${JSON.stringify(error.errmsg)}`, req.details);
        manageError(res, error);
      }
      if (isCancelled) {
        Logger.info(`Cancel linked data successful: ${JSON.stringify(isCancelled)}`);
        res.status(HttpStatus.ACCEPTED).json({ isCancelled, inDayRange, message });
      } else {
        Logger.info(`Cancel linked data message: ${JSON.stringify(message)}`);
        res.status(HttpStatus.NOTFOUND).json({ isCancelled, inDayRange, message });
      }
    },
    validatePensionsToInsert: async (req, res) => {
      try {
        const results = await service.validateData(req.body);
        res.json({ results });
      } catch (err) {
        Logger.error(`Get selection error: ${JSON.stringify(err)}`, req.details);
        res.json({});
      }
    }
  };
};

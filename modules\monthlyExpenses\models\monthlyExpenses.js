const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const MonthlyExpensesSchema = new Schema(
  {
    month: { type: String },
    year: { type: Number },
    date: { type: Date },
    spentProfessionalDisease: { type: Number, default: 0 },
    spentAccident: { type: Number, default: 0 },
    spentSurvival: { type: Number, default: 0 },
    totalSpent: { type: Number, default: 0 },
    validPensioners: { type: Number, default: 0 },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

MonthlyExpensesSchema.plugin(paginate);
MonthlyExpensesSchema.index({ month: 1, year: 1 });

module.exports = mongoose.model('MonthlyExpenses', MonthlyExpensesSchema);

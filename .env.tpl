MONGODB_DB_URL=mongodb://localhost:27017/achs-pensiones-dev

TENANT_ID=6d4bbe0a-5654-4c69-a682-bf7dcdaed8e7
CLIENT_ID=737410a6-2211-461f-8348-762c2540706c

CRON_TO_INACTIVATE_SAP_URL= **************************************/SiniestroMiddleware/api/Siniestros/ObtenerDatosSiniestro
CRON_TO_INACTIVATE_SAP_AUTH_TYPE= Ocp-Apim-Subscription-Key
CRON_TO_INACTIVATE_SAP_KEY=2edba90a2c3a4218b33e90be3c36a567

CIVIL_REGISTRATION_SFTP_USER=ftpuser
CIVIL_REGISTRATION_SFTP_HOST=**************
CIVIL_REGISTRATION_SFTP_PASS=FTPAchs23people
CIVIL_REGISTRATION_SFTP_PORT=22
CIVIL_REGISTRATION_SFTP_INPUT_FOLDER=files/Entrada/
CIVIL_REGISTRATION_SFTP_OUTPUT_FOLDER="/files/Salida"

FEMALE_END_OF_VALIDITY_DATE=60
MALE_END_OF_VALIDITY_DATE=65
YEAR_WIDOW=1
LIFE_PENSION_LIMIT_YEARS=110

CRON_INACTIVATE_BY_AGE_LIMIT= "0 0 31 11 *"
CRON_REACTIVATE_BY_TRANSIENTS= "*/1 * * * *"

HOLIDAYS_URL=https://apis.digital.gob.cl/fl/feriados
CRON_BASE_MINIMUN_PENSION_WORKER=1 month
CRON_BASE_MINIMUN_PENSION_MAX_AGE=75
ARTICLE_41_INCREASE_PERCENTAGE=5
CRON_PRE_INACTIVATE_BY_TRANSIENTS=1 month

USER_IPC_SERVICE = *********
PASSWORD_IPC_SERVICE = ZIr1MSDH9JoDQmz
SERIE_ID_IPC_SERVICE = F074.IPC.IND.Z.EP18.C.M
URL_API_IPC_SERVICE = https://si3.bcentral.cl/sietews/sietews.asmx?wsdl

CRON_UNIFIED_BULK_LOAD_AND_IPS_FREQUENCY=5 minutes

CAJA_LA_ARAUCANA_FTP_HOST=**************
CAJA_LA_ARAUCANA_FTP_USER=ftpuser
CAJA_LA_ARAUCANA_FTP_PASS=FTPAchs23people
CAJA_LA_ARAUCANA_FTP_PORT=22
BULKLOAD_LA_ARAUCANA_FILES_FTP_FOLDER_PATH=/files/Salida

BULKLOAD_CAJA18_FILES_FTP_FOLDER_PATH=/files/Salida
CAJA18_FTP_HOST=**************
CAJA18_FTP_USER=ftpuser
CAJA18_FTP_PASS=FTPAchs23people
CAJA18_FTP_PORT=22

BULKLOAD_LOSHEROES_FILES_FTP_FOLDER_PATH=/files/Salida
CAJA_LOSHEROES_FTP_HOST=**************
CAJA_LOSHEROES_FTP_USER=ftpuser
CAJA_LOSHEROES_FTP_PASS=FTPAchs23people
CAJA_LOSHEROES_FTP_PORT=22

BULKLOAD_IPS_FILES_FTP_FOLDER_PATH=/files/haciamutual
IPS_FTP_HOST=**************
IPS_FTP_USER=ftpuser
IPS_FTP_PASS=FTPAchs23people
IPS_FTP_PORT=22

BUSINESS_DAYS_LIMIT_FOR_CAJAS_AND_IPS_FILES_BULK_LOAD=12

//-------------------------------------------------------------------

CRON_PRE_INACTIVATE_BY_TRANSIENTS=1 month

ACHS_FTP_USER=ftpuser
ACHS_FTP_HOST=achs-mock-ftp.brazilsouth.cloudapp.azure.com
ACHS_FTP_PASS=FTPAchs23people
ACHS_FTP_PORT=21
ACHS_FTP_OUTPUT_FOLDER="/files/Salida"

CRON_CALCULATE_PAYMENT_DATES_FREQUENCY=1 hour
CRON_CALCULATE_TAXABLE_PENSIONS=1 minutes
CRON_SET_VALUE_TO_ZERO_PENSIONS=1 hour

CRON_UFVALUE_FREQUENCY=1 minute
USER_UF_SOAP_SERVICE=*********
PASSWORD_UF_SOAP_SERVICE=ZIr1MSDH9JoDQmz
SERIE_ID_UF_SOAP_SERVICE=F073.UFF.PRE.Z.D
URL_UF_SOAP_SERVICE=https://si3.bcentral.cl/sietews/sietews.asmx?wsdl

CRON_TOTAL_ASSETS_AND_DISCOUNTS_FREQUENCY=1 minute
CRON_HISTORICAL_PENSION_REPORTS=10 minutes

PREVIRED_LOGIN_PAGE_URL=https://www.previred.com/wPortal/login/login.jsp
PREVIRED_LOGIN_PAGE_USER=18634442-1
PREVIRED_LOGIN_PAGE_PASSWORD=achs2020
PREVIRED_ACHS_EMAIL=<EMAIL>
PREVIRED_PAYROLL_TYPE=8
PREVIRED_PAYROLL_FILE_FORMAT=2
CRON_GENERATE_AND_UPLOAD_PREVIRED_FILE_FREQUENCY= 2 minutes

CRON_SET_FIXED_BASE_PENSION_VALUE_FREQUENCY=2 minutes
CRON_RESET_FIXED_BASE_PENSION_VALUE_FREQUENCY=2 minutes

TOTAL_DAYS_TO_ALLOW_REACTIVATION_INACTIVATION = 7
TOTAL_BUSINESS_DAYS = 28

CRON_REAJUST_BASEPENSION_AND_ARTICLES_FREQUENCY= 3 minutes

BUSINESS_DAYS_LINK=4
BUSINESS_DAYS_INACTIVATE_PENSIONS=8
BUSINESS_DAYS_VALIDATE_MIN_PENSION=10
BUSINESS_DAYS_UPDATE_PENSIONER_DATA=12
BUSINESS_DAYS_MASSIVE_LOAD_CCAF=12
BUSINESS_DAYS_MASSIVE_LOAD_FONASA=12
BUSINESS_DAYS_SETTLEMENT_GENERATION=14
BUSINESS_DAYS_IPS_CONCILIATION=14
BUSINESS_DAYS_BANK_PAYROLL=15
BUSINESS_DAYS_GIRO_TO_THIRD=15
BUSINESS_DAYS_VALID_CAPITAL=18
BUSINESS_DAYS_FUTURE_LINK=21
BUSINESS_DAYS_IN_A_MONTH=23
BUSINESS_DAYS_UPDATE_PENSION_TYPE=7

BUSINESS_DAY_NUMBER_FOR_ASSETS_DISCOUNTS_RETROACTIVE_CRON_EXECUTION=13

CRON_SCHEDULING_CRONJOBS_FREQUENCY=5 minutes
TIME_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS=21:00:00
BUSINESS_DAY_TO_EXECUTE_INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNMENT_PROCESS=7
BUSINESS_DAYS_TO_PAY_WORKER=8
CRON_MONTHLY_EXPENSES=1 month

PENSIONER_DOCUMENTS_FTP_HOST=**************
PENSIONER_DOCUMENTS_FTP_USER=ftpuser
PENSIONER_DOCUMENTS_FTP_PASS=FTPAchs23people
PENSIONER_DOCUMENTS_FTP_PORT=21
PENSIONER_DOCUMENTS_FTP_INPUT_FOLDER=/ftp
CRON_WIDOWHOOD_PAYMENT=1 month

CRON_UPDATE_BASE_PENSION_BY_ARTICLE_50_FREQUENCY=24 hours

CRON_POST_LIQUIDATION_CHECKPOINT_REPORT_FREQUENCY=1 month
CRON_RESERVED_AMOUNT_NONFORMULABLE_ASSETS_DISCOUNTS= 1 month
CRON_GENERATE_AND_UPLOAD_BANK_FILE_FREQUENCY=15 minutes
CRON_INACTIVATE_OR_REACTIVATE_AF_CRONS_GROUP_FREQUENCY=1 month
CRON_KEY_BUILDER_FRECUENCY= 1 month

CRON_SOCIAL_DISCOUNTS_CHECK_POINT_FREQUENCY = 1 month
CRON_ANALYSYS_OF_CURRENT_CAPITAL_FREQUENCY = 1 month
CRON_CALCULATE_UNIFIED_TOTAL_ASSETS_AND_DISCOUNTS_AND_NET_PENSIONS_LIQUIDATION_REPORTS_FREQUENCY = 1 month
DISCOUNTS_AND_ASSETS_UPDATE_DEADLINE=14

CRON_RESERVED_ASSETS_AND_DISCOUNTS_AMOUNT_CALCULATION_FREQUENCY = 1 month

DISCOUNTS_AND_ASSETS_NON_FORMULABLE_UPDATE_DEADLINE= 13
CRON_COLLECTION_DISCOUNT_HEALTH_FRECUENCY= "0 * 17 * *"
COLLECTION_DISCOUNT_HEALTH_SFTP_OUTPUT_FOLDER=/uploads/desdemutual
COLLECTION_DISCOUNT_HEALTH_SFTP_HOST=**************
COLLECTION_DISCOUNT_HEALTH_SFTP_PORT=22
COLLECTION_DISCOUNT_HEALTH_SFTP_USER=ftpuser
COLLECTION_DISCOUNT_HEALTH_SFTP_PASS=FTPAchs23people
CRON_TRANSFER_PENSIONER_INFO_FREQUENCY = "0 0 1 * *"

CIRCULAR_FILE_2480_SFTP_OUTPUT_FOLDER=/uploads/desdemutual
HEALTH_REJECTION_SFTP_OUTPUT_FOLDER=/uploads/haciamutual
SFTP_HOST=**************
SFTP_PORT=22
SFTP_USER=ftpuser
SFTP_PASS=FTPAchs23people

CRON_FILE_HEALTH_REJECTION_FREQUENCY = 10 minutes
FILE_HEALTH_REJECTION_START_DAY = 5
FILE_HEALTH_REJECTION_END_DAY = 12
CRON_HEALTH_EXEMPTION_PAYMENT_FREQUENCY= "0 * 17 * *"

JWTsecret="+I0qp`&@MTc}I^%"
JWTexpires=1d
TOKEN_EXPIRACY_TIME=86400
CRON_TRANSFER_PENSIONS_FREQUENCY = "0 1 1 * *"

CRON_GENERATE_AND_UPLOAD_APS_COLLECTION_FILE_FREQUENCY="0 * 18 * *"
APS_ACHS_SFTP_FOLDER_PATH=/uploads/haciamutual/APS-ACHS
APS_ACHS_SFTP_UPLOAD_FOLDER_PATH=/uploadss/desdemutual/APS-ACHS
APS_ACHS_FTP_HOST=**************
APS_ACHS_SFTP_PORT=22
APS_ACHS_SFTP_USER=ftpuser
APS_ACHS_SFTP_PASS=FTPAchs23people

SENDGRID_API_KEY="*********************************************************************"
EMAILS_TO_NOT_APPROVED_CHECKPOINT= "<EMAIL>"
EMAIL_FROM_NOT_APPROVED_CHECKPOINT="<EMAIL>"
TEMPLATE_ID_NOT_APPROVED_CHECKPOINT="d-400f29f68c664ea38a8d8970341f69e9"
EMAIL_CC_NOT_APPROVED_CHECKPOINT="<EMAIL>,<EMAIL>"

CRON_REJECTED_RETROACTIVE_AMOUNT_FREQUENCY=1 month
CRON_REJECTED_RESERVED_AMOUNT_FREQUENCY=1 month
CRON_MANUALLY_INACTIVATE_MARKED_PENSIONS_FREQUENCY=1 month

TOTAL_DAYS_TO_ALLOW_ISAPRE_PORTAL=11
PEC_BOSS = Jorge Niemann Martínez

CRON_ASSING_BONUS = "0 * 1 3,7,10 *"

RETRY_CRON_EXECUTION_MINUTES_FREQUENCY=5
CRONS_EXECUTION_ATTEMPTS = 10
CRON_SET_WINTER_BONUS="*/5 0 1 4 *"
CRON_SEND_CIRCULAR_FILE_2480_FREQUENCY="0 */1 8 * *"

EXPENSE_ACCOUNTING_REPORT_FREQUENCY=1 month
CRONS_SUCCESS_NOTIFICATION_LIST=<EMAIL>
CRONS_ERROR_NOTIFICATION_LIST=<EMAIL>
TEMPLATE_ID_CRON_NOTIFICATION=d-d48d26f03f654886b96d9e0da2dc4b84
DEFAULT_FROM_EMAIL=<EMAIL>
MINUTES_TO_EXECUTE_AFTER_SCHEDULE=5

CRON_SET_AMOUNT_PENSIONERS_NATIONAL_HOLIDAYS_BONUS= "0 * 1 8 *"
CRON_SET_AMOUNT_PENSIONERS_CHRISTMAS_BONUS= "* * * 1 *"

PENSIONER_AFP_CERTIFICATE_TARGET_FILE_NAME="Certificado_de_afiliación AFP.pdf"
PENSIONER_NOUROLOGIC_CERTIFICATE_TARGET_FILE_NAME="Certificado_neurológico.pdf"
PENSIONER_NOTARIAL_TARGET_FILE_NAME="Poder_notarial.pdf"
PENSIONER_COURTS_OPINION_TARGET_FILE_NAME="Dictamen_de_tribunales.pdf"

INSERT_PENSIONERS_AUTHORIZATION=<VALOR PARA EVITAR QUE CUALQUIER PERSONA INGRESE PENSIONADOS>
SECRET_KEY_DECRYPT_HISTORICAL_PENSION_DATA=<VALOR PARA DESENCRIPTAR DATOS>

CRON_SEND_NOTIFICATION_EMAIL=1 month

ALFRESCO_API=**************************************/ServiciosGenerales/api/PrestacionesEconomicas
ALFRESCO_AF=80c5f7c3-f675-4d16-98a1-73603e31de98
MIDDLEWARE_SUBSCRIPTION_KEY=2b4beff42a5b433cad5bb1bc67b81fc3
/* eslint-disable no-console */
const moment = require('moment');
const { getNBusinessDaysService, getRemainingDaysForProcesses } = require('./businessDays.service');
const dueDayList = require('../helpers/dueDaysList');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const LogModel = require('../../sharedFiles/models/processedJob');

describe('verify cases for business days', () => {
  beforeAll(beforeAllTests);

  const OLD_ENV = process.env;
  let getFirstNbusinessDays;
  let getMonthHolidays;

  beforeEach(() => {
    process.env = { ...OLD_ENV };
    getFirstNbusinessDays = jest.fn(() => Promise.resolve(['2020-07-01']));
    getMonthHolidays = jest.fn(() => Promise.resolve([]));
  });

  it('shoult return the current Date', async done => {
    const date = new Date(2020, 6, 1);
    const { businessDays, error } = await getNBusinessDaysService(
      date,
      1,
      getFirstNbusinessDays,
      getMonthHolidays
    ).catch(err => console.log(err));

    expect(error).toBeDefined();
    expect(businessDays[0]).toBe(moment(date).format('YYYY-MM-DD'));
    done();
  });

  it('should return error if number of days is negative', async done => {
    const date = new Date(2020, 6, 1);
    const { businessDays, error } = await getNBusinessDaysService(
      date,
      -1,
      getFirstNbusinessDays,
      getMonthHolidays
    ).catch(err => console.log(err));

    expect(error).toBeDefined();
    expect(businessDays).toStrictEqual([]);
    done();
  });

  it('should return the array of different remaining days at first businessDay', async done => {
    const date = new Date(2020, 6, 1);
    const businessDays = ['2020-07-01', '2020-07-02', '2020-07-03'];
    const dueDayListSynthetic = jest.fn(() => [
      { activity: 'Giro a terceros', lastDependencyMarks: [], numberOfDays: 2 }
    ]);
    const { dueDaysForProcesses, errorDueDaysForProcesses } = await getRemainingDaysForProcesses(
      dueDayListSynthetic,
      businessDays,
      date
    );

    expect(dueDaysForProcesses[0].dueDate).toBe('02-07-2020');
    expect(dueDaysForProcesses[0].remainingDays).toBe(1);
    expect(errorDueDaysForProcesses).toBe(false);
    done();
  });

  it('should return the array of different remaining days at first businessDay', async done => {
    const date = new Date(2020, 6, 1);
    const businessDays = ['2020-07-01', '2020-07-02', '2020-07-03'];
    const dueDayListSynthetic = jest.fn(() => new Error('something went wrong'));
    const { dueDaysForProcesses, errorDueDaysForProcesses } = await getRemainingDaysForProcesses(
      dueDayListSynthetic,
      businessDays,
      date
    );

    expect(errorDueDaysForProcesses).toBeDefined();
    expect(dueDaysForProcesses.length).toBe(0);
    done();
  });

  it('should return negative remaining days for an expired date', async done => {
    const date = new Date(2020, 6, 6);
    const businessDays = ['2020-07-01', '2020-07-02', '2020-07-03'];
    const dueDayListSynthetic = jest.fn(() => [
      { activity: 'Giro a terceros', lastDependencyMarks: [], numberOfDays: 1 }
    ]);
    const { dueDaysForProcesses, errorDueDaysForProcesses } = await getRemainingDaysForProcesses(
      dueDayListSynthetic,
      businessDays,
      date
    );

    expect(dueDaysForProcesses[0].dueDate).toBe('01-07-2020');
    expect(dueDaysForProcesses[0].remainingDays).toBe(-5);
    expect(errorDueDaysForProcesses).toBe(false);
    done();
  });

  it('should return zero remaining days for a final limit day', async done => {
    const date = new Date(2020, 6, 1);
    const businessDays = ['2020-07-01', '2020-07-02', '2020-07-03'];
    const dueDayListSynthetic = jest.fn(() => [
      { activity: 'Giro a terceros', lastDependencyMarks: [], numberOfDays: 1 }
    ]);
    const { dueDaysForProcesses, errorDueDaysForProcesses } = await getRemainingDaysForProcesses(
      dueDayListSynthetic,
      businessDays,
      date
    );

    expect(dueDaysForProcesses[0].dueDate).toBe('01-07-2020');
    expect(dueDaysForProcesses[0].remainingDays).toBe(0);
    expect(errorDueDaysForProcesses).toBe(false);
    done();
  });

  it('should return the names coming from dueDaysList but not the dates', async done => {
    process.env.BUSINESS_DAYS_LINK = 4;
    process.env.BUSINESS_DAYS_INACTIVATE_PENSIONS = 8;
    process.env.BUSINESS_DAYS_VALIDATE_MIN_PENSION = 10;
    process.env.BUSINESS_DAYS_UPDATE_PENSIONER_DATA = 12;
    process.env.BUSINESS_DAYS_MASSIVE_LOAD_CCAF = 12;
    process.env.BUSINESS_DAYS_MASSIVE_LOAD_FONASA = 12;
    process.env.BUSINESS_DAYS_SETTLEMENT_GENERATION = 14;
    process.env.BUSINESS_DAYS_IPS_CONCILIATION = 14;
    process.env.BUSINESS_DAYS_BANK_PAYROLL = 15;
    process.env.BUSINESS_DAYS_GIRO_TO_THIRD = 15;
    process.env.BUSINESS_DAYS_VALID_CAPITAL = 18;
    process.env.BUSINESS_DAYS_FUTURE_LINK = 21;

    const date = new Date(2020, 6, 1);
    const businessDays = ['2020-07-01', '2020-07-02', '2020-07-03'];
    const { dueDaysForProcesses } = await getRemainingDaysForProcesses(
      dueDayList,
      businessDays,
      date
    );

    expect(dueDaysForProcesses[0].activity).toBe('Enlace mes anterior');
    expect(dueDaysForProcesses[1].activity).toBe('Inactivar/Reactivar pensiones');
    expect(dueDaysForProcesses[2].activity).toBe('Validar pensión mínima');
    expect(dueDaysForProcesses[3].activity).toBe('Actualizar datos pensionados');
    expect(dueDaysForProcesses[4].activity).toBe('Carga masiva CCAF/IPS');
    expect(dueDaysForProcesses[5].activity).toBe('Carga masiva Fonasa/Los Andes');
    expect(dueDaysForProcesses[6].activity).toBe('Generación de liquidación');
    expect(dueDaysForProcesses[7].activity).toBe('Conciliación con IPS');
    expect(dueDaysForProcesses[8].activity).toBe('Upload de nómina bancaria');
    expect(dueDaysForProcesses[9].activity).toBe('Giro a terceros');
    expect(dueDaysForProcesses[10].activity).toBe('Generación de capitales vigentes');
    expect(dueDaysForProcesses[11].activity).toBe('Carga Enlace Mes N+1');
    done();
  });

  afterEach(async () => {
    await LogModel.deleteMany({}).catch(e => console.error(e));
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');

const {
  worker: netPensionsLiquidationReports
} = require('../../netPensionsLiquidationReports/workers');

module.exports = {
  name: 'calculateTotalAssetsDiscountsAndnetPensionsLiquidationReports',
  worker: deps =>
    workerModule.workerFn({
      logService,
      netPensionsLiquidationReports,
      ...deps
    }),
  repeatInterval:
    process.env
      .CRON_CALCULATE_UNIFIED_TOTAL_ASSETS_AND_DISCOUNTS_AND_NET_PENSIONS_LIQUIDATION_REPORTS_FREQUENCY,
  endPoint: 'unifiedtotalassetsanddiscountswithnetpensionliquidationreport',
  description:
    'Cron unificado para calculo de pensión liquida, haberes y descuentos no formulables por motivo, totales de haberes y descuentos formulables y no formulables',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.depsMarks
};

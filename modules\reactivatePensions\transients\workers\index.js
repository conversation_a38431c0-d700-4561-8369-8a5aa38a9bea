const axios = require('axios');
const transientHelper = require('../services/transientHelper');
const transientService = require('../services/transients.service');
const sapRequests = require('../services/sapRequests');
const linkService = require('../../../linkPensions/services/link.service');
const workerModule = require('./worker');
const pensionService = require('../../../pensions/services/pension.service');
const logService = require('../../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'reactivatePensionsByTransients',
  dependencies: ['inactivatePensionsByTransientsPreWorker'],
  worker: deps =>
    workerModule.workerFn({
      pensionService,
      linkService,
      logService,
      axios,
      transientService,
      transientHelper,
      sapRequests,
      ...deps
    }),
  repeatInterval: process.env.CRON_REACTIVATE_BY_TRANSIENTS,
  description: 'Reactivar pensiones transitorias',
  endPoint: 'reactivatepensionsbytransients',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

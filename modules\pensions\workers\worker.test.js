/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const textTable = require('text-table');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');
const mapperData = require('./mapper');
const uploadToSFTP = require('./sftpClient');
const result = require('../../../resources/pensions.json');

describe('Worker Civil Registration Test', () => {
  beforeAll(beforeAllTests);
  let sftpClient;
  let linkService;
  let fs;
  let done;
  let logService;
  let connectToSFTPServer;
  let Logger;
  beforeEach(() => {
    done = jest.fn();
    sftpClient = {
      uploadFrom: jest.fn(),
      close: jest.fn()
    };
    fs = {
      writeFile: jest.fn(() => Promise.resolve()),
      unlink: jest.fn(() => Promise.resolve())
    };
    linkService = {
      getAllAndFilter: jest.fn().mockResolvedValue({ result })
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
  });
  it('worker with error conection ftp', async () => {
    sftpClient.access = Promise.reject(new Error('error conection ftp'));
    await workerModule.workerFn({
      Logger,
      done,
      sftpClient,
      fs,
      service: linkService,
      logService,
      mapperData,
      table: textTable
    });
    expect(sftpClient.access).rejects.toThrow('error conection ftp');
  });
  it('worker create and delete file', async () => {
    await workerModule.workerFn({
      Logger,
      done,
      sftpClient,
      connectToSFTPServer,
      fs,
      service: linkService,
      logService,
      mapperData,
      table: textTable,
      moment,
      uploadToSFTP
    });
    expect(fs.writeFile).toHaveBeenCalled();
    expect(fs.unlink).toHaveBeenCalled();
  });
  it('worker create and dont delete file', async () => {
    fs.unlink = Promise.reject(new Error(`don't delete file`));
    await workerModule.workerFn({
      Logger,
      done,
      sftpClient,
      fs,
      service: linkService,
      logService,
      mapperData,
      table: textTable,
      moment,
      uploadToSFTP
    });
    expect(fs.writeFile).toHaveBeenCalled();
    expect(fs.unlink).rejects.toThrow(`don't delete file`);
  });
  it('worker don`t create file', async () => {
    fs = {
      writeFile: jest.fn(() => Promise.reject(new Error('dont create file'))),
      unlink: jest.fn(() => Promise.reject(new Error(`don't delete file`)))
    };
    await workerModule.workerFn({
      Logger,
      done,
      sftpClient,
      fs,
      service: linkService,
      logService,
      table: textTable
    });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(fs.writeFile()).rejects.toThrow('dont create file');
    expect(fs.unlink()).rejects.toThrow(`don't delete file`);
  });

  it('mapper data chilean rut ', async () => {
    const data = result[0];

    data.beneficiary.rut = '19023819-9';
    data.beneficiary.name = 'cristianmarceloarellanomoscosocristianmarceloarellanomoscoso';
    const processData = mapperData(data);

    const nationalId = processData[0].slice(0, 9);
    const lastName = processData[0].slice(9, 39);
    const mothersLastName = processData[0].slice(39, 69);
    const name = processData[0].slice(69, 119);
    const birthDate = processData[0].slice(119, 139);
    const flag = processData[0].slice(139, 145);

    expect(nationalId).toBe('190238199');
    expect(name.trim()).not.toBe('cristianmarceloarellanomoscosocristianmarceloarellanomoscoso');
    expect(name.trim()).toBe(
      'cristianmarceloarellanomoscosocristianmarceloarellanomoscoso'.slice(0, 50)
    );
    expect(lastName.trim()).toBe('YANEZ');
    expect(mothersLastName.trim()).toBe('BAHAMONDES');
    expect(birthDate.trim()).toBe('19971008');
    expect(flag.trim()).toBe('11111');
  });

  it('cut nationalId Longer', async () => {
    const data = result[0];

    data.beneficiary.rut = '19023912312321321';
    const processData = mapperData(data);
    const lastName = processData[0].slice(9, 39);

    expect(lastName.trim()).toBe('YANEZ');
  });

  it(' nationalId with dots', async () => {
    const data = result[0];

    data.beneficiary.rut = '19.023.819-9';
    const processData = mapperData(data);
    const lastName = processData[0].slice(9, 39);
    const rut = processData[0].slice(0, 9);
    expect(rut.trim()).toBe('190238199');
    expect(lastName.trim()).toBe('YANEZ');
  });

  it('should throw an error when there is an error on uploadToSFTP', async () => {
    connectToSFTPServer = jest.fn(() => Promise.reject(new Error('Connect to SFTP error')));
    await workerModule.workerFn({
      Logger,
      done,
      logService,
      sftpClient,
      fs,
      uploadToSFTP,
      connectToSFTPServer,
      service: linkService,
      table: textTable,
      mapperData,
      moment
    });
    expect(Logger.error).toHaveBeenCalled();
  });

  it('should return executionCompleted when there are no errors', async () => {
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      done,
      logService,
      sftpClient,
      fs,
      uploadToSFTP,
      connectToSFTPServer,
      service: linkService,
      table: textTable,
      mapperData,
      moment
    });
    expect(executionCompleted).toBe(true);
  });

  it('should return when has been already executed', async () => {
    logService.existsLogandRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      done,
      logService,
      sftpClient,
      fs,
      uploadToSFTP,
      connectToSFTPServer,
      service: linkService,
      table: textTable,
      mapperData,
      moment
    });
    expect(Logger.info).toHaveBeenCalledTimes(4);
  });

  it('should return when there is a missing dependency', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({
      Logger,
      done,
      logService,
      sftpClient,
      fs,
      uploadToSFTP,
      connectToSFTPServer,
      service: linkService,
      table: textTable,
      mapperData,
      moment
    });
    expect(Logger.info).toHaveBeenCalledTimes(3);
  });

  afterAll(afterAllTests);
});

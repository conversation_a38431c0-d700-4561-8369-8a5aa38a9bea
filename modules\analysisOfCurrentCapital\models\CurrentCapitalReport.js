const mongoose = require('mongoose');

const { Schema } = mongoose;

const CurrentCapitalReportSchema = new Schema(
  {
    reportType: { type: String },
    category: { type: String },
    pensionporaccidentedetrabajo: { type: Number },
    pensionporaccidentedetrayecto: { type: Number },
    pensionporenfermedadprofesional: { type: Number },
    pensionpororfandad: { type: Number },
    pensiondeorfandaddepadreymadre: { type: Number },
    pensiondeviudezconhijos: { type: Number },
    pensiondeviudezsinhijos: { type: Number },
    pensiondemadredehijodefiliacionnomatrimonialconhijos: { type: Number },
    pensiondemadredehijodefiliacionnomatrimonialsinhijos: { type: Number },
    detailsByInvalidityType: [Object],
    monthColumnSummatory: { type: Number },
    ipcColumnSummatory: { type: Number },
    capitalPlusIpc: { type: Number }
  },
  { timestamps: true }
);

module.exports = mongoose.model('CurrentCapitalReport', CurrentCapitalReportSchema);

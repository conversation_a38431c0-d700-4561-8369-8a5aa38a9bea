/* eslint-disable no-console */
const { beforeAllTests, afterAllTests, Logger } = require('../../testsHelper');
const workerModule = require('./worker');
const { calculateBasePension, createIpcObject } = require('./ipcHelper');
const pensionData = require('../../../resources/pensions.json');
const LogModel = require('../../sharedFiles/models/processedJob');

describe('Cron SOAP IPC', () => {
  // By using mongoose.connect
  beforeAll(beforeAllTests);
  let ipcHelper;
  let ipcService;
  let pensionService;
  let job;
  let done;
  let logService;

  beforeEach(() => {
    done = jest.fn();
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    ipcHelper = {
      getPreviousMonthIPC: jest.fn(async () =>
        Promise.resolve({
          result: {
            indexDateString: '01-11-2020',
            serieskey: {
              keyfamilyid: 'F074',
              seriesid: 'F074.IPC.IND.Z.EP18.C.M',
              datastage: 'INTERNAL',
              exists: 'true'
            },
            status: 'OK',
            value: '105.06'
          },
          error: false
        })
      ),
      calculateBasePension,
      createIpcObject
    };
    pensionService = {
      getAllAndFilter: jest.fn(async () =>
        Promise.resolve({
          result: [pensionData[0]],
          error: false
        })
      ),
      createUpdatePensionIPC: jest.fn(async val => ({
        result: { ...val },
        error: false
      }))
    };
    ipcService = {
      getLastPercentageChange: jest.fn(async () =>
        Promise.resolve({
          _id: '5ea3131d7e755544bedee240',
          isLastPercentageChange: false,
          date: '2019-11-01T03:00:00.000Z',
          value: '103.55',
          percentage: '',
          createdAt: '2020-04-24T16:26:05.013Z',
          updatedAt: '2020-04-24T16:26:05.013Z',
          __v: 0
        })
      ),
      insertIPC: jest.fn(() =>
        Promise.resolve({
          result: {
            _id: '5ea3174a7e755544bedee241',
            isLastPercentageChange: false,
            date: '2020-03-01T03:00:00.000Z',
            value: '105.06',
            percentage: '1.4582',
            createdAt: '2020-04-24T16:43:55.145Z',
            updatedAt: '2020-04-24T16:43:55.145Z',
            __v: 0
          },
          error: false
        })
      )
    };
    job = { repeatEvery: jest.fn(), save: jest.fn(() => Promise.resolve()) };
  });

  it('sucess get last month ipc without changes', async () => {
    await workerModule.workerFn({
      Logger,
      ipcService,
      ipcHelper,
      pensionService,
      job,
      done,
      logService
    });
    expect(logService.existsLogAndRetry).toBeCalled();
  });

  it('success get last month ipc without changes', async () => {
    await workerModule.workerFn({
      Logger,
      ipcService,
      ipcHelper,
      pensionService,
      job,
      done,
      logService
    });
    expect(pensionService.getAllAndFilter).not.toHaveBeenCalled();
    expect(pensionService.createUpdatePensionIPC).not.toHaveBeenCalled();
  });

  it('sucess get last month ipc with changes', async () => {
    ipcService.insertIPC = jest.fn(() =>
      Promise.resolve({
        result: {
          _id: '5ea3174a7e755544bedee241',
          isLastPercentageChange: true,
          date: '2020-03-01T03:00:00.000Z',
          value: '105.06',
          percentage: '1.4582',
          createdAt: '2020-04-24T16:43:55.145Z',
          updatedAt: '2020-04-24T16:43:55.145Z',
          __v: 0
        },
        error: false
      })
    );

    await workerModule.workerFn({
      Logger,
      ipcService,
      ipcHelper,
      pensionService,
      job,
      done,
      logService
    });
  });

  it('error on getPreviousMonthIPC', async () => {
    ipcHelper = {
      getPreviousMonthIPC: jest.fn(async () =>
        Promise.resolve({ result: { indexDateString: '', value: '' }, error: true })
      ),
      calculateBasePension,
      createIpcObject
    };

    await workerModule.workerFn({
      Logger,
      ipcService,
      ipcHelper,
      pensionService,
      job,
      done,
      logService
    });
  });

  it('error get last month ipc without changes', async () => {
    ipcService.insertIPC = jest.fn(() =>
      Promise.resolve({
        result: null,
        error: true
      })
    );

    await workerModule.workerFn({
      Logger,
      ipcService,
      ipcHelper,
      pensionService,
      job,
      done,
      logService
    });
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await LogModel.deleteMany({}).catch(error => console.error(error));
  });

  afterAll(afterAllTests);
});

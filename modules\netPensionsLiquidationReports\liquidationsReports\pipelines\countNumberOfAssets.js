const { recursiveCount } = require('../../../sharedFiles/helpers');

const paths = [
  'article40',
  'article41',
  'assets.aps',
  'basePension',
  'assets.rebsal',
  'assets.winterBonus',
  'assets.marriageBonus',
  'assets.christmasBonus',
  'assets.forFamilyAssignment',
  'assets.nationalHolidaysBonus',
  'assets.adjustedHealthExemption',
  'retroactiveAmounts.forBonuses',
  'retroactiveAmounts.forArticle40',
  'retroactiveAmounts.forArticle41',
  'retroactiveAmounts.forRejection',
  'retroactiveAmounts.forBasePension',
  'retroactiveAmounts.forFamilyAssignment',
  'retroactiveAmounts.forInstitutionalPatient'
];

const count = pension => {
  const { numberOfTaxableNonFormulableAssets = 0, numberOfNetNonFormulableAssets = 0 } = pension;
  return {
    ...pension,
    liquidation: {
      ...pension.liquidation,
      numberOfAssets:
        recursiveCount(pension, paths, true) +
        numberOfTaxableNonFormulableAssets +
        numberOfNetNonFormulableAssets
    }
  };
};

module.exports = count;

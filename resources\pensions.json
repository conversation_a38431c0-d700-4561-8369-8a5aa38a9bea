[{"institutionalPatient": false, "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "endDateOfValidity": "2022-10-08T04:00:00.000Z", "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "Depósito cuenta ahorro otros bancos", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": "", "phone": "*********"}, "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "numberOfChargesArticle41": 3, "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionStartDate": "2016-09-21T03:00:00.000Z", "pensionCodeId": "17153", "article40": 10000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3", "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": "33"}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "0", "apsOrigin": ""}, "amountOtherPension": 0, "dl1026": 0, "retirement": false, "basePensionRules": [{"_id": "60c29255892c7603dda1c15d", "label": "Pensión por accidente de trabajo", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c760950a1c15e", "label": "Pensión por accidente de trabajo", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765d50a1c15f", "label": "Pensión por accidente de trabajo", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c761e30a1c160", "label": "Pensión por accidente de trayecto", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c7624dba1c161", "label": "Pensión por accidente de trayecto", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c76b049a1c162", "label": "Pensión por accidente de trayecto", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c76e835a1c163", "label": "Pensión por enfermedad profesional", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c76511da1c164", "label": "Pensión por enfermedad profesional", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765c65a1c165", "label": "Pensión por enfermedad profesional", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c7674e3a1c166", "label": "Pensión de viudez con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "79436.65", "law19403": "16727.34", "law19539": "28226.08"}}, {"_id": "60c29255892c7645e9a1c167", "label": "Pensión de viudez con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23"}}, {"_id": "60c29255892c76128ea1c168", "label": "Pensión de viudez con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23", "law19953": "9108.18"}}, {"_id": "60c29255892c76a8aca1c169", "label": "Pensión de viudez sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "94965.97", "law19403": "16727.34", "law19539": "34648.03"}}, {"_id": "60c29255892c76bb06a1c16a", "label": "Pensión de viudez sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37"}}, {"_id": "60c29255892c76865ba1c16b", "label": "Pensión de viudez sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37", "law19953": "10715.48"}}, {"_id": "60c29255892c760106a1c16c", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "47662.03", "law19403": "10036.43", "law19539": "16935.64"}}, {"_id": "60c29255892c767e91a1c16d", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34"}}, {"_id": "60c29255892c76721ea1c16e", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34", "law19953": "5464.88"}}, {"_id": "60c29255892c76b466a1c16f", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "56979.55", "law19403": "10036.43", "law19539": "20788.78"}}, {"_id": "60c29255892c76b4a2a1c170", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54"}}, {"_id": "60c29255892c7682dfa1c171", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54", "law19953": "6429.35"}}, {"_id": "60c29255892c76e124a1c172", "label": "Pensión por orfandad", "type": "pension", "age": "Edad > 0", "value": {"minimun": "21951.19"}}]}, {"institutionalPatient": false, "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "1111111-1", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionStartDate": "2016-09-21T03:00:00.000Z", "pensionCodeId": "17154", "article40": 20000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3", "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": "33.9"}, "basePensionRules": [{"_id": "60c29255892c7603dda1c15d", "label": "Pensión por accidente de trabajo", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c760950a1c15e", "label": "Pensión por accidente de trabajo", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765d50a1c15f", "label": "Pensión por accidente de trabajo", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c761e30a1c160", "label": "Pensión por accidente de trayecto", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c7624dba1c161", "label": "Pensión por accidente de trayecto", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c76b049a1c162", "label": "Pensión por accidente de trayecto", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c76e835a1c163", "label": "Pensión por enfermedad profesional", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c76511da1c164", "label": "Pensión por enfermedad profesional", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765c65a1c165", "label": "Pensión por enfermedad profesional", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c7674e3a1c166", "label": "Pensión de viudez con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "79436.65", "law19403": "16727.34", "law19539": "28226.08"}}, {"_id": "60c29255892c7645e9a1c167", "label": "Pensión de viudez con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23"}}, {"_id": "60c29255892c76128ea1c168", "label": "Pensión de viudez con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23", "law19953": "9108.18"}}, {"_id": "60c29255892c76a8aca1c169", "label": "Pensión de viudez sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "94965.97", "law19403": "16727.34", "law19539": "34648.03"}}, {"_id": "60c29255892c76bb06a1c16a", "label": "Pensión de viudez sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37"}}, {"_id": "60c29255892c76865ba1c16b", "label": "Pensión de viudez sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37", "law19953": "10715.48"}}, {"_id": "60c29255892c760106a1c16c", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "47662.03", "law19403": "10036.43", "law19539": "16935.64"}}, {"_id": "60c29255892c767e91a1c16d", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34"}}, {"_id": "60c29255892c76721ea1c16e", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34", "law19953": "5464.88"}}, {"_id": "60c29255892c76b466a1c16f", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "56979.55", "law19403": "10036.43", "law19539": "20788.78"}}, {"_id": "60c29255892c76b4a2a1c170", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54"}}, {"_id": "60c29255892c7682dfa1c171", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54", "law19953": "6429.35"}}, {"_id": "60c29255892c76e124a1c172", "label": "Pensión por orfandad", "type": "pension", "age": "Edad > 0", "value": {"minimun": "21951.19"}}]}, {"institutionalPatient": false, "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-1", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "No vigente", "pensionType": "Pensión por orfandad", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionStartDate": "2016-09-21T03:00:00.000Z", "endDateOfTheoricalValidity": "2020-10-02T03:00:00.000Z", "pensionCodeId": "17155", "article40": 30000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3", "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": 33.33}, "basePensionRules": [{"_id": "60c29255892c7603dda1c15d", "label": "Pensión por accidente de trabajo", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c760950a1c15e", "label": "Pensión por accidente de trabajo", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765d50a1c15f", "label": "Pensión por accidente de trabajo", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c761e30a1c160", "label": "Pensión por accidente de trayecto", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c7624dba1c161", "label": "Pensión por accidente de trayecto", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c76b049a1c162", "label": "Pensión por accidente de trayecto", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c76e835a1c163", "label": "Pensión por enfermedad profesional", "age": "Edad < 70", "type": "pension", "value": {"minimun": "146341.28"}}, {"_id": "60c29255892c76511da1c164", "label": "Pensión por enfermedad profesional", "age": "70<= Edad < 75", "type": "pension", "value": {"minimun": "160012.95"}}, {"_id": "60c29255892c765c65a1c165", "label": "Pensión por enfermedad profesional", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "170728.41"}}, {"_id": "60c29255892c7674e3a1c166", "label": "Pensión de viudez con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "79436.65", "law19403": "16727.34", "law19539": "28226.08"}}, {"_id": "60c29255892c7645e9a1c167", "label": "Pensión de viudez con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23"}}, {"_id": "60c29255892c76128ea1c168", "label": "Pensión de viudez con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "102322.36", "law19403": "14405.39", "law19539": "19283.23", "law19953": "9108.18"}}, {"_id": "60c29255892c76a8aca1c169", "label": "Pensión de viudez sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "94965.97", "law19403": "16727.34", "law19539": "34648.03"}}, {"_id": "60c29255892c76bb06a1c16a", "label": "Pensión de viudez sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37"}}, {"_id": "60c29255892c76865ba1c16b", "label": "Pensión de viudez sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "118493.82", "law19403": "16635.74", "law19539": "24883.37", "law19953": "10715.48"}}, {"_id": "60c29255892c760106a1c16c", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "47662.03", "law19403": "10036.43", "law19539": "16935.64"}}, {"_id": "60c29255892c767e91a1c16d", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34"}}, {"_id": "60c29255892c76721ea1c16e", "label": "Pensión de madre de hijo de filiación no matrimonial con hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "69263.44", "law19403": "9944.86", "law19539": "2398.34", "law19953": "5464.88"}}, {"_id": "60c29255892c76b466a1c16f", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad < 70", "type": "pension", "value": {"minimun": "56979.55", "law19403": "10036.43", "law19539": "20788.78"}}, {"_id": "60c29255892c76b4a2a1c170", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "70 <= Edad < 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54"}}, {"_id": "60c29255892c7682dfa1c171", "label": "Pensión de madre de hijo de filiación no matrimonial sin hijos", "age": "Edad >= 75", "type": "pension", "value": {"minimun": "78966.17", "law19403": "11283.00", "law19539": "5758.54", "law19953": "6429.35"}}, {"_id": "60c29255892c76e124a1c172", "label": "Pensión por orfandad", "type": "pension", "age": "Edad > 0", "value": {"minimun": "21951.19"}}]}]
const cronMark = 'UF_VALUE';
const cronDescription = 'get uf value';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({
  Logger,
  done,
  service,
  logService,
  fetchSoapData,
  util,
  client,
  requestArgs,
  url,
  keys,
  job
}) => {
  try {
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`Inicio ejecución cron para obtener valor UF.`);
    const { data } = await fetchSoapData({ util, client, requestArgs, url, keys });
    if (!data) throw new Error('error getting UF value');

    const { indexDateString, value } = data;
    await service.createUfValue({ date: indexDateString, value });
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

const Excel = require('exceljs');
const fs = require('fs');

module.exports = ({ HttpStatus, Logger, service }) => {
  return {
    getReport: async (req, res) => {
      try {
        const { reportType } = req.params;
        const { csvReportData, filename, error } = await service.getCsvReportData(reportType);
        if (error) throw new Error(error);

        const workbook = new Excel.Workbook();
        const filePath = `${__dirname}/${filename}.csv`;

        fs.writeFileSync(filePath, csvReportData);
        await workbook.csv.readFile(filePath);
        res.header(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );
        res.header('Content-Disposition', `attachment; filename=${filename}.xlsx`);
        fs.unlinkSync(filePath);
        return workbook.xlsx.write(res).then(() => {
          res.status(HttpStatus.OK).end();
        });
      } catch (error) {
        Logger.error(`Current capital report error ${error}`);
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: true,
          message: error.message
        });
      }
    }
  };
};

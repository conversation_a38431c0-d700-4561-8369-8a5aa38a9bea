const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');

const FactoryController = require('../modules/manualCronExecution/controllers/index.controller');
const listOfWorkers = require('../modules/manualCronExecution/controllers/listOfCrons');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    Logger,
    listOfWorkers
  });

  router.post(
    '/:cronName',
    validateAccess(),
    startContextMiddleware,
    getUser(),
    controller.execute
  );
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const worker = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

describe('worker shared files bulkLoad Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let sftp;
  let downloadFilesFromSFTP;
  let getParsedLinesFromFiles;
  beforeEach(() => {
    done = jest.fn();
    sftp = { Client: jest.fn() };
    downloadFilesFromSFTP = jest.fn(() => Promise.resolve({ files: ['file1'], error: null }));
    getParsedLinesFromFiles = jest.fn(() => Promise.resolve({}));

    service = {
      updatePensions: jest.fn(() => Promise.resolve({ isError: false, err: null }))
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    service.updatePensions = jest.fn(() => Promise.reject(new Error('Error service')));
    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail reading file', async () => {
    downloadFilesFromSFTP = jest.fn(() => Promise.resolve({ files: [], error: true }));

    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(2);
  });

  it('fail by empty file', async () => {
    downloadFilesFromSFTP = jest.fn(() => Promise.resolve({ files: [], error: false }));

    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(2);
  });

  it('Error by update pension', async () => {
    service.updatePensions = jest.fn(() => Promise.resolve({ isError: true, err: 'err' }));
    await worker({
      Logger,
      service,
      logService,
      done,
      sftp,
      downloadFilesFromSFTP,
      getParsedLinesFromFiles
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(2);
  });
  afterAll(afterAllTests);
});

[{"_doc": {"_id": "5e7d04a34fb395369bdcdc60", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": ""}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "inactivationReason": "alta médica", "institutionalPatient": false, "enabled": true, "basePension": 255564, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "No vigente", "endDateOfValidity": "2011-10-15T03:00:00.000Z", "pensionType": "Pensión de madre de hijo de filiación no matrimonial con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": ********, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "1999-01-03T03:00:00.000Z", "createdAt": "2020-03-26T19:38:07.641Z", "updatedAt": "2020-03-30T16:47:14.166Z", "linkedDate": "2020-03-26T19:38:11.568Z"}, "_id": "5e7d04a34fb395369bdcdc60", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": ""}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "inactivationReason": "alta médica", "institutionalPatient": false, "enabled": true, "basePension": 255564, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "No vigente", "endDateOfValidity": "2011-10-15T03:00:00.000Z", "pensionType": "Pensión de madre de hijo de filiación no matrimonial con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": ********, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "1999-01-03T03:00:00.000Z", "createdAt": "2020-03-26T19:38:07.641Z", "updatedAt": "2020-03-30T16:47:14.166Z", "linkedDate": "2020-03-26T19:38:11.568Z"}, {"_id": "5e7d04a34fb395369bdcdc61", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-5", "name": "ANDRES", "lastName": "ASTRELLI", "mothersLastName": "JIMENEZ"}, "collector": {"rut": "********-5", "name": "ANDRES", "lastName": "ASTRELLI", "mothersLastName": "JIMENEZ", "address": ""}, "beneficiary": {"rut": "********-5", "name": "ANDRES", "lastName": "ASTRELLI", "mothersLastName": "JIMENEZ", "email": ""}, "inactivationReason": "alta médica", "institutionalPatient": false, "enabled": true, "basePension": 285262, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 5.783, "dateOfBirth": "1974-06-24T04:00:00.000Z", "gender": "M", "endDateOfValidity": "2017-10-15T03:00:00.000Z", "afpAffiliation": "AFP HABITAT S.A.", "healthAffiliation": "FONASA", "validityType": "No vigente", "pensionType": "Pensión de madre de hijo de filiación no matrimonial con hijos", "disabilityDegree": 70, "disabilityType": "Invalidez total", "resolutionNumber": ********, "accidentNumber": 2648949, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1998-10-05T03:00:00.000Z", "pensionCodeId": "13348", "pensionStartDate": "2010-01-11T03:00:00.000Z", "createdAt": "2020-03-26T19:38:07.641Z", "updatedAt": "2020-03-26T19:38:07.641Z", "linkedDate": "2020-03-26T19:38:11.568Z"}]
const winston = require('winston');
require('winston-mongodb');

const SECONDS_MONTH = 30 * 24 * 3600;
const logger = (
  level = 'info',
  options = {
    level,
    db: process.env.MONGODB_DB_URL,
    collection: 'logs',
    expireAfterSeconds: 3 * SECONDS_MONTH,
    decolorize: false
  }
) =>
  winston.createLogger({
    level,
    format: winston.format.simple(),
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(winston.format.colorize(), winston.format.simple())
      }),
      new winston.transports.MongoDB(options)
    ]
  });

module.exports = logger();
module.exports.LoggerFactory = logger;

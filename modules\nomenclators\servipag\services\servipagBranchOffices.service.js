const Model = require('../models/servipag');

const service = {
  async getAll(query = { enabled: true }) {
    return Model.find(query)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async updateServipag({ id, ...servipagData }) {
    const criteria = {
      enabled: false,
      $or: [{ code: servipagData.code }, { name: servipagData.name }]
    };
    try {
      const oldServipag = await Model.findOne(criteria).exec();
      if (oldServipag && oldServipag.id) {
        await Model.remove({ id: oldServipag.id }).exec();
      }
      const savedServipag = await Model.findOneAndUpdate(
        { id, enabled: true },
        { $set: { ...servipagData } },
        { returnNewDocument: true, upsert: true, new: true }
      ).exec();
      return { result: savedServipag };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async createServipag(servipagData) {
    const criteria = {
      enabled: false,
      $or: [{ code: servipagData.code }, { name: servipagData.name }]
    };
    try {
      const result = await Model.findOne(criteria).exec();
      if (result) {
        const savedServipag = await Model.findOneAndUpdate(
          criteria,
          { ...servipagData, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedServipag };
      }
      const data = await Model.create(servipagData);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async delete(id) {
    try {
      const data = await Model.update(
        { id, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const pensionsData = require('../../../../resources/pensions.json');
const fileGenerationUtils = require('./fileGenerationUtils');

describe('collection discounth health file generation methods test', () => {
  beforeAll(beforeAllTests);

  const expectedOutputLine = [
    `${moment().format('YYYYMM')}${moment().format('YYYYMM')}01`,
    '703601006',
    `19685004KYANEZ${''.padStart(15)}`,
    `BAHAMONDES${''.padStart(10)}`,
    `CHRISTOPHER${''.padStart(19)}`,
    '07',
    '00010672',
    '00152452\n'
  ].join('');

  it('should convert each pensioner object to a line for the collection discount health file', async () => {
    const { getLine } = fileGenerationUtils;
    const pensioner = pensionsData[0];
    const line = getLine({ pension: pensioner });
    expect(line).toBe(expectedOutputLine);
    expect(line.length).toBe(121);
  });

  it('should dynamically generate file name base on current date', async () => {
    const { getZipFileName, getFileName } = fileGenerationUtils;
    const tmp = { dirSync: jest.fn().mockReturnValue({ name: '/temp' }) };
    expect(getFileName(tmp)).toMatch(`/temp/rsalud${moment().format('YYYYMM')}.703601006`);
    expect(getZipFileName()).toBe(
      `rsalud.703601006.ips_${moment().format('YYMM')}_${moment().format(
        'YYMMDD'
      )}_${moment().format('HHmm')}.zip`
    );
  });

  afterAll(afterAllTests);
});

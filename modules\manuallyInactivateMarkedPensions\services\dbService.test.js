/* eslint-disable no-console */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const pensionsData = require('../../../resources/pensions.json');
const PensionModel = require('../../../models/pension');
const pensionService = require('../../pensions/services/pension.service');
const service = require('./dbService');

describe('Manually  Inactivate Marked Pension Service', () => {
  beforeAll(beforeAllTests);

  it('should filter and inactivate pensions', async () => {
    const stringPreviousMonthDate = moment()
      .subtract(1, 'months')
      .toString();
    const previousMonthDate = new Date(stringPreviousMonthDate);

    const inactivateManuallyPensionEnabled = {
      ...pensionsData[0],
      inactivateManually: true,
      endDateOfValidity: previousMonthDate
    };
    const inactivateManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      inactivateManually: true,
      endDateOfValidity: previousMonthDate
    };
    const notInactivateManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      inactivateManually: false,
      endDateOfValidity: previousMonthDate
    };

    await PensionModel.insertMany([
      inactivateManuallyPensionEnabled,
      inactivateManuallyPensionDisabled,
      notInactivateManuallyPensionDisabled
    ]);

    const { updatedIncativatedPensioners } = await service.inactivatePensions();
    expect(updatedIncativatedPensioners.length).toBe(1);
    expect(updatedIncativatedPensioners[0].validityType).toMatch(/No vigente/i);
  }, 120000);

  it('should filter and notReactivate pensions', async () => {
    const reactivatedManuallyPensionEnabled = {
      ...pensionsData[0],
      manuallyReactivated: true
    };
    const reactivatedManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      manuallyReactivated: true
    };
    const notReactivatedManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      manuallyReactivated: false
    };

    await PensionModel.insertMany([
      reactivatedManuallyPensionEnabled,
      reactivatedManuallyPensionDisabled,
      notReactivatedManuallyPensionDisabled
    ]);

    const { updatedNotReactivatedPensioners } = await service.notReactivatePensions();
    expect(updatedNotReactivatedPensioners.length).toBe(1);
    expect(updatedNotReactivatedPensioners[0].manuallyReactivated).toBe(false);
  }, 120000);

  it('should filter and inactivate and not Reactivate Pensions', async () => {
    const stringPreviousMonthDate = moment()
      .subtract(1, 'months')
      .toString();
    const previousMonthDate = new Date(stringPreviousMonthDate);

    const reactivatedManuallyPensionEnabled = {
      ...pensionsData[0],
      inactivateManually: true,
      manuallyReactivated: true,
      endDateOfValidity: previousMonthDate
    };
    const reactivatedManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      manuallyReactivated: true
    };
    const notReactivatedManuallyPensionDisabled = {
      ...pensionsData[0],
      enabled: false,
      manuallyReactivated: false
    };

    await PensionModel.insertMany([
      reactivatedManuallyPensionEnabled,
      reactivatedManuallyPensionDisabled,
      notReactivatedManuallyPensionDisabled
    ]);

    const {
      updatedMixedInactivateReactivated
    } = await service.inactivateAndNotReactivatePensions();
    expect(updatedMixedInactivateReactivated.length).toBe(1);
    expect(updatedMixedInactivateReactivated[0].manuallyReactivated).toBe(false);
  }, 120000);

  it('should save the modified pensioners successfully', async () => {
    const updatedIncativatedPensioners = [];
    const updatedNotReactivatedPensioners = [];
    const updatedMixedInactivateReactivated = [];

    const inactivatedPensioner = {
      ...pensionsData[0],
      validityType: 'No vigente',
      inactivationDate: new Date(),
      inactivateManually: false
    };

    const notReactivatedPensioner = {
      ...pensionsData[0],
      manuallyReactivated: false
    };

    const mixedInactivatedAndNotReactivatedPensioner = {
      ...pensionsData[0],
      validityType: 'No vigente',
      inactivationDate: new Date(),
      inactivateManually: false,
      manuallyReactivated: false
    };

    updatedIncativatedPensioners.push(inactivatedPensioner);
    updatedNotReactivatedPensioners.push(notReactivatedPensioner);
    updatedMixedInactivateReactivated.push(mixedInactivatedAndNotReactivatedPensioner);

    const { completed, error } = await service.updateMarkedPensions({
      pensionService,
      updatedIncativatedPensioners,
      updatedNotReactivatedPensioners,
      updatedMixedInactivateReactivated
    });

    expect(completed).toBe(true);
    expect(error).toBe(null);
  }, 120000);

  it('should save the modified pensioners will fail', async () => {
    jest.spyOn(pensionService, 'updatePensions').mockImplementationOnce(() => {
      throw new Error();
    });

    const updatedIncativatedPensioners = [];
    const updatedNotReactivatedPensioners = [];
    const updatedMixedInactivateReactivated = [];

    const inactivatedPensioner = {
      ...pensionsData[0],
      validityType: 'No vigente',
      inactivationDate: new Date(),
      inactivateManually: false
    };

    const notReactivatedPensioner = {
      ...pensionsData[0],
      manuallyReactivated: false
    };

    updatedIncativatedPensioners.push(inactivatedPensioner);
    updatedNotReactivatedPensioners.push(notReactivatedPensioner);

    const { completed, error } = await service.updateMarkedPensions({
      pensionService,
      updatedIncativatedPensioners,
      updatedNotReactivatedPensioners,
      updatedMixedInactivateReactivated
    });

    expect(completed).toBe(false);
    expect(error).toBeDefined();
  }, 120000);

  afterEach(async () => {
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

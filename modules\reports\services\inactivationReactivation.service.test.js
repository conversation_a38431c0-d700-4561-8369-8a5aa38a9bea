const xl = require('excel4node');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const reportService = require('./inactivationReactivation.service');

const InactivationReactivationData = require('../../../resources/temporaryInactivateReactivePension.json');
const InactivationTemporaryModel = require('../../historicPensions/models/temporaryInactivationPension');
const ReactivationTemporaryModel = require('../../historicPensions/models/temporaryReactivationPension');

const { excelService } = require('./excel.service');

describe('Report service test', () => {
  beforeAll(beforeAllTests);

  afterEach(async () => {
    jest.restoreAllMocks();
    await InactivationTemporaryModel.deleteMany({}).catch(err => console.error(err));
    await ReactivationTemporaryModel.deleteMany({}).catch(err => console.error(err));
  });
  afterAll(afterAllTests);

  it('Should return all documents and the server date in the collection', async () => {
    const [inactivation, reactivation] = InactivationReactivationData;
    await InactivationTemporaryModel.insertMany([inactivation]);
    await ReactivationTemporaryModel.insertMany([reactivation]);

    const { result, isError, error } = await reportService.getInactivationReactivationReport();
    const { inactivations, reactivations } = result;

    expect(error).toBe(null);
    expect(isError).toBe(false);
    expect(inactivations.length).toBe(1);
    expect(reactivations.length).toBe(1);
  });

  it('Should export an excel', async () => {
    const wb = new xl.Workbook();
    const excelData = {
      inactivations: InactivationReactivationData[0],
      reactivations: InactivationReactivationData[1]
    };
    await excelService(excelData, wb);
    expect(wb.getStringIndex('Número de pensión')).toBe(0);
    expect(wb.getStringIndex('Tipo de pensión')).toBe(1);
    expect(wb.getStringIndex('Tipo de vigencia')).toBe(2);
    expect(wb.getStringIndex('Pensión base')).toBe(3);
    expect(wb.getStringIndex('Nombre del beneficiario')).toBe(4);
    expect(wb.getStringIndex('Fecha de fin de vigencia teórica')).toBe(5);
    expect(wb.getStringIndex('Fecha de fin de vigencia anterior')).toBe(6);
    expect(wb.getStringIndex('Motivo de reactivación')).toBe(7);
    expect(wb.getStringIndex('Mes')).toBe(8);
    expect(wb.getStringIndex('Transitoria')).toBe(9);
  });

  it('Should return an empty array if theres no documents in collection', async () => {
    const { result, isError, error } = await reportService.getInactivationReactivationReport();
    expect(isError).toBe(false);
    expect(typeof result).toBe('object');
    expect(error).toBe(null);
  });
});

const extractSubstring = (line, position, length) => line.substr(position, length);
const getCauseOfExcessCode = ({ inactivationReason = '' }) => {
  return inactivationReason.match(/Fallecimiento/i) ? '01' : '07';
};

const composeDpeapsLine = (line, pension) => {
  const emissionPeriod = extractSubstring(line, 0, 6);
  const institutionRut = '703601006';
  const transferDate = extractSubstring(line, 148, 8);
  const transferCode = extractSubstring(line, 156, 10);
  const resolutionNumber = extractSubstring(line, 15, 8);
  const resolutionDate = extractSubstring(line, 23, 8);
  const pensionerRut = extractSubstring(line, 31, 9);
  const beneficiaryLastName = extractSubstring(line, 40, 20).padEnd(20);
  const beneficiaryMothersLastName = extractSubstring(line, 60, 20).padEnd(20);
  const beneficiaryName = extractSubstring(line, 80, 30).padEnd(30);
  const paidPeriod = extractSubstring(line, 124, 6);
  const uniquePaymentId = extractSubstring(line, 130, 16);
  const numberOfDays = extractSubstring(line, 146, 2);
  const mutualAmount = '000000000000000';
  const ipsAmount = extractSubstring(line, 114, 8).padStart(15, '0');
  const ipsDifference = ipsAmount;
  const causeOfExcess = getCauseOfExcessCode(pension);
  const benefitType = extractSubstring(line, 112, 2);
  return [
    emissionPeriod,
    institutionRut,
    transferDate,
    transferCode,
    resolutionNumber,
    resolutionDate,
    pensionerRut,
    beneficiaryLastName,
    beneficiaryMothersLastName,
    beneficiaryName,
    paidPeriod,
    uniquePaymentId,
    numberOfDays,
    mutualAmount,
    ipsAmount,
    ipsDifference,
    causeOfExcess,
    benefitType
  ].join('');
};

module.exports = composeDpeapsLine;

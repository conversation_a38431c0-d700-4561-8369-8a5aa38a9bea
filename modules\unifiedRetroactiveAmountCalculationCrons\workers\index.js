const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const {
  worker: calculateRetroactiveDisabilityPension
} = require('../../retroactiveDisabilityPension/workers');
const { worker: calculateRetroactiveBankFile } = require('../../retroactiveBank/workers');
const {
  worker: calculateRetroactiveAmountForInstitutianalPatient
} = require('../../retroactiveAmountsInstitutionalPatient/workers');
const {
  worker: calculateRetroactiveAmountForSurvival
} = require('../../retroactiveAmountsForSurvivalPension/worker');

module.exports = {
  name: 'calculateTotalAssetsDiscountsAndRetroactiveAmounts',
  worker: deps =>
    workerModule.workerFn({
      logService,
      calculateRetroactiveDisabilityPension,
      calculateRetroactiveBankFile,
      calculateRetroactiveAmountForInstitutianalPatient,
      calculateRetroactiveAmountForSurvival,
      ...deps
    }),
  description: 'Cron unificado para el calculo de retroactivos',
  endPoint: 'unifiedretroactiveamounts',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const pipe = (...fns) => pensioner => fns.reduce((y, fn) => fn(y), pensioner);

const nonNestedDateFields = [
  'dateOfBirth',
  'resolutionDate',
  'accidentDate',
  'disabilityStartDate',
  'pensionStartDate',
  'inactivationDate',
  'endDateOfValidity',
  'reactivationDate',
  'evaluationDate',
  'endDateOfTheoricalValidity',
  'deathDate'
];

const apsInfoDateFields = ['apsResolutionDate'];

const transformStringToDate = (obj, arrOfDateFields = []) =>
  arrOfDateFields.reduce((dateObj, field) => {
    if (dateObj[field]) return { ...dateObj, [field]: new Date(dateObj[field]) };
    return { ...dateObj };
  }, obj);

const transformApsStringToDate = (obj, arrOfDateFields = []) =>
  arrOfDateFields.reduce((dateObj, field) => {
    if (dateObj[field]) return { ...dateObj, [field]: new Date(dateObj[field]) };
    return { ...dateObj, [field]: 0 };
  }, obj);

const transformToDates = (pensioner = {}) => {
  const { apsInfo, ...otherFields } = pensioner;

  const updatedPensionerFields = transformStringToDate(otherFields, nonNestedDateFields);
  const updatedApsInfoPensioner = transformApsStringToDate(apsInfo, apsInfoDateFields);

  return { ...updatedPensionerFields, apsInfo: { ...updatedApsInfoPensioner } };
};

const setDefaultInitialBasePension = ({ basePension, initialBasePension, ...rest }) => {
  if (!initialBasePension) return { ...rest, basePension, initialBasePension: basePension };

  return { ...rest, basePension, initialBasePension };
};

const processSpecialCases = pipe(setDefaultInitialBasePension, transformToDates);

module.exports = { processSpecialCases };

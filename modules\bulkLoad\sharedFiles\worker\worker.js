/* eslint-disable consistent-return */
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronDescription = 'bulkload cron';

const worker = async ({
  Logger,
  done,
  service,
  FOLDER_PATH,
  ftpCredentials,
  workerName,
  logService,
  sftp,
  downloadFilesFromSFTP,
  getParsedLinesFromFiles,
  pensionService,
  useSpecialGet
}) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(workerName);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    Logger.info(`Checkeando servidor FTP. Verificando recepcion archivo ${workerName}.`);
    const client = new sftp.Client();
    const { files, error } = await downloadFilesFromSFTP({
      client,
      sftp,
      ftpCredentials,
      FOLDER_PATH,
      workerName,
      useSpecialGet
    });

    if (error) {
      Logger.error(`Error al leer el archivo ${workerName} ${error}`);
      throw new Error(`${cronDescription} ${workerName}: ${error}`);
    }

    if (!files.length) {
      Logger.error(`Verificacion terminado. Aun no se ha recibido el archivo. ${workerName} `);
      throw new Error(`${cronDescription} ${workerName}: Aun no se ha recibido el archivo`);
    }

    Logger.info(`Archivo recibido. Inicio procesamiento archivos ${workerName}`);
    const parsedLines = await getParsedLinesFromFiles(...files);
    const { isError, error: err } = await service.updatePensions(pensionService, parsedLines);
    if (isError) {
      Logger.error(`${err}`);
      throw new Error(`${cronDescription} ${workerName}: ${err}`);
    }

    Logger.info(`Fin procesamiento archivos ${workerName}`);
    await logService.saveLog(workerName);
    return { executionCompleted: true, status: 'OK' };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(workerName);
    return { message: `${cronDescription} ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = worker;

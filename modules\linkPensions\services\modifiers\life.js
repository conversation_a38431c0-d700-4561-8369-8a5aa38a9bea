const moment = require('moment');

const isLifePension = validityType => /Vigente\s+vitalicia/i.test(validityType);

const setEndDatesOfValidity = (finalData, birthDate) => {
  if (finalData && birthDate) {
    const validityEndDate = moment(birthDate)
      .add(`${process.env.LIFE_PENSION_LIMIT_YEARS}`, 'years')
      .toDate();
    return {
      ...finalData,
      endDateOfTheoricalValidity: validityEndDate,
      endDateOfValidity: validityEndDate
    };
  }
  return finalData;
};

const setEndDateForLifePension = (pension, validityType) => {
  let modifiedData = { ...pension };
  if (isLifePension(validityType)) {
    modifiedData = setEndDatesOfValidity({ ...pension }, pension.dateOfBirth);
  }
  return {
    ...pension,
    ...modifiedData
  };
};

module.exports = {
  isLifePension,
  setEndDatesOfValidity,
  setEndDateForLifePension
};

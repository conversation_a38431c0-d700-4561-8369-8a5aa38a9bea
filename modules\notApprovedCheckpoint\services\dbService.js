const moment = require('moment');
const btoa = require('btoa');

const PensionModel = require('../../../models/pension');
const { excelService } = require('./excel.service');
const { pensionFields, simpleFields } = require('../formatters/fields');

const VALIDITY_TYPE = /No vigente/i;
const {
  EMAILS_TO_NOT_APPROVED_CHECKPOINT,
  EMAIL_FROM_NOT_APPROVED_CHECKPOINT,
  TEMPLATE_ID_NOT_APPROVED_CHECKPOINT,
  EMAIL_CC_NOT_APPROVED_CHECKPOINT
} = process.env;

const buildMail = excelAttachment => {
  const subject = `${moment().format(
    'YYYYMM'
  )} Control de Pensiones Vigentes - Error en punto de control de Pensión Líquida.`;
  const msg = {
    to: EMAILS_TO_NOT_APPROVED_CHECKPOINT,
    from: EMAIL_FROM_NOT_APPROVED_CHECKPOINT,
    cc: EMAIL_CC_NOT_APPROVED_CHECKPOINT,
    templateId: TEMPLATE_ID_NOT_APPROVED_CHECKPOINT,
    dynamic_template_data: {
      subject
    }
  };
  const filename = `=?UTF-8?B?${btoa(
    unescape(
      encodeURIComponent(`${moment().format('YYYYMM')} Punto de control Pensión Liquida.xlsx`)
    )
  )}?=`;

  const attachments = [
    {
      content: excelAttachment,
      filename,
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      disposition: 'attachment'
    }
  ];

  return { msg, attachments };
};

const getNotApprovedCheckpoint = () => {
  return PensionModel.aggregate([
    {
      $match: {
        validityType: { $not: VALIDITY_TYPE },
        checkPoint: false,
        enabled: true
      }
    },
    {
      $lookup: {
        from: 'liquidations',
        let: {
          pensionBeneficiaryRut: '$beneficiary.rut',
          pensionCausantRut: '$causant.rut',
          pensionCode: '$pensionCodeId'
        },

        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: ['$beneficiaryRut', '$$pensionBeneficiaryRut']
                  },
                  {
                    $eq: ['$causantRut', '$$pensionCausantRut']
                  },
                  {
                    $eq: ['$pensionCodeId', '$$pensionCode']
                  }
                ]
              }
            }
          }
        ],
        as: 'liquidation'
      }
    },
    {
      $unwind: '$liquidation'
    }
  ]);
};

const service = {
  async notApprovedCheckpoint(mailService) {
    try {
      const result = await getNotApprovedCheckpoint();

      if (result.length) {
        const wb = excelService(result, {
          headers: pensionFields,
          simpleFields,
          sheetName: 'Punto de control Pensión Liquida'
        });

        const excelAttachment = (await wb.writeToBuffer()).toString('base64');
        const { msg, attachments } = buildMail(excelAttachment);
        const { error } = await mailService.sendEmail(msg, attachments);
        if (error) throw new Error(error);
      }

      return { completed: true };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

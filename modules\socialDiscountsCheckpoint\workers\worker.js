const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'SOCIAL_DISCOUNTS_CHECK_POINT';
const dependencyMark = 'UNIFIED_TOTAL_ASSETS_AND_DISCOUNTS_WITH_NET_PENSION_LIQUIDATIONS_REPORT';
const missingDepMsg = `No se ha ejecutado la dependencia ${dependencyMark}`;
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const cronDescription = 'Punto de control de descuentos sociales';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, service, logService, job }) => {
  try {
    Logger.info(`Cron execution start: ${cronMark}. Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDepMsg);
      return { message: missingDepMsg, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark} process started`);
    const { error, updatedPensions } = await service.recalculateSocialDiscountsCheckpoint();
    if (error) throw new Error(error);
    const { error: recalculateError } = await service.recalculateNetPension(updatedPensions);
    if (recalculateError) throw new Error(recalculateError);

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark}: process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

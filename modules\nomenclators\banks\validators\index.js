const { check } = require('express-validator');
const { regRule, codeRule } = require('./validator');

const validators = [
  check('bank.city')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('bank.name')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('bank.direction')
    .notEmpty()
    .withMessage('city should not be empty')
    .isLength({ min: 1, max: 255 })
    .withMessage('max length should be 255')
    .matches(regRule)
    .withMessage('city should match the format'),
  check('bank.code')
    .notEmpty()
    .withMessage('code should not be empty')
    .isLength({ min: 3, max: 3 })
    .withMessage('min a & max length should be 5')
    .matches(codeRule)
    .withMessage('name should match the format')
];

module.exports = validators;

/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('unified worker create upload bank file test', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let service;
  let logService;
  let serviceGenerateFile;
  let storageService;
  let fsClient;
  let done;

  beforeEach(() => {
    done = jest.fn();

    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    service = {
      sendNotificationEmail: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    storageService = {
      uploadFileFromLocal: jest.fn(() => Promise.resolve({ status: 200, data: '2443' })),
      saveFileRegistry: jest.fn(() => Promise.resolve({ result: 'qwerty' }))
    };

    serviceGenerateFile = {
      generateBankPayRoll: jest.fn(() => Promise.resolve({ error: false }))
    };

    fsClient = {
      unlink: jest.fn(() => Promise.resolve())
    };
  });

  it('should call unified worker for bank file generation and upload', async () => {
    await workerModule.workerFn({
      service,
      moment,
      Logger,
      logService,
      serviceGenerateFile,
      storageService,
      fsClient,
      done
    });

    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(storageService.uploadFileFromLocal).toHaveBeenCalledTimes(1);
    expect(storageService.saveFileRegistry).toHaveBeenCalledTimes(1);
    expect(service.sendNotificationEmail).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(1);
  });

  it('should not find the reservedAsset filemark', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      service,
      moment,
      Logger,
      logService,
      serviceGenerateFile,
      storageService,
      fsClient,
      done
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(service.sendNotificationEmail).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should find the generateAndUploadBank filemark', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      service,
      moment,
      Logger,
      logService,
      serviceGenerateFile,
      storageService,
      fsClient,
      done
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(service.sendNotificationEmail).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should fail file generation', async () => {
    serviceGenerateFile.generateBankPayRoll = jest.fn(() => Promise.resolve({ error: true }));
    await workerModule.workerFn({
      service,
      moment,
      Logger,
      logService,
      serviceGenerateFile,
      storageService,
      fsClient,
      done
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(service.sendNotificationEmail).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

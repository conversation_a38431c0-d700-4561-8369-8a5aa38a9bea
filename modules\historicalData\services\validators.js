const NAME_PATTERN = /^[0-9a-záéíóúàèìòùãẽĩõũỹñäöüëïâêîôûçğş/#*"'.\s-]+$/i;
const RUT_PATTERN = /^\d{4,9}-[\dk]/i;
const EMPTY_PATTERN = /^$/i;

const isValidName = name => NAME_PATTERN.test(`${name}`);
const isValidSurname = name => NAME_PATTERN.test(`${name}`) || EMPTY_PATTERN.test(`${name}`);
const isValidRutPattern = rut => RUT_PATTERN.test(`${rut}`);

module.exports = {
  isValidName,
  isValidRutPattern,
  isValidSurname
};

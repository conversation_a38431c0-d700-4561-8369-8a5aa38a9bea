const { check, oneOf } = require('express-validator');

const checkDigitValidation = rutWithDV => {
  const [rut, currentDV] = rutWithDV.replace(/\./, '').split('-');
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const rutSanitizer = rut => rut.replace(/[^\dKk-]/g, '').toUpperCase();

const SI = /s[ií]/i;
const transformToBoolean = data => {
  const { institutionalPatient, ...otherFields } = data;
  if (!institutionalPatient) return { ...otherFields };
  return { ...otherFields, institutionalPatient: SI.test(institutionalPatient) };
};

const sanitizeRut = data => {
  const { collectorRut = '', ...otherFields } = data;
  if (!collectorRut) return { ...otherFields };
  return { ...otherFields, collectorRut: collectorRut.replace(/[^0-9K-]/gi, '') };
};

const listOfPensionTypes = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i,
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
  /Pensi[oó]n por orfandad/i,
  /Pensi[oó]n de orfandad de padre y madre/i
];

const setDefaultChangeOfPensionTypeDueToCharges = ({ pensionType, ...otherFields }) => {
  const ChangeOfPensionTypeDueToCharges = [
    /Pensi[oó]n de viudez con hijos/i,
    /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i
  ].some(regex => regex.test(pensionType));
  return { ...otherFields, pensionType, ChangeOfPensionTypeDueToCharges };
};

const checkPensionType = pensionType => listOfPensionTypes.some(regex => regex.test(pensionType));

const RUT_PATTERN = /^[1-9][0-9]?\d{6}-[0-9Kk]$/;
const PENSION_PATTERN = /^\d+$/;

const validators = oneOf(
  [
    check('query.pensionCodeId')
      .matches(PENSION_PATTERN)
      .withMessage('pensionCodeId should be a string of five numbers'),
    check('query.beneficiary.rut')
      .matches(RUT_PATTERN)
      .withMessage('beneficiary rut should match the format')
      .custom(value => checkDigitValidation(value))
      .withMessage('Should have a valid check digit'),
    check('query.causant.rut')
      .matches(RUT_PATTERN)
      .withMessage('causantRut should match the format')
      .custom(value => checkDigitValidation(value))
      .withMessage('Should have a valid check digit'),
    check('query.collector.rut')
      .matches(RUT_PATTERN)
      .withMessage('causantRut should match the format')
      .custom(value => checkDigitValidation(value))
      .withMessage('Should have a valid check digit')
  ],
  'either a rut or pension code must be provided'
);

const extendedDataValidation = [
  check('rutCausant')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('causantRut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('rutBeneficiary')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('beneficiary rut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit')
];

const extendedDataValidationPensioner = [
  check('causantRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('causantRut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('beneficiaryRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('beneficiary rut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('pensionerInfo')
    .notEmpty()
    .customSanitizer(data => transformToBoolean(data))
    .customSanitizer(data => sanitizeRut(data))
];

const pensionTypeValidation = [
  check('causantRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('causantRut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('beneficiaryRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('beneficiary rut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('pensionType')
    .notEmpty()
    .custom(data => checkPensionType(data))
    .withMessage('Should have a valid pension type'),
  check('.').customSanitizer(value => setDefaultChangeOfPensionTypeDueToCharges(value))
];

const rutSanitizationAndValidation = [
  check('causantRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('causantRut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit'),
  check('beneficiaryRut')
    .customSanitizer(value => rutSanitizer(value))
    .matches(RUT_PATTERN)
    .withMessage('beneficiary rut should match the format')
    .custom(value => checkDigitValidation(value))
    .withMessage('Should have a valid check digit')
];

module.exports = {
  validators,
  extendedDataValidation,
  extendedDataValidationPensioner,
  checkDigitValidation,
  pensionTypeValidation,
  rutSanitizationAndValidation,
  RUT_PATTERN,
  PENSION_PATTERN
};

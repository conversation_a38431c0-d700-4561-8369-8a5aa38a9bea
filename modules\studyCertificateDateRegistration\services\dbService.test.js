/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const service = require('./dbService');
const pensionsData = require('../../../resources/pensions.json');
const FamilyAssignmentModel = require('../../familyAssignment/models/temporaryFamilyAssignment');

describe('Register start date and end date of study certificate', () => {
  beforeAll(beforeAllTests);

  const familyAssignment = {
    chargeId: '11111111-1',
    causantId: '22222222-2',
    retroactiveFamilyAssignment: 10,
    familyAssignment: 1,
    typeOfAssocietedPension: 'Supervivencia',
    startDateOfCertificationValidity: '2019-05-01T04:00:00.000Z',
    endDateOfCertificationValidity: '2019-12-31T03:00:00.000Z',
    collectorId: '22222222-2',
    chargeValidityType: 'Interna activa'
  };

  const chargeIdPension = {
    ...pensionsData[0],
    enabled: true,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión por orfandad',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '11111111-1'
    },
    createdAt: '2019-12-11T04:00:00.000Z'
  };

  const chargeIdPension1 = {
    ...pensionsData[0],
    enabled: false,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión por orfandad',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '11111111-1'
    },
    createdAt: '2019-12-20T04:00:00.000Z'
  };

  const chargeIdPension2 = {
    ...pensionsData[0],
    enabled: false,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión por orfandad',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '11111111-1'
    },
    createdAt: '2019-12-30T04:00:00.000Z'
  };

  const collectorIdPension = {
    ...pensionsData[0],
    enabled: true,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión de madre de hijo de filiación no matrimonial con hijos',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '22222222-2'
    },
    createdAt: '2019-11-01T04:00:00.000Z'
  };

  const collectorIdPension1 = {
    ...pensionsData[0],
    enabled: true,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión de madre de hijo de filiación no matrimonial con hijos',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '22222222-2'
    },
    createdAt: '2019-08-01T04:00:00.000Z'
  };

  const collectorIdPension2 = {
    ...pensionsData[0],
    enabled: true,
    validatedStudyPeriod: 'No',
    validityType: 'vigente',
    pensionType: 'Pensión de madre de hijo de filiación no matrimonial con hijos',
    beneficiary: {
      ...pensionsData[0].beneficiary,
      rut: '22222222-2'
    },
    createdAt: '2019-08-20T04:00:00.000Z'
  };

  it('should add two pensions when there are two family assignment matches', async () => {
    await FamilyAssignmentModel.create(familyAssignment);
    await PensionModel.insertMany([
      chargeIdPension,
      chargeIdPension1,
      chargeIdPension2,
      collectorIdPension,
      collectorIdPension1,
      collectorIdPension2
    ]);
    const { completed, error } = await service.registerStartAndEndDateOfStudyCertificate();
    const totalPension = await PensionModel.countDocuments();
    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(totalPension).toBe(6);
  });

  it('should change validityStudyPeriod when study certificate is within valid date range', async () => {
    await FamilyAssignmentModel.create(familyAssignment);
    await PensionModel.insertMany([
      chargeIdPension,
      chargeIdPension1,
      chargeIdPension2,
      collectorIdPension,
      collectorIdPension1,
      collectorIdPension2
    ]);
    const { completed, error } = await service.registerStartAndEndDateOfStudyCertificate();
    const updatedPensions = await PensionModel.find({ validatedStudyPeriod: /S[íi]/i });
    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPensions.length).toBe(3);
    updatedPensions.forEach(pension => {
      expect(pension.validatedStudyPeriod).toMatch(/S[íi]/i);
    });
  });

  afterEach(async () => {
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

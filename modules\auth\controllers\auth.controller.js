/* eslint-disable consistent-return */
module.exports = ({ HttpStatus, userService, authService, Logger }) => {
  return {
    login: async (req, res) => {
      try {
        const { email } = req.authInfo;
        if (!email)
          return res
            .status(HttpStatus.UNAUTHORIZED)
            .json(
              'No se encuentra un email asociado al token de autorización, intente adquirir un nuevo token'
            );

        const user = await userService.findByEmail(email);
        if (!user)
          return res
            .status(HttpStatus.NOTFOUND)
            .json('Usuario no se encuentra registrado en el sistema');
        const { data } = await authService.login(user);
        return res.status(HttpStatus.OK).json(data);
      } catch (error) {
        Logger.error(`Login ${error}`);
        return res
          .status(HttpStatus.INTERNAL_SERVER_ERROR)
          .json(
            'En este momento no es posible realizar su petición, intente más tarde o comuniquese con el administrador'
          );
      }
    },
    logout: async (req, res) => {
      try {
        await authService.logout(req);
        res.json({ result: 'OK' });
      } catch (error) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: `${error}` });
      }
    }
  };
};

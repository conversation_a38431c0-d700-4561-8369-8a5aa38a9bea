const { roundValue, pipe } = require('../../sharedFiles/helpers');

const pensionTypes = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const setFixedBasePension = pension => ({
  ...pension,
  fixedBasePension: roundValue(pension.basePension)
});
const resetFixedBasePension = pension => ({
  ...pension,
  basePension: roundValue(pension.fixedBasePension)
});

const isMatched = ({ pensionType }) => pensionTypes.some(type => type.test(pensionType));

const setFixedArticle40And41 = pension =>
  isMatched(pension)
    ? {
        ...pension,
        fixedArticle40: roundValue(pension.article40),
        fixedArticle41: roundValue(pension.article41)
      }
    : { ...pension };

const resetFixedArticle40And41 = pension =>
  isMatched(pension)
    ? {
        ...pension,
        article40: roundValue(pension.fixedArticle40),
        article41: roundValue(pension.fixedArticle41)
      }
    : { ...pension };

const setFixedValues = obj => pipe(setFixedBasePension, setFixedArticle40And41)(obj);
const resetFixedValues = obj => pipe(resetFixedBasePension, resetFixedArticle40And41)(obj);

module.exports = { setFixedValues, resetFixedValues };

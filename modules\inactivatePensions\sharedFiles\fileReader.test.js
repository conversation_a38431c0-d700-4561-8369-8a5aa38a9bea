/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const path = require('path');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const fileReader = require('./fileReader');

// lib/moduleA/component1.js

describe('File Reader Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});
  it('success file reader', async () => {
    const fileTxt = path.join(__dirname, '../../../resources/Achs_202002_out.txt');
    const lines = await fileReader.readLines(fileTxt, 'death');
    const [rut, date] = lines[0];
    expect(rut).toBe('255552074');
    expect(date).toBe('20200101');
  });

  afterAll(afterAllTests);
});

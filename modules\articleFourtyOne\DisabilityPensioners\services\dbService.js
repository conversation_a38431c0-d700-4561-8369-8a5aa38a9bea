/* eslint-disable consistent-return */
const PensionModel = require('../../../../models/pension');

const INITIAL_NUMBER_OF_CHARGES = 3;
const INCREASE_PERCENTAGE = process.env.ARTICLE_41_INCREASE_PERCENTAGE || 5;
const DISABILITY_TYPES = [/Invalidez\s+parcial/i, /Invalidez\s+total/i, /Gran\s+inv[áa]lido/i];

const PENSION_TYPES = [
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trabajo/i,
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trayecto/i,
  /Pensi[óo]n\s+por\s+enfermedad\s+profesional/i
];

const maxCharges = {
  invalidezparcial: 12,
  invalideztotal: 22,
  graninvalido: 30
};

const getMaxChargesKey = type =>
  type
    .replace(/\s+/g, '')
    .replace(/á/g, 'a')
    .toLowerCase();

const getCharges = (numberOfChargesArticle41, disabilityType) => {
  const key = getMaxChargesKey(disabilityType);
  return numberOfChargesArticle41 > maxCharges[key] ? maxCharges[key] : numberOfChargesArticle41;
};

const calculateArticleFourtyOne = ({
  numberOfChargesArticle41,
  disabilityType,
  basePension,
  ...data
}) => {
  const charges = getCharges(numberOfChargesArticle41, disabilityType);
  const article41 = +((INCREASE_PERCENTAGE / 100) * (charges - 2) * basePension).toFixed(2);
  return { ...data, numberOfChargesArticle41, disabilityType, basePension, article41 };
};

const getModifiedPensionList = array =>
  array.map(({ _id, __v, ...data }) => calculateArticleFourtyOne({ ...data }));

const getPensions = async () => {
  return PensionModel.find({
    enabled: true,
    pensionType: { $in: PENSION_TYPES },
    disabilityType: { $in: DISABILITY_TYPES },
    numberOfChargesArticle41: { $gte: INITIAL_NUMBER_OF_CHARGES }
  })
    .lean()
    .exec();
};

module.exports = {
  async updatePensions(pensionsService) {
    try {
      const pensions = await getPensions();
      if (!pensions.length) return { completed: true, error: null };
      const modifiedPensions = getModifiedPensionList(pensions);
      const { completed, error } = await pensionsService.createUpdatePension(modifiedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

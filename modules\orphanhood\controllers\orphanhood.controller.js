module.exports = ({
  HttpStatus,
  orphanhoodService,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  const service = orphanhoodService;
  const inactivationReasonOrphanhood = /Vencimiento de certificado de estudios/i;
  const validityTypeOnDesactivationOrphanhood = /No vigente/i;

  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    insertAllOrphanhood: async (req, res) => {
      const { body } = req;
      const { isError, error, result } = await service.bulkAndDeleteOrphanhood(body, service);

      Logger.info('Bulk creation Orphanhood:');

      if (isError) {
        Logger.error(`Bulk insertion Orphanhood error: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.CREATED).json(result);
      }
    },

    wasInactivatedOrphanhood: async (req, res) => {
      if (Object.entries(req.body).length > 0) {
        res.status(HttpStatus.BAD_REQUEST).end();
        return;
      }

      const { wasInactivated, completed, isError, error } = await service.wasInactivatedOrphanhood(
        inactivationReasonOrphanhood,
        validityTypeOnDesactivationOrphanhood
      );
      if (isError) {
        Logger.error(`Get error: ${error}`);
        manageError(res, error);
      } else {
        Logger.info(`wasInactivated result: ${JSON.stringify(wasInactivated, completed)}`);
        res.status(HttpStatus.OK).json({ wasInactivated, completed });
      }
    }
  };
};

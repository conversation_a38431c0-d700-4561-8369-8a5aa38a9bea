// Taken from https://gist.github.com/rotvulpix/69a24cc199a4253d058c
const RUT_REGEX = /(\d{1,2})\.?(\d{3})\.?(\d{3})-?(\d|K)/i;
const calculateCheckDigit = rut => {
  const stringRut = `${rut}`;
  let count = 0;
  let multiple = 2;

  for (let i = 1; i <= stringRut.length; i += 1) {
    const index = multiple * stringRut.charAt(stringRut.length - i);
    count += index;

    multiple = multiple < 7 ? multiple + 1 : 2;
  }

  const expectedCheckDigit = 11 - (count % 11);
  if (expectedCheckDigit === 10) return 'K';
  if (expectedCheckDigit === 11) return '0';
  return `${expectedCheckDigit}`;
};

const isValidRut = rut => {
  if (!rut || rut.toString().trim().length < 3) return false;
  const cleanRut = rut.toString().replace(/[^0-9kK-]/g, '');

  if (cleanRut.length < 3 || cleanRut.length > 10) return false;

  const split = cleanRut.split('-');
  if (split.length !== 2) return false;

  const numericRut = parseInt(split[0], 10);
  const checkDigit = split[1].toUpperCase();

  const expectedCheckDigit = calculateCheckDigit(numericRut);
  return checkDigit === expectedCheckDigit && RUT_REGEX.test(rut);
};

const rutFormatter = rut =>
  `${rut}`
    .replace(/^0+|[^\dkK]/g, '')
    .replace(/k(?!\b)/gi, '')
    .replace(/([0-9kK])$/, '-$1')
    .toUpperCase();

const codeRegex = /(\d{2})/;
const codeRule = new RegExp(codeRegex, 'i');

const codeFormatter = code =>
  code
    ? code
        .toString()
        .replace(/[^0-9]+/gi, '')
        .replace(/(\d{2})/gi, '$1')
    : '';

module.exports = { calculateCheckDigit, isValidRut, codeRule, rutFormatter, codeFormatter };

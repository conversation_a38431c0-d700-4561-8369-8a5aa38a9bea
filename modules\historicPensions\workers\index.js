const workerModule = require('./worker');
const service = require('../services/dbService');
const temporaryService = require('../services/temporaryPensionService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'historical-pension-reports',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      temporaryService,
      ...deps
    }),
  repeatInterval: process.env.CRON_HISTORICAL_PENSION_REPORTS,
  description: 'Reporte de pensiones inactivadas y reactivadas en el mes',
  endPoint: 'historicalpensionreports',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

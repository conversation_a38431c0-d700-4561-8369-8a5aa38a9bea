/* eslint-disable no-param-reassign */
const xl = require('excel4node');
const { simpleFields } = require('../formatters/fields');

const { inactivateFields, reactivateFields } = require('../formatters/fields');
const { filterData } = require('../formatters/formatFields');

const MAX_COLUMN_WIDTH = 27;
const CELL_PADDING = 7;
const STARTING_ROW = 2;
const STARTING_COL = 1;

const insertCell = ({ formatter, ...props }) => formatter(props);

const putHeaders = (sheet, keys, fields) => {
  keys.forEach((v, i) => {
    sheet.cell(1, i + 1).string(fields[i][v].name);
  });
};

const maxCharacters = (current = '', incoming = '') => {
  let characterCount = incoming ? incoming.toString().length + CELL_PADDING : CELL_PADDING;
  characterCount = characterCount > MAX_COLUMN_WIDTH ? MAX_COLUMN_WIDTH : characterCount;
  if (!current) {
    return characterCount;
  }
  return characterCount > current ? characterCount : current;
};

const populateSheet = (result, sheet, keys, fields, columnWidth) => {
  result.forEach((data, row) => {
    keys.forEach((colName, col) => {
      columnWidth[col + 1] = maxCharacters(columnWidth[col + 1], data[colName]);
      insertCell({
        sheet,
        currentRow: row + STARTING_ROW,
        currentCol: col + STARTING_COL,
        value: data[colName],
        formatter: fields[col][colName].format
      });
    });
  });
};

const excelService = async (result, wb, name) => {
  const dictionary = { inactivations: 'Inactivaciones', reactivations: 'Reactivaciones' };
  const sheet = wb.addWorksheet(dictionary[name], {
    disableRowSpansOptimization: true,
    author: 'ACHS'
  });

  const fields = name === 'inactivations' ? inactivateFields : reactivateFields;

  const keys = fields.map(v => Object.keys(v));

  putHeaders(sheet, keys, fields);

  const columnsWidth = {};

  if (result.length) {
    const processedData = filterData(result, simpleFields);
    populateSheet(processedData, sheet, keys, fields, columnsWidth);
  }

  Object.keys(columnsWidth).forEach(column => sheet.column(column).setWidth(columnsWidth[column]));
};

const createSheets = data => {
  const workbook = new xl.Workbook();

  Object.keys(data).forEach(key => {
    excelService(data[key], workbook, key);
  });

  return workbook;
};

module.exports = {
  excelService,
  createSheets,
  populateSheet,
  putHeaders
};

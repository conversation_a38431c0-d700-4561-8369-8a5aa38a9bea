/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const IsapresModel = require('../models/isapre');
const TemporaryIsaprePortalModel = require('../models/temporaryIsaprePortal');
const service = require('./index.service');

let Logger;

describe('Isapres nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {});

  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };

  it('should create', async () => {
    const { error } = await service.createIsapre({
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '00',
      enabled: true
    });
    const { result, isError } = await service.getIsapres();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });

  it('should update isapres of created Pension', async () => {
    await IsapresModel.create({
      id: 'id-1-B',
      name: 'Isapre Test',
      rut: '12.345.678-7',
      code: '10',
      enabled: true
    });

    await TemporaryIsaprePortalModel.insertMany([
      { isapreId: '10', affiliateRut: '8733521-6', totalDiscount: 33.9 }
    ]);
    const { isError, executionCompleted } = await service.bulkUpdate();

    expect(isError).toBeFalsy();
    expect(executionCompleted).toBe(true);
  });

  it('fail update isapres of created Pension', async () => {
    await TemporaryIsaprePortalModel.insertMany([
      { isapreId: '75', affiliateRut: '8733521-6', totalDiscount: 33.9 }
    ]);

    jest.spyOn(TemporaryIsaprePortalModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });
    const { isError, executionCompleted } = await service.bulkUpdate();

    expect(isError).toBe(true);
    expect(executionCompleted).toBeFalsy();
  });

  it('without isapres to update', async () => {
    const { isError, executionCompleted } = await service.bulkUpdate();

    expect(isError).toBe(true);
    expect(executionCompleted).toBeFalsy();
  });
  it('check if was load data', async () => {
    await TemporaryIsaprePortalModel.insertMany([
      { isapreId: '75', affiliateRut: '8733521-6', totalDiscount: 33.9 }
    ]);

    const { wasLoadData } = await service.wasExecutedIsaprePortalProcess();

    expect(wasLoadData).toBe(true);
  });

  it('should find one and update', async () => {
    await IsapresModel.create({
      id: 'id-1-B',
      name: 'Isapre b x',
      rut: '16.913.485-5',
      code: '11',
      enabled: false
    });

    const newIsapreData = {
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '00',
      enabled: true
    };
    // create and save one  document
    await IsapresModel.create(newIsapreData);

    const { error } = await service.updateIsapre({
      ...newIsapreData,
      name: 'Isapre z',
      code: '11'
    });
    const { result, isError } = await service.getIsapres();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('Isapre z');
  });

  it('should delete an Isapre', async () => {
    // create and save one  document
    const isapre = await IsapresModel.create({
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '00',
      enabled: true
    });
    const { id, createdAt, updatedAt } = { ...isapre.toObject() };

    const { error } = await service.deleteIsapre(id);

    const [resultWithoutIsapres, resultWithIsapres] = await Promise.all([
      service.getIsapres(),
      service.getIsapres({ enabled: false })
    ]);
    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());

    expect(resultWithoutIsapres.result.length).toBe(0);
    expect(resultWithIsapres.result.length).toBe(1);
    expect(resultWithIsapres.result[0].enabled).toEqual(false);
  });

  it('should throw error when trying to update an Isapre', async () => {
    mocks = {
      exec: jest.fn(() => Promise.reject())
    };
    jest.spyOn(IsapresModel, 'findOneAndUpdate').mockImplementationOnce(() => mocks);
    // create and save one  document
    const isapre = await IsapresModel.create({
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '00',
      enabled: true
    });

    const { id, name } = await { ...isapre.toObject(), name: 'Isapre B' };
    const { isError } = await service.updateIsapre({
      id,
      name
    });

    expect(isError).toBe(true);
  });

  it('can´t  duplicate code', async () => {
    // create and save one  document
    await IsapresModel.create({
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '10',
      enabled: true
    });
    const isapre2 = await IsapresModel.create({
      id: 'id-1-B',
      name: 'Isapre B',
      rut: '11.111.111-1',
      code: '11',
      enabled: true
    });

    const { id, code } = { ...isapre2.toObject(), code: '10' };
    const { error } = await service.updateIsapre({
      id,
      code
    });
    const { result } = await service.getIsapres();

    expect(result[1].code).toBe('11');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  it('can´t  duplicate rut ', async () => {
    // create and save one  document
    await IsapresModel.create({
      id: 'id-1-A',
      name: 'Isapre A',
      rut: '12.345.678-5',
      code: '10',
      enabled: true
    });
    const isapre2 = await IsapresModel.create({
      id: 'id-1-B',
      name: 'Isapre B',
      rut: '11.111.111-1',
      code: '11',
      enabled: true
    });

    const { id, rut } = { ...isapre2.toObject(), rut: '12.345.678-5' };
    const { error } = await service.updateIsapre({
      id,
      rut
    });
    const { result } = await service.getIsapres();

    expect(result[1].rut).toBe('11.111.111-1');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });
  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await IsapresModel.deleteMany({});
      await TemporaryIsaprePortalModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

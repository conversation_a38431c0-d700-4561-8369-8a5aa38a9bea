const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  async basePensionAndArticlesFixedValues(modifierFn) {
    try {
      const pensions = await pensionService.findValidPensions();
      const modifiedPensions = pensions.map(p => modifierFn(p));
      pensionService.updatePensions(modifiedPensions);
      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

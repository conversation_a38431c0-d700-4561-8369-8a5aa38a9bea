const {
  inactivatePensionsByTransientsPreWorker,
  inactivatePensionsByTransientsPostWorker
} = require('./inactivatePensions/transients/workers');
const {
  inactivatePensionsByRetirementPreWorker,
  inactivatePensionsByRetirementPostWorker
} = require('./inactivatePensions/retirement/workers');
const {
  setFixedBasePensionAndArticlesValue,
  resetFixedBasePensionAndArticlesValue
} = require('./fixedValues/workers');

const daysOfTranstientPension = require('./transients/workers');
const inactivatePensionsDeath = require('./inactivatePensions/death/workers');
const dataToCivilRegistration = require('./pensions/workers');
const inactivatePensionsByAgeLimit = require('./inactivatePensions/ageLimit/workers');
const { marriagePostworker, marriagePreworker } = require('./inactivatePensions/marriage/workers');
const reactivatePensionsByTransients = require('./reactivatePensions/transients/workers');
const obtainIPCMonthlyOrHourly = require('./ipc/workers');
const basePensionWorker = require('./basePension/workers');
const taxablePension = require('./taxablePensions/workers');
const setZeroValuesPensionWorker = require('./resetValuePensions/workers');
const netPensionsLiquidationReports = require('./netPensionsLiquidationReports/workers');
const obtainUfValue = require('./UFvalue/workers');
const calculatePaymentDatesWorker = require('./paymentDate/workers');
const historicalPensionReports = require('./historicPensions/workers');
const generateAndUploadPreviredFile = require('./fileUpload/previred/workers');
const reajustBasePensionAndArticles = require('./reajustBasePensionAndArticles/workers');
const calculateDaysToPayWorker = require('./calculateDaysToPay/workers');
const inactivateOrReactivateFamilyAssignmentProcess = require('./InactivateOrReactivateProcess/workers');
const inactivateOrReactivateOrphanhoodProcess = require('./InactivateOrReactivateOrphanhoodProcess/workers');
const schedulingCronjobs = require('./scheduleCronExecution/workers');
const calculateUnifiedRetroactiveAmounts = require('./unifiedRetroactiveAmountCalculationCrons/workers');
const totalAssetsDiscountsAndRetroactiveUnifiedCronScheduler = require('./unifiedRetroactiveAmountCalculationCrons/workers');
const unifiedBulkLoadAndIpsFiles = require('./bulkLoad/unifiedBulkLoadAndIpsCrons/workers');
const unifiedReservedAssetsAndDiscountsAmountCalculation = require('./unifiedReservedAmountCalculationCrons/workers');
const monthlyExpenses = require('./monthlyExpenses/workers');
const widowhoodPayment = require('./widowhoodPayment/worker');
const postLiquidationCheckpointReport = require('./postLiquidationCheckpoint/workers');
const unifiedGenerateAndUploadBankFile = require('./unifiedGenerateAndUploadBankFileCrons/workers');
const inactivateOrReactivateAFCronsGroup = require('./inactivateOrReactivateAFCronsGroup/workers');
const socialDiscountsCheckPoint = require('./socialDiscountsCheckpoint/workers');
const analysisOfCurrentCapital = require('./analysisOfCurrentCapital/workers');
const unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport = require('./unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCron/workers');
const totalAssetsAndDiscounts = require('./totalAssetsAndDiscounts/workers');

const reservedAmountAssetsDiscounts = require('./reservedAmountOfNonFormulableAssetsDiscountsTotal/worker');

const keyBuilder = require('./keyBuilder/workers');
const calculateCurrentCapital = require('./calculateCurrentCapital/workers');
const collectionDiscountHealth = require('./sendCollectionFiles/collectionDiscountHealth/workers');
const sendCircularfile2480 = require('./sendCollectionFiles/sendCircular2480/workers');
const healthExemptionPayment = require('./sendCollectionFiles/healthExemptionPayment/workers');
const updatePensionerInfo = require('./queryPensions/worker');
const healthRejection = require('./healthRejection/workers');
const transferPensions = require('./transferPensions/workers');
const rejectedReservedAmount = require('./rejectedReservedAmount/workers');
const apsCollection = require('./apsCollection/workers');
const notApprovedCheckpoint = require('./notApprovedCheckpoint/workers');
const rejectedRetroactiveAmount = require('./rejectedRetroactiveAmount/workers');
const manuallyInactivateMarkedPensions = require('./manuallyInactivateMarkedPensions/workers');
const winterBonusAssignment = require('./winterBonusAssignment/workers');
const generateBonusAssignmentFile = require('./generateBonusAssignmentFile/workers');
const expenseAccountingReport = require('./expenseAccountingReport/workers');
const modifyCivilRegistryData = require('./modifyCivilRegistryData/workers');
const assignNationalHolidaysBonus = require('./assignNationalHolidaysBonus/workers');
const assignChristmasBonus = require('./assignChristmasBonus/workers');
const notificationEndOfValidity = require('./notificationEndOfValidity/workers');
const oldAgePensionInProcess = require('./oldAgePensionInProcess/workers');
const discountJudicialRetention = require('./discountJudicialRetention/workers');
const calculationOfEarnedFields = require('./calculationOfEarnedFields/workers');

module.exports = [
  assignNationalHolidaysBonus,
  assignChristmasBonus,
  inactivatePensionsByTransientsPreWorker,
  inactivatePensionsByTransientsPostWorker,
  inactivatePensionsDeath,
  dataToCivilRegistration,
  inactivatePensionsByAgeLimit,
  marriagePreworker,
  marriagePostworker,
  inactivatePensionsByRetirementPreWorker,
  inactivatePensionsByRetirementPostWorker,
  reactivatePensionsByTransients,
  obtainIPCMonthlyOrHourly,
  basePensionWorker,
  taxablePension,
  setZeroValuesPensionWorker,
  daysOfTranstientPension,
  netPensionsLiquidationReports,
  obtainUfValue,
  calculatePaymentDatesWorker,
  historicalPensionReports,
  generateAndUploadPreviredFile,
  setFixedBasePensionAndArticlesValue,
  resetFixedBasePensionAndArticlesValue,
  reajustBasePensionAndArticles,
  unifiedBulkLoadAndIpsFiles,
  calculateDaysToPayWorker,
  inactivateOrReactivateFamilyAssignmentProcess,
  inactivateOrReactivateOrphanhoodProcess,
  unifiedReservedAssetsAndDiscountsAmountCalculation,
  totalAssetsDiscountsAndRetroactiveUnifiedCronScheduler,
  calculateUnifiedRetroactiveAmounts,
  schedulingCronjobs,
  monthlyExpenses,
  widowhoodPayment,
  postLiquidationCheckpointReport,
  widowhoodPayment,
  unifiedGenerateAndUploadBankFile,
  inactivateOrReactivateAFCronsGroup,
  reservedAmountAssetsDiscounts,
  keyBuilder,
  calculateCurrentCapital,
  socialDiscountsCheckPoint,
  analysisOfCurrentCapital,
  unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport,
  updatePensionerInfo,
  healthRejection,
  collectionDiscountHealth,
  healthExemptionPayment,
  sendCircularfile2480,
  transferPensions,
  rejectedReservedAmount,
  apsCollection,
  notApprovedCheckpoint,
  rejectedRetroactiveAmount,
  manuallyInactivateMarkedPensions,
  winterBonusAssignment,
  totalAssetsAndDiscounts,
  generateBonusAssignmentFile,
  modifyCivilRegistryData,
  expenseAccountingReport,
  notificationEndOfValidity,
  oldAgePensionInProcess,
  discountJudicialRetention,
  calculationOfEarnedFields
];

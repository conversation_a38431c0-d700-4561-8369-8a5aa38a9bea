const moment = require('moment');

const getHigherRepose = (saved, currentValue) => {
  const { fechaInicioReposo } = saved;
  const { fechaInicioReposo: date } = currentValue;
  if (moment(fechaInicioReposo).diff(moment(date)) < 0) {
    return currentValue;
  }
  return saved;
};

const getHigherResolution = (saved, currentValue) => {
  const { fechaResolucion, indicadorBorrado } = saved;
  const { fechaResolucion: date, indicadorBorrado: deleteIndex } = currentValue;
  if (moment(fechaResolucion).diff(moment(date)) < 0 && !deleteIndex) {
    return currentValue;
  }
  return !indicadorBorrado ? saved : '';
};

const isRangeAccept = value =>
  moment(value).diff(moment.now()) < 0 && moment(value).diff(moment('1950-01-01')) > 0;

const isHigherThanActuallyDate = date => moment(date).diff(moment.now()) > 0;

const verifyHighMedical = fechaAlta => {
  if (!fechaAlta || isHigherThanActuallyDate(fechaAlta)) {
    return null;
  }
  return { endDateOfValidity: fechaAlta, reason: 'Alta médica' };
};

const getDataToInactivate = ({ axios, Logger, accidentCode }) => {
  const key = process.env.CRON_TO_INACTIVATE_SAP_KEY;
  const url = process.env.CRON_TO_INACTIVATE_SAP_URL;
  const authType = process.env.CRON_TO_INACTIVATE_SAP_AUTH_TYPE;

  const options = {
    method: 'GET',
    headers: { [authType]: key },
    url: `${url}?idSiniestro=${accidentCode.padStart(10, 0)}`
  };

  return axios(options).catch(err => {
    Logger.error('err:', err);
    return { data: { reposos: [], incapacidad: [] } };
  });
};

const inactivateService = ({ causantRut, beneficiaryRut, endDateOfValidity, reason }) => {
  const dateToInactivate = moment()
    .add(1, 'months')
    .startOf('month');
  const pensionToInactivate = {
    causantRut,
    beneficiaryRut,
    inactivationReason: reason,
    endDateOfValidity,
    dateToInactivate: dateToInactivate.toDate()
  };

  return pensionToInactivate;
};

const isInactivatedByResolution = ({ incapacityData, fechaAlta }) => {
  const { fechaResolucion, indicadorBorrado, modalidad } = incapacityData;

  if (isHigherThanActuallyDate(fechaResolucion)) {
    return null;
  }

  if (indicadorBorrado) {
    return verifyHighMedical(fechaAlta);
  }
  if (modalidad === '1') {
    return verifyHighMedical(fechaAlta);
  }
  return { endDateOfValidity: fechaResolucion, reason: 'Resolución definitiva' };
};

const isAvailableToInactivate = reposes => {
  if (!reposes) return { result: false };

  const { fechaInicioReposo, fechaAlta } = reposes.reduce(getHigherRepose);

  if (isHigherThanActuallyDate(fechaInicioReposo)) {
    return { result: false };
  }
  if (isRangeAccept(fechaAlta)) {
    return { result: true, fechaAlta };
  }

  if (isHigherThanActuallyDate(fechaAlta) || !fechaAlta) {
    return { result: true, fechaAlta };
  }
  return { result: false };
};

const inactivatePension = ({ incapacidad, fechaAlta }) => {
  if (!incapacidad) {
    return { endDateOfValidity: fechaAlta, reason: 'Alta médica' };
  }
  const incapacityData = incapacidad.reduce(getHigherResolution);

  if (!incapacityData) {
    if (!fechaAlta || isHigherThanActuallyDate(fechaAlta)) {
      return null;
    }
    return { endDateOfValidity: fechaAlta, reason: 'Alta médica' };
  }
  const { endDateOfValidity = '', reason = '' } =
    isInactivatedByResolution({ incapacityData, fechaAlta }) || {};
  if (!endDateOfValidity || !reason) {
    return null;
  }

  return { reason, endDateOfValidity };
};

module.exports = {
  inactivateService,
  inactivatePension,
  isAvailableToInactivate,
  getDataToInactivate,
  getHigherResolution,
  isHigherThanActuallyDate,
  isRangeAccept,
  isInactivatedByResolution,
  getHigherRepose
};

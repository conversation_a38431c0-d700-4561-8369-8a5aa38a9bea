module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger,
  fsClient,
  service,
  split
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    insertAll: async (req, res) => {
      try {
        const { historicalData } = req.files;
        const { insertpensionerskey, datetocreate } = req.headers;
        const { INSERT_PENSIONERS_AUTHORIZATION } = process.env;

        if (INSERT_PENSIONERS_AUTHORIZATION !== insertpensionerskey)
          throw new Error('No autorizado para insertar pensionados');

        Logger.info('Reading document of historicalData ...');
        const { pensioners, error: errorReadStream } = await service.getPensionersFromFile({
          path: historicalData.path,
          fsClient,
          split
        });
        if (errorReadStream) {
          Logger.error(`Bulk insertion error: ${errorReadStream}`);
          return manageError(res, errorReadStream);
        }

        Logger.info('Starting Bulk insertion of historical data');
        const { error: errorBulkInsertion } = await service.insertAll(pensioners, datetocreate);
        if (errorBulkInsertion) {
          Logger.error(`Bulk insertion error: ${errorBulkInsertion}`);
          return manageError(res, errorBulkInsertion);
        }

        return res.status(HttpStatus.CREATED).json({ message: 'Completado correctamente.' });
      } catch (error) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ errorMessage: error.message });
      }
    }
  };
};

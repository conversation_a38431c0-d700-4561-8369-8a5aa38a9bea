const mongoose = require('mongoose');

const { Schema } = mongoose;

const BanksSchema = new Schema(
  {
    id: { type: String, required: true },
    city: { type: String, required: true },
    name: { type: String, required: true },
    direction: { type: String, required: true },
    code: { type: String, minlength: 3, maxlength: 3, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);
BanksSchema.index({ id: 1 }, { unique: true });
BanksSchema.index({ code: 1 }, { unique: true });
BanksSchema.index({ name: 1 }, { unique: true, collation: { locale: 'es', strength: 1 } });

module.exports = mongoose.model('Banks', BanksSchema);

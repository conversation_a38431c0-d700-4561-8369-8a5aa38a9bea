const { beforeAllTests, afterAllTests, Logger } = require('../../testsHelper');
const workerModule = require('./worker');

describe('UF Value Worker', () => {
  beforeAll(beforeAllTests);
  let done;
  let service;
  let fetchSoapData;
  let logService;

  beforeEach(async () => {
    done = jest.fn();
    service = {
      findPreviousMonthIPCS: jest.fn().mockResolvedValue(true),
      createUfValue: jest.fn().mockResolvedValue(true)
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    fetchSoapData = jest.fn().mockResolvedValue({
      data: {
        indexDateString: '30-06-2020',
        value: 28716.52
      }
    });
  });

  it('should return if current month UF Log exists', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, done, fetchSoapData, service, logService });
    expect(fetchSoapData).not.toHaveBeenCalled();
  });

  it('should return last day current UF value', async () => {
    logService.existsLogAndRetry = jest.fn().mockResolvedValue(false);
    await workerModule.workerFn({ Logger, done, fetchSoapData, service, logService });
    expect(fetchSoapData).toHaveBeenCalled();
  });

  it('should create UF value if conditions are met', async () => {
    await workerModule.workerFn({ Logger, done, fetchSoapData, service, logService });
    expect(fetchSoapData).toHaveBeenCalled();
    expect(service.createUfValue).toHaveBeenCalled();
    expect(logService.saveLog).toHaveBeenCalled();
  });

  it('should call logger.error if there is an error', async () => {
    logService.saveLog = jest.fn().mockImplementationOnce(() => {
      throw new Error('ERROR');
    });
    await workerModule.workerFn({ Logger, done, fetchSoapData, service, logService });
    expect(Logger.error).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

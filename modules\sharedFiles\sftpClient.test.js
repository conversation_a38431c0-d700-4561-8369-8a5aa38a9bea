/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../testsHelper');
const sftp = require('./sftpClient');

describe('Send circular file 2480 worker test', () => {
  beforeAll(beforeAllTests);

  it('should instantiate Client class and the instance object should have custom and all ssh2-sftp-client methods', async () => {
    const Client = new sftp.Client();
    expect(Client.uploadFrom).toBeDefined();
    expect(Client.downloadTo).toBeDefined();
    expect(Client.exists).toBeDefined();
    expect(Client.close).toBeDefined();
    Client.uploadFrom('local.txt', 'remote.txt').catch(error =>
      expect(error.stack).toMatch(/fastPut/)
    );
    Client.downloadTo('local.txt', 'remote.txt').catch(error =>
      expect(error.stack).toMatch(/fastGet/)
    );
    Client._downloadToFile('local.txt', 'remote.txt').catch(error =>
      expect(error.stack).toMatch(/fastGet/)
    );
    Client.close('local.txt', 'remote.txt').catch(error => expect(error.stack).toMatch(/end/));
  });

  it('should connect to SFTP server', async () => {
    const Client = { connect: jest.fn().mockResolvedValue({ error: false, connected: true }) };
    const { connected, error } = await sftp.connectToSFTPServer(Client, {});
    expect(Client.connect).toHaveBeenCalled();
    expect(connected).toBe(true);
    expect(error).toBe(false);
  });

  it('should return error if connection to SFTP server fails', async () => {
    const Client = {
      connect: jest.fn(() => {
        throw new Error('connection failed');
      })
    };
    sftp.connectToSFTPServer(Client, {});
    const { connected, error } = await sftp.connectToSFTPServer(Client, {});
    expect(Client.connect).toHaveBeenCalled();
    expect(connected).toBe(false);
    expect(error.message).toBe('connection failed');
  });

  afterAll(afterAllTests);
});

{"apps": [{"name": "achs-pensiones-qa1", "script": "./dist/index.js", "watch": true, "merge_logs": true, "cwd": "/home/<USER>/backend/achs-pensiones-qa1", "env": {"NODE_ENV": "qa", "PORT": "8001", "MONGODB_DB_URL": "mongodb+srv://user-prod-achs:<EMAIL>/23people-qa1?authSource=admin&replicaSet=Cluster0-shard-0&readPreference=primary&appname=MongoDB%20Compass%20Community&ssl=true", "ACHS_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "ACHS_FTP_OUTPUT_FOLDER": "/files/qa1/Entrada", "ACHS_FTP_PASS": "FTPAchs23people", "ACHS_FTP_PORT": "21", "ACHS_FTP_USER": "ftpuser", "ARTICLE_41_INCREASE_PERCENTAGE": "*/1 * * * *", "ALFRESCO_API": "**************************************/ServiciosGenerales/api/PrestacionesEconomicas", "ALFRESCO_AF": "80c5f7c3-f675-4d16-98a1-73603e31de98", "MIDDLEWARE_SUBSCRIPTION_KEY": "2b4beff42a5b433cad5bb1bc67b81fc3", "CAJA_LOSHEROES_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "CAJA_LOSHEROES_FTP_PASS": "FTPAchs23people", "CAJA_LOSHEROES_FTP_PORT": "21", "CAJA_LOSHEROES_FTP_USER": "ftpuser", "CIVIL_REGISTRATION_FTP_HOST": "achs-mock-ftp.brazilsouth.cloudapp.azure.com", "CIVIL_REGISTRATION_FTP_INPUT_FOLDER": "files/qa1/Entrada/", "CIVIL_REGISTRATION_FTP_OUTPUT_FOLDER": "/files/qa1/Salida", "CIVIL_REGISTRATION_FTP_PASS": "FTPAchs23people", "CIVIL_REGISTRATION_FTP_PORT": "21", "CIVIL_REGISTRATION_FTP_USER": "ftpuser", "CRON_CALCULATE_TAXABLE_PENSIONS": "*/5 * * * *", "CRON_INACTIVATE_BY_AGE_LIMIT": "0 0 7 6 *", "CRON_TO_INACTIVATE_SAP_AUTH_TYPE": "Ocp-Apim-Subscription-Key", "CRON_TO_INACTIVATE_SAP_KEY": "2edba90a2c3a4218b33e90be3c36a567", "CRON_TO_INACTIVATE_SAP_URL": "https://wa-dev-pec23-qa1.azurewebsites.net/localData/object/", "CRON_TO_INACTIVATE_SAP_URL_RESPALDO": "**************************************/SiniestroMiddleware/api/Siniestros/ObtenerDatosSiniestro", "CRON_GENERATE_AND_UPLOAD_PREVIRED_FILE_FREQUENCY": "*/1 * * * *", "DOCKER_REGISTRY_SERVER_PASSWORD": "********************************", "DOCKER_REGISTRY_SERVER_URL": "https://pec23registrydev.azurecr.io/achs-app-nodejs:latest", "DOCKER_REGISTRY_SERVER_USERNAME": "************************************", "HOLIDAYS_URL": "https://apis.digital.gob.cl/fl/feriados", "LIFE_PENSION_LIMIT_YEARS": "110", "LOSHEROES_FTP_OUTPUT_FOLDER": "/files/qa1/Entrada", "PASSWORD_UF_SOAP_SERVICE": "ZIr1MSDH9JoDQmz", "PREVIRED_ACHS_EMAIL": "<EMAIL>", "PREVIRED_LOGIN_PAGE_PASSWORD": "achs2020", "PREVIRED_LOGIN_PAGE_URL": "https://www.previred.com/wPortal/login/login.jsp", "PREVIRED_LOGIN_PAGE_USER": "18634442-1", "PREVIRED_PAYROLL_FILE_FORMAT": "2", "PREVIRED_PAYROLL_TYPE": "8", "SERIE_ID_UF_SOAP_SERVICE": "F073.UFF.PRE.Z.D", "STORAGE_URL": "http://achs23peoplefiles.blob.core.windows.net/achs-storage-dev", "TEMPLATE_ID_CRON_NOTIFICATION": "d-d48d26f03f654886b96d9e0da2dc4b84", "TOTAL_DAYS_TO_ALLOW_ACTIONS": "30", "URL_UF_SOAP_SERVICE": "https://si3.bcentral.cl/sietews/sietews.asmx?wsdl", "USER_UF_SOAP_SERVICE": "764587839", "YEAR_WIDOW": "1"}}]}
const util = require('util');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const fetchSoapData = require('./soapFetcher');

const XMLObject = require('../../../resources/XMLobject');

const { getLastDayOfMonth } = require('../../sharedFiles/helpers');

describe('SOAP Fetcher', () => {
  beforeAll(beforeAllTests);

  jest.spyOn(util, 'promisify').mockImplementationOnce(arg => arg);
  const keys = ['GetSeriesResult', 'Series', 'fameSeries', '0', 'obs'];
  const url = 'https://someurl?wsdl';
  const requestArgs = {};

  it('should get and parse XML object', async () => {
    const client = async () => ({
      GetSeries: async () => XMLObject
    });
    const { data } = await fetchSoapData({ client, util, keys, url, requestArgs });
    const { indexDateString, value } = data;
    expect(indexDateString).toBe(getLastDayOfMonth());
    expect(value).toBe(28696.42);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });
  afterAll(afterAllTests);
});

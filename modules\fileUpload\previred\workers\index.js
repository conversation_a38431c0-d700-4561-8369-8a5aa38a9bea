/* eslint-disable consistent-return */
const moment = require('moment');
const workerModule = require('./worker');
const previredService = require('../../../thirdPartyTransfer/previred/services/dbService');
const logService = require('../../../sharedFiles/services/jobLog.service');
const pensionService = require('../../../pensions/services/pension.service');
const liquidationService = require('../../../liquidation/services/liquidation.service');
const afpService = require('../../../nomenclators/afp/services/index.service');
const isapreService = require('../../../nomenclators/isapre/services/index.service');
const storageService = require('../../../fileStorage/services');
const emailService = require('../services/email.service');

const generatorArgs = [pensionService, liquidationService, afpService, isapreService];

module.exports = {
  name: 'generateAndUploadPreviredFile',
  worker: deps =>
    workerModule.workerFn({
      moment,
      logService,
      storageService,
      generatorArgs,
      previredService,
      emailService,
      ...deps
    }),
  repeatInterval: process.env.CRON_GENERATE_AND_UPLOAD_PREVIRED_FILE_FREQUENCY,
  description: 'Cron que crear y envia archivo txt con la nomina de pensionados a Previred',
  endPoint: 'generateanduploadpreviredfile',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { isLifePension, setEndDatesOfValidity, setEndDateForLifePension } = require('./life');

const malePension = require('../../../../resources/maleValidityDatePension.json');
const femalePension = require('../../../../resources/femaleValidityDatePension.json');

describe('Validations for Life Pensions', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {
    jest.resetModules();
    process.env = Object.assign(process.env, {
      LIFE_PENSION_LIMIT_YEARS: 110
    });
  });

  it('should return true when validity type is life pension', () => {
    const received = isLifePension('Vigente vitalicia');
    const received2 = isLifePension('No Vigente');
    const received3 = isLifePension(null);

    expect(received).toBe(true);
    expect(received2).toBe(false);
    expect(received3).toBe(false);
  });

  it('should set end date to 110 years since the birth date when pension and birthDate are set', () => {
    const received = setEndDatesOfValidity(femalePension, '1974-06-24T04:00:00.000Z');
    const expected = /Jun 24 2084/;

    const received1 = setEndDatesOfValidity(femalePension, null);

    const received2 = setEndDatesOfValidity(null, '1974-06-24T04:00:00.000Z');

    expect(received.endDateOfValidity.toString()).toMatch(expected);
    expect(received.endDateOfTheoricalValidity.toString()).toMatch(expected);

    expect(received1.endDateOfValidity).toBeFalsy();
    expect(received1.endDateOfTheoricalValidity).toBeFalsy();

    expect(received2).toBeFalsy();
    expect(received2).toBeFalsy();
  });

  it('should call setEndDatesOfValidity when validity type is life pension', () => {
    const validValidityType = 'Vigente vitalicia';
    const invalidValidityType = 'Vigente orfandad';

    const received = setEndDateForLifePension(malePension, validValidityType);
    const expected = /Jun 24 2084/;

    const received1 = setEndDateForLifePension(malePension, invalidValidityType);

    expect(received.endDateOfValidity.toString()).toMatch(expected);
    expect(received1.endDateOfValidity).toBeFalsy();
  });

  afterAll(afterAllTests);
});

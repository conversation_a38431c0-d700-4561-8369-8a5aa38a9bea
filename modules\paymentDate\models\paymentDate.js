const mongoose = require('mongoose');

const { Schema } = mongoose;

const PaymentDateSchema = new Schema(
  {
    year: { type: Number, required: true },
    month: { type: String, required: true },
    paymentDate: { type: Date },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);
PaymentDateSchema.index({ year: 1, month: 1 }, { unique: true });

module.exports = mongoose.model('PaymentDate', PaymentDateSchema);

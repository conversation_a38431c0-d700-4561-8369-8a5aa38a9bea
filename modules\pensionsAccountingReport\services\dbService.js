const Model = require('../../expenseAccountingReport/models/ExpenseAccountingReport');

const service = {
  getReportData: async () => {
    try {
      const data = await Model.find({})
        .lean()
        .exec();
      if (!data.length) throw new Error(`No report data found for ExpenseAccountingReport`);

      return { data };
    } catch (error) {
      return { error };
    }
  },
  existsData: async () => {
    try {
      const countDocuments = await Model.find({})
        .estimatedDocumentCount()
        .exec();

      return { doesDataExists: !!countDocuments };
    } catch (error) {
      return { error };
    }
  }
};

module.exports = service;

/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('unified worker create upload bank file test', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let logService;
  let pensionService;
  let service;
  let done;
  let Sftp;
  let tmp;
  let extractZip;

  beforeEach(() => {
    done = jest.fn();

    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(false)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    pensionService = {
      updatePensionsById: jest.fn(() => Promise.resolve({ error: false })),
      updatePensions: jest.fn(() => Promise.resolve({ error: false }))
    };

    tmp = {
      dirSync: () => {
        return { name: '' };
      }
    };

    Sftp = {
      connectToSFTPServer: jest.fn(() => Promise.resolve({ connected: true })),
      Client: jest.fn(() => ({}))
    };

    service = {
      getHealthRejectionTimePeriod: jest.fn(() => true),
      downloadZipFile: jest.fn(() => Promise.resolve({ zipLocalFilepath: 'a whole new path' })),
      readFile: jest.fn(() => Promise.resolve(['line1', 'line2'])),
      updatePensionsByHealthRejection: jest.fn(() => Promise.resolve({})),
      updatePensionsWithoutHealthRejection: jest.fn(() => Promise.resolve({}))
    };

    extractZip = jest.fn(() => Promise.resolve('filePath'));
  });

  it('should complete worker flow', async () => {
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(8);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not complete worker as it was previously executed on current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not complete worker as it is not within temporal range', async () => {
    service.getHealthRejectionTimePeriod = jest.fn(() => false);
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not complete worker as it is not able to connect to ftp', async () => {
    Sftp.connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: false }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not complete worker as it is not able to get file path', async () => {
    service.downloadZipFile = jest.fn(() => Promise.resolve({ zipLocalFilepath: '' }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not complete worker as it is not able to download to local', async () => {
    service.downloadZipFile = jest.fn(() =>
      Promise.reject(new Error('error downloading from ftp'))
    );
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      Sftp,
      tmp,
      extractZip,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const cronMark = 'INACTIVATE_MARRIAGE_PRE_WORKER';
const alreadyExecutedMessage = `${cronMark} already executed`;
const cronDescription = 'pre worker inactivar pensiones por matrimonio Nº8:';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'RETIREMENT_PRE_WORKER';
const emptyFile = 'El archivo recibido no contiene datos';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const workerFn = async ({
  Logger,
  service,
  filesHelper,
  sftpClient,
  connectToSFTPServer,
  sftpCredentials,
  pensionService,
  logService,
  tmp,
  job,
  done
}) => {
  try {
    Logger.info(`Iniciando worker Nº 8`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`Checkeando servidor FTP. Verificando recepcion archivo Registro Civil. Nº 8`);
    const { lines, error } = await filesHelper.readSFTPFile({
      sftpClient,
      connectToSFTPServer,
      sftpCredentials,
      filesHelper,
      tmp,
      dataType: 'marriage'
    });
    if (error) throw new Error(error);
    if (!lines.length) {
      Logger.info(emptyFile);
      throw new Error(emptyFile);
    }

    Logger.info(`Archivo recibido. Inicio procesamiento archivo registro Civil Nº 8`);
    const {
      inactivationError,
      pensionsToEvaluate
    } = await service.saveMarriagePensionsToInactivate({
      lines
    });
    if (inactivationError) throw new Error(inactivationError);

    const { error: createUpdatePensionError } = await pensionService.createUpdatePension(
      pensionsToEvaluate
    );
    if (createUpdatePensionError) throw new Error(createUpdatePensionError);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

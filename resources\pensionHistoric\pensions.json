[{"paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": ""}, "beneficiary": {"rut": "********-5", "name": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 123, "forDisability": 200, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 555, "afp": 256, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": 1.1}, "enabled": false, "basePension": 170000, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1945-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "No vigente", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1900-01-01T00:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T00:00:00.000Z", "article40": 234, "createdAt": "2020-05-10T14:56:57.804Z", "updatedAt": "2020-05-17T02:47:58.210Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "1945-02-10T04:00:00.000Z", "endDateOfValidity": "1945-02-10T04:00:00.000Z", "linkedDate": "2020-06-09T23:39:16.705Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "********-3"}, {"paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": ""}, "beneficiary": {"rut": "********-5", "name": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 123, "forDisability": 200, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 555, "afp": 256, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": 1.1}, "enabled": false, "basePension": 170000, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1945-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1900-01-01T00:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T00:00:00.000Z", "article40": 234, "createdAt": "2020-04-10T14:56:57.804Z", "updatedAt": "2020-04-17T02:47:58.210Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "1945-02-10T04:00:00.000Z", "endDateOfValidity": "1945-02-10T04:00:00.000Z", "linkedDate": "2020-06-09T23:39:16.705Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "********-3"}]
module.exports = ({
  HttpStatus,
  familyAssignmentService,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  const service = familyAssignmentService;
  const inactivationReason = 'Vencimiento de certificado de estudios';
  const validityTypeOnDesactivation = 'No vigente';
  const validityTypeOnDesactivationSeach = /No vigente/i;

  const getLastDayOfPreviousMonth = () => {
    const previousMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastDay = 0;
    return new Date(currentYear, previousMonth, lastDay);
  };

  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    getAll: async (req, res) => {
      const { isError, error, result } = await service.getAll();
      Logger.info('Get temporary family assignment: ');
      if (isError) {
        Logger.error(`Get selection error: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    },
    insertAll: async (req, res) => {
      const { body } = req;
      const { isError, error, result } = await service.bulkAndDelete(body, service);

      Logger.info('Bulk creation:');

      if (isError) {
        Logger.error(`Bulk insertion error: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.CREATED).json(result);
      }
    },
    wasInactivated: async (req, res) => {
      if (Object.entries(req.body).length > 0) {
        res.status(HttpStatus.BAD_REQUEST).end();
        return;
      }

      const { wasInactivated, completed, isError, error } = await service.wasInactivated(
        inactivationReason,
        validityTypeOnDesactivationSeach
      );
      if (isError) {
        Logger.error(`Get error: ${error}`);
        manageError(res, error);
      } else {
        Logger.info(`wasInactivated result: ${JSON.stringify(wasInactivated, completed)}`);
        res.status(HttpStatus.OK).json({ wasInactivated, completed });
      }
    },
    inactivate: async (req, res) => {
      const message = 'Los pensionados se inactivaron y reactivaron correctamente';
      let isError = false;
      let error = null;
      const fieldToUpdateOnInactivate = {
        inactivationReason,
        endDateOfValidity: getLastDayOfPreviousMonth(),
        inactivationDate: new Date(),
        validityType: validityTypeOnDesactivation
      };

      const inactivateResult = await service.inactivate(fieldToUpdateOnInactivate);
      if (!inactivateResult || inactivateResult.isError) {
        isError = inactivateResult.isError;
        error = inactivateResult.error;
      }

      if (!isError) {
        const activateResult = await service.activate();
        isError = activateResult.isError;
        error = activateResult.error;
      }

      Logger.info('Bulk creation:');

      if (isError) {
        Logger.error(`inactivate error: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.CREATED).json({ completed: true, message });
      }
    },
    existTemporaryAssignment: async (req, res) => {
      const { result, isError, error } = await service.existTemporaryAssignment();

      if (isError) {
        Logger.error(`Get error: ${error}`);
        manageError(res, error);
      } else {
        Logger.info(`wasInactivated result: ${JSON.stringify(result)}`);
        res.status(HttpStatus.OK).json({ result });
      }
    }
  };
};

<?php

namespace App\Domain\Pension\Services;

use App\Domain\Pension\Models\Pension;
use App\Domain\Shared\Services\UfValueService;
use App\Domain\Shared\Services\AfpService;
use App\Domain\Shared\Services\RulesEngineService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class DiscountsCalculationService
{
    public function __construct(
        private UfValueService $ufService,
        private AfpService $afpService,
        private RulesEngineService $rulesEngine
    ) {}

    /**
     * Calcula todos los descuentos de una pensión
     */
    public function calculateDiscounts(Pension $pension): array
    {
        $discounts = [
            'afp' => $this->calculateAfpDiscount($pension),
            'health' => $this->calculateHealthDiscount($pension),
            'health_loan' => $this->calculateHealthLoanDiscount($pension),
            'one_percent_adjusted' => $this->calculateOnePercentAdjusted($pension),
            'social_credits_la_araucana' => $this->calculateSocialCredits($pension, 'la_araucana'),
            'social_credits_18' => $this->calculateSocialCredits($pension, '18'),
            'social_credits_los_andes' => $this->calculateSocialCredits($pension, 'los_andes'),
            'social_credits_los_heroes' => $this->calculateSocialCredits($pension, 'los_heroes'),
            'others_los_andes' => $this->calculateOtherDiscounts($pension, 'los_andes'),
            'others_los_heroes' => $this->calculateOtherDiscounts($pension, 'los_heroes'),
            'non_formulable' => $this->calculateNonFormulableDiscounts($pension),
            'judicial_retentions' => $this->calculateJudicialRetentions($pension),
        ];

        return array_filter($discounts, fn($value) => $value > 0);
    }

    /**
     * Calcula descuento AFP
     */
    private function calculateAfpDiscount(Pension $pension): float
    {
        if (!$pension->afp_affiliation) {
            return 0;
        }

        $afpInfo = $this->afpService->getAfpByName($pension->afp_affiliation);
        if (!$afpInfo) {
            return 0;
        }

        $taxablePension = $pension->currentLiquidation?->taxable_pension ?? 0;
        $afpPercentage = $afpInfo['percentage'] / 100;

        // Calcular descuento AFP con tope
        $afpDiscount = $taxablePension * $afpPercentage;
        $maxAfpDiscount = $this->rulesEngine->getMaxAfpDiscount();

        return round(min($afpDiscount, $maxAfpDiscount), 2);
    }

    /**
     * Calcula descuento de salud
     */
    private function calculateHealthDiscount(Pension $pension): float
    {
        $discounts = $pension->discounts ?? [];
        $healthUf = $discounts['health_uf'] ?? 0;
        
        if ($healthUf <= 0) {
            return 0;
        }

        $ufValue = $this->ufService->getCurrentUfValue();
        return round($healthUf * $ufValue, 2);
    }

    /**
     * Calcula descuento por préstamo de salud
     */
    private function calculateHealthLoanDiscount(Pension $pension): float
    {
        $discounts = $pension->discounts ?? [];
        return $discounts['health_loan'] ?? 0;
    }

    /**
     * Calcula 1% ajustado
     */
    private function calculateOnePercentAdjusted(Pension $pension): float
    {
        $discounts = $pension->discounts ?? [];
        $compensationBoxes = [
            'one_percent_la_araucana' => 'la_araucana',
            'one_percent_18' => '18',
            'one_percent_los_andes' => 'los_andes',
            'one_percent_los_heroes' => 'los_heroes'
        ];

        $totalOnePercent = 0;
        $basePensionPlusLaws = $pension->total_base_pension + $pension->total_laws;

        foreach ($compensationBoxes as $field => $boxName) {
            if (($discounts[$field] ?? 'No') === 'Si') {
                $boxInfo = $this->rulesEngine->getCompensationBoxInfo($boxName);
                $onePercentAmount = $basePensionPlusLaws * ($boxInfo['percentage'] / 100);
                $totalOnePercent += min($onePercentAmount, $boxInfo['max_amount']);
            }
        }

        return round($totalOnePercent, 2);
    }

    /**
     * Calcula créditos sociales por caja de compensación
     */
    private function calculateSocialCredits(Pension $pension, string $boxName): float
    {
        $discounts = $pension->discounts ?? [];
        $fieldName = "social_credits_{$boxName}";
        
        return $discounts[$fieldName] ?? 0;
    }

    /**
     * Calcula otros descuentos por caja de compensación
     */
    private function calculateOtherDiscounts(Pension $pension, string $boxName): float
    {
        $discounts = $pension->discounts ?? [];
        $fieldName = "others_{$boxName}";
        
        return $discounts[$fieldName] ?? 0;
    }

    /**
     * Calcula descuentos no formulables
     */
    private function calculateNonFormulableDiscounts(Pension $pension): float
    {
        $discountsAndAssets = $pension->discountsAndAssets;
        
        if (!$discountsAndAssets) {
            return 0;
        }

        $discounts = $discountsAndAssets->discounts_non_formulable ?? [];
        $currentDate = now();
        $total = 0;

        foreach ($discounts as $discount) {
            // Excluir retenciones judiciales (se calculan por separado)
            if (str_contains(strtolower($discount['reason'] ?? ''), 'retención judicial')) {
                continue;
            }

            // Verificar vigencia
            $startDate = $discount['start_date'] ? Carbon::parse($discount['start_date']) : null;
            $endDate = $discount['end_date'] ? Carbon::parse($discount['end_date']) : null;

            if ($startDate && $currentDate->lt($startDate)) {
                continue;
            }

            if ($endDate && $currentDate->gt($endDate)) {
                continue;
            }

            $total += $discount['amount'] ?? 0;
        }

        return round($total, 2);
    }

    /**
     * Calcula retenciones judiciales
     */
    private function calculateJudicialRetentions(Pension $pension): float
    {
        $discountsAndAssets = $pension->discountsAndAssets;
        
        if (!$discountsAndAssets || !$discountsAndAssets->hasJudicialRetentions()) {
            return 0;
        }

        $judicialRetentions = $discountsAndAssets->getDiscountsByReason('retención judicial');
        $total = 0;
        $netPension = $this->calculateNetPensionForRetention($pension);
        $minimumSalary = $this->rulesEngine->getMinimumSalary();

        foreach ($judicialRetentions as $retention) {
            if (!($retention['judicial_retention']['retention']['validity'] ?? false)) {
                continue;
            }

            $retentionType = $retention['judicial_retention']['retention']['type'] ?? '';
            $retentionAmount = $retention['judicial_retention']['retention']['amount'] ?? 0;

            switch ($retentionType) {
                case 'percentage':
                    // Calcular porcentaje sobre pensión líquida menos mínimo no remuneracional
                    $baseForRetention = max(0, $netPension - ($minimumSalary * 0.9));
                    $calculatedAmount = $baseForRetention * ($retentionAmount / 100);
                    break;
                    
                case 'fixed':
                    $calculatedAmount = $retentionAmount;
                    break;
                    
                default:
                    $calculatedAmount = 0;
            }

            $total += $calculatedAmount;
        }

        return round($total, 2);
    }

    /**
     * Calcula pensión neta para retenciones judiciales
     */
    private function calculateNetPensionForRetention(Pension $pension): float
    {
        // Simplificado - en la implementación real se calcularía la pensión neta
        // sin incluir las retenciones judiciales
        return $pension->total_base_pension + $pension->total_assets - $pension->total_discounts;
    }

    /**
     * Valida límites de descuentos
     */
    public function validateDiscountLimits(Pension $pension, array $calculatedDiscounts): array
    {
        $netPension = $pension->total_base_pension + $pension->total_assets;
        $totalDiscounts = array_sum($calculatedDiscounts);
        $minimumSalary = $this->rulesEngine->getMinimumSalary();

        // No se puede descontar más del 90% del mínimo no remuneracional
        $maxDiscountAllowed = $netPension - ($minimumSalary * 0.9);

        if ($totalDiscounts > $maxDiscountAllowed) {
            // Prorratear descuentos proporcionalmente
            $reductionFactor = $maxDiscountAllowed / $totalDiscounts;
            
            foreach ($calculatedDiscounts as $key => $amount) {
                $calculatedDiscounts[$key] = round($amount * $reductionFactor, 2);
            }
        }

        return $calculatedDiscounts;
    }
}

const PensionModel = require('../../../models/pension');
const UpdatePensionerInfo = require('../models/UpdatePensionerInfo');
const UpdatePensionType = require('../models/UpdatePensionType');

const {
  generateObject,
  buildBeneficiaryObject,
  buildCollectorObject,
  buildPaymentInfoObject
} = require('./queryPensions.services');

const processFieldChangesInPensioner = ({
  updatedPensionType,
  updatedPensionFields,
  ...originalPensionerData
}) => {
  const { pensionType, ChangeOfPensionTypeDueToCharges } = updatedPensionType[0] || {};

  const {
    beneficiaryRut,
    causantRut,
    beneficiaryEmail,
    collectorRut,
    collectorName,
    collectorLastName,
    collectorMothersLastName,
    collectorAddress,
    collectorCommune,
    collectorCity,
    paymentGateway,
    bank,
    branchOffice,
    accountNumber,
    beneficiaryPhone,
    ...modifiedNonNestedFields
  } = updatedPensionFields[0] || {};

  const { paymentInfo, beneficiary, collector, ...otherOriginalFields } = originalPensionerData;

  return {
    ...buildBeneficiaryObject(beneficiary, updatedPensionFields[0]),
    ...buildCollectorObject(collector, updatedPensionFields[0]),
    ...buildPaymentInfoObject(paymentInfo, updatedPensionFields[0]),
    ...otherOriginalFields,
    ...modifiedNonNestedFields,
    ...generateObject(ChangeOfPensionTypeDueToCharges, 'ChangeOfPensionTypeDueToCharges'),
    ...generateObject(pensionType, 'pensionType')
  };
};

const service = {
  transferPensionerData: async () => {
    try {
      const findModifiedPensioners =
        (await PensionModel.aggregate([
          {
            $match: {
              enabled: true
            }
          },
          {
            $lookup: {
              from: 'updatepensionerinfos',
              let: {
                beneficiary: '$beneficiary.rut',
                causant: '$causant.rut',
                pensionCode: '$pensionCodeId'
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$beneficiaryRut', '$$beneficiary'] },
                        { $eq: ['$causantRut', '$$causant'] },
                        { $eq: ['$pensionCodeId', '$$pensionCode'] }
                      ]
                    }
                  }
                }
              ],
              as: 'updatedPensionFields'
            }
          },
          {
            $lookup: {
              from: 'updatepensiontypes',
              let: {
                beneficiary: '$beneficiary.rut',
                causant: '$causant.rut',
                pensionCode: '$pensionCodeId'
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$beneficiaryRut', '$$beneficiary'] },
                        { $eq: ['$causantRut', '$$causant'] },
                        { $eq: ['$pensionCodeId', '$$pensionCode'] }
                      ]
                    }
                  }
                }
              ],
              as: 'updatedPensionType'
            }
          },
          {
            $match: {
              $expr: {
                $or: [
                  { $gt: [{ $size: '$updatedPensionFields' }, 0] },
                  { $gt: [{ $size: '$updatedPensionType' }, 0] }
                ]
              }
            }
          }
        ]).exec()) || [];

      const pensionersToUpdate = findModifiedPensioners.map(processFieldChangesInPensioner);

      return { pensionersToUpdate };
    } catch (error) {
      return { error };
    }
  },
  deleteTemporalData: async Logger => {
    await UpdatePensionerInfo.deleteMany({}).catch(error =>
      Logger.error(`error at deleting temporalData ${error.message}`)
    );
    await UpdatePensionType.deleteMany({}).catch(error =>
      Logger.error(`error at deleting temporalData ${error.message}`)
    );
  }
};

module.exports = { ...service };

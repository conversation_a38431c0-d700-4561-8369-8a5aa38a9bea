const moment = require('moment');
const PensionModel = require('../../../../models/pension');

const WIDOWS_PENSIONS_TYPE = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i
];

const WIDOW_VALIDITY = /Vigente viudez/i;
const { YEAR_WIDOW } = process.env;
const NOT_VALID = /No vigente/i;

const isWidow = pensionType => WIDOWS_PENSIONS_TYPE.some(regex => regex.test(pensionType));
const isWidowhoodValidity = valid => WIDOW_VALIDITY.test(valid);
const validity = (date, addYears) =>
  addYears > 0 ? new Date(moment(new Date(date)).add(addYears, 'years')) : null;

const setEndDateOfValidityByWidow = (finalData, death, accident) => {
  return {
    ...finalData,
    endDateOfTheoricalValidity: death
      ? validity(death, `${YEAR_WIDOW}`)
      : validity(accident, `${YEAR_WIDOW}`),
    endDateOfValidity: death
      ? validity(death, `${YEAR_WIDOW}`)
      : validity(accident, `${YEAR_WIDOW}`)
  };
};

const setWidowhood = async (finalData, pensionType, validityType) => {
  let widowData = { ...finalData };
  if (isWidow(pensionType) && isWidowhoodValidity(validityType)) {
    const query = {
      'beneficiary.rut': { $regex: new RegExp(widowData.causant.rut, 'i') },
      enabled: true,
      validityType: NOT_VALID
    };
    const result = await PensionModel.findOne(query)
      .lean()
      .exec();
    const byDeath = result ? result.deathDate : null;
    const byAccident = widowData.accidentDate;

    widowData = setEndDateOfValidityByWidow({ ...finalData }, byDeath, byAccident);
  }
  return { ...finalData, ...widowData };
};

module.exports = {
  setWidowhood,
  isWidow,
  isWidowhoodValidity,
  validity,
  setEndDateOfValidityByWidow
};

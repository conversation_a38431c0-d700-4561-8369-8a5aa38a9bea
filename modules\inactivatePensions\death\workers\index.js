const tmp = require('tmp');

const service = require('../services/dbService');
const { Client, connectToSFTPServer } = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const pensionService = require('../../../pensions/services/pension.service');
const filesHelper = require('../../sharedFiles/filesHelper');
const workerModule = require('./worker');

const {
  CIVIL_REGISTRATION_SFTP_HOST,
  CIVIL_REGISTRATION_SFTP_USER,
  CIVIL_REGISTRATION_SFTP_PASS,
  CIVIL_REGISTRATION_SFTP_PORT
} = process.env;
const sftpCredentials = {
  host: CIVIL_REGISTRATION_SFTP_HOST,
  user: CIVIL_REGISTRATION_SFTP_USER,
  password: CIVIL_REGISTRATION_SFTP_PASS,
  port: CIVIL_REGISTRATION_SFTP_PORT
};

module.exports = {
  name: 'inactivatePensionsDeath',
  worker: deps => {
    const sftpClient = new Client();
    return workerModule.workerFn({
      service,
      sftpClient,
      sftpCredentials,
      connectToSFTPServer,
      logService,
      pensionService,
      filesHelper,
      tmp,
      ...deps
    });
  },
  description: 'Leer txt enviado por registro civil e inactivar pensiones por fallecimiento',
  endPoint: 'inactivatebydeath',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

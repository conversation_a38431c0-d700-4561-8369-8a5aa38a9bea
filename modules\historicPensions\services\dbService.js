/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */

const TemporaryInactivationPension = require('../models/temporaryInactivationPension');
const TemporaryReactivationPension = require('../models/temporaryReactivationPension');

const sharedFields = item => {
  const {
    pensionCodeId,
    pensionType,
    validityType,
    basePension,
    beneficiary,
    endDateOfValidity,
    endDateOfTheoricalValidity,
    transient
  } = item;
  const { name, lastName, mothersLastName } = beneficiary;
  return {
    pensionCodeId,
    pensionType,
    validityType,
    basePension,
    beneficiaryName: `${name} ${lastName} ${mothersLastName}`.toUpperCase(),
    endDateOfValidity,
    endDateOfTheoricalValidity,
    transient
  };
};

const inactivationMapper = pensions => {
  return pensions.map(item => {
    const { inactivationDate, evaluationDate, inactivationReason } = item;

    return {
      ...sharedFields(item),
      inactivationReason,
      paymentEndDate: evaluationDate || inactivationDate
    };
  });
};
const reactivationMapper = pensions => {
  return pensions.map(item => {
    const { reactivationDate, previousEndDateOfValidity, inactivationDate } = item;

    return {
      ...sharedFields(item),
      inactivationDate,
      endDateOfValidity: previousEndDateOfValidity,
      reactivationReason: 'Reactivado por sistema',
      paymentEndDate: reactivationDate
    };
  });
};

const service = {
  async historicalInactivationPensions(pensionService, temporaryService) {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    try {
      const { result: pensions } = await pensionService.getAllAndFilter({
        $and: [
          { enabled: true },
          {
            $or: [
              { inactivationDate: { $exists: true }, validityType: /no vigente/i },
              {
                evaluationDate: {
                  $gte: new Date(currentYear, currentMonth, 1),
                  $lt: new Date(currentYear, currentMonth + 1, 1),
                  $exists: true
                }
              }
            ]
          }
        ]
      });

      const mappedInactivationPensions = inactivationMapper(pensions);

      const { completed, error } = await temporaryService.createTemporaryPensions(
        mappedInactivationPensions,
        TemporaryInactivationPension
      );

      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  },
  async historicalReactivationPensions(pensionService, temporaryService) {
    try {
      const { result: pensions } = await pensionService.getAllAndFilter({
        $and: [{ enabled: true }, { reactivationDate: { $exists: true } }]
      });
      const mappedReactivationPensions = reactivationMapper(pensions);

      const { completed, error } = await temporaryService.createTemporaryPensions(
        mappedReactivationPensions,
        TemporaryReactivationPension
      );

      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service, sharedFields, inactivationMapper, reactivationMapper };

const passport = require('passport');

const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');
const FactoryController = require('../modules/auth/controllers/auth.controller');
const userService = require('../modules/systems/users/services/users.service');
const authService = require('../modules/auth/services/auth.service');

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    authService,
    userService,
    Logger
  });

  router.get('/login', passport.authenticate('oauth-bearer', { session: false }), controller.login);
  router.get('/logout', controller.logout);
};

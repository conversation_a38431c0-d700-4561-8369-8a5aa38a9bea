const sgMail = require('@sendgrid/mail');

const { DEFAULT_FROM_EMAIL } = process.env;
sgMail.setApiKey(process.env.SENDGRID_API_KEY);
const sendEmail = async (
  { to = '', cc = '', from = DEFAULT_FROM_EMAIL, ...mailOptions },
  attachments = []
) => {
  try {
    const msg = {
      to: to.split(','),
      cc: cc.split(','),
      from,
      ...mailOptions,
      attachments
    };

    await sgMail.send(msg);
    return { completed: true };
  } catch (error) {
    return { completed: false, error };
  }
};
module.exports = { sendEmail };

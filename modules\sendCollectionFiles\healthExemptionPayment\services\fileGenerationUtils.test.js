/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const pensionsData = require('../../../../resources/pensions.json');
const fileGenerationUtils = require('./fileGenerationUtils');

describe('Helth exemption pauyment generation methods test', () => {
  beforeAll(beforeAllTests);
  const currentYear = moment().format('YYYYMM');
  const expectedOutputLine = `${currentYear}${currentYear}0170360100619685004KYANEZ               BAHAMONDES          CHRISTOPHER                   000001067200152452\n`;

  it('should convert each pensioner object to a line for the circular file', async () => {
    const { getLine } = fileGenerationUtils;

    const pensioner = { ...pensionsData[0], apsInfo: { apsOrigin: 0 }, createdAt: new Date() };

    expect(getLine(pensioner)).toBe(expectedOutputLine);
  });

  it('should dynamically generate file name base on current date', async () => {
    const { getZipFileName, getFileName } = fileGenerationUtils;
    const tmp = { dirSync: jest.fn().mockReturnValue({ name: '/temp' }) };
    expect(getFileName(tmp)).toMatch(`esalud${moment().format('YYYYMM')}.703601006.txt`);
    expect(getZipFileName()).toBe(
      `esalud.703601006.ips_${moment().format('YYYYMM')}_${moment().format('YYYYMMDD_HHmm')}.zip`
    );
  });

  it('should upload file to SFTP server and return the file name', async () => {});

  afterAll(afterAllTests);
});

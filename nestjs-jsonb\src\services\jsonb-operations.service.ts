import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { Prisma } from '@prisma/client';
import {
  PensionJsonbData,
  JsonbFieldName,
  JsonbUpdateOperation,
  JsonbQuery,
  PensionAssets,
  PensionDiscounts,
  AuditEntry
} from '../types/pension-jsonb.types';

/**
 * Servicio especializado para operaciones JSONB en PostgreSQL
 * Aprovecha las capacidades nativas de JSONB para consultas y actualizaciones eficientes
 */
@Injectable()
export class JsonbOperationsService {
  private readonly logger = new Logger(JsonbOperationsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * Consultas JSONB optimizadas usando operadores nativos de PostgreSQL
   */
  async findPensionsByJsonbCriteria(queries: JsonbQuery[]): Promise<any[]> {
    const whereConditions: any = {};

    queries.forEach(query => {
      const { field, operator, value, path } = query;

      switch (operator) {
        case 'contains':
          // Busca si el JSONB contiene el valor especificado
          whereConditions[field] = {
            path: path ? path.split('.') : undefined,
            equals: value
          };
          break;

        case 'has_key':
          // Verifica si existe una clave específica
          whereConditions[field] = {
            path: [value],
            not: Prisma.DbNull
          };
          break;

        case 'path_exists':
          // Verifica si existe un path específico
          whereConditions[field] = {
            path: path?.split('.') || [],
            not: Prisma.DbNull
          };
          break;
      }
    });

    return await this.prisma.pension.findMany({
      where: whereConditions,
      include: {
        discountsAndAssets: true,
        liquidations: { take: 1, orderBy: { createdAt: 'desc' } }
      }
    });
  }

  /**
   * Consulta usando SQL raw para operaciones JSONB complejas
   */
  async findPensionsWithComplexJsonbQuery(sqlQuery: string, params: any[]): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT p.*, da.assets_non_formulable, da.discounts_non_formulable
      FROM pensions p
      LEFT JOIN discounts_and_assets da ON p.pension_code_id = da.pension_code_id
      WHERE ${Prisma.raw(sqlQuery)}
    `;
  }

  /**
   * Buscar pensiones por criterios específicos en assets
   */
  async findPensionsByAssetsCriteria(criteria: {
    hasAps?: boolean;
    minFamilyAssignment?: number;
    bonusType?: string;
    assetReason?: string;
  }): Promise<any[]> {
    const conditions: any[] = [];

    if (criteria.hasAps) {
      conditions.push(Prisma.sql`assets->>'aps' IS NOT NULL AND (assets->>'aps')::numeric > 0`);
    }

    if (criteria.minFamilyAssignment) {
      conditions.push(
        Prisma.sql`(assets->>'familyAssignment')::numeric >= ${criteria.minFamilyAssignment}`
      );
    }

    if (criteria.bonusType) {
      conditions.push(
        Prisma.sql`assets->'bonusConfig'->>'payBonus' = ${criteria.bonusType}`
      );
    }

    if (criteria.assetReason) {
      conditions.push(
        Prisma.sql`assets->'assetsByReason' @> ${JSON.stringify([{ reason: criteria.assetReason }])}`
      );
    }

    if (conditions.length === 0) {
      return [];
    }

    const whereClause = conditions.reduce((acc, condition, index) => {
      return index === 0 ? condition : Prisma.sql`${acc} AND ${condition}`;
    });

    return await this.prisma.$queryRaw`
      SELECT * FROM pensions 
      WHERE enabled = true AND ${whereClause}
      ORDER BY updated_at DESC
    `;
  }

  /**
   * Buscar pensiones con retenciones judiciales activas
   */
  async findPensionsWithActiveJudicialRetentions(): Promise<any[]> {
    return await this.prisma.$queryRaw`
      SELECT p.*, 
             jsonb_path_query_array(
               discounts, 
               '$.judicialRetentions[*] ? (@.validity == true)'
             ) as active_retentions
      FROM pensions p
      WHERE discounts->'judicialRetentions' @> '[{"validity": true}]'
        AND enabled = true
    `;
  }

  /**
   * Actualización atómica de campos JSONB
   */
  async updateJsonbField(
    pensionId: string,
    operations: JsonbUpdateOperation[]
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      for (const operation of operations) {
        await this.executeJsonbOperation(tx, pensionId, operation);
      }

      // Registrar auditoría
      await this.addAuditEntry(tx, pensionId, {
        action: 'jsonb_update',
        changes: operations,
        timestamp: new Date().toISOString()
      });
    });
  }

  private async executeJsonbOperation(
    tx: any,
    pensionId: string,
    operation: JsonbUpdateOperation
  ): Promise<void> {
    const { field, operation: op, path, value } = operation;

    switch (op) {
      case 'set':
        if (path) {
          // Actualizar un path específico dentro del JSONB
          await tx.$executeRaw`
            UPDATE pensions 
            SET ${Prisma.raw(field)} = jsonb_set(
              COALESCE(${Prisma.raw(field)}, '{}'), 
              ${`{${path.replace(/\./g, ',')}}`}, 
              ${JSON.stringify(value)}
            )
            WHERE id = ${pensionId}
          `;
        } else {
          // Reemplazar todo el campo JSONB
          await tx.pension.update({
            where: { id: pensionId },
            data: { [field]: value }
          });
        }
        break;

      case 'merge':
        // Merge con el JSONB existente
        await tx.$executeRaw`
          UPDATE pensions 
          SET ${Prisma.raw(field)} = COALESCE(${Prisma.raw(field)}, '{}') || ${JSON.stringify(value)}
          WHERE id = ${pensionId}
        `;
        break;

      case 'append':
        // Agregar a un array JSONB
        await tx.$executeRaw`
          UPDATE pensions 
          SET ${Prisma.raw(field)} = jsonb_set(
            COALESCE(${Prisma.raw(field)}, '{}'),
            ${`{${path}}`},
            (COALESCE(${Prisma.raw(field)}->>${path}, '[]')::jsonb || ${JSON.stringify([value])})
          )
          WHERE id = ${pensionId}
        `;
        break;

      case 'delete':
        if (path) {
          // Eliminar un path específico
          await tx.$executeRaw`
            UPDATE pensions 
            SET ${Prisma.raw(field)} = ${Prisma.raw(field)} #- ${`{${path.replace(/\./g, ',')}}`}
            WHERE id = ${pensionId}
          `;
        } else {
          // Limpiar todo el campo
          await tx.pension.update({
            where: { id: pensionId },
            data: { [field]: Prisma.DbNull }
          });
        }
        break;
    }
  }

  /**
   * Agregar entrada de auditoría
   */
  private async addAuditEntry(
    tx: any,
    pensionId: string,
    auditData: Partial<AuditEntry>
  ): Promise<void> {
    const auditEntry: AuditEntry = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      userId: 'system', // En producción vendría del contexto
      userName: 'System',
      action: auditData.action || 'update',
      entity: 'pension',
      entityId: pensionId,
      ...auditData
    };

    await tx.$executeRaw`
      UPDATE pensions 
      SET audit_trail = COALESCE(audit_trail, '[]'::jsonb) || ${JSON.stringify([auditEntry])}
      WHERE id = ${pensionId}
    `;
  }

  /**
   * Operaciones de agregación usando JSONB
   */
  async getAssetsStatistics(): Promise<any> {
    return await this.prisma.$queryRaw`
      SELECT 
        COUNT(*) as total_pensions,
        AVG((assets->>'aps')::numeric) as avg_aps,
        AVG((assets->>'familyAssignment')::numeric) as avg_family_assignment,
        COUNT(*) FILTER (WHERE assets->>'christmasBonus' IS NOT NULL) as pensions_with_christmas_bonus,
        jsonb_object_agg(
          pension_type, 
          AVG((assets->>'aps')::numeric)
        ) as avg_aps_by_type
      FROM pensions 
      WHERE enabled = true 
        AND assets IS NOT NULL
      GROUP BY pension_type
    `;
  }

  /**
   * Buscar pensiones con configuraciones específicas
   */
  async findPensionsWithCalculationConfig(configCriteria: {
    version?: string;
    useMinimumPension?: boolean;
    hasManualOverrides?: boolean;
  }): Promise<any[]> {
    const conditions: any[] = [];

    if (configCriteria.version) {
      conditions.push(
        Prisma.sql`calculation_config->>'version' = ${configCriteria.version}`
      );
    }

    if (configCriteria.useMinimumPension !== undefined) {
      conditions.push(
        Prisma.sql`(calculation_config->>'useMinimumPension')::boolean = ${configCriteria.useMinimumPension}`
      );
    }

    if (configCriteria.hasManualOverrides) {
      conditions.push(
        Prisma.sql`calculation_config->'manualOverrides' IS NOT NULL`
      );
    }

    if (conditions.length === 0) {
      return await this.prisma.pension.findMany({ where: { enabled: true } });
    }

    const whereClause = conditions.reduce((acc, condition, index) => {
      return index === 0 ? condition : Prisma.sql`${acc} AND ${condition}`;
    });

    return await this.prisma.$queryRaw`
      SELECT * FROM pensions 
      WHERE enabled = true AND ${whereClause}
    `;
  }

  /**
   * Optimización: Crear índices JSONB específicos
   */
  async createJsonbIndexes(): Promise<void> {
    const indexes = [
      // Índice GIN para búsquedas generales en assets
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pension_assets_gin ON pensions USING GIN (assets)`,
      
      // Índice para búsquedas específicas en APS
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pension_aps ON pensions ((assets->>'aps')) WHERE assets->>'aps' IS NOT NULL`,
      
      // Índice para retenciones judiciales
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pension_judicial_retentions ON pensions USING GIN ((discounts->'judicialRetentions'))`,
      
      // Índice para configuración de cálculo
      `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pension_calc_config ON pensions ((calculation_config->>'version'))`,
    ];

    for (const indexSql of indexes) {
      try {
        await this.prisma.$executeRawUnsafe(indexSql);
        this.logger.log(`Created index: ${indexSql.split(' ')[5]}`);
      } catch (error) {
        this.logger.warn(`Index creation failed or already exists: ${error.message}`);
      }
    }
  }
}

const mongoose = require('mongoose');

const { Schema } = mongoose;

const updatePensionerInfo = new Schema(
  {
    beneficiaryRut: { type: String, required: true },
    beneficiaryEmail: { type: String, maxlength: 120 },
    beneficiaryPhone: { type: String, maxlength: 20 },
    causantRut: { type: String, required: true },
    collectorRut: { type: String, maxlength: 12 },
    collectorName: { type: String, maxlength: 60 },
    collectorLastName: { type: String, maxlength: 60 },
    collectorMothersLastName: { type: String, maxlength: 60 },
    collectorAddress: { type: String, maxlength: 1000 },
    collectorCommune: { type: String, maxlength: 1000 },
    collectorCity: { type: String, maxlength: 1000 },
    cun: { type: String },
    paymentGateway: { type: String, maxlength: 60 },
    bank: { type: String, maxlength: 60 },
    branchOffice: { type: String, maxlength: 80 },
    accountNumber: { type: String, maxlength: 30 },
    institutionalPatient: { type: Boolean },
    afpAffiliation: { type: String },
    accidentDate: { type: Date },
    country: { type: String },
    bankRejected: { type: String, default: 'No' },
    paycheckRefunded: { type: String, default: 'No' },
    gender: { type: String, enum: ['M', 'F'], trim: true },
    endDateOfValidity: { type: Date },
    previousEndDateOfValidity: { type: Date },
    endDateOfTheoricalValidity: { type: Date },
    validityType: { type: String },
    inactivationReason: { type: String },
    basePension: { type: Number },
    pensionCodeId: { type: String }
  },
  { timestamps: true }
);

updatePensionerInfo.index({ beneficiaryRut: 1, causantRut: 1 }, { unique: true });
updatePensionerInfo.index({ createdAt: 1 }, { expires: '30d' });

module.exports = mongoose.model('updatePensionerInfo', updatePensionerInfo);

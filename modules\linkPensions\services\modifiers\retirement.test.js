const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const {
  isDisabilityPensionType,
  isRetirement,
  getEndDateOfValidity,
  setEndDateOfValidityByGender
} = require('./retirement');

const { FEMALE_END_OF_VALIDITY_DATE, MALE_END_OF_VALIDITY_DATE } = process.env;

const malePension = require('../../../../resources/maleValidityDatePension.json');
const femalePension = require('../../../../resources/femaleValidityDatePension.json');

// const disabilityTypeValue = 'Pensión por accidente de trabajo';

describe('Doing some calculations for the end of date getEndDateOfValidity date ..', () => {
  beforeAll(beforeAllTests);

  it('Pension type condition to set end of date ...', () => {
    const received = isDisabilityPensionType('Pensión por accidente de trabajo');
    const received1 = isDisabilityPensionType('Pension por accidente de trabajo');
    const received2 = isDisabilityPensionType('Pension de viudez con hijos');
    const received3 = isDisabilityPensionType('Pensión de viudez con hijos');
    const received4 = isDisabilityPensionType('12345');
    const received5 = isDisabilityPensionType('Pensión por enfermedad profesional');
    const received6 = isDisabilityPensionType(null);

    expect(received).toBe(true);
    expect(received1).toBe(true);
    expect(received2).toBe(false);
    expect(received3).toBe(false);
    expect(received4).toBe(false);
    expect(received5).toBe(true);
    expect(received6).toBe(false);
  });

  it('Type of getEndDateOfValidity to set end of date...s', () => {
    const received = isRetirement('Vigente hasta la jubilación');
    const received1 = isRetirement('Vigente hasta la jubilacion');
    const received2 = isRetirement('No Vigente');
    const received3 = isRetirement('123456');
    const received4 = isRetirement(null);
    expect(received).toBe(true);
    expect(received1).toBe(true);
    expect(received2).toBe(false);
    expect(received3).toBe(false);
    expect(received4).toBe(false);
  });

  it('Type of validty condition to set end of date...a', () => {
    const received = getEndDateOfValidity(
      '2013-07-10T04:00:00.000Z',
      `${FEMALE_END_OF_VALIDITY_DATE}`
    );
    const expected = new Date('2073-07-09T04:00:00.000Z');

    const received1 = getEndDateOfValidity(null, null);
    const expected1 = null;

    const received2 = getEndDateOfValidity(20131007, 'asdasdf');
    const expected2 = null;

    const received3 = getEndDateOfValidity(
      '2013-07-10T04:00:00.000Z',
      `-${FEMALE_END_OF_VALIDITY_DATE}`
    );
    const expected3 = null;

    const received4 = getEndDateOfValidity(
      '2013-07-10T04:00:00.000Z',
      `-${MALE_END_OF_VALIDITY_DATE}`
    );
    const expected4 = null;

    const received5 = getEndDateOfValidity(
      '2013-07-10T04:00:00.000Z',
      `${MALE_END_OF_VALIDITY_DATE}`
    );
    const expected5 = new Date('2078-07-09T04:00:00.000Z');

    expect(received).toString(expected);
    expect(received1).toStrictEqual(expected1);
    expect(received2).toString(expected2);
    expect(received3).toString(expected3);
    expect(received4).toString(expected4);
    expect(received5).toString(expected5);
  });

  it('Set the getEndDateOfValidity date for the respective gender', () => {
    const received = setEndDateOfValidityByGender(malePension, '1974-06-24T04:00:00.000Z', 'M');
    const received1 = setEndDateOfValidityByGender(femalePension, '1977-08-11T04:00:00.000Z', 'F');
    const received2 = setEndDateOfValidityByGender('', '', '');
    const received3 = setEndDateOfValidityByGender(null, null, null);

    expect(received.endDateOfValidity).toBeDefined();
    expect(received.endDateOfValidity).toString('2039-06-24 04:00:00.000Z');
    expect(received.endDateOfValidity).toString(new Date('2039-06-24 04:00:00.000Z'));

    expect(received.endDateOfTheoricalValidity).toBeDefined();
    expect(received.endDateOfTheoricalValidity).toString('2039-06-24 04:00:00.000Z');
    expect(received.endDateOfTheoricalValidity).toString(new Date('2039-06-24 04:00:00.000Z'));

    expect(received1.endDateOfValidity).toBeDefined();
    expect(received1.endDateOfValidity).toString('2037-08-11 04:00:00.000Z');
    expect(received1.endDateOfValidity).toString(new Date('2037-08-11 04:00:00.000Z'));

    expect(received1.endDateOfTheoricalValidity).toBeDefined();
    expect(received1.endDateOfTheoricalValidity).toString('2037-08-11 04:00:00.000Z');
    expect(received1.endDateOfTheoricalValidity).toString(new Date('2037-08-11 04:00:00.000Z'));
    expect(received.toString(malePension, '2039-06-24 04:00:00.000Z', '2039-06-24 04:00:00.000Z'));
    expect(
      received1.toString(femalePension, '2037-08-11T04:00:00.000Z', '2037-08-11T04:00:00.000Z')
    );
    expect(received2).toString('');
    expect(received3).toBe(null);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

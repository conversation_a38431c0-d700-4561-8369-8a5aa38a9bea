/* eslint-disable no-console */
const PensionModel = require('../../../models/pension');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./dbService');
const pensionService = require('../../pensions/services/pension.service');

describe('Set reserved amount ', () => {
  beforeAll(beforeAllTests);

  it('should get calculate the special case and add upto 24 historical month', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    const pension1 = {
      ...pensionData,
      ChangeOfPensionTypeDueToCharges: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'vigente',
      enabled: true,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 150
      },
      validatedStudyPeriod: 'Si',
      reactivationDate: new Date(currentYear, currentMonth, 2),
      createdAt: new Date(currentYear, currentMonth, 2)
    };
    const pension2 = {
      ...pensionData,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 200
      },
      validatedStudyPeriod: 'Si',
      reactivationDate: new Date(currentYear, currentMonth, 1),
      createdAt: new Date(currentYear, currentMonth, 1)
    };
    const pension3 = {
      ...pensionData,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 1, 2)
    };
    const pension4 = {
      ...pensionData,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 25, 1)
    };
    const pension5 = {
      ...pensionData,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 26, 1)
    };

    await PensionModel.insertMany([pension1]).catch(e => console.error(e));
    await PensionHistoricModel.insertMany([pension2, pension3, pension4, pension5]).catch(e =>
      console.error(e)
    );

    const { completed, error } = await service.processRetroactiveAmountForSurvival(pensionService);

    const [updatedPension] = await PensionModel.find({ enabled: true }).catch(err =>
      console.error(err)
    );
    const [enabledFalsePension] = await PensionHistoricModel.find({
      createdAt: new Date(currentYear, currentMonth - 1, 2)
    }).catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(enabledFalsePension.validatedStudyPeriod).toBe('Pagado');
    expect(updatedPension.retroactiveAmounts.forSurvival).toBe(200);
  }, 60000);

  it('should get pensions and set the retroactiveAmounts.forSurvival field', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();
    const pension1 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'vigente',
      enabled: true,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 200
      },
      validatedStudyPeriod: 'Si',
      reactivationDate: new Date(currentYear, currentMonth, 2),
      createdAt: new Date(currentYear, currentMonth, 2)
    };
    const pension2 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forTaxableTotalNonFormulableAssets: 200,
        forBasePension: 100
      },
      validatedStudyPeriod: 'Si',
      reactivationDate: new Date(currentYear, currentMonth, 1),
      createdAt: new Date(currentYear, currentMonth, 1)
    };
    const pension3 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 50
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 1, 2)
    };
    const pension4 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 1, 1)
    };
    const pension5 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 200
      },
      validatedStudyPeriod: 'No',
      createdAt: new Date(currentYear, currentMonth - 2, 2)
    };
    const pension6 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'no',
      createdAt: new Date(currentYear, currentMonth - 2, 1)
    };
    const pension7 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 200
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 3, 2)
    };
    const pension8 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'No vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'Si',
      createdAt: new Date(currentYear, currentMonth - 3, 1)
    };
    const pension9 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forSurvival: 100
      },
      validatedStudyPeriod: 'no',
      createdAt: new Date(currentYear, currentMonth - 4, 2)
    };
    const pension10 = {
      ...pensionData,
      pensionType: 'Pensión por orfandad',
      validityType: 'vigente',
      enabled: false,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forTaxableTotalNonFormulableAssets: 100,
        forBasePension: 100
      },
      validatedStudyPeriod: 'no',
      createdAt: new Date(currentYear, currentMonth - 4, 1)
    };

    await PensionModel.insertMany([pension1]).catch(e => console.error(e));

    await PensionHistoricModel.insertMany([
      pension2,
      pension3,
      pension4,
      pension5,
      pension6,
      pension7,
      pension8,
      pension9,
      pension10
    ]).catch(e => console.error(e));

    const { completed, error } = await service.processRetroactiveAmountForSurvival(pensionService);

    const [updatedPension] = await PensionModel.find({ enabled: true });
    const [enabledFalsePension] = await PensionHistoricModel.find({
      createdAt: new Date(currentYear, currentMonth - 1, 2)
    });

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(enabledFalsePension.validatedStudyPeriod).toBe('Pagado');
    expect(updatedPension.retroactiveAmounts.forSurvival).toBe(250);
  }, 26000);

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(e => console.error(e));
    await PensionHistoricModel.deleteMany({}).catch(e => console.error(e));
  });

  afterAll(afterAllTests);
});

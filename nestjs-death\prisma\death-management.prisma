// Schema para manejo de fallecimientos y transiciones de pensión

model DeathNotification {
  id                    String   @id @default(cuid())
  pensionCodeId         String   @map("pension_code_id")
  beneficiaryRut        String   @map("beneficiary_rut")
  
  // Información del fallecimiento
  deathDate             DateTime @map("death_date")
  deathCertificateNumber String  @map("death_certificate_number")
  civilRegistry         String   @map("civil_registry")
  
  // Información del notificante
  notifiedBy            String   @map("notified_by") // RUT de quien notifica
  notificationDate      DateTime @map("notification_date")
  notificationMethod    String   @map("notification_method") // 'FAMILY', 'CIVIL_REGISTRY', 'AUTOMATIC'
  
  // Documentos adjuntos
  documents             Json     @map("documents") @db.JsonB
  
  // Estado del proceso
  status                String   @default("PENDING") // 'PENDING', 'VERIFIED', 'PROCESSED', 'REJECTED'
  verifiedAt            DateTime? @map("verified_at")
  verifiedBy            String?   @map("verified_by")
  
  // Observaciones
  observations          String?
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  // Relaciones
  pension               Pension  @relation(fields: [pensionCodeId], references: [pensionCodeId])
  deathProcess          DeathProcess?
  
  @@map("death_notifications")
  @@index([pensionCodeId, deathDate])
  @@index([status, notificationDate])
}

model DeathProcess {
  id                    String   @id @default(cuid())
  deathNotificationId   String   @unique @map("death_notification_id")
  pensionCodeId         String   @map("pension_code_id")
  
  // Proceso de transición
  processType           String   @map("process_type") // 'TERMINATE', 'CONVERT_TO_SURVIVAL', 'REDISTRIBUTE'
  processStatus         String   @map("process_status") @default("INITIATED")
  
  // Fechas importantes
  lastPaymentDate       DateTime @map("last_payment_date")
  terminationDate       DateTime? @map("termination_date")
  
  // Cálculos finales
  finalPaymentAmount    Decimal  @map("final_payment_amount") @db.Decimal(12, 2)
  proportionalAmount    Decimal  @map("proportional_amount") @db.Decimal(12, 2)
  pendingPayments       Json     @map("pending_payments") @db.JsonB
  
  // Beneficiarios identificados
  identifiedBeneficiaries Json   @map("identified_beneficiaries") @db.JsonB
  
  // Nuevas pensiones generadas
  newPensions           Json?    @map("new_pensions") @db.JsonB
  
  // Pagos pendientes y devoluciones
  overpayments          Json?    @map("overpayments") @db.JsonB
  refundsRequired       Json?    @map("refunds_required") @db.JsonB
  
  // Metadatos del proceso
  processedBy           String?  @map("processed_by")
  processedAt           DateTime? @map("processed_at")
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  // Relaciones
  deathNotification     DeathNotification @relation(fields: [deathNotificationId], references: [id])
  
  @@map("death_processes")
  @@index([pensionCodeId, processStatus])
}

model SurvivalPension {
  id                    String   @id @default(cuid())
  originalPensionId     String   @map("original_pension_id")
  newPensionCodeId      String   @unique @map("new_pension_code_id")
  
  // Información del nuevo beneficiario
  newBeneficiaryRut     String   @map("new_beneficiary_rut")
  newBeneficiaryName    String   @map("new_beneficiary_name")
  relationshipType      String   @map("relationship_type") // 'SPOUSE', 'CHILD', 'PARENT'
  
  // Cálculo de la nueva pensión
  originalPensionAmount Decimal  @map("original_pension_amount") @db.Decimal(12, 2)
  survivalPercentage    Decimal  @map("survival_percentage") @db.Decimal(5, 2)
  newPensionAmount      Decimal  @map("new_pension_amount") @db.Decimal(12, 2)
  
  // Fechas
  effectiveDate         DateTime @map("effective_date")
  
  // Documentos requeridos
  requiredDocuments     Json     @map("required_documents") @db.JsonB
  submittedDocuments    Json     @map("submitted_documents") @db.JsonB
  
  // Estado
  status                String   @default("PENDING_DOCUMENTS")
  approvedAt            DateTime? @map("approved_at")
  approvedBy            String?   @map("approved_by")
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  @@map("survival_pensions")
  @@index([originalPensionId])
  @@index([newBeneficiaryRut])
}

model PaymentSuspension {
  id                    String   @id @default(cuid())
  pensionCodeId         String   @map("pension_code_id")
  
  // Motivo de suspensión
  suspensionReason      String   @map("suspension_reason") // 'DEATH', 'INVESTIGATION', 'DOCUMENTS'
  suspensionDate        DateTime @map("suspension_date")
  
  // Pagos afectados
  affectedPayments      Json     @map("affected_payments") @db.JsonB
  suspendedAmount       Decimal  @map("suspended_amount") @db.Decimal(12, 2)
  
  // Resolución
  resolutionDate        DateTime? @map("resolution_date")
  resolutionType        String?   @map("resolution_type") // 'RESUME', 'TERMINATE', 'TRANSFER'
  resolutionDetails     Json?     @map("resolution_details") @db.JsonB
  
  // Autorización
  authorizedBy          String   @map("authorized_by")
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  @@map("payment_suspensions")
  @@index([pensionCodeId, suspensionDate])
}

model RefundProcess {
  id                    String   @id @default(cuid())
  pensionCodeId         String   @map("pension_code_id")
  
  // Información del reembolso
  refundReason          String   @map("refund_reason") // 'OVERPAYMENT_DEATH', 'DUPLICATE_PAYMENT'
  refundAmount          Decimal  @map("refund_amount") @db.Decimal(12, 2)
  
  // Período afectado
  periodFrom            DateTime @map("period_from")
  periodTo              DateTime @map("period_to")
  
  // Pagos involucrados
  affectedPayments      Json     @map("affected_payments") @db.JsonB
  
  // Proceso de recuperación
  recoveryMethod        String   @map("recovery_method") // 'BANK_REVERSAL', 'DEDUCTION', 'LEGAL'
  recoveryStatus        String   @map("recovery_status") @default("PENDING")
  
  // Información de contacto para recuperación
  contactInfo           Json     @map("contact_info") @db.JsonB
  
  // Resolución
  recoveredAmount       Decimal? @map("recovered_amount") @db.Decimal(12, 2)
  recoveredAt           DateTime? @map("recovered_at")
  
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  
  @@map("refund_processes")
  @@index([pensionCodeId, recoveryStatus])
}

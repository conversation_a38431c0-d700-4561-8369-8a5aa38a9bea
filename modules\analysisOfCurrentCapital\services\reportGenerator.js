/* eslint-disable no-unused-vars */
const { roundValue, pipe } = require('../../sharedFiles/helpers');

const initialAccumulator = {
  pensionporaccidentedetrabajo: 0,
  pensionporaccidentedetrayecto: 0,
  pensionporenfermedadprofesional: 0,
  pensionpororfandad: 0,
  pensiondeorfandaddepadreymadre: 0,
  pensiondeviudezconhijos: 0,
  pensiondeviudezsinhijos: 0,
  pensiondemadredehijodefiliacionnomatrimonialconhijos: 0,
  pensiondemadredehijodefiliacionnomatrimonialsinhijos: 0
};

const accumulatorReport5 = {
  monthColumnSummatory: 0,
  ipcColumnSummatory: 0,
  capitalPlusIpc: 0
};

const invalidityCategories = [
  { category: 'Invalidez parcial', regex: /Invalidez\s+parcial/i },
  { category: 'Gran inválido', regex: /Gran\s+inv[áa]lido/i },
  { category: 'Invalidez total', regex: /Invalidez\s+total/i },
  { category: 'No invalida', regex: /No\s+inv[áa]lida/i },
  { category: 'supervivencia inválida', regex: /supervivencia\s+inv[áa]lida/i }
];

const inactivationReasonCategories = [
  { category: 'Transitoria reactivada', regex: /Alta\s+m[ée]dica/i },
  {
    category: 'Reactivada por Certificado de estudios',
    regex: /(Expiraci[óo]n\s+de\s+a[ñn]o\s+de\s+pago)|(vencimiento\s+de\s+certificado\s+de\s+estudios)/i
  }
];

const report4InactReasonCategories = [
  { category: 'Fallecimiento', regex: /Fallecimiento/i },
  { category: 'Matrimonio', regex: /Matrimonio/i },
  { category: 'Resolución definitiva', regex: /Resoluci[óo]n\s+definitiva/i },
  { category: 'Alta médica', regex: /Alta\s+m[ée]dica/i },
  { category: 'Jubilación', regex: /Jubilaci[óo]n/i },
  { category: 'Cumplimiento de edad límite', regex: /Cumplimiento\s+de\s+edad\s+l[íi]mite/i },
  { category: 'Expiración de año de pago', regex: /Expiraci[óo]n\s+de\s+año\s+de\s+pago/i },
  {
    category: 'Vencimiento de certificado de estudios',
    regex: /Vencimiento\s+de\s+certificado\s+de\s+estudios/i
  }
];

const report5CapitalLawMapper = {
  capitalLaw19578: 'Capital Ley 19.578',
  capitalLaw19953: 'Capital Ley 19.953',
  capitalLaw20102: 'Capital Ley 20.102',
  capitalBonusLaw19403: 'Capital Bono Ley 19403',
  capitalBonusLaw19539: 'Capital Bono Ley 19539',
  capitalBonusLaw19953: 'Capital Bono Ley 19953',
  capitalTotalBonus: 'Capital Total Aguinaldos'
};

const report5CapitalLawIpcMapper = {
  capitalLaw19578Ipc: 'Capital Ley 19.578 IPC',
  capitalLaw19953Ipc: 'Capital Ley 19.953 IPC',
  capitalLaw20102Ipc: 'Capital Ley 20.102 IPC',
  capitalBonusLaw19403Ipc: 'Capital Bono Ley 19403 IPC',
  capitalBonusLaw19539Ipc: 'Capital Bono Ley 19539 IPC',
  capitalBonusLaw19953Ipc: 'Capital Bono Ley 19953 IPC',
  capitalTotalBonusIpc: 'Capital Total Aguinaldos IPC'
};

const disabilityTypes = [
  'Pensi[óo]n por accidente de trabajo',
  'Pensi[óo]n por accidente de trayecto',
  'Pensi[óo]n por enfermedad profesional'
];

const widowhoodTypes = [
  'Pensi[óo]n de viudez con hijos',
  'Pensi[óo]n de viudez sin hijos',
  'Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos',
  'Pensi[óo]n de madre de hijo de filiación no matrimonial sin hijos'
];
const orphanhoodTypes = ['Pensión por orfandad', 'Pensión de orfandad de padre y madre'];

const cleanPensionType = value =>
  value
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .toLowerCase()
    .trim();

const reducer = (acc, pension) => {
  const { pensionType, currentCapitalCalculation = {} } = pension;
  const { totalCapital = 0 } = currentCapitalCalculation;
  const key = cleanPensionType(pensionType);
  acc[key] = roundValue((acc[key] || 0) + totalCapital);
  return acc;
};

const groupPensionsCategory = categories => categoryField => pensions => {
  return categories.map(({ category, regex }) => ({
    category,
    pensions: pensions.filter(pension => (pension[categoryField] || '').match(regex))
  }));
};

const sumTotalCapitalByCategory = list => {
  return list.map(({ category, pensions }) => {
    const totalCapitalSumByPensionTypeObj = pensions.reduce(reducer, { ...initialAccumulator });
    return { category, ...totalCapitalSumByPensionTypeObj };
  });
};

const getReportObject = reports => {
  return reports.reduce(
    (acc, report) => {
      const keys = Object.keys(initialAccumulator);
      keys.forEach(key => {
        acc[key] += report[key];
      });
      return { ...acc };
    },
    { ...initialAccumulator }
  );
};

const getReport5Object = reports => {
  return reports.reduce(
    (acc, report) => {
      const keys = Object.keys(accumulatorReport5);
      keys.forEach(key => {
        acc[key] += report[key];
      });
      return { ...acc };
    },
    { ...accumulatorReport5 }
  );
};
const getPrevAndCurrMonthReportList = pipe(
  groupPensionsCategory(invalidityCategories)('disabilityType'),
  sumTotalCapitalByCategory
);

const getReactInCurrMonthReportList = pipe(
  groupPensionsCategory(inactivationReasonCategories)('inactivationReason'),
  sumTotalCapitalByCategory
);

const getInactPensionsInCurrMonthReportList = pipe(
  groupPensionsCategory(report4InactReasonCategories)('inactivationReason'),
  sumTotalCapitalByCategory
);

const createPrevAndCurrMonthReport = (pensions, category, reportType) => {
  const reports = getPrevAndCurrMonthReportList(pensions);
  const reportObject = getReportObject(reports);
  return { category, reportType, ...reportObject, detailsByInvalidityType: reports };
};

const createTotalCapitalReportByPensionType = (pensions, category, reportType) => {
  const totalCapitalByPensionTypeObj = pensions.reduce(reducer, { ...initialAccumulator });
  return { category, reportType, ...totalCapitalByPensionTypeObj };
};

const createReactivatedInCurrMonthReport = (pensions, reportType) => {
  const reportsList = getReactInCurrMonthReportList(pensions);
  const reports = reportsList.map(report => ({ reportType, ...report }));
  return reports;
};

const createInactPensionsInCurrMonthReport = (pensions, reportType) => {
  const reportsList = getInactPensionsInCurrMonthReportList(pensions);
  const reports = reportsList.map(report => ({ reportType, ...report }));
  return reports;
};

const sumKeysValue = (keys, pensions) => {
  const sumObj = {};
  pensions.forEach(({ currentCapitalCalculation = {} }) => {
    keys.forEach(key => {
      const keyValue = currentCapitalCalculation[key] || 0;
      sumObj[key] = (sumObj[key] || 0) + keyValue;
    });
  });
  return sumObj;
};

const getReport5TotalReports = reports => {
  return reports.reduce(
    (acc, report) => {
      const keys = ['monthColumnSummatory', 'ipcColumnSummatory', 'capitalPlusIpc'];
      keys.forEach(key => {
        acc[key] = roundValue((acc[key] || 0) + report[key]);
      });
      return acc;
    },
    { reportType: 'Reporte 5', category: 'Total General' }
  );
};

const filterPensions = (types, pensions) =>
  pensions.filter(({ pensionType }) =>
    types.some(type => pensionType.match(new RegExp(type, 'i')))
  );

const getReportByPensionType = (pensions, options) => {
  const keys = ['basePensionCapital', 'capitalPBIpc'];
  const { types, category } = options;
  const filteredPensions = filterPensions(types, pensions);
  const summatoryObj = sumKeysValue(keys, filteredPensions);
  const { basePensionCapital = 0, capitalPBIpc = 0 } = summatoryObj;

  return {
    reportType: 'Reporte 5',
    category,
    monthColumnSummatory: roundValue(basePensionCapital),
    ipcColumnSummatory: roundValue(capitalPBIpc - basePensionCapital),
    capitalPlusIpc: roundValue(capitalPBIpc)
  };
};

const createValidCurrMonthPensionsReport = pensions => {
  const lawKeys = Object.keys(report5CapitalLawMapper);
  const lawIpcKeys = Object.keys(report5CapitalLawIpcMapper);
  const keys = [...lawIpcKeys, ...lawKeys];
  const summatoryObj = sumKeysValue(keys, pensions);

  const capitalLawReports = lawKeys.map(key => ({
    reportType: 'Reporte 5',
    category: report5CapitalLawMapper[key],
    monthColumnSummatory: roundValue(summatoryObj[key] || 0),
    ipcColumnSummatory: roundValue((summatoryObj[`${key}Ipc`] || 0) - (summatoryObj[key] || 0)),
    capitalPlusIpc: roundValue(summatoryObj[`${key}Ipc`] || 0)
  }));

  const disabilityReport = getReportByPensionType(pensions, {
    types: disabilityTypes,
    category: 'Pensionados (con tipo de pensión invalidez)'
  });

  const widowhoodReport = getReportByPensionType(pensions, {
    types: widowhoodTypes,
    category: 'Viudez'
  });

  const orphanhoodReport = getReportByPensionType(pensions, {
    types: orphanhoodTypes,
    category: 'Orfandad'
  });

  const reports = [...capitalLawReports, disabilityReport, widowhoodReport, orphanhoodReport];
  const totalReports = getReport5TotalReports(reports);

  return [...reports, totalReports];
};

module.exports = {
  createPrevAndCurrMonthReport,
  getReportObject,
  sumTotalCapitalByCategory,
  createTotalCapitalReportByPensionType,
  createReactivatedInCurrMonthReport,
  createInactPensionsInCurrMonthReport,
  createValidCurrMonthPensionsReport
};

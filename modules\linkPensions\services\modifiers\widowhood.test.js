const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const {
  validity,
  isWidow,
  isWidowhoodValidity,
  setEndDateOfValidityByWidow
} = require('./widowhood');

const { YEAR_WIDOW } = process.env;

const malePension = require('../../../../resources/maleValidityDatePension.json');

describe('Doing some calculations for the end of date validity date ..', () => {
  beforeAll(beforeAllTests);

  it('Pension type condition to set end of date by widowhood ...', () => {
    const received = isWidow('Pensión de viudez con hijos');
    const received1 = isWidow('Pension de viudez con hijos');
    const received2 = isWidow('Pensión de viudez sin hijos');
    const received3 = isWidow('Pension de viudez sin hijos');
    const received4 = isWidow('12345');
    const received5 = isWidow('Pensión por enfermedad profesional');
    const received6 = isWidow(null);

    expect(received).toBe(true);
    expect(received1).toBe(true);
    expect(received2).toBe(true);
    expect(received3).toBe(true);
    expect(received4).toBe(false);
    expect(received5).toBe(false);
    expect(received6).toBe(false);
  });

  it('Type of validity to set end of date by widowhood...', () => {
    const received = isWidowhoodValidity('Vigente viudez');
    const received1 = isWidowhoodValidity('vigente viudez');
    const received2 = isWidowhoodValidity('vigenTe ViUdEz');
    const received3 = isWidowhoodValidity('No Vigente');
    const received4 = isWidowhoodValidity('123456');
    const received5 = isWidowhoodValidity(null);
    expect(received).toBe(true);
    expect(received1).toBe(true);
    expect(received2).toBe(true);
    expect(received3).toBe(false);
    expect(received4).toBe(false);
    expect(received5).toBe(false);
  });

  it('Type of validty condition to set end of date by widowhood...', () => {
    const received = validity('2020-07-07T04:00:00.000Z', `${YEAR_WIDOW}`);
    const expected = new Date('2021-07-07T04:00:00.000Z');

    const received1 = validity(null, null);
    const expected1 = null;

    const received2 = validity(20131007, 'asdasdf');
    const expected2 = null;

    const received3 = validity('2013-07-10T04:00:00.000Z', `-${YEAR_WIDOW}`);
    const expected3 = null;

    expect(received).toString(expected);
    expect(received1).toStrictEqual(expected1);
    expect(received2).toString(expected2);
    expect(received3).toString(expected3);
  });

  it('Set the validity date for the respective widowhood', () => {
    const received = setEndDateOfValidityByWidow(
      malePension,
      '2010-05-04 03:00:00.000Z',
      '1998-10-05 03:00:00.000Z'
    );
    const received1 = setEndDateOfValidityByWidow(malePension, '', '1998-10-05 03:00:00.000Z');
    const received2 = setEndDateOfValidityByWidow('', '', '');
    const received3 = setEndDateOfValidityByWidow(null, null, null);

    expect(received.endDateOfValidity).toBeDefined();
    expect(received.endDateOfValidity).toString('2011-05-04 03:00:00.000Z');
    expect(received.endDateOfValidity).toString(new Date('2011-05-04 03:00:00.000Z'));

    expect(received.endDateOfTheoricalValidity).toBeDefined();
    expect(received.endDateOfTheoricalValidity).toString('2011-05-04 03:00:00.000Z');
    expect(received.endDateOfTheoricalValidity).toString(new Date('2011-05-04 03:00:00.000Z'));

    expect(received1.endDateOfValidity).toBeDefined();
    expect(received1.endDateOfValidity).toString('1999-10-05 03:00:00.000Z');
    expect(received1.endDateOfValidity).toString(new Date('1999-10-05 03:00:00.000Z'));

    expect(received1.endDateOfTheoricalValidity).toBeDefined();
    expect(received1.endDateOfTheoricalValidity).toString('2000-10-05 03:00:00.000Z');
    expect(received1.endDateOfTheoricalValidity).toString(new Date('2000-10-05 03:00:00.000Z'));
    expect(received2).toString('');
    expect(received3).toString({ endDateOfTheoricalValidity: null, endDateOfValidity: null });
  });
  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

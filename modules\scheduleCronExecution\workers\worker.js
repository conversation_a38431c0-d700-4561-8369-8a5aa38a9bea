const cronMark = 'SCHEDULING_CRON_JOB';
const cronDescription = 'retroactivity disability pension';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = `Process ${cronMark} completed successfully.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({
  Logger,
  scheduleCronJobList,
  jobFields,
  logService,
  service,
  getFirstNbusinessDays,
  getMonthHolidays,
  done,
  job
}) => {
  try {
    Logger.info('Verifying file mark for scheduling Cronjob ...');
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info('Calculating next execution date for each cron ...');
    const {
      jobListWithExecutionDate,
      errorJobListWithExecutionDate
    } = await service.getJobListWithExecutionDate({
      scheduleCronJobList,
      jobFields,
      getFirstNbusinessDays,
      getMonthHolidays
    });

    if (errorJobListWithExecutionDate) throw new Error(errorJobListWithExecutionDate);

    Logger.info('Creating/Updating cron jobs with nextRunAt parameter ...');
    const { errorSchedulingJobs } = await service.scheduleJobs(jobListWithExecutionDate);

    if (errorSchedulingJobs) throw new Error(errorSchedulingJobs);

    const { areAllJobsFinished, errorGettingMarks } = await service.areAllJobsFinished({
      logService,
      scheduleCronJobList
    });

    if (errorGettingMarks) throw new Error(errorGettingMarks);

    if (areAllJobsFinished) {
      await logService.saveLog(cronMark);
      Logger.info(`${cronDescription} process finished`);
      return { executionCompleted: true, message: successMessage };
    }

    Logger.info(`${cronDescription} process pendings yet`);
    return { executionCompleted: true, message: 'process pending yet' };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable consistent-return */

const cronDescription = 'restablecer valores fijos';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const cronMsgText = 'Restablecer valores fijos para basePension, artículo 40 y artículo 41';
const dependency = 'GENERATE_AND_UPLOAD_BANK_FILE';
const successMessage = 'Proceso completado con éxito.';
const cronMark = 'RESET_FIXED_BASE_PENSION_VALUE';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `Dependencia "${dep}" aún no ejecutada`;

const workerFn = async ({ Logger, done, service, logService, resetFixedValues, job }) => {
  try {
    Logger.info(`Inicio cron ${cronMsgText}`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    if (!(await logService.existsLog(dependency))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependency)}`);
      return { message: getMissingDependencyMessage(dependency) };
    }

    const { error } = await service.basePensionAndArticlesFixedValues(resetFixedValues);
    if (error) throw new Error(Error);

    await logService.saveLog(cronMark);
    Logger.info(`${successMessage} ${cronMsgText}`);

    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependency, workerFn };

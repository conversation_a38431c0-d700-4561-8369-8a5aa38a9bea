/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const ViewModel = require('../models/views.model');
const viewService = require('./view.service');
const viewsResource = require('../../resources/views.json');
const viewsModel = require('../models/views.model');

describe('test for system utils', () => {
  beforeAll(beforeAllTests);

  it('should retrieve primary key for a given model', () => {
    const keys = viewService.getPrimaryKeysFields();

    expect(keys).toBeDefined();
    expect(keys[0]).toBe('view');
  });

  it('should insert many views', async () => {
    const [firstView, secondView] = [...viewsResource];

    const bulkResult = await viewService.bulkUpdateOrCreateUnordered([firstView, secondView]);
    const viewsCount = await (await viewsModel.find()).length;

    expect(bulkResult).toBeDefined();
    expect(viewsCount).toBe(2);
  });

  afterEach(async () => {
    await ViewModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

/* eslint-disable func-names */
const mongoose = require('mongoose');

const {
  findPreviousRecord,
  trackingPostFindOneAndUpdate,
  previousSave,
  trakingPostSave,
  customDiffRoles
} = require('../../../trackingUserActivity/services/trackingUserActivity.service');

const ACTION_CREATE = 'crear rol';
const ACTION_UPDATE = 'actualizar rol';
const MODEL_NAME = 'roles';

const { Schema } = mongoose;

const RoleSchema = new Schema(
  {
    roleName: {
      type: String,
      required: true,
      primary: true
    },
    views: [
      {
        view: { type: Schema.Types.ObjectId, required: true, ref: 'View' },
        permission: {
          type: String,
          enum: ['NoRead', 'Read', 'ReadWrite'],
          default: 'NoRead'
        }
      },
      { _id: false }
    ],
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

RoleSchema.pre(/findOneAndUpdate|updateOne/, findPreviousRecord);
RoleSchema.post(/findOneAndUpdate|updateOne/, async function(doc) {
  return trackingPostFindOneAndUpdate(doc, this, customDiffRoles)(ACTION_UPDATE);
});

RoleSchema.pre('save', previousSave);
RoleSchema.post('save', async function(doc) {
  return trakingPostSave(doc, MODEL_NAME)(ACTION_CREATE);
});
RoleSchema.index({ roleName: 1 }, { collation: { locale: 'es', strength: 1 } });

module.exports = mongoose.model('Role', RoleSchema);

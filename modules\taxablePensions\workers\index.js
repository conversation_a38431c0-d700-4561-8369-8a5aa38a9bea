const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const liquidationService = require('../../liquidation/services/liquidation.service');

module.exports = {
  name: 'calculate-taxable-pensions',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      liquidationService,
      ...deps
    }),
  repeatInterval: process.env.CRON_CALCULATE_TAXABLE_PENSIONS,
  description: 'Calculo de la pensión imponible para todos los pensionados',
  endPoint: 'calculatetaxablepension',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependenciesArray
};

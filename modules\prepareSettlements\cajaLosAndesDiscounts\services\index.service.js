/* eslint-disable no-unused-expressions */
const moment = require('moment');

const CajaLosAndesDiscountModel = require('../models/cajaLosAndesDiscount');
const { getFirstNbusinessDays, getMonthHolidays } = require('../../../sharedFiles/helpers');
const logService = require('../../../sharedFiles/services/jobLog.service');

const {
  createUpdatePension,
  getAllAndFilter
} = require('../../../pensions/services/pension.service');

const { transformDataBeforeBulk } = require('../transformers');

const processMark = 'BULK_LOAD_LOS_ANDES';
const DAYS_LIMIT = process.env.BUSINESS_DAYS_LIMIT_FOR_FONASA_AND_LOS_ANDES_BULK_LOAD;

const getMappedDiscountsData = discounts => {
  const rutRegexList = [];
  const dataMappedToObj = {};

  discounts.forEach(({ rut, movementType, creditDiscountAmount, anotherDiscountAmount }) => {
    rutRegexList.push(new RegExp(rut, 'i'));
    dataMappedToObj[rut] = {};

    ['onePercentLosAndes', 'socialCreditsLosAndes', 'othersLosAndes'].forEach(key => {
      let value;

      if (key === 'onePercentLosAndes') {
        value = (movementType === 1 && 'Si') || (movementType === 3 && 'No') || undefined;
      } else if (key === 'socialCreditsLosAndes') {
        value = parseFloat(creditDiscountAmount);
      } else if (key === 'othersLosAndes') {
        value = parseFloat(anotherDiscountAmount);
      }

      if (value != null && !Number.isNaN(value)) {
        dataMappedToObj[rut] = {
          ...dataMappedToObj[rut],
          [key]: value
        };
      }
    });
  });

  return [rutRegexList, dataMappedToObj];
};

const getProcessedPensions = (pensions, dataMappedToObj) => {
  const list = pensions.map(({ _doc: { _id, __v, ...pensionerData } }) => {
    return {
      ...pensionerData,
      discounts: {
        ...pensionerData.discounts,
        ...dataMappedToObj[pensionerData.beneficiary.rut]
      }
    };
  });

  return list;
};

const service = {
  async getCurrentMonthYear() {
    return { result: { currentMonthYear: moment().format('MMYY') } };
  },

  async isActionAllowedOnCurrentDate() {
    try {
      const first12BusinessDays = await getFirstNbusinessDays(
        new Date(),
        DAYS_LIMIT,
        getMonthHolidays
      );
      const isInDaysLimitRange = first12BusinessDays.includes(moment().format('YYYY-MM-DD'));
      return { result: { isInDaysLimitRange, nDays: DAYS_LIMIT } };
    } catch (error) {
      return { error };
    }
  },

  async insertMany(body) {
    const session = await CajaLosAndesDiscountModel.startSession();
    session.startTransaction();
    try {
      await CajaLosAndesDiscountModel.deleteMany({}).exec();
      await CajaLosAndesDiscountModel.insertMany(transformDataBeforeBulk(body));
      await session.commitTransaction();
      return { result: { completed: true } };
    } catch (e) {
      await session.abortTransaction();
      return { result: { completed: false }, isError: true, error: e };
    }
  },

  async processCajaLosAndesDiscounts() {
    const markDependency = 'CRON_BASE_MINIMUN_PENSION_WORKER';
    const missingDepMsg = `No se ha ejecutado la dependencia ${markDependency}`;
    const alreadyExecutedMessage = `Este proceso ya se ejecutó para el mes actual`;

    if (!(await logService.existsLog(markDependency))) {
      return { isError: true, error: 'UNAUTHORIZED', message: missingDepMsg };
    }
    if (await logService.existsLog(processMark)) {
      return { isError: true, error: 'UNAUTHORIZED', message: alreadyExecutedMessage };
    }

    const session = await CajaLosAndesDiscountModel.startSession();
    session.startTransaction();
    try {
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      const cajaLosAndesDiscounts = await CajaLosAndesDiscountModel.find({
        createdAt: {
          $gte: new Date(currentYear, currentMonth, 1),
          $lt: new Date(currentYear, currentMonth + 1, 1)
        }
      }).exec();

      const [rutList, dataMappedToObj] = getMappedDiscountsData(cajaLosAndesDiscounts);

      const { result } = await getAllAndFilter({
        validityType: { $not: /^no\s+vigente$/i },
        'beneficiary.rut': { $in: rutList },
        enabled: true
      });

      const pensionList = getProcessedPensions(result, dataMappedToObj);

      const { completed, error } = await createUpdatePension(pensionList);

      await session.commitTransaction();
      await logService.saveLog(processMark);
      return { result: { completed }, isError: !completed, error };
    } catch (e) {
      await session.abortTransaction();
      return { result: { completed: false }, isError: true, error: e };
    }
  }
};
module.exports = { processMark, ...service };

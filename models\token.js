const mongoose = require('mongoose');

const { Schema } = mongoose;

const { TOKEN_EXPIRACY_TIME } = process.env;

const TokenSchema = new Schema(
  {
    user: { type: String, required: true, unique: true },
    token: { type: String },
    expiresAt: {
      type: Date,
      expires: 0,
      default: () => {
        const date = new Date();
        const seconds = Number.parseInt(TOKEN_EXPIRACY_TIME || 86400, 10);
        date.setSeconds(date.getSeconds() + seconds);
        return date;
      }
    }
  },
  { timestamps: true }
);

TokenSchema.index({ token: 1, user: 1 });

module.exports = mongoose.model('Token', TokenSchema);

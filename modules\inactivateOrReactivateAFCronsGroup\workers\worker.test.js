/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker inactivate or reactivate AFC groupCrons Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let cronsGroup;
  beforeEach(() => {
    done = jest.fn();

    cronsGroup = {
      registerStartAndEndDateOfStudyCertificate: jest.fn(() =>
        Promise.resolve({ alreadyExecuted: true })
      ),
      reactivateForRenewalOfStudyCertificate: jest.fn(() =>
        Promise.resolve({ alreadyExecuted: true })
      ),
      updateByArticleFourtyOne: jest.fn(() => Promise.resolve({ alreadyExecuted: true })),
      reactivateOrphanhood: jest.fn(() => Promise.resolve({ alreadyExecuted: true })),
      widowUnder45Service: jest.fn(() => Promise.resolve({ alreadyExecuted: true })),
      changePensionTypes: jest.fn(() => Promise.resolve({ alreadyExecuted: true }))
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, logService, cronsGroup, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.allMarksExists).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, cronsGroup, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, logService, cronsGroup, done });

    expect(logService.allMarksExists).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    cronsGroup.registerStartAndEndDateOfStudyCertificate = jest.fn(() =>
      Promise.reject(new Error('Error service'))
    );
    await workerModule.workerFn({ Logger, service, logService, cronsGroup, done });

    expect(logService.allMarksExists).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

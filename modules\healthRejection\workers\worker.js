const cronDescription = 'health rejection file:';
const alreadyExecutedMessage = 'Este proceso ya fue realizado el mes actual';
const dependencyMark = '';
const cronMark = 'HEALTH_REJECTION_FILE';
const successMessage = `Proceso ${cronMark}: completado correctamente`;
const notWithinPeriodOfTime = `Proceso ${cronMark}: no puede ser ejecutado pues no está dentro del periodo`;
const unzipError = 'Error al descomprimir archivo de rechazo salud';
const noFileError = 'Verificación terminada, aún no se ha recibido el archivo';
const emptyFileError = 'El archivo de salud se encuentra sin datos';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const {
  HEALTH_REJECTION_SFTP_OUTPUT_FOLDER: basePath,
  FILE_HEALTH_REJECTION_START_DAY: startingDay,
  FILE_HEALTH_REJECTION_END_DAY: endingDay
} = process.env;

const workerFn = async ({
  Logger,
  logService,
  sftpCredentials,
  pensionService,
  service,
  Sftp,
  tmp,
  extractZip,
  job,
  done
}) => {
  try {
    Logger.info(`Verificando servidor FTP. Verificando recepción archivos rechazo salud.`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`Verificando plazo temporal para proceso ${cronMark}`);
    const isWithin5thAnd12thDay = service.getHealthRejectionTimePeriod({
      currentDate: new Date(),
      startingDay,
      endingDay
    });
    if (!isWithin5thAnd12thDay) return { message: notWithinPeriodOfTime, status: 'UNAUTHORIZED' };

    Logger.info('Conectando a servidor SFTP');
    const sftpClient = new Sftp.Client();
    const { connected, error: sftpConnectionError } = await Sftp.connectToSFTPServer(
      sftpClient,
      sftpCredentials
    );
    if (!connected) throw new Error(sftpConnectionError);

    Logger.info('Descargando archivo de rechazo de salud');
    const { zipLocalFilepath, error: sftpDownloadError } = await service.downloadZipFile(
      sftpClient,
      basePath
    );
    if (!zipLocalFilepath) {
      Logger.error(`Verificación terminada, aún no se ha recibido el archivo`);
      return { message: noFileError, status: 'NOTFOUND' };
    }
    if (sftpDownloadError) throw new Error(sftpDownloadError);

    const filePath = await extractZip(`${zipLocalFilepath}`, `${tmp.dirSync().name}`, 'tbos');
    if (!filePath) {
      Logger.error(unzipError);
      return { message: unzipError, status: 'NOTFOUND' };
    }

    Logger.info('Archivo de salud descomprimido. Inicio del procesamiento');
    const fileLines = await service.readFile(filePath);
    if (!fileLines.length) {
      Logger.error('El archivo de salud se encuentra sin datos');
      return { message: emptyFileError, status: 'NOTFOUND' };
    }

    const {
      updatedPensions,
      error: fileHealthRejectionError
    } = await service.updatePensionsByHealthRejection(service.parseDataFromLines, fileLines);
    if (fileHealthRejectionError) throw new Error(fileHealthRejectionError);

    Logger.info('Actualizando pensiones con rechazo de salud');
    const { error: updateHealthRejectionError } = await pensionService.updatePensions(
      updatedPensions
    );
    if (updateHealthRejectionError) throw new Error(updateHealthRejectionError);

    Logger.info('Buscando pensionados históricos para actualizar rechazo de salud');
    const {
      historicalPensions,
      error: historicalPensionsError
    } = await service.updatePensionsWithoutHealthRejection(pensionService);
    if (historicalPensionsError) throw new Error(historicalPensionsError);

    Logger.info('Actualizando pensiones históricas con rechazo de salud');
    const {
      error: historicalHealthRejectionError
    } = await pensionService.updatePensionsByBeneficiaryCausant(historicalPensions);
    if (historicalHealthRejectionError) throw new Error(historicalHealthRejectionError);

    await logService.saveLog(cronMark);
    Logger.info(`Fin procesamiento archivo rechazo de salud`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${unzipError}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${unzipError}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint no-param-reassign: "error" */
const PensionModel = require('../../../models/pension');
const temporaryHorphanhoodModel = require('../models/temporaryHorphanhood');

const validityExp = /s/i;

const proccessBody = body => {
  body.forEach(obj => {
    Object.entries(obj).forEach(([key, value]) => {
      if (key === 'collectorRut') {
        obj[key] = `${value}-${obj.collectorDv}`;
      }
      if (key === 'causantRut') {
        obj[key] = `${value}-${obj.causantDv}`;
      }
      if (key === 'beneficiaryRut') {
        obj[key] = `${value}-${obj.beneficiaryDv}`;
      }
      if (key === 'validity') {
        obj[key] = validityExp.test(value);
      }
      if (key === 'pensionId') {
        obj[key] = [value.slice(0, 1), '0', value.slice(1)].join('');
      }
      if (key === 'motherRut') {
        obj[key] = `${value}-${obj.motherDv}`;
      }
    });
  });

  return body;
};

module.exports = {
  async bulkAndDeleteOrphanhood(body) {
    try {
      await temporaryHorphanhoodModel.deleteMany({}).exec();
      const bodyAux = proccessBody(body);

      const result =
        JSON.stringify(bodyAux) !== '{}'
          ? await temporaryHorphanhoodModel.insertMany(bodyAux)
          : null;
      return { isError: !result, result, error: { name: 'MongoError', code: 11000 } };
    } catch (error) {
      return { isError: true, error };
    }
  },

  async wasInactivatedOrphanhood(
    inactivationReasonOrphanhood,
    validityTypeOnDesactivationOrphanhood
  ) {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    try {
      const [hasPensionDocumentsOrphanhood, hasAssignmentDocumentsOrphanhood] = await Promise.all([
        PensionModel.findOne({})
          .countDocuments()
          .exec(),
        temporaryHorphanhoodModel
          .findOne({})
          .countDocuments()
          .exec()
      ]);

      if (hasPensionDocumentsOrphanhood && hasAssignmentDocumentsOrphanhood) {
        const wasInactivatedOrphanhood = await PensionModel.findOne({
          inactivationDate: {
            $gte: new Date(currentYear, currentMonth, 1),
            $lt: new Date(currentYear, currentMonth + 1, 1)
          },
          inactivationReason: inactivationReasonOrphanhood,
          validityType: validityTypeOnDesactivationOrphanhood,
          enabled: true
        })
          .countDocuments()
          .exec();
        return {
          wasInactivated: !!wasInactivatedOrphanhood,
          completed: !!wasInactivatedOrphanhood,
          isError: false,
          error: null
        };
      }
      return { wasInactivated: true, completed: false, isError: false, error: null };
    } catch (error) {
      return { isError: true, error };
    }
  },

  async horphanhoodFileAlreadyImported() {
    return temporaryHorphanhoodModel
      .findOne({})
      .countDocuments()
      .exec();
  },

  async getInactiveTemporaryHorphanhood(validity, typeValidity, TypePension) {
    return temporaryHorphanhoodModel.aggregate([
      {
        $match: {
          validity
        }
      },
      {
        $lookup: {
          from: 'pensions',
          let: {
            beneficiary: '$beneficiaryRut',
            causant: '$causantRut'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$beneficiary.rut', '$$beneficiary'] },
                    { $eq: ['$causant.rut', '$$causant'] },
                    { $eq: ['$enabled', true] }
                  ]
                }
              }
            }
          ],
          as: 'pension'
        }
      },
      {
        $unwind: {
          path: '$pension',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          'pension.validityType': typeValidity,
          'pension.pensionType': { $in: TypePension },
          'pension.manuallyReactivated': {
            $ne: true
          },
          'pension.inactivateManually': {
            $ne: true
          },
          'pension.enabled': true
        }
      },
      {
        $project: {
          'pension.beneficiary': 1,
          'pension.causant': 1,
          'pension.pensionCodeId': 1,
          endDate: 1
        }
      }
    ]);
  },

  async filterPensionsTypesChange() {
    return temporaryHorphanhoodModel.aggregate([
      {
        $addFields: {
          pensionCodeId: { $toString: '$pensionId' }
        }
      },
      {
        $lookup: {
          from: 'pensions',
          let: {
            pensionBeneficiaryRut: '$beneficiaryRut',
            pensionCausantRut: '$causantRut',
            pensionCodeIdO: '$pensionCodeId'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$beneficiary.rut', '$$pensionBeneficiaryRut'] },
                    { $eq: ['$causant.rut', '$$pensionCausantRut'] },
                    { $eq: ['$pensionCodeId', '$$pensionCodeIdO'] }
                  ]
                }
              }
            }
          ],
          as: 'pension'
        }
      },
      {
        $unwind: {
          path: '$pension',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          validity: 1,
          'pension._id': 1,
          'pension.validityType': 1,
          'pension.pensionCodeId': 1,
          'pension.beneficiary': 1,
          'pension.causant': 1,
          'pension.endDateOfTheoricalValidity': 1,
          'pension.inactivationReason': 1,
          'pension.enabled': 1
        }
      }
    ]);
  }
};

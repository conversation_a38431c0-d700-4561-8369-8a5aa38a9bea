const workerModule = require('./worker');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('set winter bonus worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let logService;
  let pensionService;
  let Logger;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      setWinterBonus: jest.fn(() => Promise.resolve({ completed: true, err: false }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {
      updatePensionsById: jest.fn(() => Promise.resolve({ completed: true, err: false }))
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setWinterBonus).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current year', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resoolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setWinterBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  afterAll(afterAllTests);
});

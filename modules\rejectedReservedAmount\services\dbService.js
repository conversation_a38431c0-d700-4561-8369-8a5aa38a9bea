/* eslint-disable no-restricted-syntax */

const { PensionModel } = require('../../linkPensions/services/link.service');
const { roundValue } = require('../../sharedFiles/helpers');
const { buildAggregation } = require('./aggregationBuilder');

const getRejectedPensions = async (Model, firstDay) => {
  const modifiedPensions = [];
  const limitMatchedPensionBack = 1;
  const query = { bankRejected: { $regex: /s[ií]/i } };
  const aggregation = buildAggregation(firstDay, query, limitMatchedPensionBack);
  const cursor = Model.aggregate(aggregation).cursor();

  for await (const doc of cursor) {
    const { matchedPensions = [], ...data } = doc;

    if (matchedPensions.length) {
      const [previousPension = {}] = matchedPensions;
      const { liquidation = {} } = previousPension;
      const { netPension = 0 } = liquidation;

      modifiedPensions.push({
        ...data,
        reservedAmounts: {
          ...doc.reservedAmounts,
          forRejection: roundValue(netPension)
        }
      });
    }
  }
  return modifiedPensions;
};

const getpaycheckRefundedPensions = async (Model, firstDay) => {
  const modifiedPensions = [];
  const limitMatchedPensionBack = 4;
  const query = { paycheckRefunded: { $regex: /s[ií]/i } };
  const aggregation = buildAggregation(firstDay, query, limitMatchedPensionBack);
  const cursor = Model.aggregate(aggregation).cursor();

  for await (const doc of cursor) {
    const { matchedPensions = [], ...data } = doc;
    if (matchedPensions.length) {
      const { liquidation = {} } = matchedPensions[3] || {};
      const { netPension = 0 } = liquidation;
      modifiedPensions.push({
        ...data,
        reservedAmounts: {
          ...doc.reservedAmounts,
          forPayCheck: roundValue(netPension)
        }
      });
    }
  }
  return modifiedPensions;
};

module.exports = {
  async calculateReservedAmountByRejectedPensions(pensionsService, Model = PensionModel) {
    try {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const rejectedPensions = await getRejectedPensions(Model, firstDay);
      const { completed, error } = await pensionsService.updatePensions([...rejectedPensions]);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  },
  async calculateReservedAmountBypaycheckRefunded(pensionsService, Model = PensionModel) {
    try {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);

      const paycheckRefundedPensions = await getpaycheckRefundedPensions(Model, firstDay);

      const { completed, error } = await pensionsService.updatePensions([
        ...paycheckRefundedPensions
      ]);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

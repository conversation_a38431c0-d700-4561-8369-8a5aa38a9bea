const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const CriteriaBuilder = require('../lib/builders/criteria-builder');
const validateAccess = require('../lib/auth/validate');
// const FactoryController = require('../modules/linkPensions/controllers/link.controller');
const JobsMonitorController = require('../modules/JobsMonitor/controllers/jobsMonitor.controller');
// const { setUserAndEndpointInfo } = require('../lib/middleware/setUserAndEndpointInfo');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

// const SEE_MORE_ENDPOINT = '/pensionados/consulta-pensionados/ver-mas';
// const LINK_PENSION_ENDPOINT = '/nuevas-pensiones/importar';
const SYSTEM_USER = '/sistemas/usuarios';

module.exports = router => {
  const jobsMonitorController = JobsMonitorController(
    { HttpStatus, ErrorBuilder, Logger, CriteriaBuilder } // deps injected
  );

  router.get('/', validateAccess(), jobsMonitorController.getAllJobs);

  router.put(
    '/:cronMark',
    validateAccess(),
    startContextMiddleware,
    getUser(SYSTEM_USER),
    jobsMonitorController.insertMarkJob
  );
};

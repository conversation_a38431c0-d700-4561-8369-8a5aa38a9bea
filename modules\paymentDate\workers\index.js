const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'calculatePaymentDatesWorker',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_CALCULATE_PAYMENT_DATES_FREQUENCY,
  description: 'Calculo de fechas de pago mensual por el año actual y siguiente',
  endPoint: 'calculatepaymentdatesworker',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

/* eslint-disable no-param-reassign */
const xl = require('excel4node');
const { pensionFields, simpleFields } = require('../formatters/fields');
const { populateSheet, putHeaders } = require('../../services/excel.service');
const { listOfDynamicFields } = require('../formatters/formatFields');

const { filterData } = require('../formatters/formatFields');

const excelService = result => {
  const workbook = new xl.Workbook();

  const sheet = workbook.addWorksheet('Liquidaciones', {
    disableRowSpansOptimization: true,
    author: 'ACHS'
  });

  const columnsWidth = {};

  if (result.length) {
    const { processedData, excelFields } = filterData(
      pensionFields,
      simpleFields,
      listOfDynamicFields,
      result
    );
    const excelSimpleFields = excelFields.map(field => Object.keys(field)[0]);
    putHeaders(sheet, excelSimpleFields, excelFields);
    populateSheet(processedData, sheet, excelSimpleFields, excelFields, columnsWidth);
  }
  if (!result.length) putHeaders(sheet, simpleFields, pensionFields);

  Object.keys(columnsWidth).forEach(column => sheet.column(column).setWidth(columnsWidth[column]));

  return workbook;
};

module.exports = { excelService };

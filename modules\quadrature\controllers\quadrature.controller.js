module.exports = ({ HttpStatus, validationResult, service, moment, Logger }) => {
  return {
    getDate: async (req, res) => {
      Logger.info('Getting month and year for file validation');
      const monthYear = moment().format('MM-YYYY');
      return res.status(HttpStatus.OK).json(monthYear);
    },
    insertFactorsData: async (req, res) => {
      try {
        Logger.info('Starting Insertion of factors...');
        const { data } = req.body;
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          Logger.info('Quadrature: insert factors has validation errors...');
          return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
        }
        const { error } = await service.insertFactors(data);
        if (error) {
          throw new Error(error);
        }

        Logger.info('Quadrature info was successfully created ...');
        return res.status(HttpStatus.OK).json({ dataInsertion: true });
      } catch (err) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          data: [],
          error: true,
          message: err.message
        });
      }
    },
    insertConcurrenciesData: async (req, res) => {
      try {
        Logger.info('Starting Insertion of concurrencies...');
        const { data } = req.body;
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          Logger.info('Quadrature: insert concurrencies has validation errors...');
          return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
        }
        const { error } = await service.insertConcurrencies(data);
        if (error) {
          throw new Error(error);
        }

        Logger.info('Quadrature info was successfully created ...');
        return res.status(HttpStatus.OK).json({ dataInsertion: true });
      } catch (err) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          data: [],
          completed: false,
          message: err.message
        });
      }
    },
    wasDataUploaded: async (req, res) => {
      try {
        Logger.info('Quadrature: checking if data was previously inserted...');
        const [month, year] = moment()
          .format('MM-YYYY')
          .split('-');
        const { wasDataUploaded, error } = await service.wasDataUploaded({ month, year });
        if (error) {
          throw new Error(error);
        }
        return res.status(HttpStatus.OK).json({ wasDataUploaded });
      } catch (err) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          wasDataUploaded: { factors: false, concurrencies: false },
          error: err.message
        });
      }
    },
    getConcurrencies: async (req, res) => {
      try {
        const [month, year] = moment()
          .format('MM-YYYY')
          .split('-');

        const filename = `Concurrencias_${month}${year}.csv`;

        Logger.info(`Quadrature: generating CSV file for concurrencies...`);
        const { csvData, error } = await service.generateConcurrencyCSV({ month, year });
        if (error) {
          throw new Error(error);
        }

        res.header('content-type', 'text/csv');
        res.header('content-disposition', `attachment; filename=${filename}`);

        Logger.info(`Quadrature: sending data of Concurrencies ...`);
        return res.send(csvData);
      } catch (err) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: true,
          message: err.message
        });
      }
    },
    getFactors: async (req, res) => {
      try {
        const year = moment().format('YYYY');

        const filename = `Factores_${year}.csv`;
        Logger.info(`Quadrature: generating CSV file for Factors ...`);
        const { csvData, error } = await service.generateFactorCSV();
        if (error) {
          throw new Error(error);
        }

        res.header('content-type', 'text/csv');
        res.header('content-disposition', `attachment; filename=${filename}`);

        Logger.info(`Quadrature: sending data of Factors ...`);
        return res.send(csvData);
      } catch (err) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          error: true,
          message: err.message
        });
      }
    }
  };
};

import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { CalculationService } from '../modules/calculation/calculation.service';
import { FactoryManager } from '../common/testing/model-factory';
import { EloquentQueryBuilder } from '../common/database/eloquent-query-builder';

/**
 * Comandos estilo Laravel Artisan para NestJS
 * Inspirado en php artisan pero con TypeScript
 */

@Injectable()
@Command({
  name: 'pension:calculate',
  description: 'Calcula beneficios y descuentos de pensiones',
})
export class PensionCalculateCommand extends CommandRunner {
  private readonly logger = new Logger(PensionCalculateCommand.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly calculationService: CalculationService,
  ) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    const { pensionId, batch, type, outdated } = options;

    this.logger.log('🚀 Iniciando cálculo de pensiones...');

    try {
      if (pensionId) {
        await this.calculateSingle(pensionId);
      } else if (batch) {
        await this.calculateBatch(type, parseInt(batch));
      } else if (outdated) {
        await this.calculateOutdated();
      } else {
        await this.calculateAll();
      }

      this.logger.log('✅ Cálculo completado exitosamente');
    } catch (error) {
      this.logger.error('❌ Error en el cálculo:', error);
      process.exit(1);
    }
  }

  @Option({
    flags: '-p, --pension-id <pensionId>',
    description: 'ID de pensión específica a calcular',
  })
  parsePensionId(val: string): string {
    return val;
  }

  @Option({
    flags: '-b, --batch <size>',
    description: 'Procesar en lotes de tamaño específico',
  })
  parseBatch(val: string): string {
    return val;
  }

  @Option({
    flags: '-t, --type <type>',
    description: 'Tipo de pensión a procesar',
  })
  parseType(val: string): string {
    return val;
  }

  @Option({
    flags: '-o, --outdated',
    description: 'Solo procesar pensiones desactualizadas',
  })
  parseOutdated(): boolean {
    return true;
  }

  private async calculateSingle(pensionId: string): Promise<void> {
    this.logger.log(`Calculando pensión ${pensionId}...`);
    const result = await this.calculationService.calculatePension(pensionId);
    this.logger.log(`Pensión neta: $${result.netPension.toLocaleString()}`);
  }

  private async calculateBatch(type?: string, batchSize: number = 500): Promise<void> {
    const query = new EloquentQueryBuilder(this.prisma, 'pension')
      .where('enabled', true);

    if (type) {
      query.where('pensionType', type);
    }

    const totalCount = await query.count();
    this.logger.log(`Procesando ${totalCount} pensiones en lotes de ${batchSize}...`);

    let processed = 0;
    await query.chunk(batchSize, async (pensions) => {
      const pensionIds = pensions.map((p: any) => p.id);
      await this.calculationService.calculatePensionBatch(pensionIds);
      processed += pensions.length;
      this.logger.log(`Progreso: ${processed}/${totalCount} (${Math.round(processed/totalCount*100)}%)`);
    });
  }

  private async calculateOutdated(): Promise<void> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const query = new EloquentQueryBuilder(this.prisma, 'pension')
      .where('enabled', true)
      .where('updatedAt', '<', thirtyDaysAgo);

    const count = await query.count();
    this.logger.log(`Encontradas ${count} pensiones desactualizadas`);

    if (count > 0) {
      await this.calculateBatch(undefined, 500);
    }
  }

  private async calculateAll(): Promise<void> {
    this.logger.log('Calculando todas las pensiones activas...');
    await this.calculateBatch();
  }
}

@Injectable()
@Command({
  name: 'pension:seed',
  description: 'Genera datos de prueba para pensiones',
})
export class PensionSeedCommand extends CommandRunner {
  private readonly logger = new Logger(PensionSeedCommand.name);

  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    const { count, type, withLiquidations } = options;
    const factory = new FactoryManager(this.prisma);

    this.logger.log(`🌱 Generando ${count || 10} pensiones de prueba...`);

    try {
      if (withLiquidations) {
        for (let i = 0; i < (count || 10); i++) {
          await factory.createPensionWithLiquidation(
            type ? { pensionType: type } : {}
          );
        }
      } else {
        const pensionFactory = factory.pension();
        
        if (type) {
          pensionFactory.state(type);
        }

        await pensionFactory.times(count || 10).create();
      }

      this.logger.log('✅ Datos de prueba generados exitosamente');
    } catch (error) {
      this.logger.error('❌ Error generando datos:', error);
      process.exit(1);
    }
  }

  @Option({
    flags: '-c, --count <count>',
    description: 'Número de pensiones a generar',
  })
  parseCount(val: string): number {
    return parseInt(val, 10);
  }

  @Option({
    flags: '-t, --type <type>',
    description: 'Tipo específico de pensión',
  })
  parseType(val: string): string {
    return val;
  }

  @Option({
    flags: '-l, --with-liquidations',
    description: 'Generar también liquidaciones',
  })
  parseWithLiquidations(): boolean {
    return true;
  }
}

@Injectable()
@Command({
  name: 'pension:report',
  description: 'Genera reportes de pensiones',
})
export class PensionReportCommand extends CommandRunner {
  private readonly logger = new Logger(PensionReportCommand.name);

  constructor(private readonly prisma: PrismaService) {
    super();
  }

  async run(passedParams: string[], options?: Record<string, any>): Promise<void> {
    const { type, month, year, format } = options;

    this.logger.log('📊 Generando reporte de pensiones...');

    try {
      const query = new EloquentQueryBuilder(this.prisma, 'pension')
        .where('enabled', true)
        .with(['liquidations']);

      if (type) {
        query.where('pensionType', type);
      }

      if (month && year) {
        // Filtrar por liquidaciones del mes/año específico
        query.where('liquidations', {
          some: {
            liquidationMonth: parseInt(month),
            liquidationYear: parseInt(year),
          }
        });
      }

      const pensions = await query.get();
      
      if (format === 'json') {
        console.log(JSON.stringify(pensions, null, 2));
      } else {
        this.printTableReport(pensions);
      }

      this.logger.log('✅ Reporte generado exitosamente');
    } catch (error) {
      this.logger.error('❌ Error generando reporte:', error);
      process.exit(1);
    }
  }

  @Option({
    flags: '-t, --type <type>',
    description: 'Tipo de pensión para el reporte',
  })
  parseType(val: string): string {
    return val;
  }

  @Option({
    flags: '-m, --month <month>',
    description: 'Mes para el reporte',
  })
  parseMonth(val: string): string {
    return val;
  }

  @Option({
    flags: '-y, --year <year>',
    description: 'Año para el reporte',
  })
  parseYear(val: string): string {
    return val;
  }

  @Option({
    flags: '-f, --format <format>',
    description: 'Formato del reporte (table|json)',
  })
  parseFormat(val: string): string {
    return val;
  }

  private printTableReport(pensions: any[]): void {
    console.table(pensions.map(p => ({
      ID: p.id.substring(0, 8),
      Tipo: p.pensionType,
      'Pensión Base': `$${p.basePension.toLocaleString()}`,
      'RUT Beneficiario': p.beneficiaryRut,
      Estado: p.enabled ? 'Activa' : 'Inactiva',
      'Última Actualización': new Date(p.updatedAt).toLocaleDateString(),
    })));

    // Estadísticas resumen
    const stats = {
      'Total Pensiones': pensions.length,
      'Pensión Promedio': `$${Math.round(pensions.reduce((sum, p) => sum + Number(p.basePension), 0) / pensions.length).toLocaleString()}`,
      'Por Tipo': pensions.reduce((acc, p) => {
        acc[p.pensionType] = (acc[p.pensionType] || 0) + 1;
        return acc;
      }, {}),
    };

    console.log('\n📈 Estadísticas:');
    console.table(stats);
  }
}

// Uso:
// npm run command pension:calculate --pension-id abc123
// npm run command pension:calculate --batch 1000 --type INVALIDEZ_TOTAL
// npm run command pension:seed --count 100 --with-liquidations
// npm run command pension:report --type SUPERVIVENCIA --month 12 --year 2024

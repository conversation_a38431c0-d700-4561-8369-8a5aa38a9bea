/* eslint-disable no-unused-expressions */
const moment = require('moment');
const PensionModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const { formatRut } = require('../../sharedFiles/helpers');
const {
  findOneAndUpdateWithTracking
} = require('../../trackingUserActivity/services/trackingUserActivity.service');

const buildAggregationlookup = [
  {
    $lookup: {
      from: 'liquidations',
      let: {
        pensionBeneficiaryRut: '$beneficiary.rut',
        pensionCausantRut: '$causant.rut',
        pensionCodeIdP: '$pensionCodeId'
      },

      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ['$beneficiaryRut', '$$pensionBeneficiaryRut']
                },
                {
                  $eq: ['$causantRut', '$$pensionCausantRut']
                },
                {
                  $eq: ['$pensionCodeId', '$$pensionCodeIdP']
                }
              ]
            }
          }
        }
      ],
      as: 'liquidation'
    }
  },
  {
    $unwind: {
      path: '$liquidation'
    }
  }
];

const VALIDITY_TYPE = /^No\s+vigente$/i;
const moneySanitizer = (amount = '') =>
  `${amount}`
    .replace(/,/, '.')
    .replace(/^(\d+)\.?(\d{0,2})?\d*$/, '$1.$2')
    .replace(/(\.0+|\.)$/, '');

const service = {
  async findOnePensionAndUpdate(id) {
    try {
      const data = await PensionModel.findOneAndUpdate(
        { pension: id, enabled: true },
        { $set: { enabled: false } },
        { returnNewDocument: true }
      )
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async getAllAndFilter(query) {
    return PensionModel.find(query)
      .then(data => ({ result: data }))
      .catch(error => ({
        result: [],
        isError: true,
        error
      }));
  },

  async getAllWithFilter(query, selectedFields) {
    try {
      const data = await PensionModel.find(query, selectedFields)
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { result: [], error };
    }
  },

  async findValidPensionsByRut(rutList) {
    return PensionModel.find({
      validityType: { $not: VALIDITY_TYPE },
      enabled: true,
      'beneficiary.rut': { $in: rutList }
    }).lean();
  },

  async findValidPensions() {
    return PensionModel.find({
      validityType: { $not: VALIDITY_TYPE },
      enabled: true
    }).lean();
  },
  async createUpdatePension(pensionList) {
    if (!pensionList || !pensionList.length) return { completed: true, error: null };
    let isBulked = false;
    const session = await PensionHistoricModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionHistoricModel.collection.initializeOrderedBulkOp();
      const bulkUpdate = PensionModel.collection.initializeOrderedBulkOp();
      const promiseFunctions = pensionList.map(pension => async () => {
        const {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut },
          pensionCodeId,
          _id,
          ...pensionOverride
        } = pension;

        const dbPension = await PensionModel.findOne({
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          pensionCodeId,
          deathDate: { $exists: false },
          enabled: true
        })
          .lean()
          .exec();

        if (dbPension) {
          const { _id: id, ...pensionData } = dbPension;
          bulkUpdate
            .find({
              'beneficiary.rut': beneficiaryRut,
              'causant.rut': causantRut,
              pensionCodeId,
              deathDate: { $exists: false },
              enabled: true
            })
            .updateOne({
              $set: {
                ...pensionData,
                ...pensionOverride,
                createdAt: new Date(),
                updatedAt: new Date()
              }
            });

          bulk.insert({
            ...pensionData,
            enabled: false
          });
          isBulked = true;
        }
      });
      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }

      if (bulkUpdate.length) await bulkUpdate.execute();
      isBulked && (await bulk.execute());
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (e) {
      await session.abortTransaction();
      return { completed: false, error: e };
    }
  },
  async updateAssetsAndDiscountOfPension(pension, user) {
    try {
      const {
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        assets: { forFamilyAssignment },
        discounts: { healthLoan },
        article40
      } = pension;

      const previousPension = await PensionModel.findOne({
        'beneficiary.rut': { $regex: new RegExp(formatRut(beneficiaryRut), 'i') },
        'causant.rut': { $regex: new RegExp(formatRut(causantRut), 'i') },
        pensionCodeId,
        deathDate: { $exists: false },
        enabled: true
      }).exec();

      if (!previousPension) throw new Error('pension no encontrada');

      const {
        _doc: { _id, __v, assets, discounts, ...pensionData }
      } = previousPension;

      await findOneAndUpdateWithTracking({
        model: PensionModel,
        user,
        fullQuery: [
          {
            'beneficiary.rut': { $regex: new RegExp(formatRut(beneficiaryRut), 'i') },
            'causant.rut': { $regex: new RegExp(formatRut(causantRut), 'i') },
            pensionCodeId,
            deathDate: { $exists: false },
            enabled: true
          },
          {
            ...pensionData,
            assets: { ...assets, forFamilyAssignment: +moneySanitizer(forFamilyAssignment) },
            discounts: { ...discounts, healthLoan: +moneySanitizer(healthLoan) },
            article40: +moneySanitizer(article40)
          }
        ]
      });

      return { completed: true, error: null };
    } catch (e) {
      return { completed: false, error: e };
    }
  },

  async updatePensions(pensionsList) {
    if (!pensionsList.length) return { completed: true, error: null };
    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();

      pensionsList.forEach(({ _id, ...pension }) => {
        const {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut },
          pensionCodeId,
          enabled,
          ...pensionOverride
        } = pension;
        bulk
          .find({
            'beneficiary.rut': beneficiaryRut,
            'causant.rut': causantRut,
            pensionCodeId,
            enabled
          })
          .updateOne({
            $set: {
              ...pensionOverride,
              enabled,
              updatedAt: new Date()
            }
          });
      });
      await bulk.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async getPensionHistoryByMonth(beneficiaryRut = '', causantRut = '', query = {}) {
    return PensionHistoricModel.aggregate([
      {
        $match: {
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          createdAt: {
            $lt: moment()
              .startOf('month')
              .startOf('day')
              .toDate()
          },
          ...query
        }
      },
      {
        $group: {
          _id: { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } },
          date: { $first: '$createdAt' },
          data: { $first: '$$ROOT' }
        }
      },
      { $replaceRoot: { newRoot: '$data' } },
      { $sort: { createdAt: -1 } }
    ]);
  },

  async getPensionsWithLiquidation(queryObject) {
    try {
      const pensions = await PensionModel.aggregate([
        {
          $match: {
            ...queryObject
          }
        },
        ...buildAggregationlookup
      ]).exec();
      return { result: pensions, error: null };
    } catch (error) {
      return { result: null, error };
    }
  },

  async getPensionsFieldsWithLiquidations(queryObject, selectedFields) {
    try {
      const pensions = await PensionModel.aggregate([
        {
          $match: {
            ...queryObject
          }
        },
        {
          $project: {
            ...selectedFields
          }
        },
        ...buildAggregationlookup
      ]).exec();
      return { result: pensions, error: null };
    } catch (error) {
      return { result: null, error };
    }
  },

  async aggregationSearch(agregation) {
    try {
      const result = await PensionModel.aggregate([...agregation]);
      return { result };
    } catch (error) {
      return { completed: false, error };
    }
  },
  async updatePensionsById(pensionsList) {
    if (!pensionsList.length) return { completed: true, error: null };
    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();

      pensionsList.forEach(({ _id, ...pension }) => {
        bulk
          .find({
            _id
          })
          .updateOne({
            $set: {
              ...pension,
              updatedAt: new Date()
            }
          });
      });
      await bulk.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async updatePensionsByIdBonus(pensionsListBonus) {
    if (!pensionsListBonus.length) return { completed: true, error: null };
    const sessionBonus = await PensionModel.startSession();
    sessionBonus.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();

      pensionsListBonus.forEach(({ _id, ...pension }) => {
        const { assets, payBonus } = pension;
        bulk
          .find({
            _id
          })
          .updateOne({
            $set: {
              payBonus,
              'assets.christmasBonus': assets.christmasBonus,
              'assets.nationalHolidaysBonus': assets.nationalHolidaysBonus,
              'assets.winterBonus': assets.winterBonus,
              updatedAt: new Date()
            }
          });
      });
      await bulk.execute();
      await sessionBonus.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await sessionBonus.abortTransaction();
      return { completed: false, error };
    }
  },

  async filterPensionersAssignBonus() {
    return PensionModel.aggregate([
      {
        $match: {
          enabled: true
        }
      },
      {
        $lookup: {
          from: 'temporaryimportedbonuspensioners',
          localField: 'beneficiary.rut',
          foreignField: 'pensionerRut',
          as: 'temporaryimportedbonuspensioners'
        }
      },
      {
        $project: {
          'beneficiary.rut': 1,
          'causant.rut': 1,
          assets: 1,
          payBonus: 1,
          basePension: 1,
          article40: 1,
          law19403: 1,
          law19539: 1,
          law19953: 1,
          amountOtherPension: 1,
          dl1026: 1,
          temporaryimportedbonuspensioners: 1
        }
      },
      {
        $addFields: {
          id: { $toString: '$_id' }
        }
      }
    ]);
  },

  async updatePensionsReset() {
    const sessionReset = await PensionModel.startSession();
    sessionReset.startTransaction();
    try {
      const bulkUpdate = PensionModel.collection.initializeOrderedBulkOp();

      const pensionados = await PensionModel.find({ enabled: true }).lean();
      pensionados.forEach(({ _id, ...pensioner }) => {
        const {
          assets,
          retroactiveAmounts,
          reservedAmounts,
          discounts,
          apsInfo,
          currentCapitalCalculation
        } = pensioner;
        bulkUpdate
          .find({
            _id
          })
          .updateOne({
            $set: {
              ...pensioner,
              bankRejected: 'No',
              paycheckRefunded: 'No',
              validatedStudyPeriod: 'No',
              article41: 0,
              numberOfCharges: 0,
              rejectionHealthExemptionAmount: 0,
              rejectionHealthReductionAmount: 0,
              ChangeOfPensionTypeDueToCharges: false,
              rejectionIPS: false,
              assets: {
                ...assets,
                aps: 0,
                forFamilyAssignment: 0,
                christmasBonus: 0,
                nationalHolidaysBonus: 0,
                winterBonus: 0,
                marriageBonus: 0,
                healthExemption: 'No',
                healthDiscount: 'No',
                netTotalNonFormulable: 0,
                taxableTotalNonFormulable: 0
              },
              retroactiveAmounts: {
                ...retroactiveAmounts,
                forSurvival: 0,
                forDisability: 0,
                forInstitutionalPatient: 0,
                forBonuses: 0,
                forBasePension: 0,
                forArticle40: 0,
                forArticle41: 0,
                forFamilyAssignment: 0,
                forTaxableTotalNonFormulableAssets: 0,
                forNetTotalNonFormulableAssets: 0,
                forTotalNonFormulableDiscounts: 0,
                forPayCheck: 0,
                forRejection: 0
              },
              reservedAmounts: {
                ...reservedAmounts,
                forSurvival: 0,
                forDisability: 0,
                forInstitutionalPatient: 0,
                forBasePension: 0,
                forArticle40: 0,
                forArticle41: 0,
                forTaxableTotalNonFormulableAssets: 0,
                forNetTotalNonFormulableAssets: 0,
                forTotalNonFormulableDiscounts: 0,
                forBonuses: 0,
                forPayCheck: 0,
                forRejection: 0
              },
              discounts: {
                ...discounts,
                socialCredits18: 0,
                socialCreditsLosAndes: 0,
                socialCreditsLosHeroes: 0,
                socialCreditsLaAraucana: 0,
                othersLosAndes: 0,
                othersLosHeroes: 0,
                healthLoan: 0,
                health: 0,
                afp: 0,
                onePercentAdjusted: 0,
                onePercentLaAraucana: 'No',
                onePercent18: 'No',
                onePercentLosAndes: 'No',
                onePercentLosHeroes: 'No',
                totalNonFormulable: 0
              },
              apsInfo: {
                ...apsInfo,
                apsResolutionNumber: 0,
                apsResolutionDate: 0,
                apsPaymentUniqueId: 0,
                apsTransferCode: 0,
                apsOrigin: ''
              },
              currentCapitalCalculation: {
                ...currentCapitalCalculation,
                basePensionCapital: 0,
                capitalLaw19578: 0,
                capitalLaw19953: 0,
                capitalLaw20102: 0,
                basePensionWorkingCapital: 0,
                basePensionNotWorkingCapital: 0,
                workingCapitalLaw19578: 0,
                notWorkingCapitalLaw19578: 0,
                workingCapitalLaw19953: 0,
                notWorkingCapitalLaw19953: 0,
                workingCapitalLaw20102: 0,
                notWorkingCapitalLaw20102: 0,
                capitalPBIpc: 0,
                capitalLaw19578Ipc: 0,
                capitalLaw19953Ipc: 0,
                capitalLaw20102Ipc: 0,
                totalCapitalIpc: 0,
                capitalBonusLaw19403: 0,
                capitalBonusLaw19953: 0,
                capitalBonusLaw19539: 0,
                workingCapitalBonusLaw19403: 0,
                notWorkingCapitalBonusLaw19403: 0,
                workingCapitalBonusLaw19953: 0,
                notWorkingCapitalBonusLaw19953: 0,
                workingCapitalBonusLaw19539: 0,
                notWorkingCapitalBonusLaw19539: 0,
                capitalBonusLaw19403Ipc: 0,
                capitalBonusLaw19953Ipc: 0,
                capitalBonusLaw19539Ipc: 0,
                capitalTotalBonus: 0,
                capitalTotalBonusIpc: 0,
                totalCapital: 0
              },
              updatedAt: new Date(),
              numberOfChargesExternal: 0,
              numberOfChargesArticle41: 0
            }
          });
      });

      await bulkUpdate.execute();
      await sessionReset.commitTransaction();

      return { completed: true, error: null };
    } catch (error) {
      await sessionReset.abortTransaction();
      return { completed: false, error };
    }
  },

  async getAllEnabled() {
    return PensionModel.aggregate([
      {
        $match: { enabled: true }
      },
      {
        $project: {
          'beneficiary.rut': 1,
          'causant.rut': 1,
          pensionType: 1,
          basePension: 1,
          law19403: 1,
          law19539: 1,
          law19953: 1,
          dateOfBirth: 1,
          article40: 1,
          dl1026: 1,
          retirement: 1,
          amountOtherPension: 1
        }
      },
      {
        $addFields: {
          id: { $toString: '$_id' }
        }
      }
    ]);
  },

  async updatePensionAndInsertHistory(pensionList) {
    const session = await PensionHistoricModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionHistoricModel.collection.initializeOrderedBulkOp();
      const bulkUpdate = PensionModel.collection.initializeOrderedBulkOp();
      const promiseFunctions = pensionList.map(pension => async () => {
        const { _id, basePension, law19403, law19539, law19953 } = pension;

        bulkUpdate
          .find({
            _id
          })
          .updateOne({
            $set: {
              basePension,
              law19403,
              law19539,
              law19953,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });

        const dbPension = await PensionModel.findOne({
          _id
        }).lean();

        if (dbPension) {
          const { _id: id, ...pensionData } = dbPension;
          bulk.insert({
            ...pensionData,
            enabled: false
          });
        }
      });
      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }
      await bulk.execute();
      await bulkUpdate.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (e) {
      await session.abortTransaction();
      return { completed: false, error: e };
    }
  },

  async updatePensionsByBeneficiaryCausant(pensionsList) {
    if (!pensionsList.length) return { completed: true, error: null };
    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();

      pensionsList.forEach(({ _id, ...pension }) => {
        const {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut }
        } = pension;

        bulk
          .find({
            'beneficiary.rut': beneficiaryRut,
            'causant.rut': causantRut
          })
          .updateOne({
            $set: {
              ...pension,
              updatedAt: new Date()
            }
          });
      });
      await bulk.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async updatePensionsByIdRetroActivoAmounts(pensionsList) {
    if (!pensionsList.length) return { completed: true, error: null };
    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();
      const bulkHistoric = PensionHistoricModel.collection.initializeOrderedBulkOp();
      pensionsList.forEach(({ _id, ...pension }) => {
        bulk
          .find({
            _id
          })
          .updateOne({
            $set: {
              ...pension,
              updatedAt: new Date()
            }
          });

        bulkHistoric
          .find({
            _id
          })
          .updateOne({
            $set: {
              ...pension,
              updatedAt: new Date()
            }
          });
      });
      if (bulk.length) await bulk.execute();
      if (bulkHistoric.length) await bulkHistoric.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },
  async createUpdatePensionIPC(pensionList) {
    if (!pensionList || !pensionList.length) return { completed: true, error: null };
    const session = await PensionHistoricModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionHistoricModel.collection.initializeOrderedBulkOp();
      const bulkUpdate = PensionModel.collection.initializeOrderedBulkOp();

      const pensionados = await PensionModel.find({ enabled: true }).lean();

      pensionados.forEach(pensionData => {
        const { _id, ...pensionOld } = pensionData;

        bulk.insert({
          ...pensionOld,
          enabled: false
        });
      });

      const promiseFunctions = pensionList.map(pension => async () => {
        const {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut },
          pensionCodeId,
          _id,
          ...pensionOverride
        } = pension;

        bulkUpdate
          .find({
            'beneficiary.rut': beneficiaryRut,
            'causant.rut': causantRut,
            pensionCodeId,
            deathDate: { $exists: false },
            enabled: true
          })
          .updateOne({
            $set: {
              ...pensionOverride,
              updatedAt: new Date()
            }
          });
      });
      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }

      if (bulkUpdate.length) await bulkUpdate.execute();
      if (bulk.length) await bulk.execute();

      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (e) {
      await session.abortTransaction();
      return { completed: false, error: e };
    }
  },

  async updateDisableByDeath() {
    const session = await PensionModel.startSession();
    const today = moment(new Date());
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();
      bulk
        .find({
          enabled: true,
          inactivationReason: 'Fallecimiento',
          endDateOfValidity: { $lt: today },
          validityType: { $nin: [/No Vigente/i] }
        })
        .update({
          $set: {
            validityType: 'No vigente',
            updatedAt: new Date()
          }
        });

      await bulk.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  },

  async filterPensionsHorphanhoodMother(pensionType) {
    return PensionModel.aggregate([
      {
        $match: {
          pensionType: { $in: pensionType },
          manuallyReactivated: { $ne: true },
          inactivateManually: { $ne: true },
          enabled: true
        }
      },
      {
        $addFields: {
          pensionIdCode: {
            $toInt: '$pensionCodeId'
          }
        }
      },
      {
        $lookup: {
          from: 'temporaryhorphanhoods',
          let: {
            pensionBeneficiaryRut: '$beneficiary.rut',
            pensionCausantRut: '$causant.rut',
            pensionCodeIdP: '$pensionIdCode'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$causantRut', '$$pensionCausantRut'] },
                    { $eq: ['$motherRut', '$$pensionBeneficiaryRut'] },
                    { $eq: ['$pensionId', '$$pensionCodeIdP'] }
                  ]
                }
              }
            }
          ],
          as: 'temporaryhorphanhoodsMadre'
        }
      },
      {
        $project: {
          pensionCodeId: 1,
          beneficiary: 1,
          causant: 1,
          pensionType: 1,
          endDateOfTheoricalValidity: 1,
          inactivationReason: 1,
          temporaryhorphanhoodsMadre: 1,
          enabled: 1
        }
      }
    ]);
  }
};

module.exports = { ...service };

const moment = require('moment');

const ORPHANHOOD_TYPE_LIST = [
  /Pensi[oó]n\s+por\s+orfandad/i,
  /Pensi[oó]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];
const ORPHANDHOOD_LIMIT = 24;
const LASTDAY_OF_THE_YEAR = '1231';
const isOrphanhood = validityType => /Vigente\s+orfandad/i.test(validityType);
const isInOrphanhoodTypeList = type => ORPHANHOOD_TYPE_LIST.some(regex => regex.test(type));

const setEndDateOfValidityByOrphanhood = (pension, dateOfBirth) => {
  const yearLimit = moment(dateOfBirth).year() + ORPHANDHOOD_LIMIT;
  const validityEndDate = moment(`${yearLimit}${LASTDAY_OF_THE_YEAR}`).toDate();
  return {
    ...pension,
    endDateOfTheoricalValidity: validityEndDate,
    endDateOfValidity: validityEndDate
  };
};

const setEndDateByOrphanhood = (pension, pensionType, validityType) => {
  let modifiedData = { ...pension };
  if (isOrphanhood(validityType) && isInOrphanhoodTypeList(pensionType)) {
    modifiedData = setEndDateOfValidityByOrphanhood({ ...pension }, pension.dateOfBirth);
  }
  return { ...pension, ...modifiedData };
};

module.exports = setEndDateByOrphanhood;

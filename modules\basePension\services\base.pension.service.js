/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
const mongoose = require('mongoose');
const rules = require('./rules');

const aplicateMinimumPensionPayment = pension => {
  const { dl1026 } = pension;
  if (dl1026 > 0) return false;

  return true;
};

module.exports = {
  async obtainActivePensions(pensionService) {
    try {
      return await pensionService.getAllEnabled();
    } catch (e) {
      return [];
    }
  },
  async calculateBasePension(pensions) {
    const newPensions = [];
    const basePensionModel = mongoose.connection.db.collection('basePensionRules');
    const basePensionRules = await basePensionModel.find({ type: 'pension' }).toArray();

    for (const pension of pensions) {
      if (aplicateMinimumPensionPayment(pension)) {
        let calculatePension = { ...pension, basePensionRules };
        for (const rule of rules) {
          const [calculated] = await rule({ ...calculatePension });
          if (calculated) {
            calculatePension = { ...calculatePension, ...calculated };
            break;
          }
        }
        newPensions.push(calculatePension);
      }
    }
    return newPensions;
  }
};

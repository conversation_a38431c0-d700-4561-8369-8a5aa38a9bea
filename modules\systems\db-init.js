/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');

const viewsResource = require('./resources/views.json');
const rolesResource = require('./resources/roles.json');
const usersResource = require('./resources/users.json');
const motivesResource = require('./resources/motives.json');

const viewService = require('./rolesAndPermissions/services/view.service');
const roleService = require('./rolesAndPermissions/services/role.service');
const motiveService = require('./motives/services/motivesService');
const userService = require('./users/services/users.service');

const initDB = async () => {
  const { motives } = await motiveService.readMotives();
  const { views } = await viewService.readViews();

  if (!motives.length) {
    await motiveService.bulkDefaultMotives(
      motivesResource.map(motive => ({
        ...motive,
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDefault: true
      }))
    );
  }

  if (!views.length) {
    await viewService.bulkUpdateOrCreateUnordered(
      viewsResource.map(view => ({
        ...view,
        _id: new mongoose.Types.ObjectId(view._id),
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }))
    );
    await roleService.bulkUpdateOrCreateUnordered(
      rolesResource.map(role => ({
        ...role,
        _id: new mongoose.Types.ObjectId(role._id),
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }))
    );
    await userService.bulkUpdateOrCreateUnordered(
      usersResource.map(user => ({
        ...user,
        role: mongoose.Types.ObjectId(user.role),
        isActive: true,
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        __v: 0
      }))
    );
  }
};

module.exports = initDB;

const { formatter } = require('./formatFields');

const pensionFields = [
  {
    namePensioner: { name: 'Nombre beneficiario', format: formatter.normal, path: 'namePensioner' }
  },
  {
    lastName: { name: 'Apellido paterno beneficiario', format: formatter.normal, path: 'lastName' }
  },
  {
    mothersLastName: {
      name: 'Apellido materno beneficiario',
      format: formatter.normal,
      path: 'mothersLastName'
    }
  },
  {
    pensionCodeId: {
      name: 'Codigo identificador de pensión',
      format: formatter.number,
      path: 'pensionCodeId'
    }
  },
  { pensionType: { name: 'Tipo de pensión', format: formatter.normal, path: 'pensionType' } },
  {
    endDateOfTheoricalValidity: {
      name: 'Fecha de fin de vigencia teórica',
      format: formatter.date,
      path: 'endDateOfTheoricalValidity'
    }
  },
  { phone: { name: 'Teléfono', format: formatter.number, path: 'phone' } },
  { email: { name: '<PERSON><PERSON><PERSON> electrón<PERSON>', format: formatter.normal, path: 'email' } },
  { address: { name: '<PERSON><PERSON><PERSON><PERSON> cobrante', format: formatter.normal, path: 'address' } },
  { commune: { name: '<PERSON><PERSON><PERSON>', format: formatter.normal, path: 'commune' } },
  { city: { name: 'Ciudad', format: formatter.normal, path: 'city' } }
];

const simpleFields = pensionFields.map(x => Object.keys(x)[0]);

module.exports = { pensionFields, simpleFields };

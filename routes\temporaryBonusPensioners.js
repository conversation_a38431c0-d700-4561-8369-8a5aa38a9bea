const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const FactoryController = require('../modules/bonusPensioners/controllers/temporaryBonusPensioners.controller');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const temporaryBonusPensionersController = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    validationResult
  });

  router.get('/download', validateAccess(), async (req, res) =>
    temporaryBonusPensionersController.getFileString(req, res)
  );

  router.post('/import', validateAccess(), async (req, res) =>
    temporaryBonusPensionersController.postFileString(req, res)
  );
};

const moment = require('moment');

const highDeferred = 3;
const highInTheDay = 2;

const isHigherThanOtherDate = (date, compareDate) => moment(date).diff(compareDate) > 0;

const isHigherThanActuallyDate = date => moment(date).diff(moment.now()) > 0;

const isYearOneOrEmpty = fechaAlta => {
  if (!fechaAlta) return true;

  const fechaAltaAux = new Date(fechaAlta);
  return fechaAltaAux.getFullYear() === 1;
};

const getHigherRepose = reposes =>
  reposes.reduce((item, compare) =>
    moment(item.fechaInicioReposo).diff(moment(compare.date)) < 0 ? compare : item
  );

const reposesRules = repose => {
  const { fechaInicioReposo, fechaAlta, tipoAlta } = repose;
  const fechaAltaAux = new Date(fechaAlta);
  const anho = fechaAltaAux.getFullYear();
  return (
    tipoAlta === highInTheDay ||
    tipoAlta === highDeferred ||
    isHigherThanOtherDate(fechaAlta, fechaInicioReposo) ||
    isHigherThanActuallyDate(fechaAlta) ||
    !fechaAlta ||
    anho === 1
  );
};

const isAvailableToReactivate = (reposes, endDateOfValidity) => {
  if (!reposes || !reposes.length) return { result: false };

  const reposesAfterEndValidity = reposes.filter(item =>
    isHigherThanOtherDate(item.fechaInicioReposo, endDateOfValidity)
  );

  const resposesMatchWithCondicions = reposesAfterEndValidity.filter(reposesRules);

  if (!resposesMatchWithCondicions.length) return { result: false };

  const { fechaAlta } = getHigherRepose(resposesMatchWithCondicions);

  return { fechaAlta, result: true };
};

const getHigherResolution = incapacities =>
  incapacities.reduce((item, compare) =>
    moment(item.fechaResolucion).diff(moment(compare.date)) < 0 ? compare : item
  );

const reactivatePension = ({ incapacidad, fechaAlta, pension }) => {
  const {
    endDateOfTheoricalValidity,
    beneficiary: { rut: beneficiaryRut },
    causant: { rut: causantRut },
    _doc
  } = pension;
  const { _id, __v, ...data } = _doc;

  const dateToInactivate = moment()
    .add(1, 'months')
    .startOf('month')
    .toDate();

  if (isHigherThanActuallyDate(fechaAlta) || isYearOneOrEmpty(fechaAlta)) {
    return {
      ...data,
      causantRut,
      beneficiaryRut,
      reactivationDate: new Date(),
      endDateOfValidity: endDateOfTheoricalValidity,
      validityType: 'Vigente vitalicia',
      inactivationReason: ''
    };
  }

  if (!incapacidad || !incapacidad.length)
    return {
      ...data,
      endDateOfValidity: new Date(fechaAlta),
      causantRut,
      beneficiaryRut,
      inactivationReason: 'Alta médica',
      validityType: 'Vigente vitalicia',
      reactivationDate: new Date(),
      evaluationDate: new Date(),
      dateToInactivate
    };

  const withIncapacityTwo = incapacidad.filter(item => item.modalidad === '2');

  const incapacityData = withIncapacityTwo.length ? getHigherResolution(withIncapacityTwo) : null;

  if (incapacityData) {
    return {
      ...data,
      endDateOfValidity: incapacityData.fechaResolucion,
      causantRut,
      beneficiaryRut,
      inactivationReason: 'Resolución definitiva',
      validityType: 'Vigente vitalicia',
      reactivationDate: new Date(),
      evaluationDate: new Date(),
      dateToInactivate
    };
  }

  return {
    ...data,
    endDateOfValidity: new Date(fechaAlta),
    causantRut,
    beneficiaryRut,
    inactivationReason: 'Alta médica',
    validityType: 'Vigente vitalicia',
    dateToInactivate,
    reactivationDate: new Date(),
    evaluationDate: new Date()
  };
};

module.exports = {
  reposesRules,
  getHigherResolution,
  reactivatePension,
  isAvailableToReactivate,
  isHigherThanOtherDate,
  isHigherThanActuallyDate
};

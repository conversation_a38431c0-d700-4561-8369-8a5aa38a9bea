module.exports = ({ HttpStatus, Error<PERSON>uilder, minimunSalaryService, validationResult, Logger }) => {
  return {
    updateMinimunSalary: async (req, res) => {
      Logger.info('update minimun salary: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { name, amount, publishDate, validityDate } = req.body.im;
      const { result, isError, error } = await minimunSalaryService.updateMinimunSalary({
        name,
        amount,
        publishDate,
        validityDate
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on minimun salary has been successfully completed');
        res.status(HttpStatus.OK).json({ result });
      }
    },

    getMinimunSalarys: async (req, res) => {
      try {
        Logger.info('Get all minimun salary');
        const { result } = await minimunSalaryService.getMinimunSalarys();
        res.status(HttpStatus.OK).json({ result });
      } catch (error) {
        Logger.error(error);
      }
    }
  };
};

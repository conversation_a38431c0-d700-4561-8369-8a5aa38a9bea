/**
 * Tipos TypeScript para campos JSONB del sistema de pensiones
 * Proporciona type safety para datos JSON almacenados en PostgreSQL
 */

// Tipos para Assets (Beneficios)
export interface PensionAssets {
  aps?: number;
  familyAssignment?: number;
  christmasBonus?: number;
  nationalHolidaysBonus?: number;
  winterBonus?: number;
  marriageBonus?: number;
  healthExemption?: number;
  rebsal?: number;
  nonFormulableNet?: number;
  nonFormulableTaxable?: number;
  
  // Assets por razón específica
  assetsByReason?: AssetByReason[];
  
  // Configuración de bonos
  bonusConfig?: {
    payBonus: 'SI' | 'NO';
    bonusExclusions?: string[];
    customBonusRules?: Record<string, any>;
  };
}

export interface AssetByReason {
  reason: string;
  amount: number;
  assetType: 'imponible' | 'líquido';
  startDate?: string;
  endDate?: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

// Tipos para Discounts (Descuentos)
export interface PensionDiscounts {
  afp?: number;
  health?: number;
  healthLoan?: number;
  healthUF?: number;
  onePercentAdjusted?: number;
  
  // Descuentos por caja de compensación
  compensationBoxes?: {
    laAraucana?: CompensationBoxDiscount;
    caja18?: CompensationBoxDiscount;
    losAndes?: CompensationBoxDiscount;
    losHeroes?: CompensationBoxDiscount;
  };
  
  // Descuentos no formulables
  nonFormulable?: number;
  nonFormulableByReason?: DiscountByReason[];
  
  // Retenciones judiciales
  judicialRetentions?: JudicialRetention[];
}

export interface CompensationBoxDiscount {
  onePercent: 'Si' | 'No';
  socialCredits: number;
  others: number;
  maxAmount?: number;
}

export interface DiscountByReason {
  reason: string;
  amount: number;
  startDate?: string;
  endDate?: string;
  isActive: boolean;
  category?: 'judicial' | 'voluntary' | 'legal';
}

export interface JudicialRetention {
  id: string;
  type: 'percentage' | 'fixed';
  amount: number;
  validity: boolean;
  paymentInfo?: {
    bank: string;
    accountNumber: string;
    accountType: string;
  };
  collector?: {
    rut: string;
    name: string;
    address: string;
  };
  courtOrder?: {
    number: string;
    date: string;
    court: string;
  };
}

// Tipos para Retroactive Amounts (Montos Retroactivos)
export interface RetroactiveAmounts {
  forBasePension?: number;
  forArticle40?: number;
  forArticle41?: number;
  forBonuses?: number;
  forFamilyAssignment?: number;
  forSurvival?: number;
  forDisability?: number;
  forInstitutionalPatient?: number;
  forRejection?: number;
  forPayCheck?: number;
  
  // Retroactivos por período
  byPeriod?: RetroactivePeriod[];
  
  // Configuración de cálculo
  calculationConfig?: {
    method: 'automatic' | 'manual';
    basePeriod: string;
    adjustmentFactor?: number;
  };
}

export interface RetroactivePeriod {
  period: string; // YYYY-MM
  amount: number;
  reason: string;
  calculatedAt: string;
  approvedBy?: string;
}

// Tipos para Reserved Amounts (Montos Reservados)
export interface ReservedAmounts {
  forBasePension?: number;
  forArticle40?: number;
  forArticle41?: number;
  forBonuses?: number;
  forSurvival?: number;
  forDisability?: number;
  forInstitutionalPatient?: number;
  forRejection?: number;
  
  // Reservas por categoría
  byCategory?: {
    [category: string]: ReservedCategory;
  };
}

export interface ReservedCategory {
  amount: number;
  reason: string;
  reservedAt: string;
  expiresAt?: string;
  status: 'active' | 'used' | 'expired';
}

// Tipos para Payment Info (Información de Pago)
export interface PaymentInfo {
  bank: string;
  accountNumber: string;
  accountType: 'CUENTA_CORRIENTE' | 'CUENTA_VISTA' | 'CUENTA_AHORRO';
  branchOffice?: string;
  
  // Información adicional
  paymentMethod?: 'BANK_TRANSFER' | 'CHECK' | 'CASH';
  paymentSchedule?: {
    frequency: 'MONTHLY' | 'BIWEEKLY';
    dayOfMonth?: number;
    excludeHolidays: boolean;
  };
  
  // Validaciones
  isValidated: boolean;
  validatedAt?: string;
  validatedBy?: string;
}

// Tipos para Calculation Config (Configuración de Cálculo)
export interface CalculationConfig {
  version: string;
  rulesVersion: string;
  
  // Configuraciones específicas
  useMinimumPension: boolean;
  applyIpcAdjustment: boolean;
  calculateRetroactive: boolean;
  
  // Overrides manuales
  manualOverrides?: {
    [field: string]: {
      value: any;
      reason: string;
      appliedBy: string;
      appliedAt: string;
    };
  };
  
  // Configuración de reglas
  ruleOverrides?: {
    disabledRules: string[];
    customRules: CustomRule[];
  };
}

export interface CustomRule {
  id: string;
  name: string;
  condition: string; // JSON Logic expression
  action: string;
  priority: number;
  isActive: boolean;
}

// Tipos para Audit Trail (Pista de Auditoría)
export interface AuditEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  action: string;
  entity: string;
  entityId: string;
  
  // Cambios realizados
  changes?: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  
  // Contexto adicional
  context?: {
    ip: string;
    userAgent: string;
    sessionId: string;
    reason?: string;
  };
}

// Tipos para Custom Fields (Campos Personalizados)
export interface CustomFields {
  [key: string]: CustomField;
}

export interface CustomField {
  value: any;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  label: string;
  description?: string;
  isRequired: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
  createdBy: string;
  createdAt: string;
}

// Tipo principal que combina todos los JSONB fields
export interface PensionJsonbData {
  assets?: PensionAssets;
  discounts?: PensionDiscounts;
  retroactiveAmounts?: RetroactiveAmounts;
  reservedAmounts?: ReservedAmounts;
  paymentInfo?: PaymentInfo;
  calculationConfig?: CalculationConfig;
  auditTrail?: AuditEntry[];
  customFields?: CustomFields;
}

// Utility types para validación
export type JsonbFieldName = keyof PensionJsonbData;

export interface JsonbUpdateOperation {
  field: JsonbFieldName;
  operation: 'set' | 'merge' | 'append' | 'delete';
  path?: string; // JSON path para operaciones anidadas
  value: any;
}

// Tipos para consultas JSONB
export interface JsonbQuery {
  field: JsonbFieldName;
  operator: 'contains' | 'contained_by' | 'has_key' | 'has_any_key' | 'has_all_keys' | 'path_exists';
  value: any;
  path?: string;
}

const cronDescription = 'set calculation of earned fields';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual.';
const cronMark = 'SET_CALCULATION_OF_EARNED_FIELDS';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = 'TRANSFER_PENSIONS';

const getMissingDependency = dep => `La siguiente dependencia ${dep} aún no se ha ejecutado`;

const workerFn = async ({ Logger, logService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} comprobar si este proceso se ejecutó previamente o no`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription}: iniciar verificación de dependencia`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependency(dependencyMark));
      return { executionCompleted: false, message: getMissingDependency(dependencyMark) };
    }

    const { error } = await service.setAccruedFields();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} proceso finalizado`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const LiquidationModel = require('../../../models/liquidation');

const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./dbService');
const pensionService = require('../../pensions/services/pension.service');

describe('Set Reserved Amount for Institutional Patient', () => {
  beforeAll(beforeAllTests);

  it('should get pensions and set the reservedAmounts.forInstitutionalPatient field', async () => {
    const pension = {
      ...pensionData,
      pensionType: 'Pension por accidente de trabajo',
      validityType: 'vigente',
      institutionalPatient: true
    };

    const liquidation = {
      beneficiaryRut: pension.beneficiary.rut,
      causantRut: pension.causant.rut,
      pensionCodeId: pension.pensionCodeId,
      netPension: 12345.056
    };

    const insertedPension = await PensionModel.create(pension).catch(e => console.error(e));
    await LiquidationModel.create(liquidation).catch(e => console.error(e));

    const { completed, error } = await service.updatePensions(pensionService);
    const updatedPension = await PensionModel.findById(insertedPension._id);
    expect(completed).toBe(true);
    expect(error).toBe(null);

    expect(updatedPension.reservedAmounts.forInstitutionalPatient).toBe(12345.06);
  });

  afterAll(afterAllTests);
});

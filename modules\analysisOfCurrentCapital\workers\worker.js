const cronMark = 'ANALYSIS_OF_CURRENT_CAPITAL';
const dependencyMark = 'CALCULATE_CURRENT_CAPITAL';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const missingDependencyMsg = `Dependency ${dependencyMark} not yet executed`;
const successMessage = `Process ${cronMark} completed successfully.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, service, logService, job }) => {
  try {
    Logger.info(`Cron execution start: ${cronMark}. Checking if cron was executed previously ...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: dependency verification started...`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDependencyMsg);
      return { message: missingDependencyMsg };
    }

    Logger.info(`${cronMark}: process started`);
    await service.generateValidPensionsInPrevAndCurrMonthReport({
      firstCategory: 'Mantiene',
      secondCategory: 'Nuevo',
      reportType: 'Reporte 1'
    });
    await service.generateValidPensionsInPrevAndCurrMonthReport({
      firstCategory: 'Salida',
      secondCategory: 'Mantiene',
      reportType: 'Reporte 2'
    });
    await service.generatePensionsLinkedAndReactivatedInCurrentMonthReport();
    await service.generatePensionsInactInCurrMonthReport();
    await service.generateValidPensionInCurrentMonthReport();
    await logService.saveLog(cronMark);

    Logger.info(`${cronMark}: process finished`);

    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronMark} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronMark} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

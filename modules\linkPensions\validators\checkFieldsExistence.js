const { body } = require('express-validator');

const requiredPathFields = [
  'beneficiary.rut',
  'basePension',
  'pensionType',
  'validityType',
  'pensionCodeId',
  'dateOfBirth',
  'gender',
  'causant.rut',
  'causant.name',
  'causant.lastName',
  'collector.rut',
  'collector.name',
  'collector.lastName',
  'beneficiary.name',
  'beneficiary.phone',
  'beneficiary.lastName',
  'disabilityDegree',
  'resolutionNumber',
  'resolutionDate',
  'accidentDate',
  'accidentNumber',
  'transient',
  'country',
  'initialBasePension',
  'pensionStartDate',
  'article40',
  'discounts.healthUF',
  'familyGroup',
  'increasingInLaw19578',
  'increasingInLaw19953',
  'increasingInLaw20102',
  'basePensionWithoutIncreases',
  'heavyDuty',
  'parentRUT'
];

const getField = (path, data) =>
  path.split('.').reduce((object, nestedField) => object && object[nestedField], data);

const addDefaultNumericalValue = (value, defaultValue) => {
  if (value === '' || value === null) return defaultValue;
  return value;
};

const requiredFieldsExistence = data =>
  requiredPathFields.every(path => getField(path, data) !== undefined);

const RUT_PATTERN = /^(\d{1,2})\.?(\d{3})\.?(\d{3})-([0-9kK])$/;
const MIN_RUT_LENGTH_WITHOUT_DOTS = 8;
const isLengthBelowAdmitted = (rut = '') =>
  rut.replace(/^0+/, '').replace(/[^\dk]/gi, '').length < MIN_RUT_LENGTH_WITHOUT_DOTS;

const formatRut = (rut = '') => {
  if (isLengthBelowAdmitted(rut)) return '';
  return rut
    .replace(/^0+/, '')
    .replace(/[^\dkK]/g, '')
    .replace(/([0-9kK])$/, '-$1')
    .replace(/[k](?=.*[k])/gi, '')
    .replace(RUT_PATTERN, '$1$2$3-$4')
    .toUpperCase();
};
const sanitizeRuts = ({ beneficiary, causant, collector, parentRUT, ...otherFields }) => {
  const { rut: beneficiaryRut = '' } = beneficiary;
  const { rut: causantRut = '' } = causant;
  const { rut: collectorRut = '' } = collector;

  return {
    beneficiary: {
      ...beneficiary,
      rut: formatRut(beneficiaryRut)
    },
    causant: {
      ...causant,
      rut: formatRut(causantRut)
    },
    collector: {
      ...collector,
      rut: formatRut(collectorRut)
    },
    parentRUT: formatRut(parentRUT),
    ...otherFields
  };
};

const sanitizeNumericalFields = ({
  familyGroup,
  increasingInLaw19953,
  increasingInLaw19578,
  increasingInLaw20102,
  basePensionWithoutIncreases,
  ...otherFields
}) => {
  return {
    ...otherFields,
    familyGroup: addDefaultNumericalValue(familyGroup, 1),
    increasingInLaw19578: addDefaultNumericalValue(increasingInLaw19578, 0),
    increasingInLaw19953: addDefaultNumericalValue(increasingInLaw19953, 0),
    increasingInLaw20102: addDefaultNumericalValue(increasingInLaw20102, 0),
    basePensionWithoutIncreases: addDefaultNumericalValue(basePensionWithoutIncreases, 0)
  };
};
const TYPES_OF_DISABILITY_PENSIONS = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const setDisabilityType = (pensionType, disabilityType) => {
  const isTypeOfDisability = TYPES_OF_DISABILITY_PENSIONS.some(regex => regex.test(pensionType));

  if (isTypeOfDisability && !disabilityType) {
    return 'Invalidez total';
  }
  if (!isTypeOfDisability && !disabilityType) {
    return 'No invalida';
  }
  return disabilityType;
};

const sanitizeDisabilityTypeField = ({ pensionType, disabilityType, ...otherFields }) => {
  return {
    ...otherFields,
    pensionType,
    disabilityType: setDisabilityType(pensionType, disabilityType)
  };
};

const validators = [
  body('*')
    .notEmpty()
    .custom(data => requiredFieldsExistence(data))
    .withMessage('required fields are missing')
    .customSanitizer(data => sanitizeRuts(data))
    .customSanitizer(data => sanitizeNumericalFields(data))
    .customSanitizer(data => sanitizeDisabilityTypeField(data))
];

module.exports = {
  validators,
  requiredFieldsExistence,
  getField,
  sanitizeNumericalFields,
  sanitizeRuts,
  sanitizeDisabilityTypeField
};

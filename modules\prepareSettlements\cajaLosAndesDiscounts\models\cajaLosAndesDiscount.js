const mongoose = require('mongoose');

const { Schema } = mongoose;

const CajaLosAndesDiscountSchema = new Schema(
  {
    rut: { type: String, required: true, maxlength: 12 },
    movementType: { type: Number },
    creditDiscountAmount: { type: Number },
    anotherDiscountAmount: { type: Number },
    month: { type: Number },
    year: { type: Number }
  },
  { timestamps: true }
);

CajaLosAndesDiscountSchema.index({
  rut: 1,
  month: 1,
  year: 1
});

module.exports = mongoose.model('TemporaryCajaLosAndesDiscount', CajaLosAndesDiscountSchema);

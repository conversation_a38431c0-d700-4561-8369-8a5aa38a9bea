const { codeFormatter, rutFormatter } = require('../validators/rutValidator');
const { mapperTemporaryIsapres } = require('../validators/isaprePortalValidator');

module.exports = ({ HttpStatus, ErrorBuilder, isapreService, validationResult, Logger }) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }
  return {
    wasExecuted: async (req, res) => {
      if (Object.entries(req.body).length > 0) {
        res.status(HttpStatus.BAD_REQUEST).end();
        return;
      }

      const {
        wasLoadData,
        completed,
        isError,
        error
      } = await isapreService.wasExecutedIsaprePortalProcess();
      if (isError) {
        Logger.error(`Get error: ${error}`);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info(`wasExecuted result: ${JSON.stringify(wasLoadData, completed)}`);
        res.status(HttpStatus.OK).json({ wasLoadData, completed });
      }
    },
    insertTemporaryIsaprePortal: async (req, res) => {
      const errors = validationResult(req);
      if (!errors.isEmpty())
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      const dataToInsert = req.body.map(isapre => mapperTemporaryIsapres(isapre));
      const { result, isError, error } = await isapreService.insertTemporaryIsaprePortal(
        dataToInsert
      );
      if (isError) {
        Logger.error(`Get error: ${error.message}`, req.details);
        return manageError(res, error);
      }
      Logger.info('Operation on nomenclator Isapre has been successfully completed');
      return res.status(HttpStatus.OK).json({ result });
    },
    bulkUpdate: async (req, res) => {
      const { result, executionCompleted, isError, error } = await isapreService.bulkUpdate();
      if (isError) {
        Logger.error(error);
        if (error.code === HttpStatus.NOTFOUND) {
          res.status(HttpStatus.NOTFOUND).json({ error });
          return;
        }
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Isapre has been successfully completed');
        res.status(HttpStatus.OK).json({ result, executionCompleted });
      }
    },

    updateIsapre: async (req, res) => {
      Logger.info('update nomenclator Isapre: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, name, rut, code } = req.body.isapre;
      const { result, isError, error } = await isapreService.updateIsapre({
        id,
        name,
        rut: rutFormatter(rut),
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Isapre has been successfully completed');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    createIsapre: async (req, res) => {
      Logger.info('create nomenclator Isapre: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, name, rut, code } = req.body.isapre;
      const { result, isError, error } = await isapreService.createIsapre({
        id,
        name,
        rut: rutFormatter(rut),
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.ALREADY_EXIST).json({ error, isError });
      } else {
        Logger.info('Operation on nomenclator Isapre has been successfully completed');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    deleteIsapre: async (req, res) => {
      Logger.info('Delete nomenclator Isapre: ', req.params.id);
      const { result, isError, error } = await isapreService.deleteIsapre(req.params.id);
      if (isError) {
        Logger.error('error');
        res.status(HttpStatus.ALREADY_EXIST).json({ error, isError });
      } else {
        Logger.info('Deletion on nomenclator Isapre has been successfully completed');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    getIsapres: async (req, res) => {
      try {
        Logger.info('Get all Isapres');
        const { result } = await isapreService.getIsapres();
        res.status(HttpStatus.OK).json({ result });
      } catch (error) {
        Logger.error(error);
      }
    }
  };
};

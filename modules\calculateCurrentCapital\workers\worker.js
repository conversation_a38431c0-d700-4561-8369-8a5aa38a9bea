/* eslint-disable consistent-return */

const cronDescription = 'calculando  capitales vigentes:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'CALCULATE_CURRENT_CAPITAL';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependenciesArray = ['RESET_FIXED_BASE_PENSION_VALUE', 'CRON_KEY_BUILDER'];
const hasDataTableMessage = 'Tabla Factores o Concurrencias actual(es) no tiene(n) datos';
const FactorModel = require('../../quadrature/models/factor');
const ConcurrencyModel = require('../../quadrature/models/concurrency');

const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = `No se ha ejecutado una o más de las dependencias ${dependenciesArray.join(
  ', '
)} `;

const workerFn = async ({ Logger, done, logService, service, pensionService, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    if (!(await logService.allMarksExists(dependenciesArray))) {
      Logger.info(getMissingDependencyMessage);
      return {
        message: getMissingDependencyMessage,
        status: 'UNAUTHORIZED'
      };
    }
    if (
      !(await logService.hasDataInTable(FactorModel)) ||
      !(await logService.hasDataInTable(ConcurrencyModel))
    ) {
      Logger.info(hasDataTableMessage);
      return { message: hasDataTableMessage };
    }

    Logger.info(`${cronDescription} process started`);
    const { error } = await service.calculateCurrentCapital(pensionService);

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependenciesArray, workerFn };

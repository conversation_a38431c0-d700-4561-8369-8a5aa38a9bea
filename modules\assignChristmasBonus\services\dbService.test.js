const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const resourcePensionsYes = require('../../../resources/pensionsPartyBonusesYes.json');
const resourceRules = require('../../../resources/rulesBonus.json');
const pensionService = require('../../pensions/services/pension.service');

describe('set-amount-pensioners-bonus-christmas', () => {
  beforeAll(beforeAllTests);

  it('get rules for calculation', async () => {
    service.getRulesChristmasBonus = jest.fn(() => Promise.resolve(resourceRules));
    pensionService.filterPensionersAssignBonus = jest.fn(() =>
      Promise.resolve(resourcePensionsYes)
    );

    const { completed, pensioners } = await service.setPensionersBonusChristmas();
    expect(pensioners.length).toBe(3);
    expect(completed).toBe(true);
    expect(pensioners[0].payBonus).toBe('Si');
    expect(pensioners[0].assets.christmasBonus).toBe(20000);
    expect(pensioners[1].payBonus).toBe('Si');
    expect(pensioners[1].assets.christmasBonus).toBe(20000);
    expect(pensioners[2].payBonus).toBe('No');
    expect(pensioners[2].assets.christmasBonus).toBe(0);
  });

  afterAll(afterAllTests);
});

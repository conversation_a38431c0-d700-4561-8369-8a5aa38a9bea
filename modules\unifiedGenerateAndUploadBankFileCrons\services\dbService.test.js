const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const notificationUserEmail = require('../../../resources/notificationUserEmail.json');
const mailService = require('../../sendMail/service/sendMail.service');
const rolesModel = require('../../systems/rolesAndPermissions/models/roles.model');
const userModel = require('../../../models/user');
const rolesSeed = require('../../systems/resources/roles.json');
const usersSeed = require('../../systems/resources/users.json');

describe('service for send notification email', () => {
  beforeAll(beforeAllTests);

  it('get destinationList from user list successfully', async () => {
    await rolesModel.insertMany([...rolesSeed]).catch(e => console.error(e));
    await userModel.insertMany([...usersSeed]).catch(e => console.error(e));

    const { _id: rolId } = rolesSeed.find(({ roleName }) => roleName === 'Jefe de PEC');
    const usersToNotify = usersSeed.filter(({ role }) => role === rolId);

    const destinationList = await service.destinationList();
    expect(destinationList.length).toBe(usersToNotify.length);
    expect(destinationList[0]).toBe(usersToNotify[0].email);
  });

  it('send-email successfully', async () => {
    service.destinationList = jest.fn(() => Promise.resolve([notificationUserEmail[0].email]));
    mailService.sendEmail = jest.fn(() => Promise.resolve(true));

    const { completed } = await service.sendNotificationEmail();
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    try {
      await rolesModel.deleteMany({});
      await userModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

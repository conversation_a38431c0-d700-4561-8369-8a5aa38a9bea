const mongoose = require('mongoose');

const { Schema } = mongoose;

const IsapreSchema = new Schema(
  {
    id: { type: String, required: true },
    name: { type: String, required: true },
    rut: { type: String, required: true },
    code: { type: String, minlength: 2, maxlength: 2, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

IsapreSchema.index({ id: 1 }, { unique: true });
IsapreSchema.index({ code: 1 }, { unique: true });
IsapreSchema.index({ rut: 1 }, { unique: true });
module.exports = mongoose.model('Isapre', IsapreSchema);

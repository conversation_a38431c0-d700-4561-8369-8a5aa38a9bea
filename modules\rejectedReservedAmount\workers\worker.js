const cronDescription = 'Reserved amounts institutional patient:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'CALCULATE_RESERVED_AMOUNT_BY_REJECTED';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'CRON_TRANSFER_PENSIONER_INFO';
const missingDepMsg = `No se ha ejecutado la dependencia ${dependencyMark}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, logService, service, pensionService, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: dependency verification started...`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDepMsg);
      return { message: missingDepMsg, status: 'UNAUTHORIZED' };
    }
    Logger.info(`${cronDescription} process started`);
    const { error } = await service.calculateReservedAmountByRejectedPensions(pensionService);
    if (error) throw new Error(error);
    const { error: err } = await service.calculateReservedAmountBypaycheckRefunded(pensionService);
    if (err) throw new Error(err);
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

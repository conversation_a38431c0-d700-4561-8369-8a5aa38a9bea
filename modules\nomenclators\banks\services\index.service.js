/* eslint-disable no-unused-expressions */
const BanksModel = require('../models/banks');

const service = {
  async updateBank({ id, ...bankData }) {
    const criteria = { enabled: false, $or: [{ code: bankData.code }, { name: bankData.name }] };
    try {
      const oldBank = await BanksModel.findOne(criteria).exec();
      if (oldBank && oldBank.id) {
        await BanksModel.remove({ id: oldBank.id }).exec();
      }
      const data = await BanksModel.findOneAndUpdate(
        { id, enabled: true },
        { $set: { ...bankData } },
        { returnNewDocument: true, upsert: true, new: true }
      ).exec();
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async createBank(bankData) {
    const criteria = { enabled: false, $or: [{ code: bankData.code }, { name: bankData.name }] };
    try {
      const result = await BanksModel.findOne(criteria).exec();
      if (result) {
        const savedBank = await BanksModel.findOneAndUpdate(
          criteria,
          { ...bankData, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedBank };
      }
      const data = await BanksModel.create(bankData);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async deleteBank(id) {
    try {
      const data = await BanksModel.updateOne(
        { id, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getBanks(query = { enabled: true }) {
    return BanksModel.find(query)
      .lean()
      .then(data => ({ result: data.map(({ _id, __v, ...bank }) => ({ ...bank })) }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

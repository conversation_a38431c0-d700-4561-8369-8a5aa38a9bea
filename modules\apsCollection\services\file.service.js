const tmp = require('tmp');
const lineReader = require('line-reader');
const { appendFile } = require('fs');
const moment = require('moment');
const { promisify } = require('util');

const Sftp = require('../../sharedFiles/sftpClient');
const { extractZip, compressFile } = require('../../sharedFiles/helpers');

const appendFileAsync = promisify(appendFile);

const { env } = process;
const remoteFolderPath = env.APS_ACHS_SFTP_FOLDER_PATH;
const remoteUploadFolderPath = env.APS_ACHS_SFTP_UPLOAD_FOLDER_PATH;
const sftpCredentials = {
  host: env.APS_ACHS_FTP_HOST,
  port: env.APS_ACHS_SFTP_PORT,
  username: env.APS_ACHS_SFTP_USER,
  password: env.APS_ACHS_SFTP_PASS
};

const currMonthVariables = () => {
  const YY_MM_DD = moment().format('YYMMDD');
  const YYYY_MM = moment().format('YYYYMM');
  const YY_MM = moment().format('YYMM');
  const HH_MM = moment().format('HHmm');
  return { YY_MM_DD, YYYY_MM, YY_MM, HH_MM };
};

const getDestinationZipFileName = () => {
  const { YY_MM, YY_MM_DD, HH_MM } = currMonthVariables();
  const zipFileName = `dpaps.703601006.ips_${YY_MM}_${YY_MM_DD}_${HH_MM}.zip`;
  return zipFileName;
};

const getPathForCompressFile = () => {
  const YYYY_MM = moment().format('YYYYMM');
  const dirName = tmp.dirSync().name;
  const innerFileName = `aIII_dpaps${YYYY_MM}.703601006`;
  const zipFileName = getDestinationZipFileName();
  return {
    filePath: `${dirName}/${innerFileName}`,
    destinationPath: `${dirName}/${zipFileName}`
  };
};

const getRemoteZipFilePath = async ({ client, folderPath, regex }) => {
  const list = await client.list(folderPath);
  const file = list.filter(({ name }) => name.match(regex)).pop();
  if (!file) throw new Error('patpr.ips.703601006 File Not Found');
  return `${folderPath}/${file.name}`;
};

const downloadRemoteZipFile = async ({ client, remoteFilePath, temp = tmp }) => {
  const destinationPath = `${temp.dirSync().name}/patpr-ips.zip`;
  await client.downloadTo(remoteFilePath, destinationPath);
  return destinationPath;
};

const downloadAndExtractZipFile = async ({
  SFTP = Sftp,
  temp = tmp,
  zipExtractor = extractZip,
  folderPath = remoteFolderPath,
  getRemoteZipFilePathFn = getRemoteZipFilePath
}) => {
  const { YY_MM, YYYY_MM } = currMonthVariables();
  const patprIpsFileStr = `^patpr.ips.703601006_${YY_MM}_${YY_MM}[0-9]{2}_[0-9]{4}.zip$`;
  const regex = new RegExp(patprIpsFileStr, 'i');
  const client = new SFTP.Client();
  await client.connect(sftpCredentials);
  const remoteFilePath = await getRemoteZipFilePathFn({ client, folderPath, regex });
  const downloadedFilePath = await downloadRemoteZipFile({ client, remoteFilePath });
  const destination = `${temp.dirSync().name}/extractedFiles`;
  const zipContentPath = zipExtractor(downloadedFilePath, destination, 'patpr');
  await client.close();
  return `${zipContentPath}aIII_taps${YYYY_MM}.703601006`;
};

const readFile = path =>
  new Promise((resolve, reject) => {
    const lines = [];
    lineReader.eachLine(
      path,
      (line, last) => {
        lines.push(line);
        if (last) resolve(lines);
      },
      err => {
        if (err) reject(err);
        else resolve(lines);
      }
    );
  });

const generateEmptyDpapsZipFile = async () => {
  const dirName = tmp.dirSync().name;
  const { YY_MM_DD, YYYY_MM, YY_MM, HH_MM } = currMonthVariables();
  const textFilePath = `${dirName}/aIII_dapss${YYYY_MM}.703601006`;
  const emptyZipFileName = `dapss.703601006.ips_${YY_MM}_${YY_MM_DD}_${HH_MM}.zip`;
  await appendFileAsync(textFilePath, '');
  const emptyZipFilePath = compressFile(textFilePath, `${dirName}/${emptyZipFileName}`);
  return { emptyZipFilePath, emptyZipFileName };
};

const generatePprZipFile = async additionalFilesData => {
  const dirName = tmp.dirSync().name;
  const { YY_MM_DD, YYYY_MM, YY_MM, HH_MM } = currMonthVariables();
  const { dpeapsFileData, rctapsFileData } = additionalFilesData;
  const dpmapsFilePath = `${dirName}/aIII_dpmaps${YYYY_MM}.703601006`;
  const dpreapsFilePath = `${dirName}/aIII_dpreaps${YYYY_MM}.703601006`;
  const dprmapsFilePath = `${dirName}/aIII_dprmaps${YYYY_MM}.703601006`;
  const dpeapsFilePath = `${dirName}/aIII_dpeaps${YYYY_MM}.703601006`;
  const rctapsFilePath = `${dirName}/aIII_rctaps${YYYY_MM}.703601006`;
  const pprZipFileName = `ppr.703601006.ips_${YY_MM}_${YY_MM_DD}_${HH_MM}.zip`;
  await appendFileAsync(dpmapsFilePath, '');
  await appendFileAsync(dpreapsFilePath, '');
  await appendFileAsync(dprmapsFilePath, '');
  await appendFileAsync(dpeapsFilePath, dpeapsFileData.join('\n'));
  await appendFileAsync(rctapsFilePath, rctapsFileData);
  const pprZipFilePath = compressFile(
    [dpmapsFilePath, dpreapsFilePath, dprmapsFilePath, dpeapsFilePath, rctapsFilePath],
    `${dirName}/${pprZipFileName}`
  );
  return { pprZipFilePath, pprZipFileName };
};

const uploadPprFileToSftpServer = async ({ fileInfo, SFTP = Sftp }) => {
  const client = new SFTP.Client();
  const { pprZipFilePath, pprZipFileName } = fileInfo;
  await client.connect(sftpCredentials);
  await client.uploadFrom(pprZipFilePath, `${remoteUploadFolderPath}/${pprZipFileName}`);
  await client.close();
};

const uploadDpasFileToSftpServer = async ({ fileToUpload, SFTP = Sftp, Logger }) => {
  const client = new SFTP.Client();
  try {
    const destinationZipFileName = getDestinationZipFileName();
    const { emptyZipFilePath, emptyZipFileName } = await generateEmptyDpapsZipFile();
    await client.connect(sftpCredentials);
    await client.uploadFrom(emptyZipFilePath, `${remoteUploadFolderPath}/${emptyZipFileName}`);
    await client.uploadFrom(fileToUpload, `${remoteUploadFolderPath}/${destinationZipFileName}`);
    await client.close();
  } catch (error) {
    Logger.error(`file.service -> uploadDpasFileToSftpServer error: ${error}`);
    await client.close();
  }
};

const formatRut = str => {
  const rut = str.replace(/^0/, '');
  const sliceLength = rut.length - 1;
  return `${rut.slice(0, sliceLength)}-${rut.slice(sliceLength)}`;
};

const mapRutToLine = lines => {
  const RUT_POSITION_START = 31;
  const RUT_LENGTH = 9;
  const rutLineObj = {};
  lines.forEach(line => {
    const rut = formatRut(line.substr(RUT_POSITION_START, RUT_LENGTH));
    rutLineObj[rut.toUpperCase()] = line;
  });
  return rutLineObj;
};

module.exports = {
  downloadAndExtractZipFile,
  readFile,
  mapRutToLine,
  getPathForCompressFile,
  appendFileAsync,
  compressFile,
  uploadDpasFileToSftpServer,
  uploadPprFileToSftpServer,
  generatePprZipFile
};

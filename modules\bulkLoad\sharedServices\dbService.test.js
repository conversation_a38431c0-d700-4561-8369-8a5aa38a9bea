/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const pensionsService = require('../../pensions/services/pension.service');
const serviceFn = require('./dbService');
const pensionsData = require('../../../resources/pensions.json');

const fieldsObj = {
  discountFieldName: 'onePercent18',
  socialCreditFieldName: 'socialCredits18'
};

const parsedLines = {
  discountFileParsedLines: [
    ['10011289-2', '1'],
    ['10037456-0', '1']
  ],
  socialCreditFileParsedLines: [
    ['13778818-7', 19295],
    ['10240876-4', 16475]
  ],
  anotherDiscountFileParsedLines: [
    ['13778818-7', 19296],
    ['10240876-4', 16477]
  ]
};
describe('Bulk Load caja 18 tests', () => {
  beforeAll(beforeAllTests);
  const service = serviceFn(fieldsObj);
  it('should update pension to add discounts', async () => {
    const pension1 = {
      ...pensionsData[0],
      beneficiary: { rut: '10011289-2' },
      enabled: true,
      validityType: 'Vigente viudez'
    };
    const pension2 = { ...pension1, beneficiary: { rut: '13778818-7' } };
    const pension3 = {
      ...pension1,
      validityType: 'NO Vigente',
      beneficiary: { rut: '13778818-7' }
    };

    await PensionModel.insertMany([pension1, pension2, pension3]);
    try {
      const { isError } = await service.updatePensions(pensionsService, parsedLines);
      const dbDocs = await PensionModel.find({});
      // now Pensions table should have 3 docs
      expect(dbDocs.length).toBe(3);
      // All docs are enabled
      const enabledDocs = await PensionModel.find({ enabled: true }).lean();
      expect(enabledDocs.length).toBe(3);

      const filteredDiscountPension = enabledDocs.filter(p => p.beneficiary.rut === '10011289-2');
      const filteredSocialPension = enabledDocs.filter(p => p.beneficiary.rut === '13778818-7');
      expect(filteredDiscountPension[0].discounts.onePercent18).toBe('Si');
      expect(filteredSocialPension[0].discounts.socialCredits18).toBe(19295);
      // No error
      expect(isError).toBe(false);
    } catch (error) {
      console.log(error);
    }
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.log(error);
    }
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { getParsedLinesFromFiles, getParsedLinesFromIpsFiles } = require('./filesParser');

const testFile1Path = `${__dirname}/../../../resources/bulkLoadCaja18/test1.txt`;
const testFile2Path = `${__dirname}/../../../resources/bulkLoadCaja18/test2.txt`;
const testFile3Path = `${__dirname}/../../../resources/bulkLoadIps/aIIItaps.txt`;
const testFile4Path = `${__dirname}/../../../resources/bulkLoadIps/papsoe.txt`;
const testFile5Path = `${__dirname}/../../../resources/bulkLoadIps/brsalud.txt`;

describe('File parser', () => {
  beforeAll(beforeAllTests);

  it('should read and parse lines from caja18 files', async () => {
    const result = await getParsedLinesFromFiles(testFile1Path, testFile2Path);
    const { discountFileParsedLines, socialCreditFileParsedLines } = result;
    expect(typeof discountFileParsedLines).toBe('object');
    expect(discountFileParsedLines[0]).toContain('10011289-2');
    expect(typeof socialCreditFileParsedLines).toBe('object');
    expect(socialCreditFileParsedLines[0]).toContain('13778818-7');
  });

  it('should read and parse lines from IPS files', async () => {
    const result = await getParsedLinesFromIpsFiles(testFile3Path, testFile4Path, testFile5Path);
    const { parsedAIIItapsLines, parsedPapsoeLines } = result;
    expect(typeof parsedAIIItapsLines).toBe('object');
    expect(parsedAIIItapsLines[0]).toContain('6564757-5');
    expect(typeof parsedPapsoeLines).toBe('object');
    expect(parsedPapsoeLines).toContain('6676287-4');
  });

  afterAll(afterAllTests);
});

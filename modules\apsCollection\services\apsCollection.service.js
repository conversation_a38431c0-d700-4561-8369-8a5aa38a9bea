/* eslint-disable no-param-reassign */
const moment = require('moment');

const PensionModel = require('../../../models/pension');
const { getPreviousMonthAndYear } = require('../../sharedFiles/helpers');
const composeDpapsLine = require('./dpapsLineCompositor');
const composeDpeapsLine = require('./dpeapsLineCompositor');
const composeRctapsLine = require('./rctapsLineCompositor');

const nonValidPensionsAggregator = rutList => [
  {
    $match: {
      inactivationReason: { $exists: true },
      validityType: new RegExp('No vigente', 'i'),
      'beneficiary.rut': { $in: rutList.map(r => new RegExp(r, 'i')) }
    }
  },
  {
    $group: {
      _id: { beneficiary: '$beneficiary.rut', causant: '$causant.rut' },
      resultGroup: { $last: '$$ROOT' }
    }
  },
  { $replaceRoot: { newRoot: '$resultGroup' } }
];

const getPensions = async (month, year, query = {}) => {
  return PensionModel.find({
    validityType: { $not: /No Vigente/i },
    $expr: {
      $and: [{ $eq: [{ $month: `$createdAt` }, month] }, { $eq: [{ $year: `$createdAt` }, year] }]
    },
    ...query
  }).lean();
};

const mapRutToPension = pensions => {
  return pensions.reduce((obj, pension) => {
    const { beneficiary } = pension;
    const { rut } = beneficiary;
    obj[rut.toUpperCase()] = pension;
    return obj;
  }, {});
};

const service = {
  async getMatchedPensionLines(linesObj) {
    const matchedLines = [];
    const [month, year] = getPreviousMonthAndYear();
    const pensions = await getPensions(month, year);
    const rutPensionMapperObj = mapRutToPension(pensions);
    const rutListFromFile = Object.keys(linesObj);

    rutListFromFile.forEach(rutFromFile => {
      if (rutPensionMapperObj[rutFromFile]) {
        const oldLine = linesObj[rutFromFile];
        const pension = rutPensionMapperObj[rutFromFile];
        const newFileLine = composeDpapsLine({ pension, oldLine });
        matchedLines.push(newFileLine);
      }
    });
    return matchedLines;
  },

  async getUnmatchedPensionLines(linesObj) {
    const unmatchedLines = [];
    const dpeapsFileData = [];
    const year = moment().year();
    const month = moment().month() + 1;
    const pensions = await getPensions(month, year, { 'assets.aps': { $gt: 0 } });
    const rutPensionMapperObj = mapRutToPension(pensions);
    const rutListFromFile = Object.keys(linesObj);

    const nonValidPensions = await PensionModel.aggregate(
      nonValidPensionsAggregator(rutListFromFile)
    ).exec();

    const nonValidPensionsMapper = mapRutToPension(nonValidPensions);

    rutListFromFile.forEach(rutFromFile => {
      if (!rutPensionMapperObj[rutFromFile]) {
        const aIIItapsLine = linesObj[rutFromFile];
        const pension = nonValidPensionsMapper[rutFromFile] || {};
        const newLine = composeDpeapsLine(aIIItapsLine, pension);
        dpeapsFileData.push(newLine);
        unmatchedLines.push(aIIItapsLine);
      }
    });

    const rctapsFileData = composeRctapsLine(Object.values(linesObj), unmatchedLines);

    return { dpeapsFileData, rctapsFileData };
  }
};

module.exports = service;

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let done;
  let logService;

  beforeEach(() => {
    service = {
      retroactiveBankFile: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
    pensionService = {};
  });

  it('success worker retroactive', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.retroactiveBankFile).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked retroactive in current month ', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.retroactiveBankFile).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker retroactive', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.retroactiveBankFile).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

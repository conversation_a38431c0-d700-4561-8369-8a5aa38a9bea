/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('Update widowhoodPayment test', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let done;
  let logService;
  let service;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = { setWidowhoodBonus: jest.fn().mockResolvedValue({ error: null }) };
  });

  it('should call widowhood bonus worker', async () => {
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.setWidowhoodBonus).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('should not find the first filemark and not continue', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setWidowhoodBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should find the second filemark and not continue', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.setWidowhoodBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should find the second filemark and not continue', async () => {
    service.setWidowhoodBonus = jest.fn(() => Promise.resolve({ error: true }));
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.setWidowhoodBonus).toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const mongoose = require('mongoose');
const pensionService = require('../../pensions/services/pension.service');

const CHRISTMAS_BONUS_TYPE_SEARCH = /^Aguinaldo de navidad$/i;
const PAY_BONUS_NOT_TEST = /No/i;
const CHRISTMAS_BONUS_TYPE = 'Aguinaldo de navidad';
const PAY_BONUS_YES = 'Si';
const PAY_BONUS_NOT = 'No';

const formatBonusType = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .replace(/[èé]/gi, 'e')
    .replace(/[áà]/gi, 'a')
    .toLowerCase()
    .trim();

const reduceBonusRulesToObj = rules => {
  return rules.reduce((rulesObj, rule) => {
    const { label, value } = rule;
    const key = formatBonusType(label);
    return { ...rulesObj, [key]: value };
  }, {});
};

const assignBonusChristmasPensionerNew = (pensioner, amountBonus) => {
  const {
    temporaryimportedbonuspensioners,
    assets,
    payBonus,
    basePension = 0,
    article40 = 0,
    law19403 = 0,
    law19539 = 0,
    law19953 = 0,
    amountOtherPension = 0,
    dl1026 = 0,
    ...pensionerAux
  } = pensioner;

  const basePensionPlusLaws = basePension + article40 + law19403 + law19539 + law19953;
  if (dl1026 > basePensionPlusLaws || amountOtherPension > basePensionPlusLaws) {
    return {
      ...pensionerAux,
      payBonus: PAY_BONUS_NOT,
      assets: { ...assets, christmasBonus: 0 }
    };
  }

  return {
    ...pensionerAux,
    payBonus: PAY_BONUS_YES,
    assets: { ...assets, christmasBonus: amountBonus }
  };
};

const assignBonusChristmasPensionerOld = (pensioner, amountBonus) => {
  const { temporaryimportedbonuspensioners, assets, ...pensionerAux } = pensioner;

  const pensionIPS = temporaryimportedbonuspensioners.find(
    row => row.institutionCode === '34' && PAY_BONUS_NOT_TEST.test(row.payBonus)
  );
  if (pensionIPS) {
    return {
      ...pensionerAux,
      payBonus: PAY_BONUS_NOT,
      assets: { ...assets, christmasBonus: 0 }
    };
  }

  return {
    ...pensionerAux,
    payBonus: PAY_BONUS_YES,
    assets: { ...assets, christmasBonus: amountBonus }
  };
};

const service = {
  async getRulesChristmasBonus() {
    try {
      return mongoose.connection.db
        .collection('basePensionRules')
        .find({ label: CHRISTMAS_BONUS_TYPE_SEARCH })
        .toArray();
    } catch (e) {
      return [];
    }
  },

  async payBonusChristmas() {
    const pensioners = await pensionService.filterPensionersAssignBonus();
    const rules = await this.getRulesChristmasBonus();
    const rulesBonusObj = reduceBonusRulesToObj(rules);
    const amountBonus = +rulesBonusObj[formatBonusType(CHRISTMAS_BONUS_TYPE)];

    return pensioners.map(pensioner => {
      const { payBonus } = pensioner;

      if (PAY_BONUS_NOT_TEST.test(payBonus)) {
        return assignBonusChristmasPensionerNew(pensioner, amountBonus);
      }

      return assignBonusChristmasPensionerOld(pensioner, amountBonus);
    });
  },

  async setPensionersBonusChristmas() {
    const pensioners = await this.payBonusChristmas();
    const { completed, error } = await pensionService.updatePensionsByIdBonus(pensioners);

    return { completed, error, pensioners };
  }
};

module.exports = { ...service };

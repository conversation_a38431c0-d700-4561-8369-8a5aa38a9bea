/* eslint-disable prefer-destructuring */
const { validationResult } = require('express-validator');

const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const CriteriaBuilder = require('../lib/builders/criteria-builder');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const userFactoryController = require('../modules/systems/users/controllers/user.controller');
const userService = require('../modules/systems/users/services/users.service');
const authService = require('../modules/auth/services/auth.service');
const validateAccess = require('../lib/auth/validate');

const SYSTEM_USER = '/sistemas/usuarios';

module.exports = router => {
  const userController = userFactoryController({
    HttpStatus,
    <PERSON>rror<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    CriteriaBuilder,
    userService,
    authService,
    validationResult
  });

  router.get('/', validateAccess(), userController.getUsers);
  router.post('/', validateAccess(), startContextMiddleware, getUser(), userController.createUser);
  router.put(
    '/:id',
    validateAccess(),
    startContextMiddleware,
    getUser(SYSTEM_USER),
    userController.updateUser
  );
  router.delete(
    '/:id',
    validateAccess(),
    startContextMiddleware,
    getUser(SYSTEM_USER),
    userController.deleteUser
  );
};

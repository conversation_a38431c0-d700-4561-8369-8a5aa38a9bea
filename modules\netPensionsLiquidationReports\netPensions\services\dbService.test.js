/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const service = require('./dbService');
const pensions = require('../../../../resources/netPensions/pensions.json');
const liquidations = require('../../../../resources/netPensions/liquidations.json');
const afps = require('../../../../resources/netPensions/afps.json');
const ProcessedJobModel = require('../../../sharedFiles/models/processedJob');
const compentation = require('../../../../resources/netPensions/compensationBoxs.json');

describe('net pension Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let liquidationService;
  let afpService;
  let ufService;
  let cajaService;
  beforeEach(() => {
    pensionService = {
      getAllWithFilter: jest.fn(() => Promise.resolve({ result: pensions })),
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    liquidationService = {
      getAllWithFilter: jest.fn(() => Promise.resolve({ result: liquidations[0] })),
      createUpdateLiquidation: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    afpService = {
      getAfpsWithFilters: jest.fn(() => Promise.resolve({ result: afps }))
    };
    ufService = {
      getCurrentUfValue: jest.fn(() =>
        Promise.resolve({
          period: '2020-05',
          value: 28690.73
        })
      )
    };
    cajaService = {
      getCajas: jest.fn(() => Promise.resolve({ result: compentation }))
    };
  });

  it('calculate net pension', async () => {
    const ufValue = 28690.73;

    const { pension, liquidation } = service.calculateNetPension(
      pensions[0],
      liquidations[0],
      afps,
      ufValue,
      compentation
    );
    expect(pension.assets.rebsal).toBe(17508.66);
    expect(pension.assets.adjustedHealthExemption).toBe(17508.66);
    expect(pension.assets.forFamilyAssignment).toBe(12313);
    expect(pension.discounts.onePercentAdjusted).toBe(1295.84);
    expect(pension.discounts.health).toBe(79760.23);
    expect(pension.discounts.afp).toBe(25437.58);
    expect(pension.retroactiveAmounts.forFamilyAssignment).toBe(12313);
    expect(pension.retroactiveAmounts.forPayCheck).toBe(12313);
    expect(liquidation.taxablePension).toBe(250123.71);
    expect(liquidation.netPension).toBe(226679.93);
  });

  it('calculate net pension monto maximo', async () => {
    const ufValue = 28690.73;
    pensions[0].discounts.onePercent18 = 'Si';
    pensions[0].discounts.onePercentLosAndes = 'No';
    const { pension, liquidation } = service.calculateNetPension(
      pensions[0],
      liquidations[0],
      afps,
      ufValue,
      compentation
    );
    expect(pension.assets.rebsal).toBe(17508.66);
    expect(pension.assets.adjustedHealthExemption).toBe(17508.66);
    expect(pension.assets.forFamilyAssignment).toBe(12313);
    expect(pension.discounts.onePercentAdjusted).toBe(455);
    expect(pension.discounts.health).toBe(79760.23);
    expect(pension.discounts.afp).toBe(25437.58);
    expect(pension.retroactiveAmounts.forFamilyAssignment).toBe(12313);
    expect(pension.retroactiveAmounts.forPayCheck).toBe(12313);
    expect(liquidation.taxablePension).toBe(250123.71);
    expect(liquidation.netPension).toBe(227520.77);
  });

  it('calculate net pension without fields', async () => {
    const ufValue = 28690.73;
    const { pension, liquidation } = service.calculateNetPension(
      pensions[1],
      liquidations[1],
      afps,
      ufValue,
      compentation
    );
    expect(pension.assets.rebsal).toBe(0);
    expect(pension.assets.adjustedHealthExemption).toBe(0);
    expect(pension.discounts.onePercentAdjusted).toBe(0);
    expect(pension.discounts.health).toBe(0);
    expect(pension.discounts.afp).toBe(0);
    expect(liquidation.netPension).toBe(0);
    expect(liquidation.taxablePension).toBe(0);
  });

  it('processed net pensions success', async () => {
    const result = await service.netPension(
      pensionService,
      liquidationService,
      afpService,
      ufService,
      cajaService
    );
    expect(result.completed).toBe(true);
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

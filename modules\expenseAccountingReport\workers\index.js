/* eslint-disable consistent-return */
const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');

module.exports = {
  name: 'expenseAccountingReport',
  worker: deps => workerModule.workerFn({ logService, service, ...deps }),
  repeatInterval: process.env.EXPENSE_ACCOUNTING_REPORT_FREQUENCY,
  description: 'Generar Reporte de contabilidad H2',
  endPoint: 'expenseaccountingreport',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

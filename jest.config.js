/* eslint-disable indent */
module.exports = {
  preset: '@shelf/jest-mongodb',
  testEnvironment: 'node',
  verbose: false,
  coveragePathIgnorePatterns: ['/node_modules/'],
  collectCoverage: true,
  collectCoverageFrom: [
    './modules/**/*.js',
    '!./modules/workers.js',
    '!./modules/historicalData/**',
    '!./modules/**/index.js',
    '!**/controllers/**',
    '!**/models/**',

    '!**/data-seed/**',
    '!**/node_modules/**',
    '!**/lib/**',
    '!./workerManager/*.js'
  ],
  modulePathIgnorePatterns: ['./dist'],
  coverageThreshold: {
    global: {
      statements: 80,
      lines: 80,
      functions: 80,
      branch: 60
    }
  },
  testResultsProcessor: 'jest-sonar-reporter',
  coverageReporters: ['json', 'lcov', 'text', 'text-summary', 'clover', 'cobertura'],
  reporters: [
    'default',
    [
      'jest-junit',
      {
        suiteName: 'Jest Tests',
        suiteNameTemplate: '{filepath}',
        outputDirectory: '.'
      }
    ]
  ]
};

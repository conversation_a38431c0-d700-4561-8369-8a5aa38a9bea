/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let liquidationReportService;
  let netPensionsService;
  let logService;
  let Logger;
  let done;
  beforeEach(() => {
    liquidationReportService = {
      createLiquidationsReports: jest.fn(() =>
        Promise.resolve({ completedLiquidationReport: true, errorLiquidationReport: false })
      )
    };
    netPensionsService = {
      netPension: jest.fn(() => Promise.resolve({ completed: true, error: false }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      liquidationReportService,
      netPensionsService,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(netPensionsService.netPension).toBeCalled();
    expect(liquidationReportService.createLiquidationsReports).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    logService.allMarksExists = jest.fn(() => Promise.resolve(false));

    try {
      await workerModule.workerFn({
        liquidationReportService,
        netPensionsService,
        Logger,
        logService,
        done
      });
    } catch (error) {
      expect(error.message).toBe(
        "Error: execution interrupted: Dependency 'NET_PENSIONS_LIQUIDATION_REPORTS' not yet executed, Dependency 'TAXABLE_PENSION,UF_VALUE,TOTAL_ASSETS_AND_DISCOUNTS' not yet executed"
      );
    }

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.allMarksExists).not.toBeCalled();
    expect(netPensionsService.netPension).not.toBeCalled();
    expect(liquidationReportService.createLiquidationsReports).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('catch error worker', async () => {
    netPensionsService.netPension = jest.fn(() => Promise.reject(new Error('error')));
    try {
      await workerModule.workerFn({
        liquidationReportService,
        netPensionsService,
        Logger,
        logService,
        done
      });
    } catch (error) {
      expect(error.message).toBe('Error: Errors while processing');
    }

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(netPensionsService.netPension).toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(liquidationReportService.createLiquidationsReports).not.toBeCalled();
  });

  it('fail worker by Net pensions liquidations report', async () => {
    logService.existsLogAndRerty = jest.fn(() => Promise.reject());

    netPensionsService.netPension = jest.fn(() =>
      Promise.resolve({ completed: false, error: true })
    );

    try {
      await workerModule.workerFn({
        liquidationReportService,
        netPensionsService,
        Logger,
        logService,
        done
      });
    } catch (error) {
      expect(error.message).toBe('Error at NET_PENSIONS_LIQUIDATION_REPORTS');
    }

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(netPensionsService.netPension).toBeCalled();
    expect(liquidationReportService.createLiquidationsReports).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

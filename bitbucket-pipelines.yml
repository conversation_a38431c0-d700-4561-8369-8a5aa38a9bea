# This is a sample build configuration for JavaScript.
# Check our guides at https://confluence.atlassian.com/x/14UWN for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: node:10.15.3

clone:
  depth: full # SonarCloud scanner needs the full history to assign issues properly

definitions:
  caches:
    sonar: ~/.sonar/cache # Caching SonarCloud artifacts will speed up your build
  services:
    docker:
      memory: 2048
  steps:
    - step: &test
        name: Test ACHS-PRESTACIONES-BACKEND App
        script:
          - npm ci
          - npm run test:cov
          - pipe: sonarsource/sonarcloud-scan:1.2.0
            variables:
              SONAR_TOKEN: ${SONAR_TOKEN}
          - pipe: sonarsource/sonarcloud-quality-gate:0.1.3
    - step: &build
        name: Build ACHS-PRESTACIONES-BACKEND App
        script:
          - npm ci
          - npm run build
        artifacts:
          - dist/**
    - step: &deploy
        name: Deploy to Environment
        script:
          - scp -r dist $USERNAME@$SERVER_IP:$FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT
          - ssh $USERNAME@$SERVER_IP "cd $FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT/dist;npm install --production;pm2 restart $PM2_CONFIGURATION-$BITBUCKET_DEPLOYMENT_ENVIRONMENT.json"
          - ssh $USERNAME@$SERVER_IP "cd $FOLDER-$BITBUCKET_DEPLOYMENT_ENVIRONMENT/dist/scripts"
          - echo "Deploy step finished"
pipelines:
  default:
    - step: *test
  custom:
    deploy-to-qa-1:
      - step: *test
      - step: *build
      - step:
          <<: *deploy
          deployment: qa1
    deploy-to-qa-2:
      - step: *test
      - step: *build
      - step:
          <<: *deploy
          deployment: qa2
    deploy-to-qa:
      - step: *test
      - step: *build
      - step:
          <<: *deploy
          deployment: qa
    pull-requests:
    '**':
      - step:
          <<: *test
  branches:
    master:
      - step:
          <<: *test
          deployment: production

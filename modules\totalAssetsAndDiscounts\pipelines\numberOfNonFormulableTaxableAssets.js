const countUniqueAssetsReason = require('./countUniqueKeyValue');

const count = pension => {
  const taxableAssetRegex = /[ií<PERSON><PERSON>]mpon[íìïi]ble/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const taxableAssets = assetsNonFormulable.filter(({ assetType }) =>
    taxableAssetRegex.test(assetType)
  );
  const numberOfTaxableNonFormulableAssets = countUniqueAssetsReason(taxableAssets);
  return { ...pension, numberOfTaxableNonFormulableAssets };
};

module.exports = count;

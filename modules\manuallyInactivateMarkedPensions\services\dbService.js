const moment = require('moment');
const PensionModel = require('../../../models/pension');

const getFirstDayOfPreviousMonth = () =>
  moment()
    .subtract(1, 'months')
    .startOf('month')
    .toDate();

const getEndDayOfPreviousMonth = () =>
  moment()
    .subtract(1, 'months')
    .endOf('month')
    .toDate();

const filterManuallyInactivatedPensions = async (Model = PensionModel) => {
  return Model.find({
    inactivateManually: true,
    manuallyReactivated: false,
    enabled: true,
    endDateOfValidity: { $gte: getFirstDayOfPreviousMonth(), $lte: getEndDayOfPreviousMonth() }
  }).lean();
};

const filterManuallyReactivatedPensions = async (Model = PensionModel) =>
  Model.find({
    manuallyReactivated: true,
    inactivateManually: false,
    enabled: true
  }).lean();

const filterManuallyInactivatedAndReactivatedPensions = async (Model = PensionModel) =>
  Model.find({
    inactivateManually: true,
    manuallyReactivated: true,
    enabled: true,
    endDateOfValidity: { $gte: getFirstDayOfPreviousMonth(), $lte: getEndDayOfPreviousMonth() }
  }).lean();

const inactivatePensioners = pensionsList =>
  pensionsList.map(pension => {
    return {
      ...pension,
      validityType: 'No vigente',
      inactivationDate: new Date(),
      inactivateManually: false
    };
  });

const notReactivatePensioners = pensionsList =>
  pensionsList.map(pension => {
    return {
      ...pension,
      manuallyReactivated: false
    };
  });

const inactivatePensionersAndNotReactivatePensioners = pensionsList =>
  pensionsList.map(pension => {
    return {
      ...pension,
      validityType: 'No vigente',
      inactivationDate: new Date(),
      inactivateManually: false,
      manuallyReactivated: false
    };
  });

const service = {
  inactivatePensions: async (Model = PensionModel) => {
    try {
      const pensionersToInactivate = await filterManuallyInactivatedPensions(Model);
      const updatedIncativatedPensioners = inactivatePensioners(pensionersToInactivate);
      return { updatedIncativatedPensioners };
    } catch (error) {
      return { error };
    }
  },
  notReactivatePensions: async (Model = PensionModel) => {
    try {
      const pensionersToNotReactivate = await filterManuallyReactivatedPensions(Model);
      const updatedNotReactivatedPensioners = notReactivatePensioners(pensionersToNotReactivate);
      return { updatedNotReactivatedPensioners };
    } catch (error) {
      return { error };
    }
  },

  inactivateAndNotReactivatePensions: async (Model = PensionModel) => {
    try {
      const mixedInactivateReactivated = await filterManuallyInactivatedAndReactivatedPensions(
        Model
      );
      const updatedMixedInactivateReactivated = inactivatePensionersAndNotReactivatePensioners(
        mixedInactivateReactivated
      );
      return { updatedMixedInactivateReactivated };
    } catch (error) {
      return { error };
    }
  },
  updateMarkedPensions: async ({
    pensionService,
    updatedIncativatedPensioners,
    updatedNotReactivatedPensioners,
    updatedMixedInactivateReactivated
  }) => {
    try {
      const pensionsToUpdated = [
        ...updatedIncativatedPensioners,
        ...updatedNotReactivatedPensioners,
        ...updatedMixedInactivateReactivated
      ];
      const { completed, error } = await pensionService.updatePensions(pensionsToUpdated);

      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};
module.exports = { ...service };

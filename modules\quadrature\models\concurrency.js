const mongoose = require('mongoose');

const { Schema } = mongoose;

const Concurrency = new Schema(
  {
    beneficiaryRut: {
      type: String,
      required: true
    },
    concurrencyPercentage: {
      type: Number,
      required: true,
      min: 0
    },
    concurrencyReceivable: {
      type: Number,
      required: true,
      min: 0
    },
    mutualPercentage: {
      type: Number,
      required: true,
      min: 0
    },
    istPercentage: {
      type: Number,
      required: true,
      min: 0
    },
    islPercentage: {
      type: Number,
      required: true,
      min: 0
    }
  },
  { timestamps: true }
);
Concurrency.index({ beneficiaryRut: 1 });

module.exports = mongoose.model('Concurrency', Concurrency);

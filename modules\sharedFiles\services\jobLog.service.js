const moment = require('moment');
const LogModel = require('../models/processedJob');
const emailService = require('../../sendMail/service/sendMail.service');
const { getMonth } = require('./helpers.service');

const ATTEMPTS = process.env.CRONS_EXECUTION_ATTEMPTS || 10;
const ERROR_NOTIFICATION_LIST = process.env.CRONS_ERROR_NOTIFICATION_LIST;
const SUCCESS_NOTIFICATION_LIST = process.env.CRONS_SUCCESS_NOTIFICATION_LIST;
const TEMPLATE_ID = process.env.TEMPLATE_ID_CRON_NOTIFICATION;
const successTitleInfo = 'El cron se ejecutó correctamente';
const failTitleInfo = 'El cron NO se ejecutó correctamente y superó el límite de reintentos';
const successSubject = 'Estado exitoso de la ejecución de crons PEC 2.0 ACHS';
const failSubject = 'FALLA en la ejecución de crons PEC 2.0 ACHS';

const successfulStatus = 'OK';

const buildReportMessage = async (data = {}) => {
  const { status, month, year, name } = data;
  const isSuccess = status === successfulStatus;
  const info = {
    name,
    month: getMonth(month),
    year,
    status,
    title: isSuccess ? successTitleInfo : failTitleInfo,
    subject: isSuccess ? successSubject : failSubject
  };

  await emailService.sendEmail({
    to: isSuccess ? SUCCESS_NOTIFICATION_LIST : ERROR_NOTIFICATION_LIST,
    templateId: TEMPLATE_ID,
    fromname: 'ACHS-PEC',
    dynamic_template_data: {
      ...info
    }
  });
};

module.exports = {
  existsLogAndRetry: async (name, processedJobModel = LogModel) => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth();
    const cronMark = await processedJobModel
      .findOne({ year, month, name })
      .lean()
      .exec();
    const { status, attempts } = cronMark || {};

    if (!cronMark) {
      return { existsLog: false, attempts };
    }

    if (status === successfulStatus) {
      return {
        message: 'Este proceso fue ejecutado para el mes actual',
        existsLog: true,
        alreadyExecuted: true,
        attempts
      };
    }

    if (attempts >= ATTEMPTS) {
      return { message: 'Intentos sobrepasados', existsLog: true };
    }

    return { existsLog: false };
  },
  existsLog: async name => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth();
    const cronMark = await LogModel.findOne({ year, month, name })
      .lean()
      .exec();
    const { status } = cronMark || {};

    return status === successfulStatus;
  },
  retryLog: async (name, processedJobModel = LogModel) => {
    try {
      const year = new Date().getFullYear();
      const month = new Date().getMonth();

      const { attempts } = await processedJobModel
        .findOneAndUpdate(
          { year, month, name },
          { year, month, name, $inc: { attempts: 1 } },
          { upsert: true, new: true }
        )
        .exec();

      if (attempts >= ATTEMPTS) {
        const { _doc: job } = await processedJobModel
          .findOneAndUpdate(
            { year, month, name },
            { status: 'ERROR' },
            {
              new: true
            }
          )
          .exec();
        await buildReportMessage(job);
      }

      return { completed: true };
    } catch (error) {
      return { error };
    }
  },
  saveLog: async (name, status = successfulStatus) => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth();
    const job = await LogModel.findOneAndUpdate(
      { year, month, name },
      { year, month, name, status },
      { upsert: true, new: true }
    ).exec();

    return buildReportMessage(job);
  },
  hasDataInTable: async Model => {
    const hasData = await Model.count();
    return hasData > 0;
  },
  allMarksExists: async marksArr => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth();
    const marks = await LogModel.find({
      name: { $in: marksArr },
      month,
      year,
      status: 'OK'
    })
      .lean()
      .exec();
    return marks.length === marksArr.length;
  },
  removeMark: async name => {
    const year = new Date().getFullYear();
    const month = new Date().getMonth();
    await LogModel.deleteOne({ name, month, year }).exec();
  },
  addRetryTimeToJob: async (job, timeToAdd) => {
    try {
      // eslint-disable-next-line no-param-reassign
      job.attrs.nextRunAt = moment()
        .add(timeToAdd, 'minutes')
        .toDate();
      await job.save();
      return { completed: true };
    } catch (error) {
      return { error };
    }
  }
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests, Logger } = require('../../testsHelper');
const workerModule = require('./worker');
const { getAllAndFilter } = require('../../../resources/daysOfTranstientPensions.json');

describe('Worker day of transient pensions Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let sapService;
  let logService;
  let transtientPensionUtils;
  let daysUtils;
  let done;

  beforeEach(() => {
    done = jest.fn();
    daysUtils = {
      daysForCalculus: jest.fn(() => ''),
      applyConditions: jest.fn(() => jest.fn()),
      calculateTotalDaysToPayForPension: jest.fn(() => jest.fn()),
      concatOldPensionWithCurrentPensions: jest.fn(() => jest.fn())
    };
    transtientPensionUtils = {
      formatTransientPensions: jest.fn(() => ''),
      createTranstientAggregation: jest.fn(() => '')
    };
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: getAllAndFilter })),
      aggregationSearch: jest.fn(() => Promise.resolve({ result: getAllAndFilter })),
      createUpdatePension: jest.fn(() => Promise.resolve({ result: getAllAndFilter }))
    };
    sapService = { getSAPSinisterData: jest.fn(() => Promise.resolve([])) };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });

  it('worker daysOfTranstientPensions on empty data', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.resolve({ result: [] }));

    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(pensionService.getAllAndFilter).toHaveBeenCalled();
  });

  it('worker daysOfTranstientPensions', async () => {
    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(pensionService.getAllAndFilter).toHaveBeenCalled();
  });

  it('worker daysOfTranstientPensions on error ', async () => {
    sapService = { getSAPSinisterData: jest.fn(() => Promise.reject(new Error('bad service'))) };
    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(pensionService.getAllAndFilter).toHaveBeenCalled();
  });

  it('worker has been proccessed  ', async () => {
    sapService = { getSAPSinisterData: jest.fn(() => Promise.reject(new Error('bad service'))) };
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(pensionService.getAllAndFilter).not.toHaveBeenCalled();
  });

  it('worker has error ', async () => {
    sapService = { getSAPSinisterData: jest.fn(() => Promise.reject(new Error('bad service'))) };
    logService.existsLog = jest.fn(() => Promise.reject(new Error('error')));
    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(pensionService.getAllAndFilter).not.toHaveBeenCalled();
  });

  it('worker has previous month ', async () => {
    sapService.getSAPSinisterData = jest.fn(() => Promise.resolve([{}, {}]));
    await workerModule.workerFn({
      Logger,
      pensionService,
      sapService,
      logService,
      transtientPensionUtils,
      daysUtils,
      done
    });
    expect(daysUtils.applyConditions).toHaveBeenCalled();
  });

  afterAll(afterAllTests);
});

/* eslint-disable func-names */
const moment = require('moment');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { downloadAndExtractZipFile, uploadDpasFileToSftpServer } = require('./file.service');

const SFTP = {
  Client: function S() {
    return {
      list: jest.fn().mockReturnValue([]),
      connect: jest.fn(),
      downloadTo: jest.fn(),
      close: jest.fn().mockReturnValue(true),
      uploadFrom: jest.fn()
    };
  }
};

describe('APSCollection / File Service', () => {
  beforeAll(beforeAllTests);

  it('should download and extract zip file end return the extracted path', async () => {
    const pathToExtractedFile = await downloadAndExtractZipFile({
      SFTP,
      temp: { dirSync: jest.fn().mockReturnValue('/dirpath') },
      zipExtractor: jest.fn().mockReturnValue('/path/'),
      getRemoteZipFilePathFn: jest.fn()
    });

    const YYYY_MM = moment().format('YYYYMM');

    expect(pathToExtractedFile).toBe(`/path/aIII_taps${YYYY_MM}.703601006`);
  });

  it('upload File To Sfp Server', async () => {
    await uploadDpasFileToSftpServer({
      SFTP,
      fileToUpload: {}
    });

    expect(SFTP.Client().close()).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

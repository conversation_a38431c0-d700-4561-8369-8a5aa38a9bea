/* eslint-disable consistent-return */
const service = require('../services');
const { worker: workerFn } = require('../../sharedFiles/worker');

const FOLDER_PATH = process.env.BULKLOAD_LA_ARAUCANA_FILES_FTP_FOLDER_PATH;

const {
  CAJA_LA_ARAUCANA_FTP_HOST,
  CAJA_LA_ARAUCANA_FTP_USER,
  CAJA_LA_ARAUCANA_FTP_PASS,
  CAJA_LA_ARAUCANA_FTP_PORT
} = process.env;
const ftpCredentials = {
  host: CAJA_LA_ARAUCANA_FTP_HOST,
  user: CAJA_LA_ARAUCANA_FTP_USER,
  password: CAJA_LA_ARAUCANA_FTP_PASS,
  port: CAJA_LA_ARAUCANA_FTP_PORT
};

const workerName = 'CAJA_LA_ARAUCANA_BULK_LOAD';
const description = 'Carga masiva Caja La Araucana';
const endPoint = 'cajalaaraucanabulkload';
const dependencyMark = '';
const name = 'SIN_NOMBRE';

const worker = async ({ Logger, done }) =>
  workerFn({ Logger, done, service, FOLDER_PATH, ftpCredentials, workerName, useSpecialGet: true });

module.exports = { worker, workerName, name, description, endPoint, dependencyMark };

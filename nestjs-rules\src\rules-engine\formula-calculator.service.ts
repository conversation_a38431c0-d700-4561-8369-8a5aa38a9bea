import { Injectable, Logger } from '@nestjs/common';
import { RuleContext } from './rules-engine.service';
import * as mathjs from 'mathjs';

/**
 * Calculadora de Fórmulas para Reglas de Pensiones
 * Evalúa fórmulas matemáticas complejas de forma segura
 */
@Injectable()
export class FormulaCalculator {
  private readonly logger = new Logger(FormulaCalculator.name);
  private readonly mathParser: any;

  constructor() {
    // Configurar parser matemático seguro
    this.mathParser = mathjs.create({
      // Restringir funciones disponibles por seguridad
      addDependencies: mathjs.addDependencies,
      subtractDependencies: mathjs.subtractDependencies,
      multiplyDependencies: mathjs.multiplyDependencies,
      divideDependencies: mathjs.divideDependencies,
      modDependencies: mathjs.modDependencies,
      powDependencies: mathjs.powDependencies,
      sqrtDependencies: mathjs.sqrtDependencies,
      absDependencies: mathjs.absDependencies,
      roundDependencies: mathjs.roundDependencies,
      floorDependencies: mathjs.floorDependencies,
      ceilDependencies: mathjs.ceilDependencies,
      minDependencies: mathjs.minDependencies,
      maxDependencies: mathjs.maxDependencies,
      ifDependencies: mathjs.ifDependencies,
    });
  }

  /**
   * Calcula una fórmula matemática con variables del contexto
   */
  async calculate(formula: string, context: RuleContext, parameters: any = {}): Promise<number> {
    try {
      // Preparar variables para la fórmula
      const variables = this.prepareVariables(context, parameters);
      
      // Procesar fórmula con funciones personalizadas
      const processedFormula = this.preprocessFormula(formula, variables);
      
      // Evaluar fórmula de forma segura
      const result = this.mathParser.evaluate(processedFormula, variables);
      
      // Validar y normalizar resultado
      return this.normalizeResult(result);
      
    } catch (error) {
      this.logger.error(`Error calculating formula "${formula}":`, error);
      throw new Error(`Formula calculation failed: ${error.message}`);
    }
  }

  /**
   * Prepara variables disponibles para las fórmulas
   */
  private prepareVariables(context: RuleContext, parameters: any): Record<string, any> {
    const pension = context.pension;
    
    const variables = {
      // Variables básicas de pensión
      basePension: Number(pension.basePension || 0),
      article40: Number(pension.article40 || 0),
      article41: Number(pension.article41 || 0),
      law19403: Number(pension.law19403 || 0),
      law19539: Number(pension.law19539 || 0),
      law19953: Number(pension.law19953 || 0),
      
      // Variables calculadas
      totalBasePension: Number(pension.basePension || 0) + 
                       Number(pension.article40 || 0) + 
                       Number(pension.article41 || 0),
      
      totalLaws: Number(pension.law19403 || 0) + 
                Number(pension.law19539 || 0) + 
                Number(pension.law19953 || 0),
      
      // Variables de persona
      numberOfCharges: Number(pension.numberOfCharges || 0),
      familyGroup: Number(pension.familyGroup || 1),
      disabilityDegree: Number(pension.disabilityDegree || 0),
      
      // Variables de fecha
      currentMonth: context.currentDate.getMonth() + 1,
      currentYear: context.currentDate.getFullYear(),
      currentDay: context.currentDate.getDate(),
      
      // Variables de edad y tiempo
      age: this.calculateAge(pension.dateOfBirth, context.currentDate),
      pensionYears: this.calculatePensionYears(pension.pensionStartDate, context.currentDate),
      
      // Variables de referencia
      UF: context.referenceValues.get('UF') || 35000,
      IPC: context.referenceValues.get('IPC') || 1.0,
      MINIMUM_SALARY: context.referenceValues.get('MINIMUM_SALARY') || 350000,
      
      // Variables booleanas (convertidas a números para fórmulas)
      enabled: pension.enabled ? 1 : 0,
      institutionalPatient: pension.institutionalPatient ? 1 : 0,
      hasCharges: (pension.numberOfCharges || 0) > 0 ? 1 : 0,
      
      // Parámetros específicos de la regla
      ...parameters,
      
      // Funciones matemáticas personalizadas
      IF: this.createIfFunction(),
      SCALE: this.createScaleFunction(),
      LOOKUP: this.createLookupFunction(),
      PERCENTAGE: this.createPercentageFunction(),
      ROUND_TO: this.createRoundToFunction(),
    };

    return variables;
  }

  /**
   * Preprocesa la fórmula para manejar funciones personalizadas
   */
  private preprocessFormula(formula: string, variables: Record<string, any>): string {
    let processedFormula = formula;

    // Reemplazar funciones personalizadas
    processedFormula = processedFormula.replace(
      /PENSION_TYPE\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
      (match, pensionType) => {
        return variables.pensionType === pensionType ? '1' : '0';
      }
    );

    // Reemplazar operadores de comparación de strings
    processedFormula = processedFormula.replace(
      /(\w+)\s*==\s*['"]([^'"]+)['"]/g,
      (match, variable, value) => {
        return variables[variable] === value ? '1' : '0';
      }
    );

    return processedFormula;
  }

  /**
   * Funciones matemáticas personalizadas
   */
  private createIfFunction() {
    return (condition: any, trueValue: any, falseValue: any) => {
      return condition ? trueValue : falseValue;
    };
  }

  private createScaleFunction() {
    return (value: number, scales: any[]) => {
      for (const scale of scales) {
        if (value >= scale.min && value <= scale.max) {
          return scale.result;
        }
      }
      return 0;
    };
  }

  private createLookupFunction() {
    return (key: string, lookupTable: Record<string, any>) => {
      return lookupTable[key] || 0;
    };
  }

  private createPercentageFunction() {
    return (value: number, percentage: number) => {
      return (value * percentage) / 100;
    };
  }

  private createRoundToFunction() {
    return (value: number, decimals: number = 0) => {
      const factor = Math.pow(10, decimals);
      return Math.round(value * factor) / factor;
    };
  }

  /**
   * Calcula edad en años
   */
  private calculateAge(birthDate: any, currentDate: Date): number {
    if (!birthDate) return 0;
    
    const birth = new Date(birthDate);
    const diffMs = currentDate.getTime() - birth.getTime();
    return Math.floor(diffMs / (365.25 * 24 * 60 * 60 * 1000));
  }

  /**
   * Calcula años de pensión
   */
  private calculatePensionYears(startDate: any, currentDate: Date): number {
    if (!startDate) return 0;
    
    const start = new Date(startDate);
    const diffMs = currentDate.getTime() - start.getTime();
    return Math.floor(diffMs / (365.25 * 24 * 60 * 60 * 1000));
  }

  /**
   * Normaliza el resultado de la fórmula
   */
  private normalizeResult(result: any): number {
    if (typeof result !== 'number' || isNaN(result) || !isFinite(result)) {
      return 0;
    }

    // Redondear a 2 decimales
    return Math.round(result * 100) / 100;
  }

  /**
   * Valida que una fórmula sea segura antes de ejecutarla
   */
  validateFormula(formula: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // Verificar caracteres peligrosos
      const dangerousPatterns = [
        /import\s+/i,
        /require\s*\(/i,
        /eval\s*\(/i,
        /function\s*\(/i,
        /=>/,
        /\$\{/,
        /`/,
      ];

      for (const pattern of dangerousPatterns) {
        if (pattern.test(formula)) {
          errors.push(`Dangerous pattern detected: ${pattern.source}`);
        }
      }

      // Verificar longitud máxima
      if (formula.length > 1000) {
        errors.push('Formula too long (max 1000 characters)');
      }

      // Intentar parsear la fórmula
      this.mathParser.parse(formula);

    } catch (error) {
      errors.push(`Syntax error: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Ejemplos de fórmulas comunes para el sistema de pensiones
   */
  getFormulaExamples(): Record<string, string> {
    return {
      // APS básico
      aps_basic: 'IF(totalBasePension <= 150000, 25000, IF(totalBasePension <= 250000, 15000, IF(totalBasePension <= 350000, 8000, 0)))',
      
      // Asignación familiar
      family_assignment: 'IF(totalBasePension <= 280000, numberOfCharges * 12364, IF(totalBasePension <= 420000, numberOfCharges * 7598, numberOfCharges * 2533))',
      
      // Bono navidad
      christmas_bonus: 'IF(currentMonth == 12 && totalBasePension <= 200000, 30000, IF(currentMonth == 12 && totalBasePension <= 400000, 20000, 0))',
      
      // Descuento AFP
      afp_discount: 'min(totalBasePension * 0.1077, UF * 80.2 * 0.1077)',
      
      // Descuento salud FONASA
      health_fonasa: 'totalBasePension * 0.07',
      
      // Pensión mínima invalidez total
      minimum_pension_invalidez: 'max(totalBasePension, 185000)',
      
      // Bono invierno con condiciones
      winter_bonus: 'IF(currentMonth >= 5 && currentMonth <= 8 && totalBasePension <= 180000, 20000, IF(currentMonth >= 5 && currentMonth <= 8 && totalBasePension <= 350000, 12000, 0))',
      
      // Cálculo con porcentaje de invalidez
      disability_adjustment: 'basePension * (disabilityDegree / 100)',
      
      // Retroactivo con IPC
      retroactive_ipc: 'basePension * (IPC - 1)',
      
      // Límite retención judicial
      judicial_retention_limit: 'max(0, (totalBasePension - MINIMUM_SALARY * 0.9) * 0.5)',
    };
  }

  /**
   * Prueba una fórmula con datos de ejemplo
   */
  async testFormula(formula: string, testData: any = {}): Promise<{ result: number; variables: any; errors: string[] }> {
    try {
      // Crear contexto de prueba
      const mockContext: RuleContext = {
        pension: {
          basePension: 200000,
          article40: 50000,
          article41: 30000,
          numberOfCharges: 2,
          pensionType: 'INVALIDEZ_TOTAL',
          enabled: true,
          institutionalPatient: false,
          disabilityDegree: 80,
          dateOfBirth: new Date('1970-01-01'),
          pensionStartDate: new Date('2020-01-01'),
          ...testData
        },
        currentDate: new Date(),
        referenceValues: new Map([
          ['UF', 35000],
          ['IPC', 1.03],
          ['MINIMUM_SALARY', 350000]
        ]),
        calculationHistory: new Map()
      };

      const variables = this.prepareVariables(mockContext, {});
      const result = await this.calculate(formula, mockContext);

      return {
        result,
        variables,
        errors: []
      };

    } catch (error) {
      return {
        result: 0,
        variables: {},
        errors: [error.message]
      };
    }
  }
}

const { roundValue } = require('../../sharedFiles/helpers');

const VALIDITY_TYPE = /^No\s+vigente$/i;
const PENSION_TYPES = [
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trabajo/i,
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trayecto/i,
  /Pensi[óo]n\s+por\s+enfermedad\s+profesional/i
];

const getPensions = async pensionsService => {
  const { result, error } = await pensionsService.getPensionsWithLiquidation({
    pensionType: { $in: PENSION_TYPES },
    enabled: true,
    validityType: { $not: VALIDITY_TYPE },
    institutionalPatient: true
  });
  return { result, getPensionsError: error };
};

module.exports = {
  async updatePensions(pensionsService) {
    try {
      const { result: pensions, getPensionsError } = await getPensions(pensionsService);
      if (getPensionsError) throw new Error(getPensionsError);
      if (!pensions) return { completed: true, error: null };
      const modifiedPensions = pensions.map(({ _id, liquidation, ...pension }) => {
        return {
          ...pension,
          reservedAmounts: {
            ...pension.reservedAmounts,
            forInstitutionalPatient: roundValue(liquidation.netPension) || 0
          }
        };
      });
      const { completed, error } = await pensionsService.updatePensions(modifiedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

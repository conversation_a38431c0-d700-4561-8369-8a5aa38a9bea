/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const ServicePagModel = require('../models/servipag');
const service = require('./servipagBranchOffices.service');

let Logger;

describe('Servipag nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {});
  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };
  it('should create', async () => {
    const { error } = await service.createServipag({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const { result, isError } = await service.getAll();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });

  it('should find one and update1', async () => {
    // create and save one  document
    const servipag = await ServicePagModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    });

    const { id, name } = { ...servipag.toObject(), name: 'ServicePag la florida' };
    const { error } = await service.updateServipag({
      id,
      name
    });
    const { result, isError } = await service.getAll();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('ServicePag la florida');
  });

  it('should find one and update2', async () => {
    await ServicePagModel.create({
      id: 'id-1-B',
      name: 'Servipag A',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '111',
      enabled: false
    });

    const newServipagData = {
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    };
    // create and save one  document
    await ServicePagModel.create(newServipagData);

    const { error } = await service.updateServipag({
      ...newServipagData,
      name: 'Servipag z',
      code: '111'
    });
    const { result, isError } = await service.getAll();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('Servipag z');
  });

  it('should delete a Servipag branch Office', async () => {
    // create and save one  document
    const servipag = await ServicePagModel.create({
      id: 'id-1-A',
      name: 'Servipag alameda',
      city: 'Santiago',
      address: 'Alameda con vicuña',
      code: '000',
      enabled: true
    });

    const { id, createdAt, updatedAt } = { ...servipag.toObject() };

    const { error } = await service.delete(id);

    const [resultWithoutServipag, resultWithServipag] = await Promise.all([
      service.getAll(),
      service.getAll({ enabled: false })
    ]);
    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());

    expect(resultWithoutServipag.result.length).toBe(0);
    expect(resultWithServipag.result.length).toBe(1);
    expect(resultWithServipag.result[0].enabled).toEqual(false);
  });

  it('should delete a Servipag branch Offices', async () => {
    mocks = {
      exec: jest.fn(() => Promise.reject())
    };
    jest.spyOn(ServicePagModel, 'findOneAndUpdate').mockImplementationOnce(() => mocks);
    // create and save one  document
    const servipag = await ServicePagModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    });

    const { id, name } = await { ...servipag.toObject(), name: 'ServicePag la florida' };
    const { isError } = await service.updateServipag({
      id,
      name
    });

    expect(isError).toBe(true);
  });

  it('can´t  duplicate code', async () => {
    // create and save one  document
    await ServicePagModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const servipag2 = await ServicePagModel.create({
      id: 'id-1-B',
      name: 'ServicePag  La Florida',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '001',
      enabled: true
    });

    const { id, code } = { ...servipag2.toObject(), code: '000' };
    const { error } = await service.updateServipag({
      id,
      code
    });
    const { result } = await service.getAll();

    expect(result[1].code).toBe('001');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  it('can´t  duplicate name', async () => {
    // create and save one  document
    await ServicePagModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const servipag2 = await ServicePagModel.create({
      id: 'id-1-B',
      name: 'ServicePag  La Florida',
      city: 'Santiago',
      address: 'Alameda 2314',
      code: '001',
      enabled: true
    });

    const { id, name } = { ...servipag2.toObject(), name: 'ServicePag  pte alto' };
    const { error } = await service.updateServipag({
      id,
      name
    });
    const { result } = await service.getAll();

    expect(result[1].name).toBe('ServicePag  La Florida');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await ServicePagModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

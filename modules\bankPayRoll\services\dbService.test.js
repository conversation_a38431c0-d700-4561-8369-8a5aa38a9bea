/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const service = require('./dbService');
const pensions = require('../../../resources/pensionToBankPayRoll.json');
const liquidations = require('../../../resources/liquidationsPayRoll.json');
const retentions = require('../../../resources/retentionToBankPayRoll.json');
const banks = require('../../../resources/banks-PayRolls.json');
const servipags = require('../../../resources/servipags-PayRolls.json');
const PaymentDateModel = require('../../paymentDate/models/paymentDate');
const LiquidationModel = require('../../../models/liquidation');
const BankModel = require('../../nomenclators/banks/models/banks');
const ServipagModel = require('../../nomenclators/servipag/models/servipag');
const CollectorModel = require('../../../models/collectorretentions');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let textTable;
  let ufService;
  let fsClient;

  beforeEach(() => {
    textTable = jest.fn();

    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: pensions }))
    };
    ufService = {
      getCurrentUfValue: jest.fn(() => Promise.resolve({ value: 28716.52 }))
    };
    fsClient = {
      writeFile: jest.fn(() => Promise.resolve())
    };
  });

  it('success create payroll bank', async () => {
    const mocks = {
      lean: jest.fn().mockResolvedValue(liquidations)
    };
    jest.spyOn(LiquidationModel, 'find').mockImplementationOnce(() => mocks);
    jest.spyOn(BankModel, 'find').mockImplementationOnce(() => banks);
    jest.spyOn(ServipagModel, 'find').mockImplementationOnce(() => servipags);
    const date = new Date();
    await PaymentDateModel.insertMany([
      {
        year: date.getFullYear(),
        month: (date.getMonth() + 1).toString().padStart(2, '0'),
        paymentDate: date,
        enabled: true,
        createdAt: '2022-04-05T20:08:52.684Z',
        updatedAt: '2022-04-05T20:08:52.684Z'
      }
    ]);

    await CollectorModel.insertMany(retentions);

    const { error, completed } = await service.generateBankPayRoll({
      pensionService,
      textTable,
      fsClient,
      ufService
    });

    expect(completed).toBe(true);
    expect(error).toBe(null);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PaymentDateModel.deleteMany({}).catch(err => console.error(err));
    await CollectorModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

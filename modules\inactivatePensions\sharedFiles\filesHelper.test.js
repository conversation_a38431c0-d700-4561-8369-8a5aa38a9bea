/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const filesHelper = require('./filesHelper');

describe('File helpers Test', () => {
  beforeAll(beforeAllTests);
  beforeEach(() => {});
  const country = 'C';

  it('valid format', async () => {
    expect(moment(filesHelper.getCurrentDateTime(), 'DD-MM-YYYY HH:mm:ss', true).isValid()).toBe(
      true
    );
  });

  it('get success Year and Month', async () => {
    const [year, month] = filesHelper.getCurrentYearAndMonth();
    const receivedYear = moment().year();
    const receivedMonth = moment().month() + 1;

    expect(month).toBe(receivedMonth);
    expect(year).toBe(receivedYear);
  });

  it('get rut formatted', async () => {
    const rut = filesHelper.getFormatedRut('190238199', country);
    const expectedRut = '19023819-9';
    expect(rut).toBe(expectedRut);
  });

  it('get rut formatted with point', async () => {
    const rut = filesHelper.getFormatedRut('19.023.819-9', country);
    const expectedRut = '19023819-9';

    expect(rut).toBe(expectedRut);
  });

  it('get rut formatted lower 10 millions', async () => {
    const rut = filesHelper.getFormatedRut('94031397', country);
    const expectedRut = '9403139-7';

    expect(rut).toBe(expectedRut);
  });

  it('invalid  date', async () => {
    const nextMonth = String(moment().month() + 2).padStart(2, 0);
    const currentYear = new Date().getFullYear();

    const date = filesHelper.getFormatedDate(`${currentYear}${nextMonth}03`);

    expect(date).toStrictEqual(null);
  });

  afterAll(afterAllTests);
});

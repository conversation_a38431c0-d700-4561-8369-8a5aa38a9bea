/* eslint-disable consistent-return */
const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'calculateCurrentCapital',
  worker: deps => workerModule.workerFn({ logService, service, pensionService, ...deps }),
  repeatInterval: process.env.CALCULATE_CURRENT_CAPITAL_FREQUENCY,
  description: 'Calculo de capitales vigentes',
  endPoint: 'calculatecurrentcapital',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependenciesArray
};

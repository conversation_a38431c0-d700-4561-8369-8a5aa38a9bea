/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const path = require('path');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const fileReader = require('./fileReader');

describe('File Reader Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});
  it('success file reader', async () => {
    const fileTxt = path.join(__dirname, '../../../resources/Achs_202003_out.txt');
    const lines = await fileReader.readLines(fileTxt);
    const [
      rut,
      resultFlag,
      fathersLastName,
      mothersLastName,
      beneficiaryNames,
      birthDate,
      gender,
      country
    ] = lines[0];
    expect(rut).toBe('270670474');
    expect(resultFlag).toBe('2');
    expect(fathersLastName.trim()).toBe('MENDOZA');
    expect(mothersLastName.trim()).toBe('MONCHON');
    expect(beneficiaryNames.trim()).toBe('JORGE ALBERTO');
    expect(birthDate).toBe('19730601');
    expect(gender).toBe('M');
    expect(country).toBe('E');
  });

  afterAll(afterAllTests);
});

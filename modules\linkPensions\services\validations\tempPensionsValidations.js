/* eslint-disable no-restricted-globals */
const DISABILITY_DEGREE_POSITION = 15;
const RESOLUTION_NUMBER_POSITION = 16;
const MAX_RUT_FOR_NATURAL_PERSON = 50000000;
const MAX_RUT_LENGTH = 10;
const ANY_NON_DIGIT_OR_DOT_REGEX = /[^\d.?]+/;
const MIN_AND_MAX_DIGIT_REGEX = /^\d{5,8}(\.\d+)?$/;
const NUMBER_REGEX = /^\d+(\.\d+)?$/;
const HEAVY_DUTY_ABLED_DISABILITY = [/^S[ií]$/i, /^No$/i];
const HEAVY_DUTY_ABLED_REST_OF_PENSIONS = [/^S[ií]$/i, /^No$/i, /^$/];
const RUT_REGEX = /(\d{1,2})\.?(\d{3})\.?(\d{3})-?(\d|k)/i;
const RUT_REGEX_ABLED_REST_OF_PENSIONS = [/(\d{1,2})\.?(\d{3})\.?(\d{3})-?(\d|k)/i, /^$/];
const TRANSIENT_SI = /s[ií]/i;
const [ERROR, WARNING] = ['error', 'warning'];
const REQUIRED_FIELDS_CSV = `Existen campos obligatorios vacíos`;
const INVALID_DATA = 'Dato inválido';
const INVALID_RUT_BENEFICIARY = 'RUT del Beneficiario es inválido';
const INVALID_RUT_CAUSANT = 'RUT del causante es inválido';
const INVALID_RUT_COLLECTOR = 'RUT del cobrante es inválido';
const INVALID_RUT_PARENTS = 'RUT de la madre no es valido';
const EXISTING_PENSIONING = 'Pensionado ya registrado. Se actualizarán sus datos';
const INVALID_DISABILITY_PENSION =
  'Ya existe una pensión registrada con el mismo RUT de beneficiario y RUT de Causante';

const TYPES_OF_DISABILITY_PENSIONS = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const ORPHANHOOD_PENSIONS_TYPE = [
  /Pensi[oó]n\s+por\s+orfandad/i,
  /Pensi[oó]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];

const SURVIVORS_PENSIONS_TYPE = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
  /Pensi[oó]n por orfandad/i,
  /Pensi[oó]n de orfandad de padre y madre/i
];

const regimenOtherPensionArray = ['1', '2', '3', '4', '5', '6'];
const maritalStatusArray = ['C', 'D', 'P', 'S', 'V'];

const isDate = date => /((0[1-9]|[12]\d|3[01])[/.-](0[1-9]|1[0-2])[/.-][12]\d{3})/gi.test(date);

const DUPLICATED_BENEFICIARY = (beneficiary = '11.111.111-1') =>
  `Advertencia: El RUT del beneficiario ${beneficiary} está duplicado en el archivo.`;

const validateRUT = rut => rut.replace(/\./g, '').match(RUT_REGEX);

const defaultNumberValue = value => (!isNaN(Number(value)) ? Number(value) : 0);

const createArrayOfValidations = (maxLength = 0) => {
  const defaultValidation = str => !str || !str.trim();

  const filledValitationArray = Array(maxLength).fill(defaultValidation);

  filledValitationArray[DISABILITY_DEGREE_POSITION] = str =>
    str && str.length ? ANY_NON_DIGIT_OR_DOT_REGEX.test(str) : true;

  filledValitationArray[RESOLUTION_NUMBER_POSITION] = str =>
    str && str.length ? ANY_NON_DIGIT_OR_DOT_REGEX.test(str) : true;

  return filledValitationArray;
};

const validationFunction = (item, index, arrayOfValidations) => {
  const fnValidator = arrayOfValidations[index];
  return fnValidator(item);
};

const requiredFields = requiredFiles => {
  const arrayOfValidations = createArrayOfValidations(requiredFiles.length);
  return (
    requiredFiles.filter((item, index) => validationFunction(item, index, arrayOfValidations))
      .length > 0
  );
};

const validateDaysPaidTransients = days => {
  return days >= 0 && days <= 31;
};

const validateHeavyDuty = (duty, pensionType) => {
  return TYPES_OF_DISABILITY_PENSIONS.some(regex => regex.test(pensionType))
    ? HEAVY_DUTY_ABLED_DISABILITY.some(regex => regex.test(duty))
    : HEAVY_DUTY_ABLED_REST_OF_PENSIONS.some(regex => regex.test(duty));
};

const validateParentRutPension = (rut, pensionType) => {
  return ORPHANHOOD_PENSIONS_TYPE.some(regex => regex.test(pensionType))
    ? RUT_REGEX.test(rut)
    : RUT_REGEX_ABLED_REST_OF_PENSIONS.some(regex => regex.test(rut));
};

const getCheckDigit = rut => {
  const body = `${rut}`;
  let sum = 0;
  let multiple = 2;

  for (let i = 1; i <= body.length; i += 1) {
    const index = multiple * body.charAt(body.length - i);
    sum += index;

    if (multiple < 7) {
      multiple += 1;
    } else {
      multiple = 2;
    }
  }

  const checkDigit = 11 - (sum % 11);

  if (checkDigit === 10) {
    return 'K';
  }
  if (checkDigit === 11) {
    return '0';
  }

  return `${checkDigit}`;
};

const rutValidation = rut => {
  if (!rut) {
    return false;
  }
  const arrayOfRutSecctions = validateRUT(rut);
  if (!arrayOfRutSecctions || !arrayOfRutSecctions.length || !arrayOfRutSecctions[4]) {
    return false;
  }

  const rutWithoutCD = `${arrayOfRutSecctions[1]}${arrayOfRutSecctions[2]}${arrayOfRutSecctions[3]}`;

  if (rutWithoutCD >= MAX_RUT_FOR_NATURAL_PERSON) {
    return false;
  }

  const checkDigit = getCheckDigit(rutWithoutCD);
  return arrayOfRutSecctions[4].toUpperCase() === checkDigit;
};

const cleanDots = text => text.replace(/[.\s]+/g, '');
const formatRut = rut => {
  if (!rut || rut.trim().length < 8) return '';
  return rut.includes('-')
    ? cleanDots(rut)
    : `${cleanDots(rut).slice(0, -1)}-${cleanDots(rut).slice(-1)}`;
};

const isDisabilityPension = pension =>
  TYPES_OF_DISABILITY_PENSIONS.some(regex => regex.test(pension));

const disabilityValidation = (savedPensioners, causantRut, beneficiaryRut, pensionType) => {
  const findedPensioners = savedPensioners.filter(
    ({ beneficiary, causant }) =>
      formatRut(beneficiary.rut) === formatRut(beneficiaryRut) &&
      formatRut(causant.rut) === formatRut(causantRut)
  );

  if (findedPensioners.length) {
    if (!isDisabilityPension(pensionType)) {
      return ERROR;
    }

    if (findedPensioners.some(pensioner => isDisabilityPension(pensioner.pensionType))) {
      return WARNING;
    }

    return ERROR;
  }

  return !!findedPensioners.length;
};

const validateRutBeneficiaryPensions = (arr, index) => {
  const errors = [];
  const uniqueDuplicatedRut = new Set();

  const primaryPension = arr[index];

  const duplicadtePensions = arr.filter(
    (secondaryPension, j) =>
      index !== j && primaryPension.beneficiaryRut === secondaryPension.beneficiaryRut
  );

  duplicadtePensions.forEach(duplicatedPension => {
    uniqueDuplicatedRut.add(duplicatedPension.beneficiaryRut);
    if (primaryPension.causantRut === duplicatedPension.causantRut) {
      errors.push({
        message:
          'RUT del beneficiario y RUT del Causante están duplicados en el archivo en más de un registro.',
        type: ERROR
      });
    } else {
      if (
        !SURVIVORS_PENSIONS_TYPE.some(regex => regex.test(primaryPension.pensionType)) &&
        !SURVIVORS_PENSIONS_TYPE.some(regex => regex.test(duplicatedPension.pensionType))
      ) {
        errors.push({
          message:
            'RUT del beneficiario DUPLICADO y RUT del Causante DISTINTO, no tiene Pensión de Superviviencia.',
          type: ERROR
        });
      }
      if (
        SURVIVORS_PENSIONS_TYPE.some(regex => regex.test(primaryPension.pensionType)) ||
        SURVIVORS_PENSIONS_TYPE.some(regex => regex.test(duplicatedPension.pensionType))
      ) {
        errors.push({
          message:
            'RUT del beneficiario DUPLICADO y RUT del Causante DISTINTO, tiene Pensión AL MENOS UNA Superviviencia.',
          type: WARNING
        });
      }
    }
  });

  const uniqueBeneficiariesError = Array.from(uniqueDuplicatedRut, rut => ({
    message: DUPLICATED_BENEFICIARY(rut),
    type: WARNING
  }));

  return [...uniqueBeneficiariesError, ...errors];
};

const displayResults = (validationResults, index) => {
  const results = [];
  if (typeof validationResults === 'object' && validationResults.length) {
    const uniqueValidations = Array.from(new Set(validationResults));

    const warningValidations = uniqueValidations.filter(validation => validation.type === WARNING);
    const errorValidations = uniqueValidations.filter(validation => validation.type === ERROR);

    if (warningValidations.length) {
      const messages = warningValidations.map(validation => validation.message);
      const mergedMessages = messages.toString().replace(',', ' - ');

      results.push({
        row: index + 1,
        message: mergedMessages,
        type: WARNING
      });
    }

    if (errorValidations.length) {
      const messages = errorValidations.map(validation => validation.message);
      const mergedMessages = messages.toString().replace(',', ' - ');

      results.push({
        row: index + 1,
        message: mergedMessages,
        type: ERROR
      });
    }

    return results;
  }
  return validationResults;
};

const validateFirstConditions = inputDataItem => {
  const errorsRowFirst = [];
  const {
    maritalStatus,
    regimenOtherPension,
    startAnotherPension,
    amountOtherPension
  } = inputDataItem;
  if (!regimenOtherPensionArray.includes(regimenOtherPension)) {
    errorsRowFirst.push({ message: INVALID_DATA, type: ERROR });
  }
  if (!maritalStatusArray.includes(maritalStatus)) {
    errorsRowFirst.push({ message: INVALID_DATA, type: ERROR });
  }
  if (startAnotherPension && !isDate(startAnotherPension)) {
    errorsRowFirst.push({ message: INVALID_DATA, type: ERROR });
  }
  if (amountOtherPension && !NUMBER_REGEX.test(amountOtherPension.trim())) {
    errorsRowFirst.push({ message: INVALID_DATA, type: ERROR });
  }

  return errorsRowFirst;
};

const validateSecondConditions = inputDataItem => {
  const errorsRowSecond = [];
  const {
    basePension,
    pensionType,
    initialBasePension,
    pensionStartDate,
    heavyDuty,
    parentRut,
    healthUF
  } = inputDataItem;

  if (heavyDuty === undefined || !validateHeavyDuty(heavyDuty.trim(), pensionType)) {
    errorsRowSecond.push({ message: REQUIRED_FIELDS_CSV, type: ERROR });
  }
  if (!isDate(pensionStartDate)) {
    errorsRowSecond.push({ message: INVALID_DATA, type: ERROR });
  }
  if (!validateParentRutPension(parentRut, pensionType)) {
    errorsRowSecond.push({ message: REQUIRED_FIELDS_CSV, type: ERROR });
  }
  if (basePension && !MIN_AND_MAX_DIGIT_REGEX.test(basePension.trim())) {
    errorsRowSecond.push({ message: INVALID_DATA, type: ERROR });
  }
  if (initialBasePension && !MIN_AND_MAX_DIGIT_REGEX.test(initialBasePension.trim())) {
    errorsRowSecond.push({ message: INVALID_DATA, type: ERROR });
  }
  if (healthUF && !NUMBER_REGEX.test(healthUF.trim())) {
    errorsRowSecond.push({ message: INVALID_DATA, type: ERROR });
  }
  return errorsRowSecond;
};

const validateThirdConditions = inputDataItem => {
  const errorsRowThird = [];
  const { beneficiaryRut, pensionType, causantRut, collectorRut, parentRut } = inputDataItem;
  if (!beneficiaryRut || !rutValidation(beneficiaryRut)) {
    errorsRowThird.push({ message: INVALID_RUT_BENEFICIARY, type: ERROR });
  }

  if (!causantRut || !rutValidation(causantRut)) {
    errorsRowThird.push({ message: INVALID_RUT_CAUSANT, type: ERROR });
  }

  if (!collectorRut || !rutValidation(collectorRut)) {
    errorsRowThird.push({ message: INVALID_RUT_COLLECTOR, type: ERROR });
  }
  if (!validateParentRutPension(parentRut, pensionType) || parentRut.length > MAX_RUT_LENGTH) {
    errorsRowThird.push({ message: INVALID_RUT_PARENTS, type: ERROR });
  }

  return errorsRowThird;
};

const validateFourthConditions = inputDataItem => {
  const errorsRowFourth = [];
  const {
    pensionType,
    increasingInLaw19578,
    increasingInLaw19953,
    increasingInLaw20102,
    basePensionWithoutIncreases,
    heavyDuty
  } = inputDataItem;

  if (increasingInLaw19578 && !NUMBER_REGEX.test(increasingInLaw19578.trim())) {
    errorsRowFourth.push({ message: INVALID_DATA, type: ERROR });
  }
  if (increasingInLaw19953 && !NUMBER_REGEX.test(increasingInLaw19953.trim())) {
    errorsRowFourth.push({ message: INVALID_DATA, type: ERROR });
  }
  if (increasingInLaw20102 && !NUMBER_REGEX.test(increasingInLaw20102.trim())) {
    errorsRowFourth.push({ message: INVALID_DATA, type: ERROR });
  }
  if (basePensionWithoutIncreases && !NUMBER_REGEX.test(basePensionWithoutIncreases.trim())) {
    errorsRowFourth.push({ message: INVALID_DATA, type: ERROR });
  }

  if (heavyDuty === undefined || !validateHeavyDuty(heavyDuty.trim(), pensionType)) {
    errorsRowFourth.push({ message: INVALID_DATA, type: ERROR });
  }

  return errorsRowFourth;
};

const validateFifthConditions = inputDataItem => {
  const errorsRowFifth = [];
  const {
    totalPensionAccrued,
    indemnityDiscount,
    strennaRetroConstitution,
    otherLink
  } = inputDataItem;

  if (defaultNumberValue(totalPensionAccrued) <= 0) {
    errorsRowFifth.push({ message: 'Total pensión devengada debe ser mayor a cero', type: ERROR });
  }

  if (defaultNumberValue(strennaRetroConstitution) < 0) {
    errorsRowFifth.push({
      message: 'Aguinaldo retro constitución debe ser mayor o igual a cero',
      type: ERROR
    });
  }
  if (defaultNumberValue(indemnityDiscount) < 0) {
    errorsRowFifth.push({
      message: 'Descuento por indemnización debe ser mayor o igual a cero',
      type: ERROR
    });
  }
  if (defaultNumberValue(otherLink) < 0) {
    errorsRowFifth.push({ message: 'Otro enlazar debe ser mayor o igual a cero', type: ERROR });
  }
  return errorsRowFifth;
};

const validatePensioners = (inputData, index, actualPensioners) => {
  const errorsRow = [];
  const {
    beneficiaryRut,
    basePension,
    pensionType,
    validityType,
    pensionCodeId,
    dateOfBirth,
    gender,
    causantRut,
    causantNames,
    causantLastName,
    collectorRut,
    collectorNames,
    collectorLastName,
    beneficiaryNames,
    beneficiaryLastName,
    disabilityDegree,
    resolutionNumber,
    resolutionDate,
    accidentDate,
    accidentNumber,
    transient,
    country,
    initialBasePension,
    pensionStartDate,
    article40,
    healthUF,
    maritalStatus,
    otherPension,
    regimenOtherPension,
    baseIncome,
    totalPensionAccrued,
    totalEstimatedDaysToPay
  } = inputData[index];

  if (
    requiredFields([
      beneficiaryRut,
      basePension,
      pensionType,
      validityType,
      pensionCodeId,
      dateOfBirth,
      gender,
      causantRut,
      causantNames,
      causantLastName,
      collectorRut,
      collectorNames,
      collectorLastName,
      beneficiaryNames,
      beneficiaryLastName,
      disabilityDegree,
      resolutionNumber,
      resolutionDate,
      accidentDate,
      accidentNumber,
      transient,
      country,
      initialBasePension,
      pensionStartDate,
      article40,
      healthUF,
      maritalStatus,
      otherPension,
      regimenOtherPension,
      baseIncome,
      totalPensionAccrued
    ])
  ) {
    errorsRow.push({ message: REQUIRED_FIELDS_CSV, type: ERROR });
  }

  const errorsRowFirst = validateFirstConditions(inputData[index]);
  Array.prototype.push.apply(errorsRow, errorsRowFirst);
  const errorsRowSecond = validateSecondConditions(inputData[index]);
  Array.prototype.push.apply(errorsRow, errorsRowSecond);

  if (!country || country.toUpperCase() === 'CHI') {
    const errorsRowThird = validateThirdConditions(inputData[index]);
    Array.prototype.push.apply(errorsRow, errorsRowThird);

    const errorsRowFourth = validateFourthConditions(inputData[index]);
    Array.prototype.push.apply(errorsRow, errorsRowFourth);
  }

  const errorsRowFifth = validateFifthConditions(inputData[index]);
  Array.prototype.push.apply(errorsRow, errorsRowFifth);

  if (TRANSIENT_SI.test(transient)) {
    if (
      !validateDaysPaidTransients(totalEstimatedDaysToPay) ||
      isNaN(Number(totalEstimatedDaysToPay)) ||
      !totalEstimatedDaysToPay.toString().trim() ||
      totalEstimatedDaysToPay.includes(',') ||
      totalEstimatedDaysToPay.includes('.')
    ) {
      errorsRow.push({
        message: 'Dato fuera de rango Días pagados pensión transitoria es inválido, Rango [0;31]',
        type: ERROR
      });
    }
  }

  if (actualPensioners.length) {
    const result = disabilityValidation(actualPensioners, causantRut, beneficiaryRut, pensionType);

    if (result === WARNING) {
      errorsRow.push({
        message: EXISTING_PENSIONING,
        type: WARNING
      });
    }
    if (result === ERROR) {
      errorsRow.push({
        message: INVALID_DISABILITY_PENSION,
        type: ERROR
      });
    }
  }

  const allErrors = [...errorsRow, ...validateRutBeneficiaryPensions(inputData, index)];
  return displayResults(allErrors, index);
};

module.exports = validatePensioners;

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const rulesValidator = require('../../../../models/basePensionRules');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

let MinimunPensionsAndBonusModel;
const service = require('./basePension.service');
const defaulModelValues = require('../../../../data-seed/1-basePensionRules/rules');

const createCollection = name => mongoose.connection.db.createCollection(name, rulesValidator);
describe('Minimun pensions and bonus service Model Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(async () => {
    MinimunPensionsAndBonusModel = await createCollection('basePensionRules');
  });

  it('should update value on model', async () => {
    await MinimunPensionsAndBonusModel.insertMany([...defaulModelValues]);
    const [firstObject] = await service.getAll();
    firstObject.value.minimun = 12345;
    const { completed, error } = await service.update([firstObject]);
    const result = await MinimunPensionsAndBonusModel.findOne({ _id: firstObject._id });

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(result.value.minimun).toBe(12345);
  });

  it('should return error on update value on model cause bad signature', async () => {
    await MinimunPensionsAndBonusModel.insertMany([...defaulModelValues]);
    const [firstObject] = await service.getAll();
    const { completed, error } = await service.update([firstObject], { age: 'EDDDAD >>> 2' });
    const result = await MinimunPensionsAndBonusModel.findOne({ _id: firstObject._id });

    expect(completed).toBe(false);
    expect(error).toBeDefined();
    expect(result.value.minimun).toBe(null);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await MinimunPensionsAndBonusModel.drop();
    } catch (error) {
      console.log('error al eliminar MinimunPensionsAndBonusModel TESTs', error);
    }
  });

  afterAll(afterAllTests);
});

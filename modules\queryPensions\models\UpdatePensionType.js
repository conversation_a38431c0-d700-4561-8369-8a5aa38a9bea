const mongoose = require('mongoose');

const { Schema } = mongoose;

const updatePensionType = new Schema(
  {
    beneficiaryRut: { type: String, required: true },
    causantRut: { type: String, required: true },
    pensionCodeId: { type: String, required: true },
    pensionType: { type: String, required: true },
    ChangeOfPensionTypeDueToCharges: { type: Boolean, default: false, required: true }
  },
  { timestamps: true }
);

updatePensionType.index({ beneficiaryRut: 1, causantRut: 1 }, { unique: true });
updatePensionType.index({ createdAt: 1 }, { expires: '30d' });

module.exports = mongoose.model('updatePensionType', updatePensionType);

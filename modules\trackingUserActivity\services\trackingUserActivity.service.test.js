const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const {
  computeDiff,
  flattenObject,
  trakingPostSave,
  trackingPostFindOneAndUpdate,
  createWithTracking,
  customDiffRoles,
  customDiffForAssetsAndDiscounts
} = require('./trackingUserActivity.service');

describe('testing tracking user activity', () => {
  beforeAll(beforeAllTests);
  let user;
  let document;
  beforeEach(() => {
    user = {
      name: 'User',
      email: '<EMAIL>',
      endpoint: 'endpoint/user'
    };
    document = {
      _id: '6052012e1f11e85c6abeebc1',
      name: 'Name',
      email: '<EMAIL>',
      isActive: true,
      enabled: true,
      role: '601ac916287abf7c7efa6967'
    };
  });

  it('should print  the difference between two objects', done => {
    const previousState = {
      beneficiary: { rut: 'this one will be updated', constant: 'this will remain unchanged' },
      fieldA: 1,
      paymentInfo: {
        branchOffice: { name: 'this will change', otherNestedField: 'this one will be deleted' }
      }
    };
    const updatedValue = {
      beneficiary: { rut: 'updated', constant: 'this will remain unchanged' },
      fieldA: 'a',
      paymentInfo: {
        branchOffice: {
          name: 'now this is a string',
          newProperty: 'this one was created'
        }
      }
    };

    const { currentFlattenedObject, previousFlattenedObject } = computeDiff(
      previousState,
      updatedValue
    );

    const previousValuesThatChanged = {
      'beneficiary-rut': 'this one will be updated',
      fieldA: 1,
      'paymentInfo-branchOffice-name': 'this will change',
      'paymentInfo-branchOffice-newProperty': undefined,
      'paymentInfo-branchOffice-otherNestedField': 'this one will be deleted'
    };

    const currentValuesThatChanged = {
      'beneficiary-rut': 'updated',
      fieldA: 'a',
      'paymentInfo-branchOffice-name': 'now this is a string',
      'paymentInfo-branchOffice-newProperty': 'this one was created',
      'paymentInfo-branchOffice-otherNestedField': undefined
    };

    expect(previousFlattenedObject).toStrictEqual(previousValuesThatChanged);
    expect(currentFlattenedObject).toStrictEqual(currentValuesThatChanged);

    done();
  });

  it('should flatten a deeply nested object', async done => {
    const aDate = moment()
      .add(2, 'days')
      .toDate();
    const objA = {
      beneficiary: { rut: '17176176-8' },
      fieldA: 1,
      paymentInfo: {
        someBranchOffice: 'jajaja',
        superDuperNested: { a: 1, threeFoldedNested: { b: 1, c: { d: { f: 1 } } } },
        aDate
      }
    };

    const manualFlattenedObject = {
      'beneficiary-rut': '17176176-8',
      fieldA: 1,
      'paymentInfo-aDate': aDate,
      'paymentInfo-someBranchOffice': 'jajaja',
      'paymentInfo-superDuperNested-a': 1,
      'paymentInfo-superDuperNested-threeFoldedNested-b': 1,
      'paymentInfo-superDuperNested-threeFoldedNested-c-d-f': 1
    };
    const flattenedObject = flattenObject(objA, '', {});

    expect(flattenedObject).toStrictEqual(manualFlattenedObject);

    done();
  });
  it('Should return undefined in trakingPostSave', done => {
    const doc = {
      _doc: document,
      _user: user
    };

    const modelName = 'users';
    const { error } = trakingPostSave(doc, modelName)('users');

    expect(error).toBeUndefined();
    done();
  });
  it('Should return error undefined in trackingPostFindOneAndUpdate', done => {
    const doc = {
      _doc: document
    };
    const context = {
      _previousModel: { ...document, email: '<EMAIL>' },
      _user: user
    };

    const { error } = trackingPostFindOneAndUpdate(doc, context)('tracking');
    expect(error).toBeUndefined();
    done();
  });
  it('Should return object with name and email parameters in createWithTracking', async () => {
    const dbToTrack = { create: jest.fn(() => Promise.resolve()) };
    const model = {
      create: jest.fn(() => Promise.resolve(document)),
      collection: { name: 'users' }
    };

    const createdDocument = await createWithTracking({ model, user, document, dbToTrack });

    expect(createdDocument.name).toBe(document.name);
    expect(createdDocument.email).toBe(document.email);
  });

  it('Should return diff specifically for roles ', async () => {
    const previousDocument = {
      views: {
        toObject: () => [
          { permission: 'noRead', view: '1' },
          { permission: 'noRead', view: '2' }
        ]
      }
    };
    const currentDocument = {
      views: {
        toObject: () => [
          { permission: 'write', view: '1' },
          { permission: 'noRead', view: '2' }
        ]
      }
    };

    const diff = customDiffRoles(previousDocument, currentDocument);

    expect(diff).toStrictEqual({
      viewDifferenceCurrent: [{ permission: 'write', view: '1' }],
      viewDifferencePrevious: [{ permission: 'noRead', view: '1' }]
    });
  });

  it('Should return diff specifically for assetsAndDiscounts ', async () => {
    const previousDocument = {
      assetsNonFormulable: [
        { haber: '1', amount: 5 },
        { haber: '2', amount: 5 }
      ]
    };
    const currentDocument = {
      assetsNonFormulable: [
        { haber: '1', amount: 5 },
        { haber: '2', amount: 7 }
      ]
    };

    const diff = customDiffForAssetsAndDiscounts(previousDocument, currentDocument);

    expect(diff).toStrictEqual({
      diffAssets: {
        previousFlattenedObject: { '1-amount': 5 },
        currentFlattenedObject: { '1-amount': 7 }
      },
      diffDiscounts: { previousFlattenedObject: {}, currentFlattenedObject: {} },
      diffOfRest: { previousFlattenedObject: {}, currentFlattenedObject: {} }
    });
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

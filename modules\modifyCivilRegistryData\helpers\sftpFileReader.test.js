/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { readSFTPFile } = require('./sftpFileReader');
const sharedHelpers = require('../../sharedFiles/helpers');

describe('Ftp file Reader Test', () => {
  beforeAll(beforeAllTests);
  let fileHelpers;
  beforeEach(() => {
    fileHelpers = {
      getSFTPFileName: jest.fn(),
      readLines: jest.fn(() =>
        Promise.resolve([
          [
            '180987797',
            '1',
            'CARMONA                       ',
            'PALOMINOS                     ',
            'HUMBERTO CARLOS                                   ',
            '19960409',
            'M',
            'C'
          ]
        ])
      ),
      getFormatedRut: jest.fn(() => '180987797'),
      getFormatedDate: jest.fn()
    };
  });

  it('should return lines when the file has been successfully read', async () => {
    const { lines } = await readSFTPFile({ fileHelpers, sharedHelpers });

    expect(fileHelpers.readLines).toHaveBeenCalled();
    expect(lines).toBeDefined();
    expect(lines.length).toBe(1);
  });

  it('should return lines with length 0 when the file has no lines', async () => {
    fileHelpers.readLines = jest.fn(() => Promise.resolve([]));
    const { lines } = await readSFTPFile({ fileHelpers, sharedHelpers });

    expect(fileHelpers.readLines).toHaveBeenCalled();
    expect(lines).toBeDefined();
    expect(lines.length).toBe(0);
  });

  afterAll(afterAllTests);
});

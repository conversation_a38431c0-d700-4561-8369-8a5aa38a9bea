const VALIDITY_TYPE = /^No\s+vigente$/i;

const buildAggregation = (actualMonth, query, limit) => [
  {
    $match: {
      enabled: true,
      validityType: { $not: VALIDITY_TYPE },
      ...query
    }
  },
  {
    $lookup: {
      from: 'pensionhistorics',
      let: {
        beneficiary: '$beneficiary.rut',
        causant: '$causant.rut',
        pensionCode: '$pensionCodeId'
      },
      pipeline: [
        {
          $match: {
            createdAt: { $lt: actualMonth },
            $expr: {
              $and: [
                {
                  $eq: ['$beneficiary.rut', '$$beneficiary']
                },
                {
                  $eq: ['$causant.rut', '$$causant']
                },
                {
                  $eq: ['$pensionCodeId', '$$pensionCode']
                },
                {
                  $eq: ['$enabled', false]
                }
              ]
            }
          }
        },
        {
          $lookup: {
            from: 'liquidations',
            let: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$beneficiaryRut', '$$beneficiary']
                      },
                      {
                        $eq: ['$liquidationMonth', '$$month']
                      },
                      {
                        $eq: ['$liquidationYear', '$$year']
                      },
                      {
                        $eq: ['$causantRut', '$$causant']
                      },
                      {
                        $eq: ['$pensionCodeId', '$$pensionCode']
                      }
                    ]
                  }
                }
              }
            ],
            as: 'liquidation'
          }
        },
        {
          $unwind: {
            path: '$liquidation'
          }
        },
        {
          $group: {
            _id: { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } },
            date: { $first: '$createdAt' },
            data: { $first: '$$ROOT' }
          }
        },
        { $replaceRoot: { newRoot: '$data' } },
        {
          $sort: {
            createdAt: -1
          }
        },
        { $limit: limit }
      ],
      as: 'matchedPensions'
    }
  }
];

module.exports = { buildAggregation };

/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const pensions = require('../../../resources/pensions.json');
const workerModule = require('./worker');

describe('Inactivate by retirement Post-Worker test', () => {
  beforeAll(beforeAllTests);
  let logService;
  let mockService;
  let pensionService;
  let Logger;
  let done;

  beforeEach(() => {
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn().mockResolvedValue(true),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() =>
        Promise.resolve({
          existsLog: false
        })
      ),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    mockService = {
      obtainActivePensions: jest.fn().mockResolvedValue([
        {
          _doc: {
            _id: 1,
            __v: 1,
            ...pensions[0]
          }
        }
      ]),
      calculateBasePension: jest.fn().mockResolvedValue([])
    };
    pensionService = {
      createUpdatePension: jest.fn().mockResolvedValue({
        error: false,
        completed: true
      })
    };
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
  });

  it('should leave a mark when process is completed', async () => {
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should return when pension type change has not been executed', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(Logger.info.mock.calls.length).toBe(3);
    expect(Logger.error.mock.calls.length).toBe(0);
  });

  it('should return when article 50 has not been executed', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(mockService.obtainActivePensions).toHaveBeenCalledTimes(0);
  });

  it('should return when cron has already been executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(mockService.obtainActivePensions).toHaveBeenCalledTimes(0);
  });

  it('should return when there are no active pensions', async () => {
    mockService.obtainActivePensions = jest.fn(() => Promise.resolve([]));
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(mockService.calculateBasePension).toHaveBeenCalledTimes(0);
  });

  it('should return when createUpdatePension has thrown an error', async () => {
    pensionService.createUpdatePension = jest.fn().mockResolvedValue({
      error: true,
      completed: false
    });
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(pensionService.createUpdatePension).toHaveBeenCalledTimes(0);
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should log error at retrieving active pensions', async () => {
    mockService.obtainActivePensions = jest.fn(() => Promise.reject(new Error()));
    await workerModule.workerFn({
      Logger,
      service: mockService,
      pensionService,
      logService,
      done
    });
    expect(logService.existsLog).toHaveBeenCalledTimes(2);
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(mockService.calculateBasePension).toHaveBeenCalledTimes(0);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

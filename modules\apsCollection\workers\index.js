const workerModule = require('./worker');

const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/apsCollection.service');
const fileService = require('../services/file.service');

module.exports = {
  name: 'generateAndUploadApsCollectionFile',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      fileService,
      ...deps
    }),
  repeatInterval: process.env.CRON_GENERATE_AND_UPLOAD_APS_COLLECTION_FILE_FREQUENCY,
  description: 'Cron para generar archivos Cobro APS',
  endPoint: 'generateanduploadapscollectionfile',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

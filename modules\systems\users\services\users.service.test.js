/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const UserModel = require('../../../../models/user');
const service = require('./users.service');
const RolesModel = require('../../rolesAndPermissions/models/roles.model');
const ViewModel = require('../../rolesAndPermissions/models/views.model');

let Logger;

describe('Users nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };

  it('should create', async () => {
    const { result } = await service.createUser({
      name: 'iron man',
      role: '5ff8642df667e60eb7833642',
      email: '<EMAIL>'
    });

    expect(result).toBeDefined();
  });

  it('should create and find it', async () => {
    const view = await ViewModel.create({ module: 'M', viewNumber: 1, view: 'V' });
    const role = await RolesModel.create({
      roleName: 'Administrador',
      views: [{ permission: 'Read', view: view._id }]
    });
    const userData = {
      name: 'iron man',
      role: role._id,
      email: '<EMAIL>'
    };
    await UserModel.create(userData);
    const result = await service.findByEmail(userData.email);

    expect(result).toBeDefined();
  });

  it('should find one and update', async () => {
    const user = await UserModel.create({
      name: 'iron man',
      role: '5ff8642df667e60eb7833642',
      email: '<EMAIL>',
      enabled: false
    });

    const newUserData = {
      name: 'iron man',
      role: '5ff8642df667e60eb7833641',
      email: '<EMAIL>'
    };

    await UserModel.create(newUserData);

    const { error } = await service.updateUser(user.id, {
      ...newUserData,
      email: '<EMAIL>'
    });
    const { result, isError } = await service.getUsers();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('iron man');
  });

  it('should find one and create', async () => {
    await UserModel.create({
      name: 'iron man',
      role: '5ff8642df667e60eb7833642',
      email: '<EMAIL>',

      enabled: false
    });

    const newUserData = {
      name: 'iron man',
      role: '5ff8642df667e60eb7833642',
      email: '<EMAIL>'
    };

    const { error } = await service.createUser(newUserData);
    const { result, isError } = await service.getUsers();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('iron man');
  });

  it('should delete a user', async () => {
    const user = await UserModel.create({
      name: 'iron man',
      role: '5ff8642df667e60eb7833642',
      email: '<EMAIL>'
    });

    const { _id, createdAt, updatedAt } = { ...user._doc };
    const { error } = await service.deleteUser(_id);
    const [resultWithoutUsers, resultWithUsers] = await Promise.all([
      service.getUsers(),
      service.getUsers({ enabled: false })
    ]);

    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());
    expect(resultWithoutUsers.result.length).toBe(0);
    expect(resultWithUsers.result.length).toBe(1);
    expect(resultWithUsers.result[0].enabled).toEqual(false);
  });

  afterEach(async () => {
    try {
      await UserModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-restricted-syntax */
const tmp = require('tmp');

const { PensionHistoricModel } = require('../../../linkPensions/services/link.service');
const { buildAggregation } = require('./aggregationBuilder');

const { env } = process;
const { CIRCULAR_FILE_2480_SFTP_OUTPUT_FOLDER } = env;
const sftpCredentials = {
  host: env.SFTP_HOST,
  port: env.SFTP_PORT,
  username: env.SFTP_USER,
  password: env.SFTP_PASS
};

const service = {
  async generateFile(fileGenerationUtils, Model = PensionHistoricModel) {
    const { getLine, getFileName, appendFile, util } = fileGenerationUtils;
    const aggregation = buildAggregation();
    const cursor = Model.aggregate(aggregation).cursor();
    const fileName = getFileName();
    const append = util.promisify(appendFile);
    let fileCreated = false;
    for await (const doc of cursor) {
      const line = getLine(doc);
      await append(fileName, line).catch(error => {
        throw new Error(error);
      });
      fileCreated = true;
    }

    return fileCreated ? fileName : null;
  },

  async uploadFileToSftpServer({ fileName, fileGenerationUtils, Sftp }) {
    const { compressFile, getZipFileName } = fileGenerationUtils;
    const zipFileName = getZipFileName();
    const fileToUpload = compressFile(fileName, `${tmp.dirSync().name}/${zipFileName}`);
    const sftpClient = new Sftp.Client();
    const { connected, error } = await Sftp.connectToSFTPServer(sftpClient, sftpCredentials);
    if (!connected) throw error;
    await sftpClient.uploadFrom(
      fileToUpload,
      `${CIRCULAR_FILE_2480_SFTP_OUTPUT_FOLDER}/${zipFileName}`
    );
    sftpClient.close();
    return fileToUpload;
  }
};

module.exports = service;

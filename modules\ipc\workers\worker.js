const PensionModel = require('../../../models/pension');

const dependencyMark = '';
const cronMark = 'REAJUST_BY_IPC';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const cronDescription = 'reajust by ipc';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  ipcService,
  ipcHelper,
  pensionService,
  done,
  logService,
  job
}) => {
  try {
    Logger.info(`${cronMark} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`Starting cron ${cronMark}`);
    Logger.info(`Getting previous month IP`);
    const {
      result: { indexDateString, value },
      error
    } = await ipcHelper.getPreviousMonthIPC(ipcHelper.parseXmlToJSON);
    if (error) {
      throw new Error('No se puede obtener el IPC del último mes');
    }

    Logger.info(`Getting last percentage change`);
    const lastVariation = await ipcService.getLastPercentageChange();
    const ipcObject = ipcHelper.createIpcObject(indexDateString, value, lastVariation);

    Logger.info(`Inserting new IPC`);
    const { result: resultInsertIpc, error: insertIpcError } = await ipcService.insertIPC(
      ipcObject
    );

    if (insertIpcError) {
      throw new Error('No se puede insertar IPC');
    }

    if (resultInsertIpc.isLastPercentageChange) {
      const percent = resultInsertIpc.percentage;
      Logger.info(`Starting base  & intital base pension change`);
      const result = await PensionModel.find(
        {
          enabled: true
        },
        {
          basePension: 1,
          beneficiary: 1,
          causant: 1,
          article40: 1,
          initialBasePension: 1,
          increasingInLaw19578: 1,
          increasingInLaw19953: 1,
          increasingInLaw20102: 1,
          basePensionWithoutIncreases: 1,
          pensionCodeId: 1
        }
      )
        .lean()
        .exec();

      Logger.info(`Will be updated ${result.length} pension(s)`);
      const updatedBasePension = result.map(
        ({
          basePension,
          beneficiary,
          causant,
          article40,
          initialBasePension,
          increasingInLaw19578,
          increasingInLaw19953,
          increasingInLaw20102,
          basePensionWithoutIncreases,
          pensionCodeId
        }) => ({
          causant: { rut: causant.rut },
          beneficiary: { rut: beneficiary.rut },
          basePension: ipcHelper.calculateField(basePension, percent),
          article40: ipcHelper.calculateField(article40, percent),
          increasingInLaw19578: ipcHelper.calculateField(increasingInLaw19578, percent),
          increasingInLaw19953: ipcHelper.calculateField(increasingInLaw19953, percent),
          increasingInLaw20102: ipcHelper.calculateField(increasingInLaw20102, percent),
          basePensionWithoutIncreases: ipcHelper.calculateField(
            basePensionWithoutIncreases,
            percent
          ),
          initialBasePension: ipcHelper.calculateField(initialBasePension, percent),
          pensionCodeId
        })
      );

      await pensionService.createUpdatePensionIPC(updatedBasePension);
      Logger.info(`End of change base pension`);
    }

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

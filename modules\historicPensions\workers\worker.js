const descriptionOfCron = 'Generating historical pension reports';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const dependencyMark = 'MANUALLY_INACTIVATE_MARKED_PENSIONS';
const cronMark = 'HISTORICAL_PENSION_REPORTS';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const getMissingDependencyMessage = dep => `La dependencia "${dep}" aún no se ha ejecutado`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  job,
  logService,
  pensionService,
  service,
  temporaryService,
  done
}) => {
  try {
    Logger.info(`${descriptionOfCron}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { message: getMissingDependencyMessage(dependencyMark) };
    }
    Logger.info(
      `${descriptionOfCron} checking whether this process was previously executed or not`
    );
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage
      };
    }

    Logger.info(`${descriptionOfCron} process started`);
    const { err } = await service.historicalInactivationPensions(pensionService, temporaryService);
    if (err) throw new Error(err);

    const { err: error } = await service.historicalReactivationPensions(
      pensionService,
      temporaryService
    );
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${descriptionOfCron} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${descriptionOfCron} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${descriptionOfCron} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

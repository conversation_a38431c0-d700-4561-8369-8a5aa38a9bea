/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const PensionModel = require('../../../../models/pension');
const ProcessedFileModel = require('../../../sharedFiles/models/processedJob');
const service = require('./dbService');
const pensions = require('../../../../resources/pensions.json');
const pensionService = require('../../../pensions/services/pension.service');
const pensionsSurvival = require('../../../../resources/pensionObjectForChangingPensionType.json');

describe('Inactivate by retirement test', () => {
  beforeAll(beforeAllTests);
  let mocks;

  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn()
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should disable and create new pension when inactivation by death criteria is met', async () => {
    const firstPension = pensions[0];
    firstPension.beneficiary.rut = '11111111-1';
    const secondPension = pensions[0];
    secondPension.beneficiary.rut = '22222222-2';
    // create 2 new pensions
    await PensionModel.insertMany([firstPension, secondPension]);
    // Create array of CSV lines with beneficiary rut and deathDate
    const lines = [
      ['11111111-1', '20200105'],
      ['22222222-2', '20190203']
    ];
    // Call service to inactivate the pensions that meet the lines criteria
    const { completed, inactivationError } = await service.createUpdateDeathPension(
      lines,
      pensionService
    );
    expect(mocks.startTransaction).not.toBeCalled();
    expect(mocks.commitTransaction).not.toBeCalled();
    // No error
    expect(mocks.abortTransaction).not.toBeCalled();
    expect(completed).toBe(true);
    expect(inactivationError).toBe(null);

    // Now collection should have 2 documents
    const count = await PensionModel.collection.countDocuments();
    expect(count).toBe(2);

    // 2 documents should have their enabled field set to true
    const countEnabledTrues = await PensionModel.collection.countDocuments({ enabled: true });
    expect(countEnabledTrues).toBe(2);
  });

  it('should execute proccess to inactivate by death', async () => {
    const beneficiaryRut = pensions[0].beneficiary.rut;
    const ExpectedDateOfValidity = '2021-03-31';
    const currentDate = new Date();
    const validPension = new PensionModel({
      ...pensions[0]
    });

    await validPension.save();
    const lines = [[beneficiaryRut, '2021-03-11']];
    const preCount = await PensionModel.collection.countDocuments();
    const { completed } = await service.createUpdateDeathPension(lines, pensionService);
    const postCount = await PensionModel.collection.countDocuments();
    const result = await PensionModel.find({});

    expect(preCount).toBe(1);
    expect(completed).toBe(true);
    expect(postCount).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(true);
    expect(result[0].inactivationReason).toBe('Fallecimiento');
    expect(moment(result[0].endDateOfValidity).format('MM/DD/YYYY')).toEqual(
      moment(ExpectedDateOfValidity).format('MM/DD/YYYY')
    );
    expect(result[0].inactivationDate).toString(new Date(currentDate));
    expect(result[0].deathDate).toStrictEqual(new Date('2021-03-11'));
    expect(result[0].validityType).toStrictEqual('No vigente');
  });

  it('should execute proccess to inactivate by death for disabilityPensions', async () => {
    const beneficiaryRut = pensions[0].beneficiary.rut;
    const currentDate = new Date();
    const previousValidity = pensions[0].validityType;
    const validPension = new PensionModel({
      ...pensions[0]
    });
    const FiveOfCurrentMonth = moment().format('YYYY-MM-05');
    const ExpectedDateOfValidity = moment(FiveOfCurrentMonth)
      .endOf('month')
      .format();
    await validPension.save();
    const lines = [[beneficiaryRut, FiveOfCurrentMonth]];
    const preCount = await PensionModel.collection.countDocuments();
    const { completed } = await service.createUpdateDeathPension(lines, pensionService);
    const postCount = await PensionModel.collection.countDocuments();
    const result = await PensionModel.find({});

    expect(preCount).toBe(1);
    expect(completed).toBe(true);
    expect(postCount).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(true);
    expect(result[0].inactivationReason).toBe('Fallecimiento');
    expect(moment(result[0].endDateOfValidity).format('MM/DD/YYYY')).toEqual(
      moment(ExpectedDateOfValidity).format('MM/DD/YYYY')
    );
    expect(result[0].inactivationDate).toString(new Date(currentDate));
    expect(result[0].deathDate).toStrictEqual(new Date(FiveOfCurrentMonth));
    expect(result[0].validityType).toStrictEqual(previousValidity);
  });

  it('should execute proccess to inactivate by death for survivalPensions', async () => {
    const beneficiaryRut = pensionsSurvival[0].beneficiary.rut;
    const currentDate = new Date();
    const previousValidity = pensionsSurvival[0].validityType;
    const validPension = new PensionModel({
      ...pensionsSurvival[0]
    });
    const FiveOfCurrentMonth = moment().format('YYYY-MM-05');
    const ExpectedDateOfValidity = moment(FiveOfCurrentMonth)
      .add(-1, 'days')
      .format();
    await validPension.save();
    const lines = [[beneficiaryRut, FiveOfCurrentMonth]];
    const preCount = await PensionModel.collection.countDocuments();
    const { completed } = await service.createUpdateDeathPension(lines, pensionService);
    const postCount = await PensionModel.collection.countDocuments();
    const result = await PensionModel.find({});

    expect(preCount).toBe(1);
    expect(completed).toBe(true);
    expect(postCount).toBe(1);
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(true);
    expect(result[0].inactivationReason).toBe('Fallecimiento');
    expect(moment(result[0].endDateOfValidity).format('MM/DD/YYYY')).toEqual(
      moment(ExpectedDateOfValidity).format('MM/DD/YYYY')
    );
    expect(result[0].inactivationDate).toString(new Date(currentDate));
    expect(result[0].deathDate).toStrictEqual(new Date(FiveOfCurrentMonth));
    expect(result[0].validityType).toStrictEqual(previousValidity);
  });
  afterEach(async () => {
    try {
      await PensionModel.deleteMany();
      await ProcessedFileModel.deleteMany({});
      jest.restoreAllMocks();
    } catch (err) {
      console.log(err);
    }
  });

  afterAll(afterAllTests);
});

const cronMark = 'CALCULATE_RETROACTIVE_BANK_FILE';
const cronDescription = 'calcular archivo bancario retroactivo';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const dependencyMark = '';

const workerFn = async ({ Logger, logService, pensionService, service, done }) => {
  try {
    Logger.info(`Inicio procesamiento cron calculo retroactivo archivo de banco`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    const { error } = await service.retroactiveBankFile(pensionService);
    if (error) throw new Error(error);
    logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

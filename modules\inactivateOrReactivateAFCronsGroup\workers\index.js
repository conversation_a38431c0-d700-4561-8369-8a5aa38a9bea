/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const {
  worker: widowUnder45Service
} = require('../../inactivatePensions/widowUnderFourtyFive/workers');
const { worker: reactivateOrphanhood } = require('../../activatePensions/orphanhood/workers');
const {
  worker: updateByArticleFourtyOne
} = require('../../articleFourtyOne/DisabilityPensioners/workers');
const {
  worker: reactivateForRenewalOfStudyCertificate
} = require('../../reactivatePensions/renewalOfStudyCertificate/workers');
const {
  worker: registerStartAndEndDateOfStudyCertificate
} = require('../../studyCertificateDateRegistration/workers');
const { worker: changePensionTypes } = require('../../pensionTypeChange/workers');

const workerModule = require('./worker');

module.exports = {
  name: 'inactivateOrReactivateAFCronsGroup',
  worker: deps =>
    workerModule.workerFn({
      cronsGroup: {
        registerStartAndEndDateOfStudyCertificate,
        reactivateForRenewalOfStudyCertificate,
        updateByArticleFourtyOne,
        reactivateOrphanhood,
        widowUnder45Service,
        changePensionTypes
      },
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_INACTIVATE_OR_REACTIVATE_AF_CRONS_GROUP_FREQUENCY,
  description: 'Cron unificado para inactivar y reactivar pensiones de supervivencia',
  endPoint: 'inactivateorreactivateafcronsgroup',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

/* eslint-disable prettier/prettier */
const COMMON_REASONS = [
  'descuento en cuotas',
  'otros descuentos',
  'descuento en % pensión',
  'pago de aps indebido',
  'retención pagada indebida'
];

const DISC_REASONS = [
  'anticipo',
  'indemnización indebida',
  'subsidio pagado en exceso',
  'ahorro voluntario',
  'ahorro vivienda ccaf',
  'descuento por diferencia de salud',
  'descuento por préstamos médicos de isapres',
  'descuento ccaf',
  ...COMMON_REASONS
];

const ASS_REASONS = ['devolución de descuentos indebidos', 'haber ccaf'];

const RETROACT_REASONS = ['retención judicial', ...COMMON_REASONS];

const REASONS_1000000022 = [
  'ahorro voluntario',
  'ahorro vivienda ccaf',
  'descuento por diferencia de salud'
];

const negate = input => ({ $multiply: [-1, input] });

const add = inputs => ({ $sum: [...inputs] });

const sumByReasonsAggregation = (input, reasons) => {
  return {
    $sum: {
      $map: {
        input,
        in: {
          $cond: [{ $in: ['$$this.reason', reasons] }, '$$this.amount', 0]
        }
      }
    }
  };
};

const aggregation = [
  {
    $match: { enabled: true, validityType: { $not: /No vigente/i } }
  },
  {
    $group: {
      _id: '',
      aporteSalud1000003765: { $sum: 0 },
      aporteSalud1000005903: { $sum: 0 },
      asigFam1106010001: { $sum: 0 },
      pensPP2102010001: { $sum: 0 },
      art531114040001: { $sum: 0 },
      asigFam1106010002: { $sum: '$assets.forFamilyAssignment' },
      asigFam1106010003: { $sum: '$retroactiveAmounts.forFamilyAssignment' },
      pensiones5103030001: { $sum: '$increasingInLaw19578' },
      pensiones5103030002: { $sum: '$increasingInLaw19953' },
      pensiones5103030003: { $sum: '$increasingInLaw20102' },
      bonoLey5103010003: { $sum: { $add: ['$law19403', '$law19539'] } },
      bonoViudez5103010004: { $sum: '$law19953' },
      bonoInv1106030004: { $sum: '$assets.winterBonus' },
      devDsc1106030001: { $sum: '$assets.aps' },
      anticipo1106030006: { $sum: { $add: ['$assets.rebsal', '$assets.adjustedHealthExemption'] } },
      ctaInstitucional2102020003: { $sum: '$retroactiveAmounts.forInstitutionalPatient' },
      pensiones5103020001: {
        $sum: {
          $sum: [
            '$basePensionWithoutIncreases',
            '$article40',
            '$article41',
            '$assets.marriageBonus',
            '$assets.netTotalNonFormulable',
            '$assets.taxableTotalNonFormulable',
            negate('$discounts.totalNonFormulable'),
            sumByReasonsAggregation('$discounts.nonFormulableByReason', DISC_REASONS),
            negate(
              add([
                sumByReasonsAggregation('$assets.netNonFormulableByReason', ASS_REASONS),
                sumByReasonsAggregation('$assets.taxableNonFormulableByReason', ASS_REASONS)
              ])
            )
          ]
        }
      },
      pensiones5103020004: {
        $sum: {
          $sum: [
            '$retroactiveAmounts.forDisability',
            '$retroactiveAmounts.forSurvival',
            '$retroactiveAmounts.forRejection',
            '$retroactiveAmounts.forPayCheck',
            '$retroactiveAmounts.forNetTotalNonFormulableAssets',
            negate('$retroactiveAmounts.forTotalNonFormulableDiscounts'),
            sumByReasonsAggregation(
              '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
              DISC_REASONS
            ),
            negate(
              add([
                sumByReasonsAggregation(
                  '$retroactiveAmounts.forNetTotalNonFormulableAssetsByReason',
                  ASS_REASONS
                ),
                sumByReasonsAggregation(
                  '$retroactiveAmounts.forTaxableTotalNonFormulableAssetsByReason',
                  ASS_REASONS
                )
              ])
            )
          ]
        }
      },
      aguin5113010001: {
        $sum: {
          $sum: [
            '$assets.nationalHolidaysBonus',
            '$assets.christmasBonus',
            '$retroactiveAmounts.forBonuses'
          ]
        }
      },
      devDsc1114020001: {
        $sum: {
          $sum: [
            add([
              sumByReasonsAggregation('$assets.netNonFormulableByReason', [
                'devolución de descuentos indebidos'
              ]),
              sumByReasonsAggregation('$assets.taxableNonFormulableByReason', [
                'devolución de descuentos indebidos'
              ])
            ]),
            add([
              sumByReasonsAggregation(
                '$retroactiveAmounts.forNetTotalNonFormulableAssetsByReason',
                ['devolución de descuentos indebidos']
              ),
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTaxableTotalNonFormulableAssetsByReason',
                ['devolución de descuentos indebidos']
              )
            ]),
            negate(sumByReasonsAggregation('$discounts.nonFormulableByReason', ['anticipo'])),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                ['anticipo']
              )
            )
          ]
        }
      },
      anticipo1114040003: {
        $sum: {
          $sum: [
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', ['descuento en cuotas'])
            ),
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', ['otros descuentos'])
            ),
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', [
                'descuento en % pensión'
              ])
            ),
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', ['pago de aps indebido'])
            ),
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', [
                'retención pagada indebida'
              ])
            ),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                RETROACT_REASONS
              )
            )
          ]
        }
      },
      descInd1114020002: {
        $sum: {
          $sum: [
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', [
                'indemnización indebida'
              ])
            ),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                ['indemnización indebida']
              )
            )
          ]
        }
      },
      subsCancExceso5101010004: {
        $sum: {
          $sum: [
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', [
                'subsidio pagado en exceso'
              ])
            ),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                ['subsidio pagado en exceso']
              )
            )
          ]
        }
      },
      aporteSalud1000000022: {
        $sum: {
          $sum: [
            negate('$discounts.health'),
            negate('$discounts.afp'),
            negate('$discounts.socialCreditsLaAraucana'),
            negate('$discounts.socialCredits18'),
            negate('$discounts.socialCreditsLosAndes'),
            negate('$discounts.socialCreditsLosHeroes'),
            negate('$discounts.onePercentAdjusted'),
            negate('$discounts.othersLosAndes'),
            negate('$discounts.othersLosHeroes'),
            negate(sumByReasonsAggregation('$discounts.nonFormulableByReason', REASONS_1000000022)),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                REASONS_1000000022
              )
            )
          ]
        }
      },
      aporteSalud5000000165: {
        $sum: {
          $sum: [
            negate('$discounts.healthLoan'),
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', [
                'descuento por préstamos médicos de isapres'
              ])
            ),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                ['descuento por préstamos médicos de isapres']
              )
            )
          ]
        }
      },
      retJudPens2102020008: {
        $sum: {
          $sum: [
            negate(
              sumByReasonsAggregation('$discounts.nonFormulableByReason', ['retención judicial'])
            )
          ]
        }
      },
      distrDePago5103020001: {
        $sum: {
          $sum: [
            add([
              sumByReasonsAggregation('$assets.netNonFormulableByReason', ['haber ccaf']),
              sumByReasonsAggregation('$assets.taxableNonFormulableByReason', ['haber ccaf'])
            ]),
            negate(sumByReasonsAggregation('$discounts.nonFormulableByReason', ['descuento ccaf'])),
            add([
              sumByReasonsAggregation(
                '$retroactiveAmounts.forNetTotalNonFormulableAssetsByReason',
                ['haber ccaf']
              ),
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTaxableTotalNonFormulableAssetsByReason',
                ['haber ccaf']
              )
            ]),
            negate(
              sumByReasonsAggregation(
                '$retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
                ['descuento ccaf']
              )
            )
          ]
        }
      }
    }
  },
  {
    $project: {
      document: '$$ROOT',
      pensPP2102010001: negate(
        add([
          '$asigFam1106010002',
          '$asigFam1106010003',
          '$asigFam1106010001',
          '$pensiones5103020001',
          '$pensiones5103020004',
          '$pensiones5103030001',
          '$pensiones5103030002',
          '$pensiones5103030003',
          '$art531114040001',
          '$aguin5113010001',
          '$bonoLey5103010003',
          '$bonoViudez5103010004',
          '$bonoInv1106030004',
          '$devDsc1114020001',
          '$devDsc1106030001',
          '$anticipo1106030006',
          '$anticipo1114040003',
          '$descInd1114020002',
          '$subsCancExceso5101010004',
          '$aporteSalud1000000022',
          '$aporteSalud1000003765',
          '$aporteSalud1000005903',
          '$aporteSalud5000000165',
          '$retJudPens2102020008',
          '$ctaInstitucional2102020003',
          '$distrDePago5103020001'
        ])
      )
    }
  }
];

module.exports = aggregation;

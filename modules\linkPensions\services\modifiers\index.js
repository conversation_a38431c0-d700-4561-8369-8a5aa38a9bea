const setFields = require('./global');
const { setRetirement } = require('./retirement');
const { setWidowhood } = require('./widowhood');
const setEndDateByOrphanhood = require('./orphanhood');
const { setEndDateForLifePension } = require('./life');
const setNumberOfCharges = require('./numberOfCharges');

const modifyData = async (obj, pensionType, validityType) => {
  const resultGlobalFields = setFields(obj);
  const resultRetirement = setRetirement({ ...resultGlobalFields }, pensionType, validityType);
  const resultWidowhood = await setWidowhood(resultRetirement, pensionType, validityType);
  const resultNumberOfCharges = setNumberOfCharges(resultWidowhood, pensionType, validityType);
  const resultOrpanhood = setEndDateByOrphanhood(resultNumberOfCharges, pensionType, validityType);
  const resultLife = setEndDateForLifePension(resultOrpanhood, validityType);

  return {
    ...obj,
    ...resultGlobalFields,
    ...resultRetirement,
    ...resultWidowhood,
    ...resultNumberOfCharges,
    ...resultOrpanhood,
    ...resultLife
  };
};

module.exports = modifyData;

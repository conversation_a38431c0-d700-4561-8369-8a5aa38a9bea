{"name": "achs-pensiones-backend", "version": "0.1.0", "description": "api-control-visita", "author": "23people", "main": "index.js", "scripts": {"copy": "cpx -C \"{config,lib,models,scripts,routes,data-seed,workerManager,modules}/**/*\" dist | cpx \"./*.{js,json}\" dist", "commit": "git-cz", "lint": "eslint . --fix --ext .js --quiet", "sonarqube": "npm run test:cov && sonar-scanner", "test": "jest --ci -i --detect<PERSON><PERSON><PERSON><PERSON>les", "test:watch": "jest --watch", "test:cov": "jest --collectCoverage -i --detectO<PERSON>Handles", "test:debug": "node -r dotenv/config --inspect break node_modules/.bin/jest --runInBand", "start": "node -r dotenv/config index.js", "start:debugging": "DEBUG='agenda:*' node -r dotenv/config index.js", "agendash": "agendash --db=$MONGODB_DB_URL --collection=agendaJobs --port=3001", "build": "npm run copy", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"@sendgrid/mail": "^7.7.0", "adm-zip": "^0.4.14", "agenda": "3.1.0", "axios": "^0.19.2", "basic-ftp": "^4.6.6", "bcrypt": "^5.0.1", "btoa": "^1.2.1", "circular-json": "^0.5.9", "cls-hooked": "^4.2.2", "connect-mongo": "^4.6.0", "construx": "^1.0.1", "construx-copier": "^1.0.0", "cors": "^2.8.5", "deep-object-diff": "^1.1.0", "dotenv": "^8.6.0", "excel4node": "^1.7.2", "exceljs": "^4.2.1", "express": "^4.18.1", "express-session": "^1.17.3", "express-validator": "^6.14.2", "form-data": "^4.0.0", "git-cz": "^4.9.0", "helmet": "^4.6.0", "jest-junit": "^13.2.0", "jsonwebtoken": "^8.5.1", "kraken-js": "^2.2.0", "line-reader": "^0.4.0", "mailgun-js": "^0.22.0", "moment": "^2.29.4", "moment-business-days": "^1.2.0", "mongo-seeding": "^3.7.2", "mongoose": "^6.4.4", "passport": "^0.4.1", "passport-azure-ad": "^4.3.3", "puppeteer": "^4.0.0", "ramda": "^0.27.1", "split": "^1.0.1", "ssh2-sftp-client": "^5.3.2", "strong-soap": "^2.0.0", "text-table": "^0.2.0", "tmp": "^0.1.0", "uuid": "^7.0.3", "validator": "^13.6.0", "winston": "^3.8.1", "winston-mongodb": "^5.0.7", "xlsx": "^0.16.9"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@shelf/jest-mongodb": "^1.2.5", "agendash": "^1.0.0", "chai": "^4.3.4", "commitizen": "^4.2.5", "cpx": "^1.5.0", "cz-conventional-changelog": "^3.1.0", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^23.6.0", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.30.1", "husky": "^6.0.0", "jest": "^24.9.0", "jest-sonar-reporter": "^2.0.0", "lint-staged": "^10.5.4", "moxios": "^0.4.0", "prettier": "^1.19.1", "sonar-scanner": "^3.1.0"}, "eslintConfig": {"env": {"es6": true, "node": true, "jest/globals": true, "browser": true}, "extends": ["airbnb", "prettier", "plugin:prettier/recommended"], "plugins": ["prettier", "jest"]}, "eslintIgnore": ["build", "dist", "node_modules"], "prettier": {"printWidth": 100, "semi": true, "singleQuote": true}, "lint-staged": {"*.{html,json,md,yml}": ["prettier --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}
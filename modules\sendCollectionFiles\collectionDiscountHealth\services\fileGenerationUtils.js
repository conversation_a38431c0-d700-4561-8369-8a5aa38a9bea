const moment = require('moment');
const { appendFile } = require('fs');
const util = require('util');
const tmp = require('tmp');

const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');
const { compressFile } = require('../../../sharedFiles/helpers');
const {
  getPensionerRUT,
  getPensionerLastName,
  getPensionerMothersLastName,
  getPensionerName
} = require('../../helpers');

const getFileName = (tempLib = tmp) =>
  `${tempLib.dirSync().name}/rsalud${moment().format('YYYYMM')}.703601006`;
const getZipFileName = () =>
  `rsalud.703601006.ips_${moment().format('YYMM')}_${moment().format('YYMMDD')}_${moment().format(
    'HHmm'
  )}.zip`;

const DISABILITY_PENSION_CODE = '07';
const SURVIVAL_PENSION_CODE = '08';
const DISABILITY_PENSION_TYPES = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const isDisabilityPension = pension => {
  const { pensionType } = pension;
  return DISABILITY_PENSION_TYPES.some(regex => pensionType.match(regex));
};

const getPeriods = () => `${moment().format('YYYYMM')}${moment().format('YYYYMM')}01`;
const getRejectedPensionPeriods = ({ createdAt }) =>
  `${moment().format('YYYYMM')}${moment(createdAt).format('YYYYMM')}01`;

const getPensionTypeCode = pension =>
  isDisabilityPension(pension) ? DISABILITY_PENSION_CODE : SURVIVAL_PENSION_CODE;

const getNetPension = pension => {
  const AMOUNT_MAX_LENGTH = 8;
  const fields = ['basePension', 'article40', 'law19403', 'law19539', 'law19953', 'assets.aps'];
  const amount = recursiveSum(pension, fields);
  return String(Math.round(roundValue(amount)))
    .padStart(AMOUNT_MAX_LENGTH, '0')
    .substring(0, AMOUNT_MAX_LENGTH);
};

const getHealthBonus = pension => {
  const { rejectionHealthReductionAmount = 0, createdAt } = pension;
  const AMOUNT_MAX_LENGTH = 8;
  const percentage = 0.07;
  const fields = ['basePension', 'article40', 'law19403', 'law19539', 'law19953', 'assets.aps'];
  const amount = recursiveSum(pension, fields);
  const healthBonus = String(Math.round(roundValue(percentage * amount))).padStart(
    AMOUNT_MAX_LENGTH,
    '0'
  );
  if (moment(createdAt).format('YYYYMM') === moment().format('YYYYMM')) return healthBonus;
  return String(rejectionHealthReductionAmount).padStart(AMOUNT_MAX_LENGTH, '0');
};

const getEntityRut = () => '703601006';

const getLine = ({ pension, isRejectedPension }) => {
  const lineData = [];
  [
    isRejectedPension ? getRejectedPensionPeriods : getPeriods,
    getEntityRut,
    getPensionerRUT,
    getPensionerLastName,
    getPensionerMothersLastName,
    getPensionerName,
    getPensionTypeCode,
    getHealthBonus,
    getNetPension
  ].forEach(fn => lineData.push(fn(pension)));

  return `${lineData.join('')}\n`;
};

module.exports = { getLine, getFileName, getZipFileName, compressFile, appendFile, util };

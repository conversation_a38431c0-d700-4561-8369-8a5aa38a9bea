/* eslint-disable no-restricted-globals */
const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');
const {
  validators,
  decimalValidator,
  rutValidator,
  heavyDutyValidator,
  integerValidator,
  basePensionValidator,
  isNumeric,
  isDate
} = require('../validators');

const { Schema } = mongoose;

const totalsByReasonSchema = new Schema({
  amount: { type: Number, default: 0 },
  reason: { type: String, default: '', lowercase: true }
});

const TemporaryPensionSchema = new Schema(
  {
    basePension: {
      type: Number,
      min: 10000.0,
      max: ********.99,
      required: true,
      validate: basePensionValidator
    },
    country: { type: String, required: true },
    transient: { type: String, required: true },
    cun: { type: String },
    initialBasePension: {
      type: Number,
      min: 10000.0,
      max: ********.99,
      required: true,
      validate: basePensionValidator
    },
    dateOfBirth: { type: Date, required: true },
    gender: { type: String, enum: ['M', 'F'], required: true, trim: true },
    afpAffiliation: { type: String },
    manuallyReactivated: { type: Boolean, default: false },
    healthAffiliation: { type: String },
    linkedDate: { type: Date },
    paymentInfo: {
      paymentGateway: { type: String, maxlength: 60 },
      accountNumber: { type: String, maxlength: 30 },
      bank: { type: String, maxlength: 60 },
      branchOffice: { type: String, maxlength: 80, default: '' }
    },
    retirementAge: { type: Number, min: 18, max: 120 },
    causant: {
      rut: { type: String, minlength: 8, maxlength: 10, required: true },
      name: { type: String, maxlength: 60, required: true },
      lastName: { type: String, maxlength: 60, required: true },
      mothersLastName: { type: String, maxlength: 60 }
    },
    collector: {
      rut: { type: String, minlength: 8, maxlength: 10, required: true },
      name: { type: String, maxlength: 60, required: true },
      lastName: { type: String, maxlength: 60, required: true },
      mothersLastName: { type: String, maxlength: 60 },
      address: { type: String, maxlength: 1000 },
      commune: { type: String, maxlength: 1000 },
      city: { type: String, maxlength: 1000 }
    },
    beneficiary: {
      rut: { type: String, minlength: 8, maxlength: 10, required: true },
      name: { type: String, maxlength: 60, required: true },
      lastName: { type: String, maxlength: 60, required: true },
      mothersLastName: { type: String, maxlength: 60 },
      email: { type: String, maxlength: 120 },
      phone: { type: String, maxlength: 20, default: '' }
    },
    validityType: { type: String, maxlength: 160, required: true },
    pensionType: { type: String, required: true },
    ChangeOfPensionTypeDueToCharges: { type: Boolean },
    disabilityDegree: { type: Number, min: 0, max: 999, required: true },
    disabilityType: { type: String, maxlength: 20, trim: true },
    resolutionNumber: { type: Number, min: 0, required: true },
    accidentNumber: { type: Number, min: 0, required: true },
    numberOfCharges: { type: Number, min: 0, default: 0 },
    resolutionDate: { type: Date, required: true },
    disabilityStartDate: { type: Date },
    accidentDate: { type: Date, required: true },
    pensionCodeId: { type: String, required: true },
    institutionalPatient: { type: Boolean, default: false },
    validatedStudyPeriod: { type: String, maxlength: 160 },
    inactivationReason: { type: String },
    article40: {
      type: Number,
      trim: true,
      required: true
    },
    article41: {
      type: Number,
      default: 0,
      validate: isNumeric
    },
    fixedBasePension: { type: Number },
    fixedArticle40: { type: Number },
    fixedArticle41: { type: Number },
    inactivationDate: { type: Date },
    marriageDate: { type: Date },
    pensionStartDate: {
      type: Date,
      required: true,
      validate: isDate
    },
    deathDate: { type: Date },
    endDateOfValidity: { type: Date },
    endDateOfTheoricalValidity: { type: Date },
    familyGroup: { type: Number, min: 1, validate: integerValidator, default: 1 },
    increasingInLaw19578: { type: Number, validate: decimalValidator, default: 0 },
    increasingInLaw19953: { type: Number, validate: decimalValidator, default: 0 },
    increasingInLaw20102: { type: Number, validate: decimalValidator, default: 0 },
    basePensionWithoutIncreases: {
      type: Number,
      default: 0,
      validate: decimalValidator
    },
    heavyDuty: {
      type: String,
      minlength: 0,
      validate: heavyDutyValidator,
      trim: true
    },
    parentRUT: {
      type: String,
      maxlength: 10,
      validate: rutValidator
    },
    reservedAmounts: {
      assetsNonFormulableTaxableTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      assetsNonFormulableNetTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      discountsNonFormulableTotalsByReason: { type: [totalsByReasonSchema], default: [] },
      forSurvival: { type: Number, min: 0, default: 0 },
      forDisability: { type: Number, min: 0, default: 0 },
      forInstitutionalPatient: { type: Number, min: 0, default: 0 },
      forRejection: { type: Number, min: 0, default: 0 },
      forBasePension: { type: Number, min: 0, default: 0 },
      forArticle40: { type: Number, min: 0, default: 0 },
      forArticle41: { type: Number, min: 0, default: 0 },
      forTaxableTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forTotalNonFormulableDiscounts: { type: Number, min: 0, default: 0 },
      forBonuses: { type: Number, min: 0, default: 0 },
      forPayCheck: { type: Number, default: 0 }
    },
    assets: {
      aps: { type: Number, min: 0, default: 0 },
      healthDiscount: { type: String, default: 'No' },
      healthExemption: { type: String, default: 'No' },
      forFamilyAssignment: { type: Number, min: 0, default: 0 },
      marriageBonus: { type: Number, min: 0, default: 0 },
      christmasBonus: { type: Number, min: 0, default: 0 },
      nationalHolidaysBonus: { type: Number, min: 0, default: 0 },
      winterBonus: { type: Number, min: 0, default: 0 },
      taxableTotalNonFormulable: { type: Number, min: 0, default: 0 },
      rebsal: { type: Number, min: 0, default: 0 },
      adjustedHealthExemption: { type: Number, min: 0, default: 0 },
      netTotalNonFormulable: { type: Number, min: 0, default: 0 },
      netNonFormulableByReason: [{ type: String, amount: Number }],
      taxableNonFormulableByReason: [{ type: String, amount: Number }]
    },
    retroactiveAmounts: {
      forSurvival: { type: Number, min: 0, default: 0 },
      forDisability: { type: Number, min: 0, default: 0 },
      forInstitutionalPatient: { type: Number, min: 0, default: 0 },
      forRejection: { type: Number, min: 0, default: 0 },
      forBonuses: { type: Number, min: 0, default: 0 },
      forTaxableTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssets: { type: Number, min: 0, default: 0 },
      forTotalNonFormulableDiscounts: { type: Number, min: 0, default: 0 },
      forBasePension: { type: Number, min: 0, default: 0 },
      forArticle40: { type: Number, min: 0, default: 0 },
      forArticle41: { type: Number, min: 0, default: 0 },
      forFamilyAssignment: { type: Number, min: 0, default: 0 },
      forNetTotalNonFormulableAssetsByReason: {
        type: [
          {
            amount: { type: Number, min: 0, default: 0 },
            reason: { type: String, lowercase: true }
          }
        ],
        default: []
      },
      forTaxableTotalNonFormulableAssetsByReason: {
        type: [
          {
            amount: { type: Number, min: 0, default: 0 },
            reason: { type: String, lowercase: true }
          }
        ],
        default: []
      },
      forTotalNonFormulableDiscountsByReason: {
        type: [
          {
            amount: { type: Number, min: 0, default: 0 },
            reason: { type: String, lowercase: true }
          }
        ],
        default: []
      }
    },

    discounts: {
      type: Schema.Types.Mixed,
      validate: validators,
      default: {
        onePercentLaAraucana: 'No',
        socialCreditsLaAraucana: 0,
        onePercent18: 'No',
        socialCredits18: 0,
        onePercentLosAndes: 'No',
        socialCreditsLosAndes: 0,
        othersLosAndes: 0,
        onePercentLosHeroes: 'No',
        socialCreditsLosHeroes: 0,
        othersLosHeroes: 0,
        healthLoan: 0,
        health: 0,
        afp: 0,
        onePercentAdjusted: 0,
        totalNonFormulable: 0,
        nonFormulableByReason: [{}]
      },
      healthUF: {
        type: Number,
        min: 0,
        required: true
      }
    },
    enabled: {
      type: Boolean,
      default: true
    },
    maritalStatus: { type: String, maxlength: 1, default: 'S' },
    otherPension: { type: String, maxlength: 2, default: 'No' },
    regimenOtherPension: { type: Number, default: 0 },
    startAnotherPension: { type: Date },
    amountOtherPension: { type: Number, default: 0 },
    baseIncome: { type: Number, default: 0 },
    retroactiveConstitution: {
      totalPensionAccrued: { type: Number, min: 0, default: 0 },
      indemnityDiscount: { type: Number, min: 0, default: 0 },
      strennaRetroConstitution: { type: Number, min: 0, default: 0 },
      otherLink: { type: Number, min: 0, default: 0 }
    },
    dl1026: { type: Number, default: 0 },
    retirement: { type: Boolean, default: false },
    totalEstimatedDaysToPay: { type: Number, default: 0 }
  },
  { timestamps: true }
);

TemporaryPensionSchema.plugin(paginate);
TemporaryPensionSchema.index({ idCode: 1 });
TemporaryPensionSchema.index({ beneficiary: 1 });

module.exports = mongoose.model('TemporaryPension', TemporaryPensionSchema);

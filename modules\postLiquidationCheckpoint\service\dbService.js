const pensionsService = require('../../pensions/services/pension.service');
const pensionService = require('../../pensions/services/pension.service');
const liquidationService = require('../../liquidation/services/liquidation.service');
const afpService = require('../../nomenclators/afp/services/index.service');
const ufService = require('../../UFvalue/services/ufValue.service');
const { roundValue, recursiveSum, recursiveCount } = require('../../sharedFiles/helpers');
const {
  netPension: calculateNetPension
} = require('../../netPensionsLiquidationReports/netPensions/services/dbService');
const cajaService = require('../../nomenclators/cajaCompensacion/services/index.service');

const VALIDITY_TYPE = /No\s+vigente/i;
const percentage = 0.25;
const discountsPath = [
  'othersLosHeroes',
  'othersLosAndes',
  'socialCreditsLaAraucana',
  'socialCredits18',
  'socialCreditsLosHeroes',
  'socialCreditsLosAndes'
];

const approvedUpdater = ({ _id, liquidation, ...p }) => ({ ...p, checkPoint: true });
const approvesCheckPoint = liquidation => {
  return liquidation.netPension >= roundValue(percentage * liquidation.taxablePension);
};

const getDifferenceAndTotalDiscounts = ({ discounts, liquidation }) => {
  const { netPension, taxablePension } = liquidation;
  const difference = roundValue(percentage * taxablePension) - netPension;
  const totalDiscounts = recursiveSum(discounts, discountsPath);
  return { difference, totalDiscounts };
};

const getPensionsToUpdate = pensions => {
  return pensions.filter(pension => {
    const { difference, totalDiscounts } = getDifferenceAndTotalDiscounts(pension);
    return totalDiscounts >= difference;
  });
};

const decreaseDiscounts = ({ _id, ...pension }) => {
  const { discounts } = pension;
  const { difference, totalDiscounts } = getDifferenceAndTotalDiscounts(pension);
  const quantityOfDiscounts = recursiveCount(discounts, discountsPath);
  return discountsPath.reduce((acc, key) => {
    if (discounts[key] && quantityOfDiscounts === 1) {
      acc[key] = roundValue(discounts[key] - difference);
    } else {
      acc[key] = roundValue(discounts[key] - (discounts[key] * difference) / totalDiscounts);
    }
    return acc;
  }, {});
};

const updateDiscounts = pension => {
  const updatedDiscounts = decreaseDiscounts(pension);
  const { _id, liquidation, ...data } = pension;
  return { ...data, discounts: { ...pension.discounts, ...updatedDiscounts } };
};

const service = {
  async getCheckPointPensions() {
    const queryObj = { enabled: true, validityType: { $not: VALIDITY_TYPE } };
    const selectedFields = {
      beneficiary: 1,
      causant: 1,
      enabled: 1,
      discounts: 1,
      assets: 1,
      pensionType: 1,
      afpAffiliation: 1,
      retroactiveAmounts: 1,
      validityType: 1,
      pensionCodeId: 1
    };
    const { result, error } = await pensionsService.getPensionsFieldsWithLiquidations(
      queryObj,
      selectedFields
    );
    if (error) throw new Error(error);
    const failedCheckPointPensions = result.filter(p => !approvesCheckPoint(p.liquidation));
    const approvedCheckPointPensions = result.filter(p => approvesCheckPoint(p.liquidation));
    return { failedCheckPointPensions, approvedCheckPointPensions };
  },

  async updateCheckPointPensions() {
    try {
      const pensions = await this.getCheckPointPensions();
      const { failedCheckPointPensions, approvedCheckPointPensions } = pensions;
      const pensionsToUpdateDiscounts = getPensionsToUpdate(failedCheckPointPensions);
      const updatedFailedPensions = pensionsToUpdateDiscounts.map(updateDiscounts);
      const updatedApprovedPensions = approvedCheckPointPensions.map(approvedUpdater);
      const { completed, error } = await pensionService.createUpdatePension(updatedFailedPensions);
      const result = await pensionService.updatePensions(updatedApprovedPensions);
      return {
        completed: completed && result.completed,
        error: result.error || error,
        updatedFailedPensions
      };
    } catch (error) {
      return { completed: false, error, updatedFailedPensions: null };
    }
  },

  async reevaluateCheckPointPensions() {
    const pensions = await this.getCheckPointPensions();
    const { failedCheckPointPensions } = pensions;
    const updatedPensions = failedCheckPointPensions.map(({ _id, liquidation, ...p }) => ({
      ...p,
      checkPoint: false
    }));
    const { error } = await pensionService.updatePensions(updatedPensions);
    if (error) throw new Error(`reevaluateCheckPointPensions() => ${error}`);
    return updatedPensions;
  },

  async recalculateNetPension(pensions) {
    const { completed, error } = await calculateNetPension(
      pensionService,
      liquidationService,
      afpService,
      ufService,
      cajaService,
      pensions
    );
    return { completed, error };
  }
};

module.exports = service;

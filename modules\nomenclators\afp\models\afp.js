const mongoose = require('mongoose');

const { Schema } = mongoose;

const AfpsSchema = new Schema(
  {
    id: { type: String, required: true },
    name: { type: String, maxlength: 255, required: true },
    rut: { type: String, maxlength: 12, required: true },
    percentage: { type: Number, maxlength: 5, required: true },
    code: { type: String, minlength: 2, maxlength: 2, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);
AfpsSchema.index({ id: 1 }, { unique: true });
AfpsSchema.index({ code: 1 }, { unique: true });
AfpsSchema.index({ rut: 1 }, { unique: true });

module.exports = mongoose.model('Afps', AfpsSchema);

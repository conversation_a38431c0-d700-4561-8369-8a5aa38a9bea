const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services');
const TemporaryFamilyAssignmentService = require('../../familyAssignment/services/assignment.service');
const workerModule = require('./worker');

module.exports = {
  name: 'inactivateOrReactivateFamilyAssignmentProcess',
  worker: deps =>
    workerModule.workerFn({ TemporaryFamilyAssignmentService, logService, service, ...deps }),
  description:
    'Cron unificado para actualizar cargas familiares e inactivar orfandad por certificado vencido',
  endPoint: 'inactivateorreactivateprocess',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.cronDependency
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const { marriagePostworker } = require('.');
const ProcessedJobModel = require('../../../sharedFiles/models/processedJob');

describe('Cron Inactivate Marriage Pension', () => {
  beforeAll(beforeAllTests);
  let service;
  let done;
  let Logger;
  let logService;

  beforeEach(() => {
    done = jest.fn();
    service = {
      getAllMarriagePensionToInactivate: jest.fn().mockResolvedValue({
        result: [
          {
            _id: '5e32e3f7e16de8c95b6ec40e',
            enabled: true,
            pension: '5e502569b20a4257fc0ece70',
            inactivationReason: 'Matrimonio',
            endDateOfValidity: '2020-02-10T03:00:00.000Z',
            dateToInactivate: '2020-03-01T03:00:00.000Z',
            createdAt: '2020-02-21T18:46:01.109Z',
            updatedAt: '2020-02-21T18:46:01.109Z',
            __v: 0
          }
        ],
        isError: false
      }),
      createUpdateMarriagePension: jest.fn()
    };
    Logger = { error: jest.fn(), info: jest.fn() };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });
  it('success inactivate marriage pension', async () => {
    service.createUpdateMarriagePension = jest.fn(() =>
      Promise.resolve({ completed: true, inactivationError: false, hasError: false })
    );

    await marriagePostworker.worker({
      Logger,
      service,
      done
    });
    const { completed, inactivationError } = await service.createUpdateMarriagePension();

    expect(completed).toBe(true);
    expect(inactivationError).toBe(false);
  });

  it('error inactivate marriage pension', async () => {
    service.createUpdateMarriagePension = jest.fn(() =>
      Promise.resolve({ inactivationError: new Error('error') })
    );
    await marriagePostworker.worker({
      Logger,
      service,
      done,
      logService
    });

    const { inactivationError } = await service.createUpdateMarriagePension();

    expect(inactivationError).toBeDefined();
    expect(Logger.error).toHaveBeenCalledTimes(3);
  });

  it('error GetAll inactivate marriage pension', async () => {
    service.createUpdateMarriagePension = jest.fn(() =>
      Promise.resolve({ completed: false, inactivationError: true })
    );

    await marriagePostworker.worker({
      Logger,
      service,
      done
    });

    const { inactivationError } = await service.createUpdateMarriagePension();

    expect(inactivationError).toBe(true);
  });

  it('throw error ', async () => {
    service.createUpdateMarriagePension = jest.fn(() =>
      Promise.resolve({ inactivationError: new Error('error') })
    );
    await marriagePostworker.worker({ Logger, service, logService, done });
    expect(Logger.error).toHaveBeenCalled();
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const TemporaryInactivationPension = require('../models/temporaryInactivationPension');
const service = require('./temporaryPensionService');

const resource = require('../../../resources/historicalInactivationPensions.json');

describe('Temporary Historical pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(TemporaryInactivationPension, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('success create historic Pension', async () => {
    const { completed, error } = await service.createTemporaryPensions(
      resource,
      TemporaryInactivationPension
    );
    expect(error).toBe(null);
    expect(completed).toBe(true);
  });

  it('should throw an error', async () => {
    jest
      .spyOn(TemporaryInactivationPension, 'deleteMany')
      .mockImplementationOnce(() => Promise.reject(new Error('error')));

    const { completed, error } = await service.createTemporaryPensions(
      resource,
      TemporaryInactivationPension
    );
    expect(error).toBeDefined();
    expect(completed).toBe(false);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await TemporaryInactivationPension.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

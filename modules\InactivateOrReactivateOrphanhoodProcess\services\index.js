const inactivateOrphanhoodService = require('../../inactivatePensions/orphanhood/services/orphanhood.service');
const Logger = require('../../../lib/logger');

module.exports = {
  async inactivationReactivationProcess() {
    try {
      await inactivateOrphanhoodService.inactivate(Logger);
      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const {
  percentageMatchRule,
  isPercent,
  validFormat,
  rutMatchRule,
  isValidRut,
  rutFormatter,
  codeRule,
  codeFormatter
} = require('./validator');

describe('Afps validators service Test', () => {
  beforeAll(beforeAllTests);

  it('should validate rut', () => {
    const validRut = '16.940.573-5';
    const invalidRut = '16.KK.9-7LLL';
    const emptyRut = '' || null;
    const numericValidRut = 169405735;
    expect(isValidRut(validRut)).toBe(true);
    expect(isValidRut(invalidRut)).toBe(false);
    expect(isValidRut(emptyRut)).toBe(false);
    expect(isValidRut(numericValidRut)).toBe(false);
  });
  it('should format rut', () => {
    const unformattedRut1 = '169405735';
    const unformattedRut2 = '1456hhjhk';
    const unformattedRut3 = 'asdfadfdsg$&';
    const unformattedRut4 = 169405735;
    const zeroStartRut = '00.000.000-0';
    const ksInTheRut = '16.7kK.789-9';
    expect(rutFormatter(unformattedRut1)).toBe('16940573-5');
    expect(rutFormatter(unformattedRut2)).toBe('1456-K');
    expect(rutFormatter(unformattedRut3)).toBe('');
    expect(rutFormatter(unformattedRut4)).toBe('16940573-5');
    expect(rutFormatter(zeroStartRut)).toString('');
    expect(rutFormatter(ksInTheRut)).toBe('167789-9');
  });

  it('should format and validate a rut', () => {
    const ruled1 = '169405735';
    const ruled2 = '1456hhjhk';
    const ruled3 = 'asdfadfdsg$&';
    const ruled4 = 169405735;
    const zeroStartRut = '00.000.000-0';
    const ksInTheRut = '16.7kK.789-9';
    expect(rutMatchRule(ruled1)).toBe(true);
    expect(rutMatchRule(ruled2)).toBe(false);
    expect(rutMatchRule(ruled3)).toBe(false);
    expect(rutMatchRule(ruled4)).toBe(true);
    expect(rutMatchRule(zeroStartRut)).toBe(false);
    expect(rutMatchRule(ksInTheRut)).toBe(false);
  });

  it('should validate percent', () => {
    const validPercent = '99.98';
    const invalidPercent = 0.99;
    const emptyPercent = '' || null;
    const numericValidPercent = 99.99;
    const alfanumericNotValid = 'asdf45.6';
    const bigNumberToValid = 99999999999999999999999999999.99;
    expect(isPercent(validPercent)).toBe(true);
    expect(isPercent(invalidPercent)).toBe(false);
    expect(isPercent(emptyPercent)).toBe(false);
    expect(isPercent(numericValidPercent)).toBe(true);
    expect(isPercent(alfanumericNotValid)).toBe(false);
    expect(isPercent(bigNumberToValid)).toBe(false);
  });

  it('should return to valid format percentage', () => {
    const percent1 = '1789';
    const percent2 = '1456hhjhk';
    const percent3 = 'asdfadfdsg$&';
    const percent4 = 133;
    const empty = 0.0 || '' || null;
    expect(validFormat(percent1)).toBe('17.89');
    expect(validFormat(percent2)).toBe('14.56');
    expect(validFormat(percent3)).toBe('');
    expect(validFormat(percent4)).toBe('13.3');
    expect(validFormat(empty)).toBe('');
  });

  it('should format and validate a percentage', () => {
    const ruled1 = '10.00';
    const ruled2 = '100.00';
    const ruled3 = '99';
    const ruled4 = 10.01;
    const zero = '00.00';
    const words = '10.aZ';
    const biggerValue = '999';
    expect(percentageMatchRule(ruled1)).toBe('');
    expect(percentageMatchRule(ruled2)).toBe('');
    expect(percentageMatchRule(ruled3)).toBe('99');
    expect(percentageMatchRule(ruled4)).toBe('10.01');
    expect(percentageMatchRule(zero)).toBe('');
    expect(percentageMatchRule(words)).toBe('');
    expect(percentageMatchRule(biggerValue)).toBe('');
  });

  it('should format a 2 digit code', () => {
    const ruled1 = '10';
    const ruled2 = 'XX';
    const ruled3 = '0a0aaaa';
    const ruled4 = 'anything$%&00';
    expect(codeFormatter(ruled1)).toBe('10');
    expect(codeFormatter(ruled2)).toBe('');
    expect(codeFormatter(ruled3)).toBe('00');
    expect(codeFormatter(ruled4)).toBe('00');
  });
  it('should validate a 2 digit code', () => {
    const ruled1 = '10';
    const ruled2 = '000000';
    const ruled3 = 10;
    const ruled4 = 'anything$%&';
    expect(codeRule.test(ruled1)).toBe(true);
    expect(codeRule.test(ruled2)).toBe(true);
    expect(codeRule.test(ruled3)).toBe(true);
    expect(codeRule.test(ruled4)).toBe(false);
  });

  afterEach(async () => {});

  afterAll(afterAllTests);
});

const moment = require('moment');
const { getMonthHolidays, getFirstNbusinessDays } = require('../../sharedFiles/helpers');

async function isActionAllowedOnCurrentDate(_date, totalDays, getMonthHolidaysFn) {
  const Sunday = 0;
  const Saturday = 6;

  let todayDate = moment(_date);
  if (todayDate.day() === Sunday) {
    todayDate = todayDate.add(1, 'days');
  }
  if (todayDate.day() === Saturday) {
    todayDate = todayDate.add(2, 'days');
  }
  todayDate = todayDate.format('YYYY-MM-DD');
  const firstNworkingDays = await getFirstNbusinessDays(_date, totalDays, getMonthHolidaysFn);
  const actionIsAllowed = firstNworkingDays.some(d =>
    moment(new Date(d)).isSame(moment(new Date(todayDate)))
  );
  const lastInRangeDay = moment(new Date(firstNworkingDays.pop()));
  const currentDay = moment(new Date(todayDate));
  const inDayRange = lastInRangeDay.diff(currentDay, 'days') >= 0;

  return { actionIsAllowed, inDayRange, totalDays };
}

async function isActionAllowedOnCurrentMonth(_date) {
  const totalDays = moment().daysInMonth();
  const todayDate = moment(_date);
  const lastInRangeDay = moment(_date).endOf('month');
  const currentDay = moment(new Date(todayDate));
  const actionIsAllowed = lastInRangeDay.diff(currentDay, 'days') >= 0;

  return { actionIsAllowed, totalDays };
}

module.exports = {
  isActionAllowedOnCurrentMonth,
  isActionAllowedOnCurrentDate,
  getFirstNbusinessDays,
  getMonthHolidays
};

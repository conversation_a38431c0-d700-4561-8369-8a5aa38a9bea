/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      calculateCurrentCapital: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    logService = {
      existsLog: jest.fn(() => true).mockImplementationOnce(() => false),
      saveLog: jest.fn(() => Promise.resolve()),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      hasDataInTable: jest.fn(() => Promise.resolve(true)),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {
      createUpdatePension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.saveLog).toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject(new Error('erro')));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail calculateCurrentCapital', async () => {
    service.calculateCurrentCapital = jest.fn(() => Promise.resolve({ error: 'error' }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(service.calculateCurrentCapital).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependencies have not yet run', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    logService.allMarksExists = jest.fn(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(service.calculateCurrentCapital).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('mark has run in this month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.calculateCurrentCapital).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('concurrency and factor have not yet executed ', async () => {
    logService.hasDataInTable = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(0);
  });

  afterAll(afterAllTests);
});

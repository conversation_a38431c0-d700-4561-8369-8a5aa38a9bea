/* eslint-disable no-console */
const xslx = require('xlsx');
const R = require('ramda');
const fs = require('fs');

const formatters = require('./formatters');
const excelFields = require('./mappingExcelFields');

const FIRST_ROW = 1;

const WarningIfColumnsDoesNotMatch = (numberOfColumns, sheet) => {
  Array.from({ length: numberOfColumns }, (v, index) => index).forEach((v, index) => {
    const excelMappedName = excelFields[index] && excelFields[index].nameInXlsx;
    const value = sheet[`${xslx.utils.encode_col(index)}${FIRST_ROW}`];
    if (value.v !== excelMappedName) {
      console.log(
        `columna |${value.v}| es distinta a la especificada por script |${excelMappedName}|, posición ${index}`
      );
    }
  });
};

const readXLSXSheet = (workBookFilePath, sheetName) => {
  const woorkbook = xslx.readFile(workBookFilePath);
  return woorkbook.Sheets[sheetName];
};

const getSheetDimensions = excelSheet => {
  const { s: start, e: ending } = xslx.utils.decode_range(excelSheet['!ref']);
  const nColumns = ending.c - start.c + 1;
  const nRows = ending.r - start.r;
  console.log(`rows: ${nRows}, columns: ${nColumns}`);
  return { nColumns, nRows };
};

const createStream = filename =>
  fs.createWriteStream(filename, {
    flags: 'a',
    encoding: 'utf8'
  });

const excludedFields = ['marriageDate', 'discountsAndAssets'];

// construye el json para cada pensionado
const buildPensionerObject = (rowIndex, excelSheet) =>
  excelFields.reduce(
    (obj, { path, type, defaultValue, minValue }, index) => {
      const rowExcel = rowIndex + 2;
      const value = excelSheet[`${xslx.utils.encode_col(index)}${rowExcel}`];

      const isFieldAdded =
        ((value && !!value.v) || defaultValue !== undefined) && !excludedFields.includes(path);

      if (!isFieldAdded) return { ...obj };

      const newObj = R.assocPath(
        path.split('.'),
        formatters[type]({ defaultValue, minValue, value }),
        obj
      );
      return { ...newObj };
    },
    { enabled: true }
  );

const writePensionersToStream = (rows, writeStream, excelSheet) => {
  const numberOfIterations = Array.from({ length: rows }, (v, i) => i);
  numberOfIterations.forEach((v, index) => {
    const pensionerObj = buildPensionerObject(index, excelSheet);
    writeStream.write(`${JSON.stringify(pensionerObj)}\n`);
  });
  writeStream.close();
};

const fileToSavePensioners = 'newEncryptation.txt';
const workbookPath = './BBDD Historica 202104.xlsx';
const sheetName = 'Migracion_202104';

const selectedSheet = readXLSXSheet(workbookPath, sheetName);
const { nColumns, nRows } = getSheetDimensions(selectedSheet);
WarningIfColumnsDoesNotMatch(nColumns, selectedSheet);
const pensionerStream = createStream(fileToSavePensioners);
writePensionersToStream(nRows, pensionerStream, selectedSheet);

const assignNationalHolidaysBonus = require('../../assignNationalHolidaysBonus/workers');
const assignChristmasBonus = require('../../assignChristmasBonus/workers');
const inactivateByTransient = require('../../inactivatePensions/transients/workers');
const inactivateByDeath = require('../../inactivatePensions/death/workers');
const toCivilRegistration = require('../../pensions/workers');
const inactivateByAgeLimit = require('../../inactivatePensions/ageLimit/workers');
const inactivateByMarriage = require('../../inactivatePensions/marriage/workers');
const inactivateByRetirement = require('../../inactivatePensions/retirement/workers');
const reactivatePensionsByTransients = require('../../reactivatePensions/transients/workers');
const ipc = require('../../ipc/workers');
const basePensionWorker = require('../../basePension/workers');
const taxablePension = require('../../taxablePensions/workers');
const setZeroValuesPensionWorker = require('../../resetValuePensions/workers');
const daysOfTranstientPension = require('../../transients/workers');
const netPensionsLiquidationReports = require('../../netPensionsLiquidationReports/workers');
const obtainUfValue = require('../../UFvalue/workers');
const calculatePaymentDatesWorker = require('../../paymentDate/workers');
const historicalPensionReports = require('../../historicPensions/workers');
const generateAndUploadPreviredFile = require('../../fileUpload/previred/workers');
const {
  setFixedBasePensionAndArticlesValue,
  resetFixedBasePensionAndArticlesValue
} = require('../../fixedValues/workers');
const reajustBasePensionAndArticles = require('../../reajustBasePensionAndArticles/workers');
const unifiedBulkLoadAndIps = require('../../bulkLoad/unifiedBulkLoadAndIpsCrons/workers');
const calculateDaysToPayWorker = require('../../calculateDaysToPay/workers');
const inactivateOrReactivateProcess = require('../../InactivateOrReactivateProcess/workers');
const InactivateOrReactivateOrphanhoodProcess = require('../../InactivateOrReactivateOrphanhoodProcess/workers');
const schedulingCronjobs = require('../../scheduleCronExecution/workers');
const monthlyExpenses = require('../../monthlyExpenses/workers');
const widowhoodPayment = require('../../widowhoodPayment/worker');

const postLiquidationCheckpoint = require('../../postLiquidationCheckpoint/workers');
const generateAndUploadBankFile = require('../../unifiedGenerateAndUploadBankFileCrons/workers');
const reservedAmountAssetsDiscounts = require('../../reservedAmountOfNonFormulableAssetsDiscountsTotal/worker');
const keyBuilder = require('../../keyBuilder/workers');
const calculateCurrentCapital = require('../../calculateCurrentCapital/workers');
const socialDiscountsCheckPoint = require('../../socialDiscountsCheckpoint/workers');
const analysisOfCurrentCapital = require('../../analysisOfCurrentCapital/workers');
const unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport = require('../../unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCron/workers');
const updatePensionerInfo = require('../../queryPensions/worker');
const healthRejection = require('../../healthRejection/workers');
const collectionDiscountHealth = require('../../sendCollectionFiles/collectionDiscountHealth/workers');
const healthExemptionPayment = require('../../sendCollectionFiles/healthExemptionPayment/workers');
const sendCircularFile2480 = require('../../sendCollectionFiles/sendCircular2480/workers');
const transferPensions = require('../../transferPensions/workers');
const rejectedReservedAmount = require('../../rejectedReservedAmount/workers');
const apsCollection = require('../../apsCollection/workers');
const notApprovedCheckpoint = require('../../notApprovedCheckpoint/workers');
const rejectedRetroactiveAmount = require('../../rejectedRetroactiveAmount/workers');
const manuallyInactivateMarkedPensions = require('../../manuallyInactivateMarkedPensions/workers');
const totalAssetsAndDiscounts = require('../../totalAssetsAndDiscounts/workers');
const generateBonusAssignmentFile = require('../../generateBonusAssignmentFile/workers');
const modifyCivilRegistryData = require('../../modifyCivilRegistryData/workers');
const expenseAccountingReport = require('../../expenseAccountingReport/workers');
const winterBonusAssignmen = require('../../winterBonusAssignment/workers');
const reservedAssetsAndDiscountsAmountCalculation = require('../../unifiedReservedAmountCalculationCrons/workers');
const unifiedRetroactiveAmounts = require('../../unifiedRetroactiveAmountCalculationCrons/workers');
const inactivateOrReactivateAFCronsGroup = require('../../inactivateOrReactivateAFCronsGroup/workers');

const calculateReservedAssetsAndDiscounts = require('../../calculateAssetsBonusDiscounts/workers');
const setReservedAmountForInstitutionalPatient = require('../../reservedAmountInstitutionalPatient/workers');
const calculateReservedAmountBySurvival = require('../../calculateReservedAmountBySurvival/workers');

const calculateRetroactiveDisabilityPension = require('../../retroactiveDisabilityPension/workers');
const calculateRetroactiveBank = require('../../retroactiveBank/workers');
const calculateRetroactiveAmountForInstitutianalPatient = require('../../retroactiveAmountsInstitutionalPatient/workers');
const calculateRetroactiveAmountForSurvival = require('../../retroactiveAmountsForSurvivalPension/worker');

const inactivateByWidowUnderFourtyFive = require('../../inactivatePensions/widowUnderFourtyFive/workers');
const reactivateOrphanhood = require('../../activatePensions/orphanhood/workers');
const calculateArticle41 = require('../../articleFourtyOne/DisabilityPensioners/workers');
const reactivateForRenewalOfStudyCertificate = require('../../reactivatePensions/renewalOfStudyCertificate/workers');
const registerStartAndEndDateStudyCertificate = require('../../studyCertificateDateRegistration/workers');
const setPensionTypeChange = require('../../pensionTypeChange/workers');

const ipsBulkLoad = require('../../bulkLoad/ips/workers');
const cajaLaAraucanaBulkLoad = require('../../bulkLoad/laAraucana/workers');

const cajaLosHeroesBulkLoad = require('../../bulkLoad/cajaLosHeroes/workers');
const caja18BulkLoad = require('../../bulkLoad/caja18/workers');
const notificationEndOfValidity = require('../../notificationEndOfValidity/workers');
const oldAgePensionInProcess = require('../../oldAgePensionInProcess/workers');
const discountJudicialRetention = require('../../discountJudicialRetention/workers');
const calculationOfEarnedFields = require('../../calculationOfEarnedFields/workers');

const markConfig = [
  {
    description: assignNationalHolidaysBonus.description,
    mark: assignNationalHolidaysBonus.cronMark,
    endPoint: assignNationalHolidaysBonus.endPoint,
    nameAgenda: assignNationalHolidaysBonus.name,
    dependencyMark: assignNationalHolidaysBonus.dependencyMark
  },
  {
    description: assignChristmasBonus.description,
    mark: assignChristmasBonus.cronMark,
    endPoint: assignChristmasBonus.endPoint,
    nameAgenda: assignChristmasBonus.name,
    dependencyMark: assignChristmasBonus.dependencyMark
  },
  {
    description: inactivateByTransient.inactivatePensionsByTransientsPreWorker.description,
    mark: inactivateByTransient.inactivatePensionsByTransientsPreWorker.cronMark,
    endPoint: inactivateByTransient.inactivatePensionsByTransientsPreWorker.endPoint,
    nameAgenda: inactivateByTransient.inactivatePensionsByTransientsPreWorker.name,
    dependencyMark: inactivateByTransient.inactivatePensionsByTransientsPreWorker.dependencyMark
  },
  {
    description: inactivateByTransient.inactivatePensionsByTransientsPostWorker.description,
    mark: inactivateByTransient.inactivatePensionsByTransientsPostWorker.cronMark,
    endPoint: inactivateByTransient.inactivatePensionsByTransientsPostWorker.endPoint,
    nameAgenda: inactivateByTransient.inactivatePensionsByTransientsPostWorker.name,
    dependencyMark: inactivateByTransient.inactivatePensionsByTransientsPostWorker.dependencyMark
  },
  {
    description: inactivateByDeath.description,
    mark: inactivateByDeath.cronMark,
    endPoint: inactivateByDeath.endPoint,
    nameAgenda: inactivateByDeath.name,
    dependencyMark: inactivateByDeath.dependencyMark
  },
  {
    description: toCivilRegistration.description,
    mark: toCivilRegistration.cronMark,
    endPoint: toCivilRegistration.endPoint,
    nameAgenda: toCivilRegistration.name,
    dependencyMark: toCivilRegistration.dependencyMark
  },
  {
    description: inactivateByAgeLimit.description,
    mark: inactivateByAgeLimit.cronMark,
    endPoint: inactivateByAgeLimit.endPoint,
    nameAgenda: inactivateByAgeLimit.name,
    dependencyMark: inactivateByAgeLimit.dependencyMark
  },
  {
    description: inactivateByMarriage.marriagePostworker.description,
    mark: inactivateByMarriage.marriagePostworker.cronMark,
    endPoint: inactivateByMarriage.marriagePostworker.endPoint,
    nameAgenda: inactivateByMarriage.marriagePostworker.name,
    dependencyMark: inactivateByMarriage.marriagePostworker.dependencyMark
  },
  {
    description: inactivateByMarriage.marriagePreworker.description,
    mark: inactivateByMarriage.marriagePreworker.cronMark,
    endPoint: inactivateByMarriage.marriagePreworker.endPoint,
    nameAgenda: inactivateByMarriage.marriagePreworker.name,
    dependencyMark: inactivateByMarriage.marriagePreworker.dependencyMark
  },
  {
    description: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.description,
    mark: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.cronMark,
    endPoint: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.endPoint,
    nameAgenda: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.name,
    dependencyMark: inactivateByRetirement.inactivatePensionsByRetirementPreWorker.dependencyMark
  },
  {
    description: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.description,
    mark: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.cronMark,
    endPoint: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.endPoint,
    nameAgenda: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.name,
    dependencyMark: inactivateByRetirement.inactivatePensionsByRetirementPostWorker.dependencyMark
  },
  {
    description: reactivatePensionsByTransients.description,
    mark: reactivatePensionsByTransients.cronMark,
    endPoint: reactivatePensionsByTransients.endPoint,
    nameAgenda: reactivatePensionsByTransients.name,
    dependencyMark: reactivatePensionsByTransients.dependencyMark
  },
  {
    description: ipc.description,
    mark: ipc.cronMark,
    endPoint: ipc.endPoint,
    nameAgenda: ipc.name,
    dependencyMark: ipc.dependencyMark
  },
  {
    description: basePensionWorker.description,
    mark: basePensionWorker.cronMark,
    endPoint: basePensionWorker.endPoint,
    nameAgenda: basePensionWorker.name,
    dependencyMark: basePensionWorker.dependencyMark
  },
  {
    description: taxablePension.description,
    mark: taxablePension.cronMark,
    endPoint: taxablePension.endPoint,
    nameAgenda: taxablePension.name,
    dependencyMark: taxablePension.dependencyMark
  },
  {
    description: setZeroValuesPensionWorker.description,
    mark: setZeroValuesPensionWorker.cronMark,
    endPoint: setZeroValuesPensionWorker.endPoint,
    nameAgenda: setZeroValuesPensionWorker.name,
    dependencyMark: setZeroValuesPensionWorker.dependencyMark
  },
  {
    description: daysOfTranstientPension.description,
    mark: daysOfTranstientPension.cronMark,
    endPoint: daysOfTranstientPension.endPoint,
    nameAgenda: daysOfTranstientPension.name,
    dependencyMark: daysOfTranstientPension.dependencyMark
  },
  {
    description: netPensionsLiquidationReports.description,
    mark: netPensionsLiquidationReports.cronMark,
    endPoint: netPensionsLiquidationReports.endPoint,
    nameAgenda: netPensionsLiquidationReports.name,
    dependencyMark: netPensionsLiquidationReports.dependencyMark
  },
  {
    description: obtainUfValue.description,
    mark: obtainUfValue.cronMark,
    endPoint: obtainUfValue.endPoint,
    nameAgenda: obtainUfValue.name,
    dependencyMark: obtainUfValue.dependencyMark
  },
  {
    description: calculatePaymentDatesWorker.description,
    mark: calculatePaymentDatesWorker.cronMark,
    endPoint: calculatePaymentDatesWorker.endPoint,
    nameAgenda: calculatePaymentDatesWorker.name,
    dependencyMark: calculatePaymentDatesWorker.dependencyMark
  },
  {
    description: historicalPensionReports.description,
    mark: historicalPensionReports.cronMark,
    endPoint: historicalPensionReports.endPoint,
    nameAgenda: historicalPensionReports.name,
    dependencyMark: historicalPensionReports.dependencyMark
  },
  {
    description: generateAndUploadPreviredFile.description,
    mark: generateAndUploadPreviredFile.cronMark,
    endPoint: generateAndUploadPreviredFile.endPoint,
    nameAgenda: generateAndUploadPreviredFile.name,
    dependencyMark: generateAndUploadPreviredFile.dependencyMark
  },
  {
    description: setFixedBasePensionAndArticlesValue.description,
    mark: setFixedBasePensionAndArticlesValue.cronMark,
    endPoint: setFixedBasePensionAndArticlesValue.endPoint,
    nameAgenda: setFixedBasePensionAndArticlesValue.name,
    dependencyMark: setFixedBasePensionAndArticlesValue.dependencyMark
  },
  {
    description: resetFixedBasePensionAndArticlesValue.description,
    mark: resetFixedBasePensionAndArticlesValue.cronMark,
    endPoint: resetFixedBasePensionAndArticlesValue.endPoint,
    nameAgenda: resetFixedBasePensionAndArticlesValue.name,
    dependencyMark: resetFixedBasePensionAndArticlesValue.dependencyMark
  },
  {
    description: reajustBasePensionAndArticles.description,
    mark: reajustBasePensionAndArticles.cronMark,
    endPoint: reajustBasePensionAndArticles.endPoint,
    nameAgenda: reajustBasePensionAndArticles.name,
    dependencyMark: reajustBasePensionAndArticles.dependencyMark
  },
  {
    description: unifiedBulkLoadAndIps.description,
    mark: unifiedBulkLoadAndIps.cronMark,
    endPoint: unifiedBulkLoadAndIps.endPoint,
    nameAgenda: unifiedBulkLoadAndIps.name,
    dependencyMark: unifiedBulkLoadAndIps.dependencyMark
  },
  {
    description: calculateDaysToPayWorker.description,
    mark: calculateDaysToPayWorker.cronMark,
    endPoint: calculateDaysToPayWorker.endPoint,
    nameAgenda: calculateDaysToPayWorker.name,
    dependencyMark: calculateDaysToPayWorker.dependencyMark
  },
  {
    description: inactivateOrReactivateProcess.description,
    mark: inactivateOrReactivateProcess.cronMark,
    endPoint: inactivateOrReactivateProcess.endPoint,
    nameAgenda: inactivateOrReactivateProcess.name,
    dependencyMark: inactivateOrReactivateProcess.dependencyMark
  },
  {
    description: InactivateOrReactivateOrphanhoodProcess.description,
    mark: InactivateOrReactivateOrphanhoodProcess.cronMark,
    endPoint: InactivateOrReactivateOrphanhoodProcess.endPoint,
    nameAgenda: InactivateOrReactivateOrphanhoodProcess.name,
    dependencyMark: InactivateOrReactivateOrphanhoodProcess.dependencyMark
  },
  {
    description: schedulingCronjobs.description,
    mark: schedulingCronjobs.cronMark,
    endPoint: schedulingCronjobs.endPoint,
    nameAgenda: schedulingCronjobs.name,
    dependencyMark: schedulingCronjobs.dependencyMark
  },
  {
    description: monthlyExpenses.description,
    mark: monthlyExpenses.cronMark,
    endPoint: monthlyExpenses.endPoint,
    nameAgenda: monthlyExpenses.name,
    dependencyMark: monthlyExpenses.dependencyMark
  },
  {
    description: widowhoodPayment.description,
    mark: widowhoodPayment.cronMark,
    endPoint: widowhoodPayment.endPoint,
    nameAgenda: widowhoodPayment.name,
    dependencyMark: widowhoodPayment.dependencyMark
  },
  {
    description: postLiquidationCheckpoint.description,
    mark: postLiquidationCheckpoint.cronMark,
    endPoint: postLiquidationCheckpoint.endPoint,
    nameAgenda: postLiquidationCheckpoint.name,
    dependencyMark: postLiquidationCheckpoint.dependencyMark
  },
  {
    description: generateAndUploadBankFile.description,
    mark: generateAndUploadBankFile.cronMark,
    endPoint: generateAndUploadBankFile.endPoint,
    nameAgenda: generateAndUploadBankFile.name,
    dependencyMark: generateAndUploadBankFile.dependencyMark
  },
  {
    description: reservedAmountAssetsDiscounts.description,
    mark: reservedAmountAssetsDiscounts.cronMark,
    endPoint: reservedAmountAssetsDiscounts.endPoint,
    nameAgenda: reservedAmountAssetsDiscounts.name,
    dependencyMark: reservedAmountAssetsDiscounts.dependencyMark
  },
  {
    description: keyBuilder.description,
    mark: keyBuilder.cronMark,
    endPoint: keyBuilder.endPoint,
    nameAgenda: keyBuilder.name,
    dependencyMark: keyBuilder.dependencyMark
  },
  {
    description: calculateCurrentCapital.description,
    mark: calculateCurrentCapital.cronMark,
    endPoint: calculateCurrentCapital.endPoint,
    nameAgenda: calculateCurrentCapital.name,
    dependencyMark: calculateCurrentCapital.dependencyMark
  },
  {
    description: socialDiscountsCheckPoint.description,
    mark: socialDiscountsCheckPoint.cronMark,
    endPoint: socialDiscountsCheckPoint.endPoint,
    nameAgenda: socialDiscountsCheckPoint.name,
    dependencyMark: socialDiscountsCheckPoint.dependencyMark
  },
  {
    description: analysisOfCurrentCapital.description,
    mark: analysisOfCurrentCapital.cronMark,
    endPoint: analysisOfCurrentCapital.endPoint,
    nameAgenda: analysisOfCurrentCapital.name,
    dependencyMark: analysisOfCurrentCapital.dependencyMark
  },
  {
    description: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.description,
    mark: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.cronMark,
    endPoint: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.endPoint,
    nameAgenda: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.name,
    dependencyMark: unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReport.dependencyMark
  },
  {
    description: updatePensionerInfo.description,
    mark: updatePensionerInfo.cronMark,
    endPoint: updatePensionerInfo.endPoint,
    nameAgenda: updatePensionerInfo.name,
    dependencyMark: updatePensionerInfo.dependencyMark
  },
  {
    description: healthRejection.description,
    mark: healthRejection.cronMark,
    endPoint: healthRejection.endPoint,
    nameAgenda: healthRejection.name,
    dependencyMark: healthRejection.dependencyMark
  },
  {
    description: collectionDiscountHealth.description,
    mark: collectionDiscountHealth.cronMark,
    endPoint: collectionDiscountHealth.endPoint,
    nameAgenda: collectionDiscountHealth.name,
    dependencyMark: collectionDiscountHealth.dependencyMark
  },
  {
    description: healthExemptionPayment.description,
    mark: healthExemptionPayment.cronMark,
    endPoint: healthExemptionPayment.endPoint,
    nameAgenda: healthExemptionPayment.name,
    dependencyMark: healthExemptionPayment.dependencyMark
  },
  {
    description: sendCircularFile2480.description,
    mark: sendCircularFile2480.cronMark,
    endPoint: sendCircularFile2480.endPoint,
    nameAgenda: sendCircularFile2480.name,
    dependencyMark: sendCircularFile2480.dependencyMark
  },
  {
    description: transferPensions.description,
    mark: transferPensions.cronMark,
    endPoint: transferPensions.endPoint,
    nameAgenda: transferPensions.name,
    dependencyMark: transferPensions.dependencyMark
  },
  {
    description: rejectedReservedAmount.description,
    mark: rejectedReservedAmount.cronMark,
    endPoint: rejectedReservedAmount.endPoint,
    nameAgenda: rejectedReservedAmount.name,
    dependencyMark: rejectedReservedAmount.dependencyMark
  },
  {
    description: apsCollection.description,
    mark: apsCollection.cronMark,
    endPoint: apsCollection.endPoint,
    nameAgenda: apsCollection.name,
    dependencyMark: apsCollection.dependencyMark
  },
  {
    description: notApprovedCheckpoint.description,
    mark: notApprovedCheckpoint.cronMark,
    endPoint: notApprovedCheckpoint.endPoint,
    nameAgenda: notApprovedCheckpoint.name,
    dependencyMark: notApprovedCheckpoint.dependencyMark
  },
  {
    description: rejectedRetroactiveAmount.description,
    mark: rejectedRetroactiveAmount.cronMark,
    endPoint: rejectedRetroactiveAmount.endPoint,
    nameAgenda: rejectedRetroactiveAmount.name,
    dependencyMark: rejectedRetroactiveAmount.dependencyMark
  },
  {
    description: manuallyInactivateMarkedPensions.description,
    mark: manuallyInactivateMarkedPensions.cronMark,
    endPoint: manuallyInactivateMarkedPensions.endPoint,
    nameAgenda: manuallyInactivateMarkedPensions.name,
    dependencyMark: manuallyInactivateMarkedPensions.dependencyMark
  },
  {
    description: winterBonusAssignmen.description,
    mark: winterBonusAssignmen.cronMark,
    endPoint: winterBonusAssignmen.endPoint,
    nameAgenda: winterBonusAssignmen.name,
    dependencyMark: winterBonusAssignmen.dependencyMark
  },
  {
    description: totalAssetsAndDiscounts.description,
    mark: totalAssetsAndDiscounts.cronMark,
    endPoint: totalAssetsAndDiscounts.endPoint,
    nameAgenda: totalAssetsAndDiscounts.name,
    dependencyMark: totalAssetsAndDiscounts.dependencyMark
  },
  {
    description: generateBonusAssignmentFile.description,
    mark: generateBonusAssignmentFile.cronMark,
    endPoint: generateBonusAssignmentFile.endPoint,
    nameAgenda: generateBonusAssignmentFile.name,
    dependencyMark: generateBonusAssignmentFile.dependencyMark
  },
  {
    description: modifyCivilRegistryData.description,
    mark: modifyCivilRegistryData.cronMark,
    endPoint: modifyCivilRegistryData.endPoint,
    nameAgenda: modifyCivilRegistryData.name,
    dependencyMark: modifyCivilRegistryData.dependencyMark
  },
  {
    description: expenseAccountingReport.description,
    mark: expenseAccountingReport.cronMark,
    endPoint: expenseAccountingReport.endPoint,
    nameAgenda: expenseAccountingReport.name,
    dependencyMark: expenseAccountingReport.dependencyMark
  },
  {
    description: reservedAssetsAndDiscountsAmountCalculation.description,
    mark: reservedAssetsAndDiscountsAmountCalculation.cronMark,
    endPoint: reservedAssetsAndDiscountsAmountCalculation.endPoint,
    nameAgenda: reservedAssetsAndDiscountsAmountCalculation.name,
    dependencyMark: reservedAssetsAndDiscountsAmountCalculation.dependencyMark
  },
  {
    description: unifiedRetroactiveAmounts.description,
    mark: unifiedRetroactiveAmounts.cronMark,
    endPoint: unifiedRetroactiveAmounts.endPoint,
    nameAgenda: unifiedRetroactiveAmounts.name,
    dependencyMark: unifiedRetroactiveAmounts.dependencyMark
  },
  {
    description: inactivateOrReactivateAFCronsGroup.description,
    mark: inactivateOrReactivateAFCronsGroup.cronMark,
    endPoint: inactivateOrReactivateAFCronsGroup.endPoint,
    nameAgenda: inactivateOrReactivateAFCronsGroup.name,
    dependencyMark: inactivateOrReactivateAFCronsGroup.dependencyMark
  },
  {
    description: calculateReservedAssetsAndDiscounts.description,
    mark: calculateReservedAssetsAndDiscounts.cronMark,
    endPoint: calculateReservedAssetsAndDiscounts.endPoint,
    nameAgenda: calculateReservedAssetsAndDiscounts.name,
    dependencyMark: calculateReservedAssetsAndDiscounts.dependencyMark
  },
  {
    description: setReservedAmountForInstitutionalPatient.description,
    mark: setReservedAmountForInstitutionalPatient.cronMark,
    endPoint: setReservedAmountForInstitutionalPatient.endPoint,
    nameAgenda: setReservedAmountForInstitutionalPatient.name,
    dependencyMark: setReservedAmountForInstitutionalPatient.dependencyMark
  },
  {
    description: calculateReservedAmountBySurvival.description,
    mark: calculateReservedAmountBySurvival.cronMark,
    endPoint: calculateReservedAmountBySurvival.endPoint,
    nameAgenda: calculateReservedAmountBySurvival.name,
    dependencyMark: calculateReservedAmountBySurvival.dependencyMark
  },
  {
    description: calculateRetroactiveDisabilityPension.description,
    mark: calculateRetroactiveDisabilityPension.cronMark,
    endPoint: calculateRetroactiveDisabilityPension.endPoint,
    nameAgenda: calculateRetroactiveDisabilityPension.name,
    dependencyMark: calculateRetroactiveDisabilityPension.dependencyMark
  },
  {
    description: calculateRetroactiveBank.description,
    mark: calculateRetroactiveBank.cronMark,
    endPoint: calculateRetroactiveBank.endPoint,
    nameAgenda: calculateRetroactiveBank.name,
    dependencyMark: calculateRetroactiveBank.dependencyMark
  },
  {
    description: calculateRetroactiveAmountForInstitutianalPatient.description,
    mark: calculateRetroactiveAmountForInstitutianalPatient.cronMark,
    endPoint: calculateRetroactiveAmountForInstitutianalPatient.endPoint,
    nameAgenda: calculateRetroactiveAmountForInstitutianalPatient.name,
    dependencyMark: calculateRetroactiveAmountForInstitutianalPatient.dependencyMark
  },
  {
    description: calculateRetroactiveAmountForSurvival.description,
    mark: calculateRetroactiveAmountForSurvival.cronMark,
    endPoint: calculateRetroactiveAmountForSurvival.endPoint,
    nameAgenda: calculateRetroactiveAmountForSurvival.name,
    dependencyMark: calculateRetroactiveAmountForSurvival.dependencyMark
  },
  {
    description: inactivateByWidowUnderFourtyFive.description,
    mark: inactivateByWidowUnderFourtyFive.cronMark,
    endPoint: inactivateByWidowUnderFourtyFive.endPoint,
    nameAgenda: inactivateByWidowUnderFourtyFive.name,
    dependencyMark: inactivateByWidowUnderFourtyFive.dependencyMark
  },
  {
    description: reactivateOrphanhood.description,
    mark: reactivateOrphanhood.cronMark,
    endPoint: reactivateOrphanhood.endPoint,
    nameAgenda: reactivateOrphanhood.name,
    dependencyMark: reactivateOrphanhood.dependencyMark
  },
  {
    description: calculateArticle41.description,
    mark: calculateArticle41.cronMark,
    endPoint: calculateArticle41.endPoint,
    nameAgenda: calculateArticle41.name,
    dependencyMark: calculateArticle41.dependencyMark
  },
  {
    description: reactivateForRenewalOfStudyCertificate.description,
    mark: reactivateForRenewalOfStudyCertificate.cronMark,
    endPoint: reactivateForRenewalOfStudyCertificate.endPoint,
    nameAgenda: reactivateForRenewalOfStudyCertificate.name,
    dependencyMark: reactivateForRenewalOfStudyCertificate.dependencyMark
  },
  {
    description: registerStartAndEndDateStudyCertificate.description,
    mark: registerStartAndEndDateStudyCertificate.cronMark,
    endPoint: registerStartAndEndDateStudyCertificate.endPoint,
    nameAgenda: registerStartAndEndDateStudyCertificate.name,
    dependencyMark: registerStartAndEndDateStudyCertificate.dependencyMark
  },
  {
    description: setPensionTypeChange.description,
    mark: setPensionTypeChange.cronMark,
    endPoint: setPensionTypeChange.endPoint,
    nameAgenda: setPensionTypeChange.name,
    dependencyMark: setPensionTypeChange.dependencyMark
  },
  {
    description: ipsBulkLoad.description,
    mark: ipsBulkLoad.cronMark,
    endPoint: ipsBulkLoad.endPoint,
    nameAgenda: ipsBulkLoad.name,
    dependencyMark: ipsBulkLoad.dependencyMark
  },
  {
    description: cajaLaAraucanaBulkLoad.description,
    mark: cajaLaAraucanaBulkLoad.workerName,
    endPoint: cajaLaAraucanaBulkLoad.endPoint,
    nameAgenda: cajaLaAraucanaBulkLoad.name,
    dependencyMark: cajaLaAraucanaBulkLoad.dependencyMark
  },
  {
    description: cajaLosHeroesBulkLoad.description,
    mark: cajaLosHeroesBulkLoad.workerName,
    endPoint: cajaLosHeroesBulkLoad.endPoint,
    nameAgenda: cajaLosHeroesBulkLoad.name,
    dependencyMark: cajaLosHeroesBulkLoad.dependencyMark
  },
  {
    description: caja18BulkLoad.description,
    mark: caja18BulkLoad.workerName,
    endPoint: caja18BulkLoad.endPoint,
    nameAgenda: caja18BulkLoad.name,
    dependencyMark: caja18BulkLoad.dependencyMark
  },
  {
    description: notificationEndOfValidity.description,
    mark: notificationEndOfValidity.workerName,
    endPoint: notificationEndOfValidity.endPoint,
    nameAgenda: notificationEndOfValidity.name,
    dependencyMark: notificationEndOfValidity.dependencyMark
  },
  {
    description: oldAgePensionInProcess.description,
    mark: oldAgePensionInProcess.workerName,
    endPoint: oldAgePensionInProcess.endPoint,
    nameAgenda: oldAgePensionInProcess.name,
    dependencyMark: oldAgePensionInProcess.dependencyMark
  },
  {
    description: discountJudicialRetention.description,
    mark: discountJudicialRetention.workerName,
    endPoint: discountJudicialRetention.endPoint,
    nameAgenda: discountJudicialRetention.name,
    dependencyMark: discountJudicialRetention.dependencyMark
  },
  {
    description: calculationOfEarnedFields.description,
    mark: calculationOfEarnedFields.workerName,
    endPoint: calculationOfEarnedFields.endPoint,
    nameAgenda: calculationOfEarnedFields.name,
    dependencyMark: calculationOfEarnedFields.dependencyMark
  }
];

const markCron = {
  getConfigCron() {
    return markConfig;
  }
};

module.exports = { ...markCron, markConfig };

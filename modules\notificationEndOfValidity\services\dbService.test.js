const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const notificationPensioners = require('../../../resources/notificationPensioners.json');
const notificationUserEmail = require('../../../resources/notificationUserEmail.json');
const notificationRoles = require('../../../resources/rolesNotificacion.json');
const mailService = require('../../sendMail/service/sendMail.service');

describe('send-notification-email', () => {
  beforeAll(beforeAllTests);

  it('send-email', async () => {
    service.filterPensioners = jest.fn(() => Promise.resolve(notificationPensioners));
    service.getRoles = jest.fn(() => Promise.resolve(notificationRoles));
    service.filteUser = jest.fn(() => Promise.resolve(notificationUserEmail));

    mailService.sendEmail = jest.fn(() => Promise.resolve(true));

    const { completed } = await service.sendNotificationEmail();
    expect(completed).toBe(true);
  });

  afterAll(afterAllTests);
});

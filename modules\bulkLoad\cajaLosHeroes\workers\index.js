/* eslint-disable consistent-return */
const service = require('../services');
const { worker: workerFn } = require('../../sharedFiles/worker');

const FOLDER_PATH = process.env.BULKLOAD_LOSHEROES_FILES_FTP_FOLDER_PATH; 

const {
  CAJA_LOSHEROES_FTP_HOST,
  CAJA_LOSHEROES_FTP_USER,
  CAJA_LOSHEROES_FTP_PASS,
  CAJA_LOSHEROES_FTP_PORT
} = process.env;
const ftpCredentials = {
  host: CAJA_LOSHEROES_FTP_HOST,
  user: CAJA_LOSHEROES_FTP_USER,
  password: CAJA_LOSHEROES_FTP_PASS,
  port: CAJA_LOSHEROES_FTP_PORT
};

const workerName = 'CAJA_LOS_HEROES_BULK_LOAD';
const description = 'Carga masiva Caja Los Heroes';
const endPoint = 'cajalosheroesbulkload';
const dependencyMark = '';
const name = 'SIN_NOMBRE';

const worker = async ({ Logger, done }) =>
  workerFn({ Logger, done, service, FOLDER_PATH, ftpCredentials, workerName });

module.exports = { worker, workerName, name, description, endPoint, dependencyMark };

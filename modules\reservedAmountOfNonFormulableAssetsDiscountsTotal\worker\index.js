const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/setReservedAmount');

module.exports = {
  name: 'set-reserved-amount-nonformulable-assets-discounts',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_RESERVED_AMOUNT_NONFORMULABLE_ASSETS_DISCOUNTS,
  description: 'Cálculo de montos reservados para haberes y descuentos no formulables',
  endPoint: 'reservedamountassetsdiscounts',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

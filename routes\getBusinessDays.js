const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');

const FactoryController = require('../modules/businessDays/controllers/businessDays.controllers');
const {
  getNBusinessDaysService,
  getRemainingDaysForProcesses
} = require('../modules/businessDays/services/businessDays.service');
const { getFirstNbusinessDays, getMonthHolidays } = require('../modules/sharedFiles/helpers');
const getDueDaysList = require('../modules/businessDays/helpers/dueDaysList');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const getBusinessDaysController = FactoryController({
    HttpStatus,
    getNBusinessDaysService,
    getFirstNbusinessDays,
    getMonthHolidays,
    getRemainingDaysForProcesses,
    getDueDaysList,
    ErrorBuilder,
    Logger
  });

  router.get(
    '/inact-react-process',
    validateAccess(),
    getBusinessDaysController.isInactReactProcessAvailable
  );
  router.get(
    '/isapre-portal-process',
    validateAccess(),
    getBusinessDaysController.isIsaprePortalProcessAvailable
  );
  router.get('/due-days', validateAccess(), getBusinessDaysController.getDueDays);
  router.get(
    '/update-pensioner-temporally-and-pension-type',
    validateAccess(),
    getBusinessDaysController.daysToUpdatePensionerInfoAndPensionType
  );
  router.get('/currentDate', validateAccess(), getBusinessDaysController.getCurrentDate);
};

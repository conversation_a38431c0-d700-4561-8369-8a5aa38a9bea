[{"_id": "5ede555145f9021254cea87b", "causantRut": "19685004-K", "beneficiaryRut": "19685004-K", "assetsNonFormulable": [{"key": "noformulable", "amount": 10, "reason": "comida", "assetType": "líquido", "_id": "5edfdd68b4f6ee33299bac7d", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}, {"key": "noformulable", "amount": 20, "reason": "salud", "assetType": "líquido", "_id": "5edfdd68b4f6ee33299bac7e", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}, {"key": "noformulable", "amount": 100, "reason": "comida", "assetType": "imponible", "_id": "5edfdd68b4f6ee33299bac7f", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}], "discountsNonFormulable": [{"key": "noformulable", "amount": 10, "reason": "comida", "_id": "5edfdd68b4f6ee33299bac7a", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}, {"key": "noformulable", "amount": 20, "reason": "salud", "_id": "5edfdd68b4f6ee33299bac7b", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}, {"key": "noformulable", "amount": 100, "reason": "comida", "_id": "5edfdd68b4f6ee33299bac7c", "startDate": "2020-02-01T00:00:00.000Z", "endDate": "2020-07-01T00:00:00.000Z"}], "createdAt": "2020-06-08T15:12:17.483Z", "updatedAt": "2020-06-09T21:12:14.338Z", "__v": 0}]
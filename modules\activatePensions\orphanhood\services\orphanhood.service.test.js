/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionModel = require('../../../../models/pension');
const TemporaryHorphanhoodModel = require('../../../orphanhood/models/temporaryHorphanhood');
const service = require('./orphanhood.service');
const pensionsData = require('../../../../resources/pensions.json');
const orphanhoodData = require('../../../../resources/orphanhood.json');

describe('Temporary Family Allowance service Model Test', () => {
  beforeAll(beforeAllTests);
  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should return on empty pension and assignment model for activate service', async () => {
    const result = await service.activate();
    expect(result).toBeUndefined();
  });

  it('should return on empty pension model for activate service', async () => {
    await TemporaryHorphanhoodModel.create(orphanhoodData[1]);
    const result = await service.activate();
    expect(result).toBeUndefined();
  });

  it('should disable and create new doc if criteria is met', async () => {
    pensionsData[0].validityType = 'No vigente';
    pensionsData[0].pensionType = 'Pensión por orfandad';
    await Promise.all([
      TemporaryHorphanhoodModel.create(orphanhoodData[1]),
      PensionModel.create(pensionsData[0])
    ]);

    const endDate = new Date('2070-08-31T03:00:00.000Z');

    await service.activate();
    const dbDocs = await PensionModel.find({}).lean();
    expect(dbDocs.length).toBe(1);
    expect(dbDocs[0].enabled).toBe(true);
    expect(dbDocs[0].inactivationReason).toBe('-');
    expect(dbDocs[0].validityType).toBe('Vigente Orfandad');
    expect(dbDocs[0].endDateOfValidity.toString()).toBe(endDate.toString());
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await TemporaryHorphanhoodModel.deleteMany({});
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

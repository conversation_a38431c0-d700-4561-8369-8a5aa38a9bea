const mongoose = require('mongoose');
const paginate = require('../../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const inactivateMarriageSchema = new Schema(
  {
    beneficiaryRut: { type: String, required: true },
    causantRut: { type: String, required: true },
    inactivationReason: { type: String, required: true },
    dateToInactivate: { type: Date, required: true },
    endDateOfValidity: { type: Date, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

inactivateMarriageSchema.plugin(paginate);
inactivateMarriageSchema.index({ idCode: 1 });

module.exports = mongoose.model('inactivateMarriage', inactivateMarriageSchema);

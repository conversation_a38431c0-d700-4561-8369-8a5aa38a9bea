const moment = require('moment');
const { matchAggregationStage, buildLookupAggregationStage } = require('./pipeline');
const FamilyAssignmentModel = require('../../familyAssignment/models/temporaryFamilyAssignment');
const { updatePensionsById } = require('../../pensions/services/pension.service');

const matchingChargeIdPensionTypes = [
  /Pensi[óo]n\s+por\s+orfandad/i,
  /pensi[óo]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];

const matchingCollectorIdPensionTypes = [
  /Pensi[óo]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+sin\s+hijos/i
];

const chargeIdLookupStage = buildLookupAggregationStage({
  idField: 'chargeId',
  pensionTypes: matchingChargeIdPensionTypes,
  alias: 'matchingChargeIdPensions'
});

const collectorIdLookupStage = buildLookupAggregationStage({
  idField: 'collectorId',
  pensionTypes: matchingCollectorIdPensionTypes,
  alias: 'matchingCollectorIdPensions'
});

const filterPensionsByDate = (pensions, date) => {
  return pensions.filter(({ createdAt }) => moment(createdAt, 'YYYY-MM').isSameOrBefore(date));
};

const extractPensions = data => {
  let chargeIdPensions = [];
  let collectorIdPensions = [];
  const {
    endDateOfCertificationValidity,
    matchingChargeIdPensions,
    matchingCollectorIdPensions
  } = data;

  const endDate = moment(endDateOfCertificationValidity, 'YYYY-MM');

  if (matchingChargeIdPensions.length) {
    chargeIdPensions = filterPensionsByDate(matchingChargeIdPensions, endDate);
  }

  if (matchingCollectorIdPensions.length) {
    collectorIdPensions = filterPensionsByDate(matchingCollectorIdPensions, endDate);
  }

  return { chargeIdPensions, collectorIdPensions };
};

const getLast = pensions => {
  const obj = {};
  const sortedPensions = pensions.sort((current, next) =>
    moment(current.createdAt).diff(moment(next.createdAt))
  );
  sortedPensions.forEach(pension => {
    const key = moment(pension.createdAt).format('YYYY-MM');
    obj[key] = pension;
  });
  return Object.values(obj);
};

const getPensions = results => {
  const pensions = [];
  results.forEach(result => {
    const { chargeIdPensions, collectorIdPensions } = extractPensions(result);
    if (chargeIdPensions.length) pensions.push(...getLast(chargeIdPensions));
    if (collectorIdPensions.length) pensions.push(...getLast(collectorIdPensions));
  });
  return pensions;
};

const service = {
  async registerStartAndEndDateOfStudyCertificate() {
    try {
      const results = await FamilyAssignmentModel.aggregate([
        ...matchAggregationStage,
        chargeIdLookupStage,
        collectorIdLookupStage
      ]);
      const pensions = getPensions(results);
      const modifiedPensions = pensions.map(pension => ({
        ...pension,
        validatedStudyPeriod: 'Sí'
      }));
      const { completed, error } = await updatePensionsById(modifiedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = service;

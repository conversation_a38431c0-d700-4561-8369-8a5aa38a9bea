const workerModule = require('./worker');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('set amount retention worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let logService;
  let Logger;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      setAmountRetention: jest.fn(() => Promise.resolve({ completed: true, err: false }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest
        .fn(() => Promise.resolve(true))
        .mockImplementationOnce(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setAmountRetention).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resoolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setAmountRetention).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('worker does not have all marks', async () => {
    logService.allMarksExists = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setAmountRetention).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setAmountRetention).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

module.exports = ({
  HttpStatus,
  QueryPensions,
  validationResult,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger,
  storageService
}) => {
  const service = QueryPensions;
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }
  return {
    queryPensions: async (req, res) => {
      Logger.info('getting data to populate pensions table');
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }
      const { query } = req.body;
      const { error, result } = await service.queryPensions(query);

      if (error) {
        Logger.error(`unable to fetch data for pension table${error}`);
        return manageError(res, error);
      }
      Logger.info('Sending data ...');
      return res.status(HttpStatus.OK).json(result);
    },
    getExtendedPensioner: async (req, res) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }
      Logger.info(
        `get extended data for causant: ${req.query.rutCausant} and beneficiary: ${req.query.rutBeneficiary} and pensionCodeId: ${req.query.pensionCodeId}`
      );

      const { result, error } = await service.findExtendedPensioner(req.query);

      if (error) {
        Logger.error(`Error: ${error}`);
        return manageError(res, error);
      }

      Logger.info('adding next paymentDate to extended pensioner info...');
      const { data } = await service.getNextPaymentDate(result);
      Logger.info('adding date to extended pensioner info...');
      data.currentDate = new Date();
      Logger.info('Sending extended pensioner info ...');
      return res.status(HttpStatus.OK).json(data);
    },
    storePensionerDocuments: async (req, res) => {
      try {
        const { name, path } = req.files.pensionerDocument;

        const {
          beneficiaryRutNumber,
          causantRutNumber,
          pensionCodeId,
          targetName
        } = service.resolveStorageTargetProps(name);

        const uniqueFileName = `${pensionCodeId}_${targetName}`;

        Logger.info('Sending document of pensioner Storage...');
        const { status, data, fileSendName } = await storageService.uploadFileFromLocal(
          path,
          uniqueFileName
        );
        if (status !== 200) throw new Error(data);
        const virtualPath = `${beneficiaryRutNumber}/${causantRutNumber}/${pensionCodeId}/${fileSendName}`;
        const { error } = await storageService.saveFileRegistry(virtualPath, data);
        if (error) throw new Error(error);

        Logger.info('storing document pensioner was successful...');
        return res.status(HttpStatus.OK).json({ success: true });
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    checkPensionerDocuments: async (req, res) => {
      try {
        const { beneficiaryRut, causantRut, pensionCodeId, nameAndRegex } = req.body;

        Logger.info('Checking if pensioner document in FTP...');
        const { storageError, fileExistenceDetails } = await service.checkPensionerDocument({
          beneficiaryRut,
          causantRut,
          pensionCodeId,
          nameAndRegex,
          storageService
        });

        if (storageError) {
          Logger.error(`Error: ${storageError}`);
          return manageError(res, storageError);
        }

        Logger.info('sending pensioner document list to client...');
        return res.status(HttpStatus.OK).json({ fileExistenceDetails });
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    getPensionerDocuments: async (req, res) => {
      try {
        const { filenameRegex } = req.params;

        const {
          beneficiaryRutNumber,
          causantRutNumber,
          pensionCodeId,
          targetName
        } = service.resolveStorageTargetProps(filenameRegex);

        const virtualPath = `${beneficiaryRutNumber}/${causantRutNumber}/${pensionCodeId}/${pensionCodeId}_${targetName}`;

        Logger.info('Getting pensioner document from storage...');
        const { result, error } = await storageService.findFileRegistry(virtualPath);
        if (error) throw new Error(error);
        if (!result)
          return res.status(HttpStatus.NOTFOUND).json('No se encontró el registro del archivo');

        const { data, status, headers } = await storageService.downloadFile(result.uuid);
        if (status !== 200) throw new Error(data);

        res.header('content-type', headers['content-type']);
        res.header('content-disposition', headers['content-disposition']);
        res.header('namefile', headers['content-disposition'].split('filename=')[1].split(';')[0]);
        res.set('Access-Control-Expose-Headers', 'nameFile');

        Logger.info('sending file to client...');
        return res.send(data);
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    updatePensionerInfoToTemporal: async (req, res) => {
      Logger.info('getting data to update tempral pensioner info');
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }
      const { user = {} } = req;
      const { beneficiaryRut, causantRut, pensionCodeId, pensionerInfo } = req.body;

      const { error, completed } = await service.updateToTemporalTable({
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        pensionerInfo,
        user
      });

      if (error) {
        Logger.error(`Error: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },
    updatePensionerInfo: async (req, res) => {
      Logger.info('getting data to update tempral pensioner info');
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }

      const { user = {} } = req;
      const { beneficiaryRut, causantRut, pensionCodeId, pensionerInfo } = req.body;

      const parameterUpdate = {
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        pensionerInfo,
        user
      };

      const { error, completed } = await service.updatePension({
        parameterUpdate
      });

      if (error) {
        Logger.error(`Error: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },
    getPensionerTemporalTable: async (req, res) => {
      try {
        const { rutBeneficiary, rutCausant, pensionCodeId } = req.query;

        Logger.info(
          `get temporal data for causant: ${rutCausant} and beneficiary: ${rutBeneficiary}`
        );

        const { result, error } = await service.findPensionerTemporalTable({
          rutBeneficiary,
          rutCausant,
          pensionCodeId
        });

        if (error) {
          Logger.error(`Error: ${error}`);
          return manageError(res, error);
        }

        return res.status(HttpStatus.OK).json(result);
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    updateTemporalPensionType: async (req, res) => {
      Logger.info('getting data to update tempral pension type');
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }

      const { user = {} } = req;
      const {
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        pensionType,
        ChangeOfPensionTypeDueToCharges
      } = req.body;

      const { error, completed } = await service.updateTemporalPensionType({
        beneficiaryRut,
        causantRut,
        pensionCodeId,
        pensionType,
        ChangeOfPensionTypeDueToCharges,
        user
      });

      if (error) {
        Logger.error(`Error: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },
    getTemporalPensionType: async (req, res) => {
      try {
        const { rutBeneficiary, rutCausant, pensionCodeId } = req.query;

        Logger.info(
          `get temporal pension type for causant: ${rutCausant} and beneficiary: ${rutBeneficiary}`
        );

        const { result, error } = await service.findTemporalPensionType({
          rutBeneficiary,
          rutCausant,
          pensionCodeId
        });

        if (error) {
          Logger.error(`Error: ${error}`);
          return manageError(res, error);
        }

        return res.status(HttpStatus.OK).json(result);
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    getHistoricalSettlementsForPDF: async (req, res) => {
      try {
        const { beneficiaryRut, causantRut, lowerDate, upperDate, pensionCodeId } = req.body;

        const { PEC_BOSS: pecBoss } = process.env;

        Logger.info(
          `get historical settlements for beneficiary: ${beneficiaryRut} and causant: ${causantRut}`
        );

        const { historicalSettlements = [], error } = await service.getHistoricalSettlementsByDate({
          beneficiaryRut,
          causantRut,
          pensionCodeId,
          lowerDate,
          upperDate
        });

        if (error) {
          Logger.error(`Error at getting liquidations: ${error}`);
          return manageError(res, error);
        }

        return res.status(HttpStatus.OK).json({ pecBoss, historicalSettlements });
      } catch (err) {
        Logger.error(`Error: ${err}`);
        return manageError(res, err);
      }
    },
    getPensionCertificateData: async (req, res) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(HttpStatus.BAD_REQUEST).json({ errors: errors.array() });
      }
      try {
        const { beneficiaryRut, causantRut, pensionCodeId } = req.body;

        Logger.info(
          `get pension certificate data for causant: ${causantRut} and beneficiary: ${beneficiaryRut} and pensionCodeId: ${pensionCodeId}`
        );

        const { PEC_BOSS: pecBoss } = process.env;
        const { result, error } = await service.getInfoForPensionCertificate({
          beneficiaryRut,
          causantRut,
          pensionCodeId
        });

        if (error) {
          Logger.error(`Error: ${error}`);
          return manageError(res, error);
        }
        return res.status(HttpStatus.OK).json({ ...result, pecBoss });
      } catch (error) {
        Logger.error(`Error: ${error}`);
        return manageError(res, error);
      }
    }
  };
};

const moment = require('moment');
const PensionModel = require('../../../../models/pension');

const notValid = 'No vigente';

const disabilityPensions = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const survivalPensions = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
  /Pensi[oó]n por orfandad/i,
  /Pensi[oó]n de orfandad de padre y madre/i
];

const getRutAndDatesList = lines => {
  const rutRegexList = [];
  const deathDateList = {};
  lines.forEach(line => {
    const [rut, deathDate] = line;
    if (rut && deathDate) {
      rutRegexList.push(new RegExp(rut, 'i'));
      deathDateList[rut] = deathDate;
    }
  });
  return [rutRegexList, deathDateList];
};

const getFirstDayOfMonth = dateToModify => {
  const currentDate = moment(dateToModify);
  const firstDayMonth = moment(currentDate)
    .startOf('month')
    .format();
  return new Date(firstDayMonth);
};

const getLastDayOfMonth = dateToModify => {
  const currentDate = moment(dateToModify);
  const lastDayMonth = moment(currentDate)
    .endOf('month')
    .format();
  return new Date(lastDayMonth);
};

const getPreviousDayOfMonth = dateToModify => {
  const currentDate = moment(dateToModify);
  const yesterdayDate = moment(currentDate)
    .add(-1, 'days')
    .format();
  return new Date(yesterdayDate);
};

const getEndDateOfValidityForDisabilityPensions = deathDate => {
  return getLastDayOfMonth(deathDate);
};

const getEndDateOfValidityForSurvivalPensions = deathDate => {
  return getPreviousDayOfMonth(deathDate);
};

const getEndDateOfValidityForAllTypePension = (deathDate, typePension, validityType) => {
  const mesMuerte = getFirstDayOfMonth(deathDate);
  const mesActual = getFirstDayOfMonth(new Date());
  let endDateOfValidity;
  let newValidityType = validityType;

  if (mesMuerte < mesActual) {
    endDateOfValidity = getLastDayOfMonth(deathDate);
    newValidityType = notValid;
    return { endDateOfValidity, newValidityType };
  }

  const regDisabilityPensions = disabilityPensions.find(tp => tp.test(typePension));
  if (regDisabilityPensions) {
    endDateOfValidity = getEndDateOfValidityForDisabilityPensions(deathDate);
    return { endDateOfValidity, newValidityType };
  }

  const regSurvivalPensions = survivalPensions.find(tp => tp.test(typePension));
  if (regSurvivalPensions) {
    endDateOfValidity = getEndDateOfValidityForSurvivalPensions(deathDate);
    return { endDateOfValidity, newValidityType };
  }

  return { endDateOfValidity: new Date(deathDate), newValidityType };
};

const getMappedPensions = (pensions, deathDateList) => {
  return pensions.map(pension => {
    const { _id, __v, ...data } = pension;
    const { rut } = data.beneficiary;
    const { pensionType, validityType } = data;
    const deathDate = deathDateList[rut];
    const { endDateOfValidity, newValidityType } = getEndDateOfValidityForAllTypePension(
      deathDate,
      pensionType,
      validityType
    );

    return {
      ...data,
      validityType: newValidityType,
      deathDate,
      inactivationReason: 'Fallecimiento',
      endDateOfValidity,
      inactivationDate: new Date()
    };
  });
};
const service = {
  async createUpdateDeathPension(lines, pensionService) {
    const [rutRegexList, deathDateList] = getRutAndDatesList(lines);
    try {
      const pensions = await PensionModel.find({
        'beneficiary.rut': { $in: rutRegexList },
        enabled: true
      })
        .lean()
        .exec();

      const pensionsList = getMappedPensions(pensions, deathDateList);

      const { completed, error } = await pensionService.createUpdatePension(pensionsList);
      return { completed, inactivationError: completed ? null : error };
    } catch (error) {
      return { completed: false, inactivationError: error };
    }
  }
};

module.exports = { ...service };

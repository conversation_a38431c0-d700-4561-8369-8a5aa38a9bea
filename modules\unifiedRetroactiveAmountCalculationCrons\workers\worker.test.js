/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('unified worker total assets and discounts', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let logService;
  let done;
  let calculateRetroactiveDisabilityPension;
  let calculateTotalAssetsAndDiscounts;
  let calculateRetroactiveBankFile;
  let calculateRetroactiveAmountForInstitutianalPatient;
  let calculateRetroactiveAmountForSurvival;

  beforeEach(() => {
    done = jest.fn();

    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve()),
      allMarksExists: jest.fn(() => Promise.resolve(true))
    };

    calculateRetroactiveDisabilityPension = jest.fn(() =>
      Promise.resolve({ alreadyExecuted: true })
    );
    calculateTotalAssetsAndDiscounts = jest.fn(() => Promise.resolve({ alreadyExecuted: true }));
    calculateRetroactiveBankFile = jest.fn(() => Promise.resolve({ alreadyExecuted: true }));
    calculateRetroactiveAmountForInstitutianalPatient = jest.fn(() =>
      Promise.resolve({ alreadyExecuted: true })
    );
    calculateRetroactiveAmountForSurvival = jest.fn(() =>
      Promise.resolve({ alreadyExecuted: true })
    );
  });

  it('should finish correctly', async () => {
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      calculateRetroactiveDisabilityPension,
      calculateTotalAssetsAndDiscounts,
      calculateRetroactiveBankFile,
      calculateRetroactiveAmountForInstitutianalPatient,
      calculateRetroactiveAmountForSurvival
    });

    expect(Logger.info).toHaveBeenCalledTimes(4);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(1);
  });

  it('should find that this process was already executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      logService,
      done
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not find the unified and bulkload filemark', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      calculateRetroactiveDisabilityPension,
      calculateTotalAssetsAndDiscounts,
      calculateRetroactiveBankFile,
      calculateRetroactiveAmountForInstitutianalPatient,
      calculateRetroactiveAmountForSurvival
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('at least one of the processes is not finished correctly', async () => {
    calculateRetroactiveAmountForSurvival = jest.fn(() =>
      Promise.resolve({ alreadyExecuted: false })
    );
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      calculateRetroactiveDisabilityPension,
      calculateTotalAssetsAndDiscounts,
      calculateRetroactiveBankFile,
      calculateRetroactiveAmountForInstitutianalPatient,
      calculateRetroactiveAmountForSurvival
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should fail file upload', async () => {
    logService.existsLog = jest.fn(() => Promise.reject(new Error()));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      calculateRetroactiveDisabilityPension,
      calculateTotalAssetsAndDiscounts,
      calculateRetroactiveBankFile,
      calculateRetroactiveAmountForInstitutianalPatient,
      calculateRetroactiveAmountForSurvival
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const { formatRut } = require('../../../sharedFiles/helpers');

const areRutsUnique = isapres => {
  const set = new Set(isapres.map(isapre => isapre.affiliateRut));
  return set.size === isapres.length;
};
const mapperTemporaryIsapres = ({ isapreId, affiliateRut, totalDiscount }) => {
  return {
    isapreId: isapreId.padStart(2, '0'),
    affiliateRut: formatRut(affiliateRut),
    totalDiscount: +`${totalDiscount}`
      .replace(/[UF\s+]/gi, '')
      .replace(',', '.')
      .slice(0, 10)
  };
};
const isValidTotalDiscount = isapre => !Number.isNaN(+isapre.totalDiscount);
const isValidIsapre = isapreName => !!isapreName.trim();
module.exports = { areRutsUnique, mapperTemporaryIsapres, isValidTotalDiscount, isValidIsapre };

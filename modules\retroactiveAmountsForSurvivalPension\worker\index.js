const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'calculateTotalAssetsDiscountsAndRetroactiveAmounts',
  description: 'Calculo de retroactivos para pensiones de supervivencia',
  endPoint: 'calculateretroactiveamountforsurvival',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

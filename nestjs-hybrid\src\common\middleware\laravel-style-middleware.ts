import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory } from 'rate-limiter-flexible';

/**
 * Middleware estilo Laravel para NestJS
 * Inspirado en Laravel Middleware con sintaxis familiar
 */

/**
 * Throttle Middleware - Equivalente a Laravel's throttle
 */
@Injectable()
export class ThrottleMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ThrottleMiddleware.name);
  private rateLimiter: RateLimiterMemory;

  constructor(private readonly maxAttempts: number = 60, private readonly decayMinutes: number = 1) {
    this.rateLimiter = new RateLimiterMemory({
      points: maxAttempts,
      duration: decayMinutes * 60,
    });
  }

  async use(req: Request, res: Response, next: NextFunction): Promise<void> {
    const key = this.resolveRequestSignature(req);

    try {
      await this.rateLimiter.consume(key);
      next();
    } catch (rejRes) {
      const remainingPoints = rejRes.remainingPoints || 0;
      const msBeforeNext = rejRes.msBeforeNext || 0;

      res.set({
        'X-RateLimit-Limit': this.maxAttempts.toString(),
        'X-RateLimit-Remaining': remainingPoints.toString(),
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      this.logger.warn(`Rate limit exceeded for ${key}`);
      res.status(429).json({
        message: 'Too Many Requests',
        retryAfter: Math.round(msBeforeNext / 1000),
      });
    }
  }

  private resolveRequestSignature(req: Request): string {
    // Usar IP + User ID si está autenticado
    const userId = (req as any).user?.id || 'anonymous';
    const ip = req.ip || req.connection.remoteAddress;
    return `${ip}:${userId}`;
  }
}

/**
 * CORS Middleware - Equivalente a Laravel's CORS
 */
@Injectable()
export class CorsMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
    const origin = req.headers.origin;

    if (allowedOrigins.includes('*') || (origin && allowedOrigins.includes(origin))) {
      res.header('Access-Control-Allow-Origin', origin || '*');
    }

    res.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400');

    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  }
}

/**
 * Request Logging Middleware - Equivalente a Laravel's logging
 */
@Injectable()
export class RequestLoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger('HTTP');

  use(req: Request, res: Response, next: NextFunction): void {
    const { method, originalUrl, ip } = req;
    const userAgent = req.get('User-Agent') || '';
    const userId = (req as any).user?.id || 'anonymous';

    const start = Date.now();

    res.on('finish', () => {
      const { statusCode } = res;
      const contentLength = res.get('content-length');
      const duration = Date.now() - start;

      const logMessage = `${method} ${originalUrl} ${statusCode} ${contentLength || 0}b - ${duration}ms - ${ip} - ${userId} - ${userAgent}`;

      if (statusCode >= 400) {
        this.logger.error(logMessage);
      } else if (statusCode >= 300) {
        this.logger.warn(logMessage);
      } else {
        this.logger.log(logMessage);
      }
    });

    next();
  }
}

/**
 * Security Headers Middleware - Equivalente a Laravel's security headers
 */
@Injectable()
export class SecurityHeadersMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    // Helmet-style security headers
    res.set({
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'",
      'X-Powered-By': '', // Remove X-Powered-By header
    });

    next();
  }
}

/**
 * Request ID Middleware - Equivalente a Laravel's request ID
 */
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const requestId = req.headers['x-request-id'] || this.generateRequestId();
    
    (req as any).requestId = requestId;
    res.set('X-Request-ID', requestId as string);

    next();
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Maintenance Mode Middleware - Equivalente a Laravel's maintenance mode
 */
@Injectable()
export class MaintenanceModeMiddleware implements NestMiddleware {
  private readonly logger = new Logger(MaintenanceModeMiddleware.name);

  use(req: Request, res: Response, next: NextFunction): void {
    const isMaintenanceMode = process.env.MAINTENANCE_MODE === 'true';
    const allowedIPs = process.env.MAINTENANCE_ALLOWED_IPS?.split(',') || [];
    const clientIP = req.ip || req.connection.remoteAddress;

    if (isMaintenanceMode && !allowedIPs.includes(clientIP)) {
      this.logger.warn(`Maintenance mode: Blocked request from ${clientIP}`);
      
      res.status(503).json({
        message: 'Sistema en mantenimiento',
        details: 'El sistema está temporalmente fuera de servicio por mantenimiento programado.',
        retryAfter: process.env.MAINTENANCE_RETRY_AFTER || '3600',
      });
      return;
    }

    next();
  }
}

/**
 * API Version Middleware - Equivalente a Laravel's API versioning
 */
@Injectable()
export class ApiVersionMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const acceptHeader = req.headers.accept;
    const versionHeader = req.headers['api-version'] as string;
    
    let apiVersion = 'v1'; // Default version

    // Check Accept header for version (e.g., application/vnd.api+json;version=2)
    if (acceptHeader && acceptHeader.includes('version=')) {
      const versionMatch = acceptHeader.match(/version=(\d+)/);
      if (versionMatch) {
        apiVersion = `v${versionMatch[1]}`;
      }
    }

    // Check explicit version header
    if (versionHeader) {
      apiVersion = versionHeader.startsWith('v') ? versionHeader : `v${versionHeader}`;
    }

    (req as any).apiVersion = apiVersion;
    res.set('API-Version', apiVersion);

    next();
  }
}

/**
 * Middleware Manager - Equivalente a Laravel's Kernel
 */
export class MiddlewareManager {
  static throttle(maxAttempts: number = 60, decayMinutes: number = 1) {
    return new ThrottleMiddleware(maxAttempts, decayMinutes);
  }

  static cors() {
    return new CorsMiddleware();
  }

  static requestLogging() {
    return new RequestLoggingMiddleware();
  }

  static securityHeaders() {
    return new SecurityHeadersMiddleware();
  }

  static requestId() {
    return new RequestIdMiddleware();
  }

  static maintenanceMode() {
    return new MaintenanceModeMiddleware();
  }

  static apiVersion() {
    return new ApiVersionMiddleware();
  }
}

/**
 * Middleware Groups - Equivalente a Laravel's middleware groups
 */
export const MiddlewareGroups = {
  web: [
    MiddlewareManager.securityHeaders(),
    MiddlewareManager.cors(),
    MiddlewareManager.requestId(),
    MiddlewareManager.requestLogging(),
    MiddlewareManager.maintenanceMode(),
  ],
  
  api: [
    MiddlewareManager.throttle(100, 1), // 100 requests per minute
    MiddlewareManager.securityHeaders(),
    MiddlewareManager.cors(),
    MiddlewareManager.requestId(),
    MiddlewareManager.apiVersion(),
    MiddlewareManager.requestLogging(),
    MiddlewareManager.maintenanceMode(),
  ],

  'api.strict': [
    MiddlewareManager.throttle(30, 1), // 30 requests per minute
    MiddlewareManager.securityHeaders(),
    MiddlewareManager.requestId(),
    MiddlewareManager.requestLogging(),
  ],
};

// Uso en app.module.ts:
// export class AppModule implements NestModule {
//   configure(consumer: MiddlewareConsumer) {
//     consumer
//       .apply(...MiddlewareGroups.api)
//       .forRoutes({ path: 'api/*', method: RequestMethod.ALL });
//   }
// }

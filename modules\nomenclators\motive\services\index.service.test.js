/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const MotiveModel = require('../models/motive');
const service = require('./index.service');

let Logger;
describe('Motive nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };

  it('should create an Haber', async () => {
    const { error } = await service.createMotive({
      id: 'id-1-A',
      motive: 'reason 1',
      option: 'Haber'
    });
    const { result, isError } = await service.getMotives();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });

  it('should NOT create an Haber with motive length over the limit', async () => {
    const { error } = await service.createMotive({
      motive:
        'reason 1234567890123tamañosobrelimitereason 1234567890123tamañosobrelimitereason 1234567890123tamañosobrelimitereason 1234567890123tamañosobrelimitereason 1234567890123tamañosobrelimitereason 1234567890123tamañosobrelimite',
      option: 'Haber'
    });
    const { result, isError } = await service.getMotives();

    expect(error.errors.motive.kind).toBe('maxlength');
    expect(isError).toBe(undefined);
    expect(result.length).toBe(0);
  });

  it('should create a Descuento', async () => {
    const { error } = await service.createMotive({
      motive: 'reason 2',
      option: 'Descuento'
    });
    const { result, isError } = await service.getMotives();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });
  it('should find one Haber and update the reason ', async () => {
    await MotiveModel.create({
      motive: 'reason 1',
      option: 'Haber',
      enabled: false
    });

    const newMotiveData = {
      motive: 'reason 2',
      option: 'Descuento',
      enabled: true
    };
    // create and save one  document
    await MotiveModel.create(newMotiveData);

    const { error } = await service.updateMotive({
      ...newMotiveData,
      motive: 'reason 2',
      option: 'Descuento'
    });
    const { result, isError } = await service.getMotives();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].motive).toBe('reason 2');
  });

  it('should find one Descuento and update', async () => {
    await MotiveModel.create({
      motive: 'reason 1',
      option: 'Descuento',
      enabled: false
    });

    const newMotiveData = {
      motive: 'reason 2',
      option: 'Haber',
      enabled: true
    };
    // create and save one  document
    await MotiveModel.create(newMotiveData);

    const { error } = await service.updateMotive({
      ...newMotiveData,
      motive: 'reason 2',
      option: 'Haber'
    });
    const { result, isError } = await service.getMotives();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].option).toBe('Haber');
  });

  it('should find one Descuento and remove  & update', async () => {
    const motiveData = {
      motive: 'reason 1',
      option: 'Haber',
      enabled: false
    };
    const motiveData1 = {
      motive: 'reason 3',
      option: 'Haber',
      enabled: true
    };
    await MotiveModel.create(motiveData);
    const motive1 = await MotiveModel.create(motiveData1);

    const { result, isError } = await service.updateMotive({
      _id: motive1.id,
      motive: 'reason 2'
    });
    expect(result.motive).toBe('reason 2');
    expect(isError).toBe(false);
  });

  it('should delete a motive', async () => {
    // create and save one  document
    const motive = await MotiveModel.create({
      motive: 'reason 1',
      option: 'Haber',
      enabled: true
    });
    const { createdAt, updatedAt } = { ...motive.toObject() };

    const { error } = await service.deleteMotive(motive.id);

    const [resultWithoutMotive, resultWithMotives] = await Promise.all([
      service.getMotives(),
      service.getMotives({ enabled: false })
    ]);
    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());

    expect(resultWithoutMotive.result.length).toBe(0);
    expect(resultWithMotives.result.length).toBe(1);
    expect(resultWithMotives.result[0].enabled).toEqual(false);
  });

  it('can´t  duplicate motive ', async () => {
    // create and save one  document
    await MotiveModel.create({
      motive: 'reason 1',
      option: 'Haber',
      enabled: true
    });
    const motive2 = await MotiveModel.create({
      motive: 'reason 2',
      option: 'Descuento',
      enabled: true
    });

    const { error } = await service.updateMotive({
      _id: motive2.id,
      motive: 'reason 1',
      option: 'Haber'
    });
    const { result } = await service.getMotives();

    expect(result[1].motive).toBe('reason 2');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  afterEach(async () => {
    try {
      await MotiveModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

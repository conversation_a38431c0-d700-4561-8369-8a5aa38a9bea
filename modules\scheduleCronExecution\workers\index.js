const workerModule = require('./worker');
const { scheduleCronJobList, jobFields } = require('../scheduleCronList');
const { getFirstNbusinessDays, getMonthHolidays } = require('../../sharedFiles/helpers');
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../service/scheduleJobs.service');

module.exports = {
  name: 'schedulingCronjobs',
  worker: deps =>
    workerModule.workerFn({
      scheduleCronJobList,
      jobFields,
      logService,
      service,
      getFirstNbusinessDays,
      getMonthHolidays,
      ...deps
    }),
  repeatInterval: process.env.CRON_SCHEDULING_CRONJOBS_FREQUENCY,
  description:
    'Cron para indicar frecuencia de reintento de los crons q se ejecutan en un día especifico',
  endPoint: 'schedulingcronjobs',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');

const FactoryController = require('../modules/verifyCronExecution/controllers/index.controller');
const LogService = require('../modules/sharedFiles/services/jobLog.service');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    LogService
  });

  router.get('/:cronName', validateAccess(), controller.verifyCronExecution);
};

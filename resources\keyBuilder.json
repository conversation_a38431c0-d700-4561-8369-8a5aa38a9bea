[{"_doc": {"_id": "5f873fc2b82444ba5a420575", "paymentInfo": {"branchOffice": "PUERTO AYSEN", "paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": "Amsterdam", "commune": "xx", "city": "XX"}, "beneficiary": {"phone": "", "rut": "********-7", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 11, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "enabled": false, "basePension": 17000.08, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1986-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por orfandad", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T04:42:46.000Z", "article40": 234, "createdAt": "2020-10-14T18:13:12.724Z", "updatedAt": "2020-10-14T19:41:33.040Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": "2055-02-10T03:00:00.000Z", "endDateOfValidity": "2055-02-10T03:00:00.000Z", "discountsAndAssets": "5f7de28f3f94782d35a5e67a", "linkedDate": "2020-10-14T18:13:22.773Z", "evaluationDate": "2020-11-10T04:00:00.000Z"}}, {"_doc": {"_id": "5f873fc2b82444ba5a420575", "paymentInfo": {"branchOffice": "PUERTO AYSEN", "paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": "Amsterdam", "commune": "xx", "city": "XX"}, "beneficiary": {"phone": "", "rut": "********-7", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 11, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "enabled": false, "basePension": 17000.08, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1986-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON>nte vital<PERSON>", "pensionType": "Pensión por orfandad", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T04:42:46.000Z", "article40": 234, "createdAt": "2020-10-14T18:13:12.724Z", "updatedAt": "2020-10-14T19:41:33.040Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": "2055-02-10T03:00:00.000Z", "endDateOfValidity": "2055-02-10T03:00:00.000Z", "discountsAndAssets": "5f7de28f3f94782d35a5e67a", "linkedDate": "2020-10-14T18:13:22.773Z", "evaluationDate": "2020-11-10T04:00:00.000Z"}}, {"_doc": {"_id": "5f873fc2b82444ba5a420575", "paymentInfo": {"branchOffice": "PUERTO AYSEN", "paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": "Amsterdam", "commune": "xx", "city": "XX"}, "beneficiary": {"phone": "", "rut": "********-7", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 11, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "enabled": false, "basePension": 17000.08, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1986-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T04:42:46.000Z", "article40": 234, "createdAt": "2020-10-14T18:13:12.724Z", "updatedAt": "2020-10-14T19:41:33.040Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": "2055-02-10T03:00:00.000Z", "endDateOfValidity": "2055-02-10T03:00:00.000Z", "discountsAndAssets": "5f7de28f3f94782d35a5e67a", "linkedDate": "2020-10-14T18:13:22.773Z", "evaluationDate": "2020-11-10T04:00:00.000Z"}}, {"_doc": {"_id": "5f873fc2b82444ba5a420575", "paymentInfo": {"branchOffice": "PUERTO AYSEN", "paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": "Amsterdam", "commune": "xx", "city": "XX"}, "beneficiary": {"phone": "", "rut": "********-7", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 11, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "enabled": false, "basePension": 17000.08, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1986-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por orfandad", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T04:42:46.000Z", "article40": 234, "createdAt": "2020-10-14T18:13:12.724Z", "updatedAt": "2020-10-14T19:41:33.040Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": "2055-02-10T03:00:00.000Z", "endDateOfValidity": "2055-02-10T03:00:00.000Z", "discountsAndAssets": "5f7de28f3f94782d35a5e67a", "linkedDate": "2020-10-14T18:13:22.773Z", "evaluationDate": "2020-11-10T04:00:00.000Z", "parentRUT": "********-7"}}, {"_doc": {"_id": "5f873fc2b82444ba5a420575", "paymentInfo": {"branchOffice": "PUERTO AYSEN", "paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": "Amsterdam", "commune": "xx", "city": "XX"}, "beneficiary": {"phone": "", "rut": "********-7", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 11, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "enabled": false, "basePension": 17000.08, "country": "CUB", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1986-02-10T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "1900-01-01T04:42:46.000Z", "article40": 234, "createdAt": "2020-10-14T18:13:12.724Z", "updatedAt": "2020-10-14T19:41:33.040Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": "2055-02-10T03:00:00.000Z", "endDateOfValidity": "2055-02-10T03:00:00.000Z", "discountsAndAssets": "5f7de28f3f94782d35a5e67a", "linkedDate": "2020-10-14T18:13:22.773Z", "evaluationDate": "2020-11-10T04:00:00.000Z"}}]
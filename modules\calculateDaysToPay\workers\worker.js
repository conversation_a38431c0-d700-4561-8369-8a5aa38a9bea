const cronDescription = 'calculate days to pay:';
const cronMark = 'CALCULATE_DAYS_TO_PAY_WORKER';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;

const dependencyMark = 'MANUALLY_INACTIVATE_MARKED_PENSIONS';

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, service, sharedHelper, logService, job, done }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription} process started`);
    const [year, month] = sharedHelper.getCurrentYearAndMonth();
    const { error } = await service.calculateDaysToPay(month, year);
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

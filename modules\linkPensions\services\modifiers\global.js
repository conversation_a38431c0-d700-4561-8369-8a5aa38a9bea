const DISABILITY_PENSION = /invalidez/i;

const defaultDisabilityType = pension =>
  DISABILITY_PENSION.test(pension.pensionType) ? 'Invalidez total' : 'No invalida';
const setFields = pension => {
  const fields = {
    disabilityType: pension.disabilityType || defaultDisabilityType(pension),
    validatedStudyPeriod: 'No',
    fixedBasePension: 0,
    fixedArticle40: 0,
    fixedArticle41: 0,
    daysToPay: 0,
    inactivateManually: false,
    beneficiary: {
      ...pension.beneficiary,
      phone: pension.beneficiary.phone
    },
    collector: {
      ...pension.collector,
      address: pension.collector.address,
      commune: pension.collector.commune,
      city: pension.collector.city
    },
    reservedAmounts: {
      ...pension.reservedAmounts,
      forDisability: 0,
      forInstitutionalPatient: 0,
      forRejection: 0,
      forSurvival: 0,
      forPayCheck: 0
    },
    assets: {
      ...pension.assets,
      aps: 0,
      healthDiscount: 'No',
      taxableTotalNonFormulable: 0,
      healthExemption: 'No',
      forFamilyAssignment: 0
    },
    retroactiveAmounts: {
      ...pension.retroactiveAmounts,
      forSurvival: 0,
      forDisability: 0,
      forInstitutionalPatient: 0,
      forRejection: 0,
      forPayCheck: 0
    },
    discounts: {
      ...pension.discounts,
      health: 0,
      nonFormulableByReason: [],
      afp: 0
    },
    capitalStatus: 'Nuevo',
    totalCapital: 0,
    apsInfo: {
      apsResolutionNumber: 0,
      apsResolutionDate: 0,
      apsPaymentUniqueId: 0,
      apsTransferCode: 0,
      apsOrigin: ''
    },
    bankRejected: 'No',
    paycheckRefunded: 'No',
    manuallyReactivated: false,
    paymentInfo: {
      ...pension.paymentInfo
    },
    ChangeOfPensionTypeDueToCharges: false,
    rejectionHealthExemptionAmount: 0,
    rejectionHealthReductionAmount: 0,
    law19403: 0,
    law19539: 0,
    law19953: 0,
    rejectionIPS: false,
    checkPoint: false
  };
  return { ...pension, ...fields };
};

module.exports = setFields;

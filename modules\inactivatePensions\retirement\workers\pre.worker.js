const cronDescription = 'pre worker inactivación por jubilación No. 7:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'RETIREMENT_PRE_WORKER';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'INACTIVATE_MARRIAGE_POST_WORKER';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const workerFn = async ({ Logger, service, pensionService, logService, job, done }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription} process started`);
    const { pensions, error } = await service.findPensionToInactivate();
    if (error) throw new Error(error);
    const { markPensionError, pensionsToEvaluate } = await service.markPensionToInactivate(
      pensions
    );
    if (markPensionError) throw new Error(markPensionError);

    const { completed: isComplete, errorPension } = await pensionService.createUpdatePension(
      pensionsToEvaluate
    );
    if (!isComplete) throw new Error(errorPension);

    await logService.saveLog(cronMark);

    Logger.info(successMessage);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

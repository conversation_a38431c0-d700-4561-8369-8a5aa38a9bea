const mongoose = require('mongoose');
const moment = require('moment');

const MINUTES_AFTER_SCHEDULE = process.env.MINUTES_TO_EXECUTE_AFTER_SCHEDULE;
const DEFAULT_MINUTES = 5;

const services = {
  getJobListWithExecutionDate: async ({
    scheduleCronJobList,
    jobFields,
    getFirstNbusinessDays,
    getMonthHolidays
  }) => {
    try {
      const cronJobListPromises = scheduleCronJobList.map(job => async () => {
        const { name, businessDaysToExecute, timeToExecute = '00:00:00' } = job;
        const businessDays = await getFirstNbusinessDays(
          new Date(),
          businessDaysToExecute,
          getMonthHolidays
        );
        const date = businessDays.pop();
        const dateTime = moment(`${date} ${timeToExecute}`, 'YYYY-MM-DD HH:mm:ss').toDate();
        const dateNow = new Date();
        const isAfterDate = moment(dateNow).diff(dateTime) > 0;
        return {
          ...jobFields,
          name,
          nextRunAt: isAfterDate
            ? moment(dateNow)
                .add(MINUTES_AFTER_SCHEDULE || DEFAULT_MINUTES, 'm')
                .toDate()
            : dateTime
        };
      });

      const jobListWithExecutionDate = await Promise.all(
        cronJobListPromises.map(promise => promise())
      );

      return { jobListWithExecutionDate, error: null };
    } catch (error) {
      return { jobListWithExecutionDate: null, error };
    }
  },
  scheduleJobs: async jobList => {
    try {
      const bulk = mongoose.connection.db.collection('agendaJobs').initializeOrderedBulkOp();
      jobList.forEach(({ name, nextRunAt, ...jobData }) => {
        bulk
          .find({
            name
          })
          .upsert()
          .updateOne({
            $set: {
              ...jobData,
              nextRunAt,
              name,
              lockedAt: null
            }
          });
      });
      await bulk.execute();
      return { completed: true, errorSchedulingJobs: null };
    } catch (error) {
      return { completed: false, errorSchedulingJobs: error };
    }
  },
  areAllJobsFinished: async ({ logService, scheduleCronJobList }) => {
    try {
      const allFilemarksNames = scheduleCronJobList.reduce(
        (obj, { fileMarks }) => [...obj, ...fileMarks],
        []
      );
      const markFileListFn = await Promise.all(
        allFilemarksNames.map(fileMark => logService.existsLog(fileMark))
      );
      return { areAllJobsFinished: markFileListFn.every(x => x), errorGettingMarks: null };
    } catch (errorGettingMarks) {
      return { areAllJobsFinished: false, errorGettingMarks };
    }
  }
};

module.exports = { ...services };

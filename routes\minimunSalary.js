const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const validateAccess = require('../lib/auth/validate');

const MinimunSalaryController = require('../modules/minimunsalary/controllers/index.controller');
const minimunSalaryService = require('../modules/minimunsalary/services/index.services');

module.exports = router => {
  const minimunSalaryController = MinimunSalaryController({
    HttpStatus,
    ErrorBuilder,
    minimunSalaryService,
    validationResult,
    Logger
  });

  router.get('/im', validateAccess(), minimunSalaryController.getMinimunSalarys);
  router.put('/im', validateAccess(), minimunSalaryController.updateMinimunSalary);
};

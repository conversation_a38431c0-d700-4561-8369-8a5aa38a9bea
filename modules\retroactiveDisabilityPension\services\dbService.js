/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */
const moment = require('moment');
const PensionHistoricModel = require('../../../models/pensionHistoric');

const PENSION_TYPE = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const isNotValid = validityType => /No\s+vigente/i.test(validityType);

const addretroactiveAmountsByDisability = async pensions => {
  let sumValue = 0;
  let isValid = false;
  for (let i = 0; pensions[i] && isNotValid(pensions[i].validityType); i += 1) {
    isValid = true;
    const { forDisability = 0 } = pensions[i].reservedAmounts;
    sumValue = forDisability + sumValue;
  }
  return { isValid, sumValue: Math.round(sumValue * 100) / 100 };
};

const evaluatePensions = async pensions => {
  const calculatedPensions = [];

  for await (const {
    _doc: { _id, ...pension }
  } of pensions) {
    const {
      beneficiary: { rut: beneficiaryRut },
      causant: { rut: causantRut },
      retroactiveAmounts
    } = pension;

    const historicPensions = await PensionHistoricModel.aggregate([
      {
        $match: {
          enabled: false,
          createdAt: {
            $lt: moment()
              .startOf('month')
              .toDate()
          },
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut
        }
      },
      { $sort: { createdAt: -1 } },
      {
        $group: {
          _id: { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } },
          date: { $first: '$createdAt' },
          data: { $first: '$$ROOT' }
        }
      },
      { $replaceRoot: { newRoot: '$data' } },
      { $sort: { createdAt: -1 } }
    ]);

    const { isValid, sumValue: forDisability } = await addretroactiveAmountsByDisability(
      historicPensions
    );

    if (isValid) {
      calculatedPensions.push({
        ...pension,
        retroactiveAmounts: { ...retroactiveAmounts, forDisability }
      });
    }
  }

  return calculatedPensions;
};

const service = {
  async retroactiveDisabilityPension(pensionService) {
    const { result: pensionToEvaluate } = await pensionService.getAllAndFilter({
      validityType: /Vigente hasta la jubilaci[oó]n/i,
      transient: /no/i,
      pensionType: { $in: PENSION_TYPE },
      enabled: true
    });

    const calculatedPensions = await evaluatePensions(pensionToEvaluate);
    const { completed, error } = await pensionService.updatePensions(calculatedPensions);
    return { completed, error };
  }
};

module.exports = { addretroactiveAmountsByDisability, ...service };

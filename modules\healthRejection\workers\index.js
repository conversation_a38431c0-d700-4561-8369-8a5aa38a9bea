const tmp = require('tmp');

const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const pensionService = require('../../pensions/services/pension.service');
const workerModule = require('./worker');
const Sftp = require('../../sharedFiles/sftpClient');
const { extractZip } = require('../../sharedFiles/helpers');

const { SFTP_HOST, SFTP_USER, SFTP_PASS, SFTP_PORT } = process.env;
const sftpCredentials = {
  host: SFTP_HOST,
  user: SFTP_USER,
  password: SFTP_PASS,
  port: SFTP_PORT
};

module.exports = {
  name: 'health-rejection',
  worker: deps =>
    workerModule.workerFn({
      sftpCredentials,
      pensionService,
      service,
      logService,
      Sftp,
      tmp,
      extractZip,
      ...deps
    }),
  repeatInterval: process.env.CRON_FILE_HEALTH_REJECTION_FREQUENCY,
  description: 'Obtener archivos Rechazos Rebaja y Exención Salud del SFTP del IPS',
  endPoint: 'healthrejection',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

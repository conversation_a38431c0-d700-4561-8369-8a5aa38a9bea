[{"chargeId": "1111111-1", "causantId": "22222222-2", "collectorId": "33333333-3", "chargeValidityType": "Carga interna inactiva", "typeOfAssocietedPension": "supervivencia", "dateOfChargeInactivation": "2020-02-06T17:10:00.002+00:00", "startDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "endDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "invalid": "No", "relationship": "<PERSON><PERSON>", "familyAssignment": 7500, "retroactiveFamilyAssignment": 0}, {"chargeId": "44444444-4", "causantId": "55555555-5", "collectorId": "66666666-6", "chargeValidityType": "Carga interna inactiva", "typeOfAssocietedPension": "Invalidez", "dateOfChargeInactivation": "2020-02-06T17:10:00.002+00:00", "startDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "endDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "invalid": "No", "relationship": "<PERSON><PERSON>", "familyAssignment": 7500, "retroactiveFamilyAssignment": 0}, {"chargeId": "1111111-1", "causantId": "22222222-2", "collectorId": "33333333-3", "chargeValidityType": "Carga interna activa", "typeOfAssocietedPension": "Supervivencia", "dateOfChargeInactivation": "2020-02-06T17:10:00.002+00:00", "startDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "endDateOfCertificationValidity": "2022-01-07T17:10:00.002+00:00", "invalid": "No", "relationship": "<PERSON><PERSON>", "validityType": "<PERSON><PERSON>nte <PERSON>", "familyAssignment": 7500, "retroactiveFamilyAssignment": 0}, {"chargeId": "1111111-1", "causantId": "22222222-2", "collectorId": "33333333-3", "chargeValidityType": "Carga externa activa", "typeOfAssocietedPension": "Supervivencia", "dateOfChargeInactivation": "2020-02-06T17:10:00.002+00:00", "startDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "endDateOfCertificationValidity": "2022-01-01T17:10:00.002+00:00", "invalid": "No", "relationship": "<PERSON><PERSON>", "validityType": "<PERSON><PERSON>nte <PERSON>", "familyAssignment": 7500, "retroactiveFamilyAssignment": 0}, {"chargeId": "1111111-1", "causantId": "22222222-2", "collectorId": "33333333-3", "chargeValidityType": "Carga externa activa", "typeOfAssocietedPension": "supervivencia", "dateOfChargeInactivation": "2020-02-06T17:10:00.002+00:00", "startDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "endDateOfCertificationValidity": "2020-02-06T17:10:00.002+00:00", "invalid": "No", "relationship": "<PERSON><PERSON>", "familyAssignment": 7500, "retroactiveFamilyAssignment": 0}]
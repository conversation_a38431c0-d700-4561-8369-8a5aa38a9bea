/* eslint-disable no-restricted-globals */

const isDate = date => /([12]\d{3}[/.-](0[1-9]|1[0-2])[/.-](0[1-9]|[12]\d|3[01]))/gi.test(date);
const parseNumberValue = number => Math.round((+number + Number.EPSILON) * 100) / 100;
const isNumber = value => !isNaN(parseFloat(value)) && isFinite(value);
const mapperDiscountsTemporary = field => {
  const temporaryPensions = field.map(item => {
    return {
      ...item,
      basePension: parseNumberValue(item.basePension),
      initialBasePension: parseNumberValue(item.initialBasePension),
      article40: isNumber(item.article40) ? parseNumberValue(item.article40) : '',
      article41: item.article41,
      pensionStartDate: isDate(item.pensionStartDate) ? new Date(item.pensionStartDate) : '',
      discounts: {
        ...item.discounts,
        healthUF: isNumber(item.discounts.healthUF)
          ? parseNumberValue(item.discounts.healthUF)
          : '',
        onePercentLaAraucana: 'No',
        nonFormulableByReason: [],
        socialCreditsLaAraucana: 0,
        onePercent18: 'No',
        socialCredits18: 0,
        onePercentLosAndes: 'No',
        socialCreditsLosAndes: 0,
        othersLosAndes: 0,
        onePercentLosHeroes: 'No',
        socialCreditsLosHeroes: 0,
        othersLosHeroes: 0,
        healthLoan: 0,
        health: 0,
        afp: 0,
        totalNonFormulable: 0,
        onePercentAdjusted: 0
      }
    };
  });
  return temporaryPensions;
};
module.exports = mapperDiscountsTemporary;

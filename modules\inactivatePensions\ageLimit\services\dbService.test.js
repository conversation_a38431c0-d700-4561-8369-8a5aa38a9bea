/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionModel = require('../../../../models/pension');
const service = require('./dbService');
const pensionsList = require('../../../../resources/pensionSoonToInactivate.json');

describe('Inactivate by age limit compliance test', () => {
  beforeAll(beforeAllTests);

  it('should find and inactivate orpanhood pensions that have endDateOfValidty matching the current year', async () => {
    await PensionModel.create({
      ...pensionsList[0],
      endDateOfValidity: new Date(),
      validityType: 'Vigente orfandad',
      pensionType: 'Pension por orfandad'
    });

    await service.inactivatePensionsByAgeLimitCompliance();
    // Now the collection shoul have two documents
    const count = await PensionModel.collection.countDocuments();
    expect(count).toBe(1);
    // One document should be disabled
    const disabledDoc = await PensionModel.find({ enabled: false });
    expect(disabledDoc.length).toBe(0);
    // The enabled doc should have a defined inactivationReason field
    const enabledDoc = await PensionModel.find({ enabled: true });
    expect(enabledDoc[0].inactivationReason).toBeDefined();
  });

  it('should return {hasError: true} if there is any error', async () => {
    jest.spyOn(PensionModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });
    const { hasError } = await service.inactivatePensionsByAgeLimitCompliance();
    expect(hasError).toBe(true);
  });

  afterAll(afterAllTests);
});

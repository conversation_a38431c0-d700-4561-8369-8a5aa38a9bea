const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./ipc.service');

describe('IPC services', () => {
  beforeAll(beforeAllTests);

  it('create new ipc', async () => {
    const todaysDate = new Date();
    const { result, error } = await service.insertIPC({
      date: todaysDate,
      value: '103.55',
      percentage: '3.1415',
      isLastPercentageChange: false
    });
    expect(result).toBeDefined();
    expect(error).toBe(null);
  });

  it('create new ipc with error', async () => {
    const { result, error } = await service.insertIPC({
      date: '',
      value: '',
      isLastPercentageChange: false
    });

    expect(result).toBeDefined();
    expect(error).toBeDefined();
  });

  it('return last percentage ipc', async () => {
    const todaysDate = new Date();
    const { result, error } = await service.insertIPC({
      date: todaysDate,
      value: '103.55',
      percentage: '3.1415',
      isLastPercentageChange: true
    });

    const pastMonth = new Date(new Date().setUTCMonth(todaysDate.getUTCMonth() - 1));
    await service.insertIPC({
      date: pastMonth,
      value: '103.55',
      percentage: '3.1415',
      isLastPercentageChange: false
    });

    const lastIpc = await service.getLastPercentageChange();
    expect(result).toBeDefined();
    expect(error).toBe(null);
    expect(lastIpc).toBeDefined();
    expect(lastIpc.isLastPercentageChange).toBe(true);
    expect(lastIpc.date).toStrictEqual(todaysDate);
  });

  it('return last percentage ipc getLastPercentage', async () => {
    const todaysDate = new Date();
    const { result, error } = await service.insertIPC({
      date: todaysDate,
      value: '103.55',
      percentage: '3.1415',
      isLastPercentageChange: false
    });

    const pastMonth = new Date(new Date().setUTCMonth(todaysDate.getUTCMonth() - 1));
    await service.insertIPC({
      date: pastMonth,
      value: '103.55',
      percentage: '3.1415',
      isLastPercentageChange: true
    });

    const lastIpc = await service.getLastPercentage();
    expect(result).toBeDefined();
    expect(error).toBe(null);
    expect(lastIpc).toBeDefined();
    expect(lastIpc.isLastPercentageChange).toBe(false);
    expect(lastIpc.date).toStrictEqual(todaysDate);
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { calculateEndDateOfValidity } = require('./modifiers');
const pensionsToReCalculateRetirement = require('../../../resources/pensionsToReCalculateRetirement.json');

describe('Modifiers file Test', () => {
  beforeAll(beforeAllTests);

  it('should return modified pension when re-calculated end date of validity', async () => {
    const malePension = await calculateEndDateOfValidity(pensionsToReCalculateRetirement[0]);

    expect(malePension).toBeDefined();
  });

  afterAll(afterAllTests);
});

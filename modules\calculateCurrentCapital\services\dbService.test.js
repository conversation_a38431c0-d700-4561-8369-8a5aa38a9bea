/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const pensions = require('../../../resources/pensionToCalculateCurrentCapital.json');
const {
  currentFactor,
  concurrency,
  ipc,
  currentFactorWith2Keys,
  currentFactorWith4Keys,
  currentConcurrency
} = require('../../../resources/factors_concurrencies_ipcs.json');
const FactorModel = require('../../quadrature/models/factor');
const ConcurrencyModel = require('../../quadrature/models/concurrency');
const IpcModel = require('../../../models/ipcs');

describe('Calculate current capital', () => {
  beforeAll(beforeAllTests);

  let pensionService;

  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: pensions })),
      createUpdatePension: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('should calculate next brithday in months', () => {
    const date = new Date();
    const newDate = new Date(date.setMonth(date.getMonth()));
    const next = service.calculateMonthOfBirth(newDate);
    expect(next).toBe(11);
  });

  it('should calculate next brithday for next month', () => {
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const next = service.calculateMonthOfBirth(newDate);
    expect(next).toBe(1);
  });

  it('should calculate base pension capital either with 2 keys or 4 keys', () => {
    const basePension = 130;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const basepensionCapitalWith2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      basePension,
      dateOfBirth,
      currentConcurrency
    );
    const basepensionCapitalWith4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      basePension,
      dateOfBirth,
      currentConcurrency
    );
    expect(basepensionCapitalWith2Keys).toBe(2111.55);
    expect(basepensionCapitalWith4Keys).toBe(362418.35);
  });

  it('should calculate increasingInLaw19578 either with 2 keys or 4 keys', () => {
    const increasingInLaw19578 = 100;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const increasingInLaw19578With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      increasingInLaw19578,
      dateOfBirth,
      currentConcurrency
    );
    const increasingInLaw19578With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      increasingInLaw19578,
      dateOfBirth,
      currentConcurrency
    );
    expect(increasingInLaw19578With2Keys).toBe(1624.27);
    expect(increasingInLaw19578With4Keys).toBe(278783.35);
  });

  it('should calculate increasingInLaw19953 either with 2 keys or 4 keys', () => {
    const increasingInLaw19953 = 150;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const increasingInLaw19953With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      increasingInLaw19953,
      dateOfBirth,
      currentConcurrency
    );
    const increasingInLaw19953With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      increasingInLaw19953,
      dateOfBirth,
      currentConcurrency
    );
    expect(increasingInLaw19953With2Keys).toBe(2436.4);
    expect(increasingInLaw19953With4Keys).toBe(418175.02);
  });

  it('should calculate increasingInLaw20102 either with 2 keys or 4 keys', () => {
    const increasingInLaw20102 = 120;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const increasingInLaw20102With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      increasingInLaw20102,
      dateOfBirth,
      currentConcurrency
    );
    const increasingInLaw20102With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      increasingInLaw20102,
      dateOfBirth,
      currentConcurrency
    );
    expect(increasingInLaw20102With2Keys).toBe(1949.12);
    expect(increasingInLaw20102With4Keys).toBe(334540.02);
  });

  it('should calculate law19403 either with 2 keys or 4 keys', () => {
    const law19403 = 270;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const law19403With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      law19403,
      dateOfBirth,
      currentConcurrency
    );
    const law19403With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      law19403,
      dateOfBirth,
      currentConcurrency
    );
    expect(law19403With2Keys).toBe(4385.52);
    expect(law19403With4Keys).toBe(752715.04);
  });

  it('should calculate Law19953 either with 2 keys or 4 keys', () => {
    const law19953 = 180.98;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const law19953With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      law19953,
      dateOfBirth,
      currentConcurrency
    );
    const law19953With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      law19953,
      dateOfBirth,
      currentConcurrency
    );
    expect(law19953With2Keys).toBe(2939.6);
    expect(law19953With4Keys).toBe(504542.1);
  });

  it('should calculate Law19539 either with 2 keys or 4 keys', () => {
    const law19539 = 134.67;
    const newDate = moment()
      .add(2, 'months')
      .toDate();
    const dateOfBirth = newDate;
    const law19539With2Keys = service.calculateAmountWithKeys(
      currentFactorWith2Keys,
      law19539,
      dateOfBirth,
      currentConcurrency
    );
    const law19539With4Keys = service.calculateAmountWithKeys(
      currentFactorWith4Keys,
      law19539,
      dateOfBirth,
      currentConcurrency
    );
    expect(law19539With2Keys).toBe(2187.4);
    expect(law19539With4Keys).toBe(375437.53);
  });
  it('calculate working and not working capital for every field needed ', () => {
    const basePension = 130;
    currentConcurrency.concurrencyPercentage = 135.67;

    const workingCapitalAmount = service.workingCapital(basePension, currentConcurrency)
      .workingCapital;
    const notWorkingCapitalAmount = service.workingCapital(basePension, currentConcurrency)
      .notWorkingCapital;
    expect(workingCapitalAmount).toBe(130);
    expect(notWorkingCapitalAmount).toBe(0);
  });
  it('calculate readjusment by ipc ', () => {
    const amount = 120;
    const { percentage } = ipc[0];
    const calculateIpc = service.calculateReadjustmentByIpc(amount, percentage);
    expect(calculateIpc).toBe(260.74);
  });
  it('should calculate set capital pensions with 2 keys', () => {
    const { currentCapitalCalculation: capitalPension } = service.setCapitalPensions(
      pensions[0]._doc,
      currentFactor,
      concurrency,
      ipc
    );
    expect(capitalPension.basePensionCapital).toBe(25408690.3);
    expect(capitalPension.capitalLaw19578).toBe(1785.45);
    expect(capitalPension.capitalLaw19953).toBe(1787.23);
    expect(capitalPension.capitalLaw20102).toBe(1789.02);
    expect(capitalPension.capitalBonusLaw19403).toBe(48159);
    expect(capitalPension.capitalBonusLaw19953).toBe(32280.8);
    expect(capitalPension.capitalBonusLaw19539).toBe(24020.64);
    expect(capitalPension.basePensionWorkingCapital).toBe(14145017.89);
    expect(capitalPension.basePensionNotWorkingCapital).toBe(11263672.41);
    expect(capitalPension.workingCapitalLaw19578).toBe(5.57);
    expect(capitalPension.notWorkingCapitalLaw19578).toBe(4.44);
    expect(capitalPension.workingCapitalLaw19953).toBe(5.58);
    expect(capitalPension.notWorkingCapitalLaw19953).toBe(4.44);
    expect(capitalPension.workingCapitalLaw20102).toBe(5.58);
    expect(capitalPension.notWorkingCapitalLaw20102).toBe(4.45);
    expect(capitalPension.workingCapitalBonusLaw19403).toBe(150.31);
    expect(capitalPension.notWorkingCapitalBonusLaw19403).toBe(119.69);
    expect(capitalPension.workingCapitalBonusLaw19953).toBe(100.75);
    expect(capitalPension.notWorkingCapitalBonusLaw19953).toBe(80.23);
    expect(capitalPension.workingCapitalBonusLaw19539).toBe(74.97);
    expect(capitalPension.notWorkingCapitalBonusLaw19539).toBe(59.7);
    expect(capitalPension.capitalPBIpc).toBe(26280208.38);
    expect(capitalPension.capitalLaw19578Ipc).toBe(1846.69);
    expect(capitalPension.capitalLaw19953Ipc).toBe(1848.53);
    expect(capitalPension.capitalLaw20102Ipc).toBe(1850.38);
    expect(capitalPension.capitalBonusLaw19403Ipc).toBe(49810.85);
    expect(capitalPension.capitalBonusLaw19953Ipc).toBe(33388.03);
    expect(capitalPension.capitalBonusLaw19539Ipc).toBe(24844.55);
    expect(capitalPension.totalCapital).toBe(25585114.560000002);
    expect(capitalPension.capitalTotalBonus).toBe(66602.12);
    expect(capitalPension.totalCapitalIpc).toBe(26462683.980000004);
    expect(capitalPension.capitalTotalBonusIpc).toBe(68886.57);
  });

  it('calculate current capital succesful ', async () => {
    jest.spyOn(service, 'getFactorAndConcurrencies').mockImplementationOnce(() => {
      return { factors: currentFactor, concurrencies: concurrency, ipcs: ipc };
    });
    const result = await service.calculateCurrentCapital(pensionService);
    expect(result.completed).toBe(true);
  });

  it('calculate current capital failed', async () => {
    jest.spyOn(service, 'getFactorAndConcurrencies').mockImplementationOnce(() => {
      return { factors: [], concurrencies: [], ipcs: [] };
    });
    const result = await service.calculateCurrentCapital(pensionService);
    expect(result.completed).toBe(false);
  });

  it('should get factors concurrencies and ipcs ', async () => {
    const mocks = {
      lean: jest.fn().mockResolvedValue([])
    };

    jest.spyOn(FactorModel, 'find').mockImplementationOnce(() => mocks);
    jest.spyOn(ConcurrencyModel, 'find').mockImplementationOnce(() => mocks);
    jest.spyOn(IpcModel, 'find').mockImplementationOnce(() => mocks);

    const { factors, concurrencies, ipcs } = await service.getFactorAndConcurrencies(pensions);

    expect(factors.length).toBe(0);
    expect(concurrencies.length).toBe(0);
    expect(ipcs.length).toBe(0);
  });

  afterEach(async () => {});

  afterAll(afterAllTests);
});

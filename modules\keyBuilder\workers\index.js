const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'keyBuilder',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_KEY_BUILDER_FRECUENCY,
  description: 'Construir llaves para calculo de capitales',
  endPoint: 'keybuilder',
  cronMark: workerModule.markOfCron,
  dependencyMark: workerModule.dependencyMark
};

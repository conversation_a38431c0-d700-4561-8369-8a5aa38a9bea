const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');

const FactoryController = require('../modules/systems/rolesAndPermissions/controllers/roles.controller');
const viewService = require('../modules/systems/rolesAndPermissions/services/view.service');
const roleService = require('../modules/systems/rolesAndPermissions/services/role.service');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const SYSTEM_ROLE = '/sistemas/roles-y-permisos';

module.exports = router => {
  const systemsController = FactoryController({
    HttpStatus,
    Logger,
    viewService,
    roleService
  });

  router.post(
    '/create-role',
    validateAccess(),
    startContextMiddleware,
    getUser(SYSTEM_ROLE),
    systemsController.createRole
  );
  router.get('/get-role/:roleName', validateAccess(), systemsController.readRole);
  router.get('/get-all-roles', validateAccess(), systemsController.readAllRoles);
  router.post(
    '/update-role',
    validateAccess(),
    startContextMiddleware,
    getUser(SYSTEM_ROLE),
    systemsController.updateRole
  );

  router.post('/create-view', validateAccess(), systemsController.createView);
  router.get('/get-all-views', validateAccess(), systemsController.readViews);
  router.post('/update-view', validateAccess(), systemsController.updateView);
  router.post('/delete-view', validateAccess(), systemsController.deleteView);
};

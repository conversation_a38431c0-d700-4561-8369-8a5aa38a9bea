/* eslint-disable no-useless-escape */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-restricted-syntax */
const tmp = require('tmp');
const fs = require('fs');
const moment = require('moment');
const AdmZip = require('adm-zip');
const { connectToFTPServer } = require('../../sharedFiles/helpers');

const { BULKLOAD_IPS_FILES_FTP_FOLDER_PATH } = process.env;
const LESS_A_MONTH = 1;

const patprFileRegex = YY_MM => {
  const patprIpsFileStr = `^patpr.ips.*********_${YY_MM}_${YY_MM}[0-9]{2}_[0-9]{4}.zip$`;
  return new RegExp(patprIpsFileStr, 'i');
};

const papsoeFileRegex = YY_MM => {
  const papsoeIpsFileStr = `^papsoe.ips.*********_${YY_MM}_${YY_MM}[0-9]{2}_[0-9]{4}.zip$`;
  return new RegExp(papsoeIpsFileStr, 'i');
};

const brsaludFileRegex = YY_MM => {
  const brsaludIpsFileStr = `^brsalud.ips.*********_${YY_MM}_${YY_MM}[0-9]{2}_[0-9]{4}.zip$`;
  return new RegExp(brsaludIpsFileStr, 'i');
};

const getExtractedFilesName = () => {
  const YYYY_MM = moment().format('YYYYMM');
  return [
    `aIII_taps${YYYY_MM}.*********`,
    `papsoe${YYYY_MM}.*********`,
    `brsalud${YYYY_MM}.*********`
  ];
};
const getRemoteFileNames = (FOLDER_PATH, workerName = '') => {
  const MMYY = moment().format('MMYY');
  if (workerName === 'CAJA_LA_ARAUCANA_BULK_LOAD')
    return [`${FOLDER_PATH}/APCRE201.${MMYY}`, `${FOLDER_PATH}/APO${MMYY}.201`];
  if (workerName === 'CAJA_18_BULK_LOAD') {
    const YYYY = moment().format('YYYY');
    const MM = moment().format('MM');
    return ['APCRE201', 'APE201'].map(
      fileName => `${FOLDER_PATH}/${YYYY}/${MM}-${YYYY}/${fileName}.${MMYY}`
    );
  }
  if (workerName === 'CAJA_LOS_HEROES_BULK_LOAD') {
    return ['apcre.201', '201', 'apotro.201'].map(
      fileName => `${FOLDER_PATH}/${fileName}.${MMYY}.txt`
    );
  }

  return ['APCRE201', 'APE201'].map(fileName => `${FOLDER_PATH}/${fileName}.${MMYY}`);
};

const extractZip = (source, destination, type) => {
  try {
    const zip = new AdmZip(source);
    const zipContentPath = `${destination}/${type}/`;
    zip.extractAllTo(zipContentPath, true);
    return zipContentPath;
  } catch (err) {
    throw new Error(err);
  }
};

const getRemoteZipFilesName = filesList => {
  if (!filesList.length) return [];
  const YY_MM = moment().format('YYMM');
  const patprIpsZipFile = filesList
    .filter(({ name }) => {
      return name.match(patprFileRegex(YY_MM));
    })
    .pop();
  const papsoeIpsZipFile = filesList.filter(({ name }) => name.match(papsoeFileRegex(YY_MM))).pop();

  const brsaludDate = new Date();
  brsaludDate.setMonth(brsaludDate.getMonth() - LESS_A_MONTH);
  const YY_MM_LESS_A_MONTH = moment(brsaludDate).format('YYMM');
  const brsaludIpsZipFile = filesList
    .filter(({ name }) => name.match(brsaludFileRegex(YY_MM_LESS_A_MONTH)))
    .pop();

  if (!(patprIpsZipFile && papsoeIpsZipFile && brsaludIpsZipFile)) return null;
  return [patprIpsZipFile.name, papsoeIpsZipFile.name, brsaludIpsZipFile.name];
};

const checkLocalFileExistence = (folderPath, fileName) => {
  const result = fs.readdirSync(folderPath).filter(file => file === fileName);
  return !!result.length;
};

const checkRemoteFilesExistence = async (client, ...paths) => {
  try {
    let remoteFilesFound = 0;
    const asyncFns = paths.map(path => async () => {
      const file = await client.exists(path);
      if (file) remoteFilesFound += 1;
    });

    for await (const fn of asyncFns) {
      await fn();
    }
    return paths.length === remoteFilesFound;
  } catch (error) {
    throw new Error(error);
  }
};

const downloadFilesTheHeroesFromSFTP = async ({
  client,
  sftp,
  ftpCredentials,
  FOLDER_PATH,
  workerName,
  useSpecialGet = false
}) => {
  try {
    const { connected, error } = await sftp.connectToSFTPServer(client, { ...ftpCredentials });
    if (!connected) return { files: [], error };

    const [remoteFile1Path, remoteFile2Path, remoteFile3Path] = getRemoteFileNames(
      FOLDER_PATH,
      workerName
    );

    const allFilesExist = await checkRemoteFilesExistence(
      client,
      remoteFile1Path,
      remoteFile2Path,
      remoteFile3Path
    );

    if (!allFilesExist) return { files: [], error: null };

    const outputFile1Path = tmp.fileSync().name;
    const outputFile2Path = tmp.fileSync().name;
    const outputFile3Path = tmp.fileSync().name;

    if (useSpecialGet) {
      await client.downloadToSpecial(remoteFile1Path, outputFile1Path);
      await client.downloadToSpecial(remoteFile2Path, outputFile2Path);
      await client.downloadToSpecial(remoteFile3Path, outputFile3Path);
      return { files: [outputFile1Path, outputFile2Path, outputFile3Path] };
    }

    await client.downloadTo(remoteFile1Path, outputFile1Path);
    await client.downloadTo(remoteFile2Path, outputFile2Path);
    await client.downloadTo(remoteFile3Path, outputFile3Path);

    return { files: [outputFile1Path, outputFile2Path, outputFile3Path], error: null };
  } catch (error) {
    return { files: [], error };
  } finally {
    client.close();
  }
};

const downloadFilesFromSFTP = async ({
  client,
  sftp,
  ftpCredentials,
  FOLDER_PATH,
  workerName,
  useSpecialGet = false
}) => {
  try {
    if (workerName === 'CAJA_LOS_HEROES_BULK_LOAD') {
      return await downloadFilesTheHeroesFromSFTP({
        client,
        sftp,
        ftpCredentials,
        FOLDER_PATH,
        workerName,
        useSpecialGet
      });
    }
    const { connected, error } = await sftp.connectToSFTPServer(client, { ...ftpCredentials });
    if (!connected) return { files: [], error };

    const [remoteFile1Path, remoteFile2Path] = getRemoteFileNames(FOLDER_PATH, workerName);

    const allFilesExist = await checkRemoteFilesExistence(client, remoteFile1Path, remoteFile2Path);

    if (!allFilesExist) return { files: [], error: null };

    const outputFile1Path = tmp.fileSync().name;
    const outputFile2Path = tmp.fileSync().name;

    if (useSpecialGet) {
      await client.downloadToSpecial(remoteFile1Path, outputFile1Path);
      await client.downloadToSpecial(remoteFile2Path, outputFile2Path);
      return { files: [outputFile1Path, outputFile2Path] };
    }

    await client.downloadTo(remoteFile1Path, outputFile1Path);
    await client.downloadTo(remoteFile2Path, outputFile2Path);
    return { files: [outputFile1Path, outputFile2Path] };
  } catch (error) {
    return { files: [], error };
  } finally {
    client.close();
  }
};

const downloadIpsZipFiles = async ({ client, sftp, ftpCredentials }) => {
  try {
    const { connected, error } = await sftp.connectToSFTPServer(client, { ...ftpCredentials });
    if (!connected) return { patprDirPath: null, papsoDirPath: null, downloadError: error };

    const remoteFilesList = await client.list(BULKLOAD_IPS_FILES_FTP_FOLDER_PATH);
    const remoteZipFiles = getRemoteZipFilesName(remoteFilesList);
    if (!remoteZipFiles) return { patprDirPath: null, papsoDirPath: null, downloadError: null };

    const [patprIpsZipFileName, papsoeIpsZipFileName, brsaludIpsZipFileName] = remoteZipFiles;
    const patprDirPath = tmp.dirSync().name;
    const papsoDirPath = tmp.dirSync().name;
    const brsaludDirPath = tmp.dirSync().name;

    await client._downloadToFile(
      `${patprDirPath}/patprFile.zip`,
      `${BULKLOAD_IPS_FILES_FTP_FOLDER_PATH}/${patprIpsZipFileName}`
    );
    await client._downloadToFile(
      `${papsoDirPath}/papsoFile.zip`,
      `${BULKLOAD_IPS_FILES_FTP_FOLDER_PATH}/${papsoeIpsZipFileName}`
    );
    await client._downloadToFile(
      `${brsaludDirPath}/brsaludFile.zip`,
      `${BULKLOAD_IPS_FILES_FTP_FOLDER_PATH}/${brsaludIpsZipFileName}`
    );

    return { patprDirPath, papsoDirPath, brsaludDirPath, downloadError: null };
  } catch (downloadError) {
    return { downloadError };
  } finally {
    client.close();
  }
};

const extractDownloadedIpsZipFiles = async (
  patprDirPath,
  papsoDirPath,
  brsaludDirPath,
  extractFn,
  checkFn
) => {
  const patprTempPath = await extractFn(
    `${patprDirPath}/patprFile.zip`,
    `${tmp.dirSync().name}`,
    'patpr'
  );
  const papsoTempPath = await extractFn(
    `${papsoDirPath}/papsoFile.zip`,
    `${tmp.dirSync().name}`,
    'papsoe'
  );
  const brsaludTempPath = await extractFn(
    `${brsaludDirPath}/brsaludFile.zip`,
    `${tmp.dirSync().name}`,
    'brsalud'
  );

  const [extractedPatprFile, extractedPapsoFile, extractedBrsaludFile] = getExtractedFilesName();

  const papsoFileExists = checkFn(papsoTempPath, extractedPapsoFile);
  const brsaludFileExists = checkFn(brsaludTempPath, extractedBrsaludFile);
  const patprFileExists = checkFn(patprTempPath, extractedPatprFile);

  if (!(patprFileExists && papsoFileExists && brsaludFileExists)) return { files: [] };

  return {
    files: [
      `${patprTempPath}/${extractedPatprFile}`,
      `${papsoTempPath}${extractedPapsoFile}`,
      `${brsaludTempPath}${extractedBrsaludFile}`
    ]
  };
};

module.exports = {
  downloadFilesFromSFTP,
  getRemoteFileNames,
  connectToFTPServer,
  checkRemoteFilesExistence,
  downloadIpsZipFiles,
  extractDownloadedIpsZipFiles,
  extractZip,
  checkLocalFileExistence
};

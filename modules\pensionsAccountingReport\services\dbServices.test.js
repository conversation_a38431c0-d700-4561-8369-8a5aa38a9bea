/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const Model = require('../../expenseAccountingReport/models/ExpenseAccountingReport');
const service = require('./dbService');
const expenseAccountingData = require('../../../resources/expenseAccountingReport.json');

describe('db test service', () => {
  beforeAll(beforeAllTests);

  it('should get a list of expenseAccountingReport', async () => {
    await Model.create(expenseAccountingData).catch(e => console.error(e));

    const { data, error } = await service.getReportData();
    expect(error).toBe(undefined);
    expect(data.length).toBe(4);
    expect(data[0].value).toBe(0);
  });

  it('should fail getReportData call ', async () => {
    await Model.create([]).catch(e => console.error(e));

    const { data, error } = await service.getReportData();
    expect(error).not.toBe(undefined);
    expect(data).toBe(undefined);
  });

  it('should get countDocuments', async () => {
    await Model.create(expenseAccountingData).catch(e => console.error(e));

    const { doesDataExists, error } = await service.existsData();
    expect(doesDataExists).not.toBe(undefined);
    expect(error).toBe(undefined);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await Model.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

const cronDescription = 'días de pensión transitoria Nº4:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'DAYS_OF_TRANSIENT_PENSION';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'INACTIVATE_TRANSIENTS_POST_WORKER';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const workerFn = async ({
  pensionService,
  sapService,
  daysUtils,
  logService,
  done,
  Logger,
  job
}) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription} process started`);
    const { getSAPSinisterData } = sapService;

    const { daysForCalculus, applyConditions, calculateTotalDaysToPayForPension } = daysUtils;

    const { result: transientPensions } = await pensionService.getAllAndFilter({
      transient: /^s[ií]$/i,
      enabled: true,
      inactivateManually: { $ne: true },
      manuallyReactivated: { $ne: true }
    });

    if (!transientPensions.length) {
      Logger.info(`${cronDescription} no se encontraron pensiones transitorias`);
      await logService.saveLog(cronMark);
      return { executionCompleted: true, message: successMessage };
    }

    const sinisterData = await Promise.all(
      transientPensions.map(({ accidentNumber }) => getSAPSinisterData(`${accidentNumber}`))
    )
      .then(results => results.filter(array => array.length))
      .catch(error => {
        Logger.error(`Error on sinisterData: ${error}`);
        return [];
      });

    if (!sinisterData.length) {
      Logger.info(`${cronDescription} no se encontraron siniestros`);
      throw new Error(`${cronDescription} no se encontraron siniestros`);
    }

    const conditionForMedicalRestPreviousMonth = applyConditions(
      daysForCalculus(-1),
      false,
      Logger
    );

    const conditionForMedicalRestCurrentMonth = applyConditions(daysForCalculus(), true, Logger);

    const realDays = sinisterData.reduce((arrayOfMedicalRest, curr) => {
      return arrayOfMedicalRest.concat(conditionForMedicalRestPreviousMonth(curr));
    }, []);

    const estimatedDays = sinisterData.reduce((arrayOfMedicalRest, curr) => {
      return arrayOfMedicalRest.concat(conditionForMedicalRestCurrentMonth(curr));
    }, []);

    const pensionsToUpdate = calculateTotalDaysToPayForPension(
      transientPensions,
      realDays,
      estimatedDays
    );

    Logger.info(
      'daysOfTranstientPension [pensionsToUpdate]',
      JSON.stringify(pensionsToUpdate, null, 2)
    );

    await pensionService.createUpdatePension(pensionsToUpdate);
    await logService.saveLog(cronMark);

    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

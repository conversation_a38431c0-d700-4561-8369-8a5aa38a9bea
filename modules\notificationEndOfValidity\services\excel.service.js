/* eslint-disable no-param-reassign */
const xl = require('excel4node');

const { populateSheet, putHeaders } = require('../../reports/services/excel.service');

const { filterData } = require('../formatters/formatFields');

const excelService = (data, { headers, sheetName, simpleFields }) => {
  const workbook = new xl.Workbook();

  const sheet = workbook.addWorksheet(sheetName, {
    disableRowSpansOptimization: true,
    author: 'ACHS'
  });

  const columnsWidth = {};

  const { processedData } = filterData(headers, simpleFields, data);
  const excelSimpleFields = headers.map(field => Object.keys(field)[0]);
  putHeaders(sheet, excelSimpleFields, headers);

  if (data.length) {
    populateSheet(processedData, sheet, excelSimpleFields, headers, columnsWidth);
    Object.keys(columnsWidth).forEach(column =>
      sheet.column(column).setWidth(columnsWidth[column])
    );
  }
  return workbook;
};

module.exports = { excelService };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { roundValue } = require('../../sharedFiles/helpers');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const modifier = require('../modifiers');
const PensionsModel = require('../../../models/pension');
const LogModel = require('../../sharedFiles/models/processedJob');
const service = require('./dbService');

const pensionsData = require('../../../resources/pensions.json');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionsModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should find pensions and set adjust values', async () => {
    // create and save one  document
    const pension = await PensionsModel.create({
      ...pensionsData[0],
      pensionType: 'Pensión por accidente de trabajo',
      enabled: true,
      daysToPay: 30,
      basePension: 142452,
      article40: 500,
      article41: 300
    });
    expect(pension._id).toBeDefined();

    const { completed, error } = await service.reajustBasePensionAndArticles(modifier);
    expect(error).toBe(null);
    expect(completed).toBe(true);
    const updatedPension = await PensionsModel.findOne({ _id: pension._id }).lean();
    const { article40, article41, basePension } = updatedPension;
    expect(roundValue(article40)).toBe(
      roundValue(pension.article40 * (pension.daysToPay / moment().daysInMonth()))
    );
    expect(roundValue(article41)).toBe(
      roundValue(pension.article41 * (pension.daysToPay / moment().daysInMonth()))
    );
    expect(roundValue(basePension)).toBe(
      roundValue(pension.basePension * (pension.daysToPay / moment().daysInMonth()))
    );
  });

  it('should find pensions and only adjust basePension', async () => {
    // create and save one  document
    const pension = await PensionsModel.create({
      ...pensionsData[0],
      pensionType: 'jubilacion',
      enabled: true,
      daysToPay: 30,
      basePension: 142452,
      article40: 500,
      article41: 300
    });
    expect(pension._id).toBeDefined();

    const { completed, error } = await service.reajustBasePensionAndArticles(modifier);
    expect(error).toBe(null);
    expect(completed).toBe(true);
    const updatedPension = await PensionsModel.findOne({ _id: pension._id }).lean();
    const { article40, article41, basePension } = updatedPension;
    expect(article40).toBe(500);
    expect(article41).toBe(300);
    expect(roundValue(basePension)).toBe(
      roundValue(pension.basePension * (pension.daysToPay / moment().daysInMonth()))
    );
  });

  afterEach(async () => {
    await PensionsModel.deleteMany({}).catch(e => console.error(e));
    await LogModel.deleteMany({}).catch(e => console.error(e));
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

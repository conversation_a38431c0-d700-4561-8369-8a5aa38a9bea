const createdAt = new Date(2021, 3, 1);
const updatedAt = new Date(2021, 3, 1);

const liquidationDefaultFields = {
  taxablePension: 0,
  totalAssets: 0,
  totalOnePercentDiscounts: 0,
  totalSocialCreditDiscounts: 0,
  totalDiscounts: 0,
  numberOfAssets: 0,
  numberOfDiscounts: 0,
  liquidationMonth: createdAt.getMonth(),
  liquidationYear: createdAt.getFullYear(),
  netPension: 0,
  enabled: true,
  createdAt,
  updatedAt
};

const discountsAndAssetsDefaultFields = {
  assetsNonFormulable: [],
  discountsNonFormulable: [],
  createdAt,
  updatedAt
};

module.exports = { liquidationDefaultFields, discountsAndAssetsDefaultFields };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker set reserved and retroactive amount on pensions', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();

    service = {
      calculateReservedAmountByRejectedPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      ),
      calculateReservedAmountBypaycheckRefunded: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateReservedAmountByRejectedPensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountByRejectedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateReservedAmountByRejectedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependency mark not found', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateReservedAmountByRejectedPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(3);
  });

  it('throw an error', async () => {
    service = {
      calculateReservedAmountByRejectedPensions: jest.fn(() => Promise.reject(new Error('error')))
    };

    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateReservedAmountByRejectedPensions).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

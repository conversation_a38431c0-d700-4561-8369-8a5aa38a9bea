const cronDescription = 'generating and uploading bank file:';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = 'Process completed successfully.';
const dependencyMark = 'RESERVED_ASSETS_AND_DISCOUNTS_AMOUNT_CALCULATION';
const cronMark = 'GENERATE_AND_UPLOAD_BANK_FILE';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `Dependency ${dep} not yet executed`;
let filePath;

const workerFn = async ({
  service,
  moment,
  Logger,
  logService,
  serviceGenerateFile,
  pensionService,
  storageService,
  fsClient,
  ufService,
  job,
  done
}) => {
  try {
    Logger.info(`${cronDescription} checking if reserved assets and discounts was executed`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark) };
    }

    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    const { error: errorGenerateFile } = await serviceGenerateFile.generateBankPayRoll({
      pensionService,
      fsClient,
      ufService,
      Logger
    });

    if (errorGenerateFile) throw new Error(errorGenerateFile);

    const DATE_FOR_PATH = moment().format('YYYY/MM');
    const DATE_FOR_FILE_NAME = moment().format('MMYYYY');
    const fileName = `nomina_bancaria${DATE_FOR_FILE_NAME}.txt`;
    filePath = `${__dirname}/${fileName}`;

    Logger.info(`Uploading file to storage...`);
    const { status, data, fileSendName } = await storageService.uploadFileFromLocal(
      filePath,
      fileName
    );
    if (status !== 200) throw new Error(data);
    const virtualPath = `${DATE_FOR_PATH}/Banco/${fileSendName}`;
    const { error: saveFileRegistryError } = await storageService.saveFileRegistry(
      virtualPath,
      data
    );
    if (saveFileRegistryError) throw new Error(saveFileRegistryError);

    const { error: sendNotificationEmailError } = await service.sendNotificationEmail();
    if (sendNotificationEmailError) throw new Error(sendNotificationEmailError);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process completed`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    await fsClient.unlink(filePath).catch(error => Logger.error(`remove bank file: ${error}`));
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

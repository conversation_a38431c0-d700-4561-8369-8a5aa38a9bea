const moment = require('moment');
const PensionModel = require('../../../../models/pension');
const ToInactivateByRetirementModel = require('../models/pensionToInactivate');
const PensionHistoricModel = require('../../../../models/pensionHistoric');

const buildCurrentMonthAndYearQuery = (dateField, additionalCriteria) => {
  const date = moment();
  return {
    ...additionalCriteria,
    $expr: {
      $and: [
        { $eq: [{ $month: `$${dateField}` }, date.month() + 1] },
        { $eq: [{ $year: `$${dateField}` }, date.year()] }
      ]
    }
  };
};

const service = {
  async findPensionToInactivate() {
    const query = buildCurrentMonthAndYearQuery('endDateOfValidity', {
      validityType: { $regex: /vigente hasta la jubilaci[oó]n/i },
      manuallyReactivated: { $ne: true },
      inactivateManually: { $ne: true },
      enabled: true
    });
    try {
      const pensions = await PensionModel.find(query)
        .select('_id validityType beneficiary.rut causant.rut pensionCodeId')
        .lean()
        .exec();
      return { pensions };
    } catch (error) {
      return { error };
    }
  },

  async markPensionToInactivate(pensions) {
    if (!pensions.length) return { pensionsToEvaluate: [] };
    const session = await ToInactivateByRetirementModel.startSession();
    let markedPensions = await ToInactivateByRetirementModel.find({})
      .lean()
      .exec();
    markedPensions = markedPensions.map(obj => String(obj.pension));
    const pensionsToEvaluate = [];

    session.startTransaction();
    try {
      const bulk = ToInactivateByRetirementModel.collection.initializeOrderedBulkOp();
      pensions.forEach(document => {
        const { _id, ...data } = document;
        const dateToInactivate = moment()
          .add(1, 'months')
          .startOf('month')
          .toDate();

        if (!markedPensions.includes(String(_id))) {
          pensionsToEvaluate.push({
            ...data,
            evaluationDate: new Date(),
            inactivationReason: 'Jubilación'
          });

          bulk.insert({
            beneficiaryRut: data.beneficiary.rut,
            causantRut: data.causant.rut,
            evaluationDate: new Date(),
            dateToInactivate
          });
        }
      });

      if (bulk.length) await bulk.execute();
      await session.commitTransaction();
      return { pensionsToEvaluate };
    } catch (e) {
      await session.abortTransaction();
      return { markPensionError: e, pensionsToEvaluate: [] };
    }
  },
  async inactivatePensionsByRetirement() {
    const query = buildCurrentMonthAndYearQuery('dateToInactivate');

    try {
      const markedPensions = await ToInactivateByRetirementModel.find(query);
      if (!markedPensions.length) return { completed: true };
      const asyncFunctions = markedPensions.map(document => async () => {
        const { beneficiaryRut, causantRut, _id: markID } = document;

        const currentPension = await PensionModel.findOne({
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          deathDate: { $exists: false },
          enabled: true,
          inactivateManually: { $ne: true },
          manuallyReactivated: { $ne: true }
        }).lean();

        if (currentPension) {
          await PensionModel.updateOne(
            {
              'beneficiary.rut': beneficiaryRut,
              'causant.rut': causantRut,
              deathDate: { $exists: false },
              enabled: true,
              inactivateManually: { $ne: true },
              manuallyReactivated: { $ne: true }
            },
            {
              $set: {
                inactivationDate: new Date(),
                validityType: 'No vigente',
                inactivationReason: 'Jubilación',
                enabled: true
              }
            }
          );

          const { _id: id, ...data } = currentPension;
          await PensionHistoricModel.create({
            ...data,
            enabled: false
          });
        }
        await ToInactivateByRetirementModel.deleteOne({ _id: markID });
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of asyncFunctions) {
        await fn();
      }

      return { completed: true };
    } catch (error) {
      return { inactivationError: error };
    }
  }
};

module.exports = { ...service };

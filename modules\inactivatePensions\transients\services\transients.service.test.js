/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests, Logger } = require('../../../testsHelper');
const InactivateTransientModel = require('../models/inactivateTransient');
const PensionModel = require('../../../../models/pension');
const pensions = require('../../../../resources/pensions.json');
const markPension = require('../../../../resources/markedPensionsList.json');
const getDataSap = require('../../../../resources/sapRequest.json');
const transientHelper = require('./transientsHelper');

const service = require('./transients.service');

describe('Temporary Pensioner service Model Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let mocks;

  let axios;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(InactivateTransientModel, 'startSession').mockImplementationOnce(() => mocks);

    pensionService = {
      findOneAndUpdate: jest.fn(() => Promise.resolve())
    };
    axios = jest.fn(() => Promise.resolve({ data: getDataSap[0] }));
  });

  it('getAll without filter', async () => {
    const response = await service.getAllAndFilter();
    expect.arrayContaining(response.result);
  });
  it('getAll with filter', async () => {
    const { result } = await service.getAllAndFilter({ enabled: false });
    expect(result).toHaveLength(0);
  });
  it('getAll with wrong filter', async () => {
    const FAKE_ID = 123;
    const response = await service.getAllAndFilter({ _id: FAKE_ID });
    expect(response.isError).toBe(true);
  });

  it('success createupdate TransientPension ', async () => {
    const result = await service.createUpdateTransientPension([], pensionService);
    expect(result.completed).toBe(true);
  });

  it('create and update pensions ', async () => {
    const pension = await PensionModel.create({ ...pensions[0] }).catch(console.log);

    await InactivateTransientModel.create({
      ...markPension[0],
      beneficiaryRut: pension.beneficiary.rut,
      causantRut: pension.causant.rut,
      dateToInactivate: new Date()
    }).catch(console.log);
    const result = await service.createUpdateTransientPension();
    expect(result.completed).toBe(true);
  });

  it('fail createUpdateTransientPension ', async () => {
    jest
      .spyOn(InactivateTransientModel, 'find')
      .mockImplementationOnce(() => Promise.reject(new Error('error')));
    const result = await service.createUpdateTransientPension();

    expect(result.completed).toBe(false);
  });

  it('success createTransientMarkPensions ', async () => {
    mocks.commitTransaction = jest.fn(() => Promise.resolve(true));
    const result = await service.createTransientMarkPension([]);
    expect(result.completed).toBe(true);
  });

  it('fail createTransientMarkPensions ', async () => {
    mocks.commitTransaction = jest.fn(() => Promise.reject(new Error('error')));
    const { inactivationError } = await service.createTransientMarkPension([]);
    expect(inactivationError).toStrictEqual(new Error('error'));
  });

  it('get Pensions to inactivate ', async () => {
    const { pensionsToInactivate, pensionsToEvaluate } = await service.getAllPensionsToInactivate(
      [
        {
          _doc: {
            accidentNumber: '00000',
            _id: 'fake',
            causant: { rut: 'fakeRut' },
            beneficiary: { rut: 'fakeRut' }
          }
        }
      ],
      transientHelper,
      axios,
      { Logger }
    );
    expect(pensionsToInactivate.length).toBe(1);
    expect(pensionsToEvaluate.length).toBe(1);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({}).catch(console.log);
    await InactivateTransientModel.deleteMany({}).catch(console.log);
  });
  afterAll(afterAllTests);
});

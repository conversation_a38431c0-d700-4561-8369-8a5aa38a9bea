const moment = require('moment');

const paymentsGateWayCode = [
  { regex: /Dep[óo]sito cuenta corriente Banco de Chile/i, code: '02' },
  { regex: /Dep[óo]sito cuenta corriente otros bancos/i, code: '02' },
  { regex: /Dep[óo]sito cuenta ahorro otros bancos/i, code: '02' },
  { regex: /Vale vista Banco de Chile/i, code: '03' },
  { regex: /Vale vista entregado directamente a la empresa/i, code: '03' },
  { regex: /Cheque electr[óo]nico Banco de Chile/i, code: '03' },
  { regex: /Servipag/i, code: '03' }
];

const getTransferDate = line => {
  const TRANSFER_DATE_POSITION = 148;
  const TRANSFER_DATE_LENGTH = 8;
  return line.substr(TRANSFER_DATE_POSITION, TRANSFER_DATE_LENGTH);
};

const getPaymentPeriod = line => {
  const PAYMENT_PERIOD_POSITION = 124;
  const PAYMENT_PERIOD_LENGTH = 6;
  return line.substr(PAYMENT_PERIOD_POSITION, PAYMENT_PERIOD_LENGTH);
};

const getResolutionNumber = pension => {
  const MAX_LENGTH = 8;
  const { apsInfo } = pension;
  const { apsResolutionNumber } = apsInfo;
  return String(apsResolutionNumber).padStart(MAX_LENGTH, '0');
};

function getResolutionDate(pension) {
  const { apsInfo } = pension;
  const { apsResolutionDate } = apsInfo;
  return moment(apsResolutionDate).format('YYYYMMDD');
}

const getCollectorRut = pension => {
  const MAX_LENGTH = 9;
  const { collector } = pension;
  const { rut } = collector;
  return rut.replace('-', '').padStart(MAX_LENGTH, '0');
};

const getBeneficiaryRut = pension => {
  const MAX_LENGTH = 9;
  const { beneficiary } = pension;
  const { rut } = beneficiary;
  return rut.replace('-', '').padStart(MAX_LENGTH, '0');
};

const getBeneficiaryLastName = pension => {
  const MAX_LENGTH = 20;
  const { beneficiary } = pension;
  const { lastName } = beneficiary;
  return lastName.padEnd(MAX_LENGTH).substring(0, MAX_LENGTH);
};

const getBeneficiaryMotherLastName = pension => {
  const MAX_LENGTH = 20;
  const { beneficiary } = pension;
  const { mothersLastName } = beneficiary;
  return mothersLastName.padEnd(MAX_LENGTH).substring(0, MAX_LENGTH);
};

const getBeneficiaryName = pension => {
  const MAX_LENGTH = 30;
  const { beneficiary } = pension;
  const { name } = beneficiary;
  return name.padEnd(MAX_LENGTH).substring(0, MAX_LENGTH);
};

const getBenefitType = line => {
  const BENEFIT_POSITION = 112;
  const BENEFIT_LENGTH = 2;
  return line.substr(BENEFIT_POSITION, BENEFIT_LENGTH);
};

const getApsAmount = pension => {
  const MAX_LENGTH = 15;
  const { assets } = pension;
  const { aps } = assets;
  return String(aps).padStart(MAX_LENGTH, '0');
};

const getPaymentGateWayCode = pension => {
  const DEFAULT_CODE = '03';
  const { paymentInfo } = pension;
  const { paymentGateway } = paymentInfo;
  const codeObj = paymentsGateWayCode.find(({ regex }) => paymentGateway.match(regex));
  return codeObj ? codeObj.code : DEFAULT_CODE;
};

const getApsTransferCode = pension => {
  const MAX_LENGTH = 10;
  const { apsInfo } = pension;
  const { apsTransferCode } = apsInfo;
  return String(apsTransferCode).padStart(MAX_LENGTH, '0');
};

const getTransferedAps = pension => {
  const MAX_LENGTH = 15;
  const { assets } = pension;
  const { aps } = assets;
  return String(aps).padStart(MAX_LENGTH, '0');
};

const getUniquePaymentId = pension => {
  const MAX_LENGTH = 16;
  const { apsInfo } = pension;
  const { apsPaymentUniqueId } = apsInfo;
  return String(apsPaymentUniqueId).padStart(MAX_LENGTH, '0');
};

const composeDpapsLine = ({ pension, oldLine }) => {
  const emissionPeriod = moment().format('YYYYMM');
  const institutionRut = '703601006';
  const disponibilityDate = `${moment().format('YYYYMM')}24`;
  const collectionDate = `${moment().format('YYYYMM')}24`;
  const apsDifference = '00000000';
  const differenceSign = ' ';
  const totalDaysInCurrentMonth = moment().daysInMonth();
  return [
    emissionPeriod,
    institutionRut,
    getTransferDate(oldLine),
    getPaymentPeriod(oldLine),
    getResolutionNumber(pension),
    getResolutionDate(pension),
    getCollectorRut(pension),
    getBeneficiaryRut(pension),
    getBeneficiaryLastName(pension),
    getBeneficiaryMotherLastName(pension),
    getBeneficiaryName(pension),
    getBenefitType(oldLine),
    getApsAmount(pension),
    getPaymentGateWayCode(pension),
    disponibilityDate,
    collectionDate,
    getApsTransferCode(pension),
    getTransferedAps(pension),
    apsDifference,
    differenceSign,
    totalDaysInCurrentMonth,
    getUniquePaymentId(pension)
  ].join('');
};

module.exports = composeDpapsLine;

const workerModule = require('./worker');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('set pensioners bonus worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let logService;
  let Logger;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      setPensionersBonus: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(service.setPensionersBonus).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current year', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.setPensionersBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.setPensionersBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependency mark already created ', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.setPensionersBonus).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker bonus', async () => {
    service.setPensionersBonus = jest.fn(() => Promise.resolve({ error: true }));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

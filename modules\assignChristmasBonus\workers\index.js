const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'set-amount-pensioners-bonus-christmas',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SET_AMOUNT_PENSIONERS_CHRISTMAS_BONUS,
  description: 'Asignar bono de aguinaldo por navidad a los pensionados que apliquen',
  endPoint: 'assignchristmasbonus',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyChristmas
};

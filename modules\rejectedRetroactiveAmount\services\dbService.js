/* eslint-disable no-restricted-syntax */

const { PensionModel } = require('../../linkPensions/services/link.service');
const { roundValue } = require('../../sharedFiles/helpers');
const { buildAggregation } = require('./aggregationBuilder');

const getReducedObj = (obj, fieldsObj) => {
  const { initialObj, targetInnerObj, targetField } = fieldsObj;
  const { matchedPensions, ...rest } = obj;
  const { results = [] } = matchedPensions;
  const amounts = results.reduce(
    (resultObj, item) => {
      const innerObj = item[targetInnerObj] || {};
      const value = innerObj[targetField] || 0;
      return { ...resultObj, [targetField]: roundValue(resultObj[targetField] + value) };
    },
    { ...rest[initialObj], [targetField]: rest[targetInnerObj][targetField] }
  );

  return { ...rest, [initialObj]: amounts };
};

const getRetroactiveRejectedPensions = async (Model, firstDay) => {
  const modifiedPensions = [];
  const query = { match: { bankRejected: { $regex: /s[ií]/i } }, targetField: 'bankRejected' };
  const aggregation = buildAggregation(firstDay, query);
  const cursor = Model.aggregate(aggregation).cursor();

  for await (const doc of cursor) {
    const reducedPension = getReducedObj(doc, {
      initialObj: 'retroactiveAmounts',
      targetInnerObj: 'reservedAmounts',
      targetField: 'forRejection'
    });
    modifiedPensions.push(reducedPension);
  }

  return modifiedPensions;
};

const getRetroactivePayCheckRefundedPensions = async (Model, firstDay) => {
  const modifiedPensions = [];
  const query = {
    match: { paycheckRefunded: { $regex: /s[ií]/i } },
    targetField: 'paycheckRefunded'
  };
  const aggregation = buildAggregation(firstDay, query);
  const cursor = Model.aggregate(aggregation).cursor();

  for await (const doc of cursor) {
    const reducedPension = getReducedObj(doc, {
      initialObj: 'retroactiveAmounts',
      targetInnerObj: 'reservedAmounts',
      targetField: 'forPayCheck'
    });
    modifiedPensions.push(reducedPension);
  }

  return modifiedPensions;
};

const service = {
  async calculateRetroactiveRejectedPensions(pensionsService, Model = PensionModel) {
    try {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const rejectedPensions = await getRetroactiveRejectedPensions(Model, firstDay);
      const { completed, error } = await pensionsService.updatePensions(rejectedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  },
  async calculateRetroactivePayCheckRefundedPensions(pensionsService, Model = PensionModel) {
    try {
      const date = new Date();
      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
      const refundedPensions = await getRetroactivePayCheckRefundedPensions(Model, firstDay);
      const { completed, error } = await pensionsService.updatePensions(refundedPensions);

      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = {
  ...service,
  getRetroactiveRejectedPensions,
  getRetroactivePayCheckRefundedPensions
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');

describe('Base pension service Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;

  beforeEach(async () => {
    pensionService = {
      getPensionsWithLiquidation: jest.fn(() => Promise.resolve({ result: [] })),
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, error: false }))
    };
  });
  it('should modified By survival', async () => {
    const { completed, error } = await service.calculateReservedAmountsBySurvival(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(false);
  });

  afterEach(async () => {});

  afterAll(afterAllTests);
});

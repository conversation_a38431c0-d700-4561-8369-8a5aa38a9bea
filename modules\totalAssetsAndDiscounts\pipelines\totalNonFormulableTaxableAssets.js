const calculate = pension => {
  const taxableAssetRegex = /[i<PERSON><PERSON><PERSON>]mpon[í<PERSON><PERSON>i]ble/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const taxableAssets = assetsNonFormulable.filter(({ assetType }) =>
    taxableAssetRegex.test(assetType)
  );
  const taxableTotalNonFormulable = taxableAssets.reduce((acc, current) => acc + current.amount, 0);
  return { ...pension, assets: { ...pension.assets, taxableTotalNonFormulable } };
};

module.exports = calculate;

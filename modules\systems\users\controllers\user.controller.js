/* eslint-disable consistent-return */
/* eslint-disable no-console */

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  userService,
  authService,
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }
  return {
    updateUser: async (req, res) => {
      Logger.info('update user: ');

      const { name, email, role } = req.body;

      const { completed, error } = await userService.updateUser(req.params.id, {
        name,
        email,
        role
      });
      if (error) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        await authService.logoutUserById(req.params.id);

        Logger.info('update user successfully');
        res.status(HttpStatus.OK).json(completed);
      }
    },
    createUser: async (req, res) => {
      try {
        Logger.info('create user: ');

        const { user } = req.body;
        const { result, error } = await userService.createUser(user);
        if (error) {
          manageError(res, error);
        } else {
          Logger.info('create user successfully');
          res.status(HttpStatus.OK).json({ result });
        }
      } catch (error) {
        manageError(res, error);
      }
    },
    deleteUser: async (req, res) => {
      Logger.info('delete user: ');
      const { result, error } = await userService.deleteUser(req.params.id);
      if (error) {
        Logger.error(error);
        res.status(HttpStatus.OK).json({ error });
      } else {
        await authService.logoutUserById(req.params.id);

        Logger.info('delete user successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    getUsers: async (req, res) => {
      Logger.info('get all users: ');
      const { result } = await userService.getUsers();
      res.status(HttpStatus.OK).json({ result });
    }
  };
};

{"databaseConfig": {"connectionUrl": "env:MONGODB_DB_URL", "dataSeedPath": "./dist/data-seed", "modelValidatorPath": "./dist/models"}, "envi": "qa", "middleware": {"devtools": {"enabled": true, "priority": 35, "module": {"name": "construx", "arguments": ["path:./public", "path:./.build", {"copier": {"module": "construx-copier", "files": "**/*"}}]}}, "multipart": {"enabled": true, "priority": 80, "module": {"name": "kraken-js/middleware/multipart", "arguments": [{"maxFieldsSize": 20960000, "multiples": true, "keepExtensions": true}]}}}}
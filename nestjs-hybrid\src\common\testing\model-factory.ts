import { PrismaService } from '../prisma/prisma.service';
import { faker } from '@faker-js/faker';

/**
 * Factory Pattern inspirado en Laravel Factories
 * Para generar datos de prueba de forma consistente
 */
export abstract class ModelFactory<T = any> {
  protected prisma: PrismaService;
  protected model: any;
  protected count: number = 1;
  protected states: Record<string, any> = {};

  constructor(prisma: PrismaService, modelName: string) {
    this.prisma = prisma;
    this.model = (prisma as any)[modelName];
  }

  /**
   * Define los atributos por defecto del modelo
   */
  abstract definition(): Partial<T>;

  /**
   * Crea instancias del modelo
   */
  async create(attributes: Partial<T> = {}): Promise<T | T[]> {
    const data = this.count === 1 
      ? await this.makeOne(attributes)
      : await this.makeMany(attributes);

    if (this.count === 1) {
      return await this.model.create({ data });
    } else {
      const results = [];
      for (const item of data as any[]) {
        results.push(await this.model.create({ data: item }));
      }
      return results;
    }
  }

  /**
   * Genera datos sin persistir
   */
  async make(attributes: Partial<T> = {}): Promise<T | T[]> {
    return this.count === 1 
      ? await this.makeOne(attributes)
      : await this.makeMany(attributes);
  }

  /**
   * Especifica la cantidad de instancias a crear
   */
  times(count: number): this {
    this.count = count;
    return this;
  }

  /**
   * Aplica un estado específico
   */
  state(stateName: string): this {
    if (this.states[stateName]) {
      Object.assign(this, this.states[stateName]);
    }
    return this;
  }

  /**
   * Define un estado
   */
  protected defineState(name: string, attributes: Partial<T> | (() => Partial<T>)): void {
    this.states[name] = typeof attributes === 'function' ? attributes() : attributes;
  }

  private async makeOne(attributes: Partial<T> = {}): Promise<T> {
    const definition = await this.definition();
    return { ...definition, ...attributes } as T;
  }

  private async makeMany(attributes: Partial<T> = {}): Promise<T[]> {
    const results = [];
    for (let i = 0; i < this.count; i++) {
      results.push(await this.makeOne(attributes));
    }
    return results;
  }
}

/**
 * Factory para Pensiones
 */
export class PensionFactory extends ModelFactory {
  constructor(prisma: PrismaService) {
    super(prisma, 'pension');
    this.defineStates();
  }

  definition() {
    return {
      pensionCodeId: faker.string.uuid(),
      beneficiaryRut: faker.string.numeric(8) + '-' + faker.string.numeric(1),
      causantRut: faker.string.numeric(8) + '-' + faker.string.numeric(1),
      basePension: faker.number.float({ min: 150000, max: 500000, precision: 0.01 }),
      initialBasePension: faker.number.float({ min: 150000, max: 500000, precision: 0.01 }),
      pensionType: faker.helpers.arrayElement(['INVALIDEZ_TOTAL', 'INVALIDEZ_PARCIAL', 'SUPERVIVENCIA', 'ORFANDAD']),
      validityType: faker.helpers.arrayElement(['PERMANENTE', 'TEMPORAL', 'REVISION']),
      enabled: true,
      article40: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
      article41: faker.number.float({ min: 0, max: 30000, precision: 0.01 }),
      law19403: faker.number.float({ min: 0, max: 20000, precision: 0.01 }),
      law19539: faker.number.float({ min: 0, max: 15000, precision: 0.01 }),
      law19953: faker.number.float({ min: 0, max: 25000, precision: 0.01 }),
      dateOfBirth: faker.date.birthdate({ min: 18, max: 80, mode: 'age' }),
      gender: faker.helpers.arrayElement(['M', 'F']),
      disabilityDegree: faker.number.int({ min: 0, max: 100 }),
      numberOfCharges: faker.number.int({ min: 0, max: 5 }),
      familyGroup: faker.number.int({ min: 1, max: 8 }),
      pensionStartDate: faker.date.past({ years: 5 }),
      endDateOfValidity: faker.helpers.maybe(() => faker.date.future({ years: 2 }), { probability: 0.3 }),
      afpAffiliation: faker.helpers.arrayElement(['PROVIDA', 'HABITAT', 'PLANVITAL', 'CUPRUM', 'CAPITAL']),
      healthAffiliation: faker.helpers.arrayElement(['FONASA', 'ISAPRE_CONSALUD', 'ISAPRE_COLMENA', 'ISAPRE_CRUZ_BLANCA']),
      assets: {
        aps: faker.number.float({ min: 0, max: 25000, precision: 0.01 }),
        familyAssignment: faker.number.float({ min: 0, max: 15000, precision: 0.01 }),
        christmasBonus: faker.number.float({ min: 0, max: 30000, precision: 0.01 }),
        winterBonus: faker.number.float({ min: 0, max: 20000, precision: 0.01 }),
      },
      discounts: {
        afp: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
        health: faker.number.float({ min: 0, max: 30000, precision: 0.01 }),
        socialCredits: faker.number.float({ min: 0, max: 10000, precision: 0.01 }),
      },
      retroactiveAmounts: {
        forBonuses: faker.number.float({ min: 0, max: 50000, precision: 0.01 }),
        forBasePension: faker.number.float({ min: 0, max: 100000, precision: 0.01 }),
      },
      paymentInfo: {
        bank: faker.helpers.arrayElement(['BANCO_CHILE', 'BANCO_ESTADO', 'SANTANDER', 'BCI']),
        accountNumber: faker.string.numeric(10),
        accountType: faker.helpers.arrayElement(['CUENTA_CORRIENTE', 'CUENTA_VISTA', 'CUENTA_AHORRO']),
      },
    };
  }

  private defineStates(): void {
    // Estado: Pensión de invalidez total
    this.defineState('invalidezTotal', {
      pensionType: 'INVALIDEZ_TOTAL',
      disabilityDegree: faker.number.int({ min: 70, max: 100 }),
      basePension: faker.number.float({ min: 200000, max: 600000, precision: 0.01 }),
    });

    // Estado: Pensión de supervivencia
    this.defineState('supervivencia', {
      pensionType: 'SUPERVIVENCIA',
      disabilityDegree: 0,
      basePension: faker.number.float({ min: 150000, max: 400000, precision: 0.01 }),
    });

    // Estado: Pensión con cargas familiares
    this.defineState('conCargas', {
      numberOfCharges: faker.number.int({ min: 1, max: 4 }),
      familyGroup: faker.number.int({ min: 2, max: 6 }),
    });

    // Estado: Pensión deshabilitada
    this.defineState('disabled', {
      enabled: false,
      endDateOfValidity: faker.date.past({ years: 1 }),
    });

    // Estado: Pensión con altos montos
    this.defineState('highAmount', {
      basePension: faker.number.float({ min: 800000, max: 1500000, precision: 0.01 }),
      article40: faker.number.float({ min: 100000, max: 200000, precision: 0.01 }),
      article41: faker.number.float({ min: 50000, max: 100000, precision: 0.01 }),
    });
  }
}

/**
 * Factory para Liquidaciones
 */
export class LiquidationFactory extends ModelFactory {
  constructor(prisma: PrismaService) {
    super(prisma, 'liquidation');
  }

  definition() {
    return {
      pensionCodeId: faker.string.uuid(),
      beneficiaryRut: faker.string.numeric(8) + '-' + faker.string.numeric(1),
      causantRut: faker.string.numeric(8) + '-' + faker.string.numeric(1),
      taxablePension: faker.number.float({ min: 150000, max: 500000, precision: 0.01 }),
      totalAssets: faker.number.float({ min: 200000, max: 600000, precision: 0.01 }),
      totalDiscounts: faker.number.float({ min: 20000, max: 100000, precision: 0.01 }),
      netPension: faker.number.float({ min: 150000, max: 550000, precision: 0.01 }),
      liquidationMonth: faker.date.recent().getMonth() + 1,
      liquidationYear: faker.date.recent().getFullYear(),
      enabled: true,
    };
  }
}

/**
 * Factory Manager - Punto de entrada principal
 */
export class FactoryManager {
  constructor(private readonly prisma: PrismaService) {}

  pension(): PensionFactory {
    return new PensionFactory(this.prisma);
  }

  liquidation(): LiquidationFactory {
    return new LiquidationFactory(this.prisma);
  }

  // Método de conveniencia para crear datos relacionados
  async createPensionWithLiquidation(pensionAttributes: any = {}, liquidationAttributes: any = {}): Promise<any> {
    const pension = await this.pension().create(pensionAttributes);
    const liquidation = await this.liquidation().create({
      pensionCodeId: (pension as any).pensionCodeId,
      beneficiaryRut: (pension as any).beneficiaryRut,
      causantRut: (pension as any).causantRut,
      ...liquidationAttributes,
    });

    return { pension, liquidation };
  }
}

// Uso en tests:
// const factory = new FactoryManager(prisma);
// const pension = await factory.pension().state('invalidezTotal').create();
// const pensions = await factory.pension().times(10).create();
// const { pension, liquidation } = await factory.createPensionWithLiquidation();

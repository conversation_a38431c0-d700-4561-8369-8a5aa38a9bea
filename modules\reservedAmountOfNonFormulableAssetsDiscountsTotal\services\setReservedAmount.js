const { roundValue } = require('../../sharedFiles/helpers');

const VALIDITY_TYPE = /^No\s+vigente$/i;

const setReservedAmounts = ({ _id, ...pension }) => {
  const { assets, discounts, reservedAmounts } = pension;
  const { taxableTotalNonFormulable = 0, netTotalNonFormulable = 0 } = assets;
  const { totalNonFormulable = 0 } = discounts;

  return {
    ...pension,
    reservedAmounts: {
      ...reservedAmounts,
      forNetTotalNonFormulableAssets: roundValue(netTotalNonFormulable),
      forTotalNonFormulableDiscounts: roundValue(totalNonFormulable),
      forTaxableTotalNonFormulableAssets: roundValue(taxableTotalNonFormulable)
    }
  };
};

const service = {
  async calculateReservedAmount(pensionService) {
    try {
      const { result: pensions } = await pensionService.getAllAndFilter({
        validityType: { $regex: VALIDITY_TYPE },
        enabled: true
      });

      const calculatedReservedAmounts = pensions.map(({ _doc }) => setReservedAmounts(_doc));
      await pensionService.createUpdatePension(calculatedReservedAmounts);
      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { setReservedAmounts, ...service };

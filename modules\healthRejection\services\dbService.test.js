/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const service = require('./dbService');
const pensionsService = require('../../pensions/services/pension.service');
const [pensionsData] = require('../../../resources/pensions.json');

describe('service for health rejection file', () => {
  beforeAll(beforeAllTests);

  it('should return true for healthRejectionPeriod', async done => {
    const startingDay = 1;
    const endingDay = 5;

    const currentDate = moment()
      .startOf('month')
      .add(1, 'seconds')
      .toDate();

    const isActionAllowed = service.getHealthRejectionTimePeriod({
      currentDate,
      startingDay,
      endingDay
    });

    expect(isActionAllowed).toBe(true);

    done();
  });

  it('should return false for healthRejectionPeriod', async done => {
    const startingDay = 1;
    const endingDay = 5;

    const currentDate = moment()
      .startOf('month')
      .add(6, 'days')
      .toDate();

    const isActionAllowed = service.getHealthRejectionTimePeriod({
      currentDate,
      startingDay,
      endingDay
    });

    expect(isActionAllowed).toBe(false);

    done();
  });

  it('should mock a file search and download from SFTP', async done => {
    const sftpClient = {
      list: jest.fn(() => Promise.resolve([{ name: 'AmazingNewFile.zip' }])),
      downloadTo: jest.fn(() => Promise.resolve()),
      close: jest.fn()
    };

    const { zipLocalFilepath, error } = await service.downloadZipFile(sftpClient, '');

    expect(error).toBeUndefined();
    expect(zipLocalFilepath.includes('tbos.zip')).toBe(true);

    done();
  });

  it('should mock a file search and download from SFTP', async done => {
    const sftpClient = {
      list: jest.fn(() => Promise.reject(new Error('Error at readingFiles'))),
      downloadTo: jest.fn(() => Promise.resolve()),
      close: jest.fn()
    };

    const { zipFilepath, error } = await service.downloadZipFile(sftpClient, '');

    expect(error).toStrictEqual(new Error('Error at readingFiles'));
    expect(zipFilepath).toBeUndefined();

    done();
  });

  it('should parse data from lines', async () => {
    const line1 =
      '202007703601006075675518NUNEZ               RAMIRES             LETICIA DEL CARMEN            2020070800000000000133410019058620';
    const line2 =
      '202007703601006075675518NUNEZ               RAMIRES             LETICIA DEL CARMEN            2020070800000000000133410019058617';
    const { fileRuts, filePensionsData } = service.parseDataFromLines([line1, line2]);

    expect(filePensionsData.length).toBe(2);
    expect(fileRuts[0]).toBe('7567551-8');
    expect(filePensionsData[0].rejectionCause).toBe(20);
    expect(filePensionsData[0].rejectionHealthExemptionAmount).toBe(0);
    expect(filePensionsData[0].rejectionHealthReductionAmount).toBe(13341);

    expect(filePensionsData[1].rejectionCause).toBe(17);
    expect(filePensionsData[1].rejectionHealthExemptionAmount).toBe(0);
    expect(filePensionsData[1].rejectionHealthReductionAmount).toBe(0);
  });

  it('should match content from file and pensioners', async done => {
    const beneficiaryRut = '12345678-k';

    const filePensionsData = [
      {
        rut: beneficiaryRut.toUpperCase(),
        rejectionCause: 20,
        rejectionHealthReductionAmount: 30,
        rejectionHealthExemptionAmount: 0
      }
    ];

    const fileRuts = [beneficiaryRut];

    const pensioner = {
      ...pensionsData,
      beneficiary: { ...pensionsData.beneficiary, rut: beneficiaryRut },
      enabled: true,
      createdAt: new Date()
    };
    await PensionModel.insertMany([pensioner]).catch(err => console.log(err));

    const fileLines = 'content';
    const getFileContent = jest.fn(() => Promise.resolve({ fileRuts, filePensionsData }));

    const { updatedPensions, error } = await service.updatePensionsByHealthRejection(
      getFileContent,
      fileLines
    );
    expect(error).toBeUndefined();
    expect(updatedPensions.length).toBe(1);
    expect(updatedPensions[0].rejectionHealthExemptionAmount).toBe(0);
    expect(updatedPensions[0].rejectionHealthReductionAmount).toBe(30);

    done();
  });

  it('should fail at matching pensioners', async done => {
    const fileLines = 'useless content';
    const getFileContent = jest.fn(() => Promise.reject(new Error('some error')));

    const { updatedPensions, error } = await service.updatePensionsByHealthRejection(
      getFileContent,
      fileLines
    );
    expect(error).toStrictEqual(new Error('some error'));
    expect(updatedPensions).toBeUndefined();

    done();
  });

  it('should update historical pensions', async done => {
    const beneficiaryRut = '12345678-k';

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const pensionerCurrentMonth = {
      ...pensionsData,
      beneficiary: { ...pensionsData.beneficiary, rut: beneficiaryRut },
      enabled: true,
      createdAt: new Date(currentYear, currentMonth, 1),
      rejectionHealthReductionAmount: 0,
      rejectionHealthExemptionAmount: 0
    };

    const pensionerMonthMinus1 = {
      ...pensionerCurrentMonth,
      enabled: false,
      createdAt: new Date(currentYear, currentMonth - 1, 1),
      rejectionHealthReductionAmount: 10,
      rejectionHealthExemptionAmount: 10
    };

    const pensionerMonthMinus2 = {
      ...pensionerCurrentMonth,
      enabled: false,
      createdAt: new Date(currentYear, currentMonth - 2, 1),
      rejectionHealthReductionAmount: 10,
      rejectionHealthExemptionAmount: 10
    };

    const pensionerMonthMinus3 = {
      ...pensionerCurrentMonth,
      enabled: false,
      createdAt: new Date(currentYear, currentMonth - 3, 1),
      rejectionHealthReductionAmount: 0,
      rejectionHealthExemptionAmount: 0
    };

    await PensionModel.insertMany([pensionerCurrentMonth]).catch(err => console.log(err));

    await PensionHistoricModel.insertMany([
      pensionerMonthMinus1,
      pensionerMonthMinus2,
      pensionerMonthMinus3
    ]).catch(err => console.log(err));

    const { historicalPensions, error } = await service.updatePensionsWithoutHealthRejection(
      pensionsService
    );

    expect(error).toBeUndefined();
    expect(historicalPensions.length).toBe(2);
    expect(historicalPensions[0].rejectionHealthExemptionAmount).toBe(0);
    expect(historicalPensions[0].rejectionHealthExemptionAmount).toBe(0);
    expect(historicalPensions[1].rejectionHealthExemptionAmount).toBe(0);
    expect(historicalPensions[1].rejectionHealthExemptionAmount).toBe(0);

    done();
  });

  it('should fail updating historical pensions', async done => {
    jest.spyOn(PensionModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });

    const { historicalPensions, error } = await service.updatePensionsWithoutHealthRejection(
      pensionsService
    );

    expect(error).toStrictEqual(new Error());
    expect(historicalPensions).toBeUndefined();

    done();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({}).catch(error => console.error(error));
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./pre.worker');
const pensionsList = require('../../../../resources/pensionSoonToInactivate.json');

describe('Inactivate by retirement Pre-Worker test', () => {
  beforeAll(beforeAllTests);
  let service;
  let pensionService;
  let Logger;
  let logService;
  let done;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
    pensionService = {
      createUpdatePension: jest.fn(() =>
        Promise.resolve({ completed: true, markPensionError: null })
      )
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });

  it('should call service to mark pensions if there is no error', async () => {
    service = {
      findPensionToInactivate: jest.fn().mockResolvedValue({ pensions: pensionsList }),
      markPensionToInactivate: jest.fn().mockResolvedValue({ pensionsToEvaluate: [] })
    };
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(service.findPensionToInactivate).toBeCalled();
    expect(service.markPensionToInactivate).toBeCalled();
    expect(pensionService.createUpdatePension).toBeCalled();
  });

  it('should call service to mark pensions if there is no error but cron mark fail', async () => {
    service = {
      findPensionToInactivate: jest.fn().mockResolvedValue({ pensions: pensionsList }),
      markPensionToInactivate: jest.fn().mockResolvedValue({ pensionsToEvaluate: [] })
    };
    logService.saveLog = jest.fn(() => Promise.reject(new Error('error')));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(service.findPensionToInactivate).toBeCalled();
    expect(service.markPensionToInactivate).toBeCalled();
    expect(pensionService.createUpdatePension).toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('worker already processed in current month', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(true));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(done).toBeCalled();
  });

  it('should return if there is an error', async () => {
    service = {
      findPensionToInactivate: jest.fn().mockResolvedValue({ error: new Error('error') }),
      markPensionToInactivate: jest.fn().mockResolvedValue({ pensionsToEvaluate: [] })
    };
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(service.findPensionToInactivate).toBeCalled();
    expect(service.markPensionToInactivate).not.toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
  });

  it('should return if there is an error if mark fail', async () => {
    service = {
      findPensionToInactivate: jest.fn().mockResolvedValue({ pensions: [] }),
      markPensionToInactivate: jest.fn(() =>
        Promise.resolve({ markPensionError: new Error('error') })
      )
    };
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(service.findPensionToInactivate).toBeCalled();
    expect(service.markPensionToInactivate).toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
  });

  it('throw error ', async () => {
    service.findPensionToInactivate = () => {
      throw Error('service error');
    };
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });
    expect(Logger.error).toHaveBeenCalled();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

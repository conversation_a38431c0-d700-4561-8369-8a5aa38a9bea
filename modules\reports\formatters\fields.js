const { formatter } = require('./formatFields');

const inactivateFields = [
  { pensionCodeId: { name: 'Número de pensión', format: formatter.number } },
  { pensionType: { name: 'Tipo de pensión', format: formatter.string } },
  { validityType: { name: 'Tipo de vigencia', format: formatter.string } },
  { basePension: { name: 'Pensión base', format: formatter.currency } },
  { beneficiaryName: { name: 'Nombre del beneficiario', format: formatter.string } },
  {
    endDateOfTheoricalValidity: { name: 'Fecha de fin de vigencia teórica', format: formatter.date }
  },
  { endDateOfValidity: { name: 'Fecha de fin de vigencia', format: formatter.date } },
  { inactivationReason: { name: 'Motivo de la inactivación', format: formatter.string } },
  { paymentEndDate: { name: 'Mes', format: formatter.monthYear } },
  { transient: { name: 'Transitoria', format: formatter.string } }
];

const reactivateFields = [
  { pensionCodeId: { name: 'Número de pensión', format: formatter.number } },
  { pensionType: { name: 'Tipo de pensión', format: formatter.string } },
  { validityType: { name: 'Tipo de vigencia', format: formatter.string } },
  { basePension: { name: 'Pensión base', format: formatter.currency } },
  { beneficiaryName: { name: 'Nombre del beneficiario', format: formatter.string } },
  {
    endDateOfTheoricalValidity: { name: 'Fecha de fin de vigencia teórica', format: formatter.date }
  },
  {
    endDateOfValidity: { name: 'Fecha de fin de vigencia anterior', format: formatter.date }
  },
  { reactivationReason: { name: 'Motivo de reactivación', format: formatter.string } },
  { paymentEndDate: { name: 'Mes', format: formatter.monthYear } },
  { transient: { name: 'Transitoria', format: formatter.string } },
  { inactivationDate: { name: 'Fecha de inactivación', format: formatter.date } }
];

const simpleFields = [...inactivateFields, ...reactivateFields].map(x => Object.keys(x)[0]);

module.exports = { simpleFields, inactivateFields, reactivateFields };

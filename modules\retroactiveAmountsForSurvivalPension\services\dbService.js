/* eslint-disable no-restricted-syntax */
const moment = require('moment');
const { roundValue } = require('../../sharedFiles/helpers');
const PensionModel = require('../../../models/pension');

const STUDY_PERIOD = /S[ií]/i;
const NON_VALID_PENSION = /No\s+vigente/i;
const PENSION_TYPES = [
  /Pensi[oó]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+sin\s+hijos/i,
  /Pensi[oó]n\s+por\s+orfandad/i,
  /Pensi[oó]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];

const SPECIAL_PENSION_TYPES = [
  /Pensi[oó]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[oó]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[oó]n\s+no\s+matrimonial\s+con\s+hijos/i
];

const filterPensioners = async () => {
  const currentYear = moment().year();
  const currentMonth = moment().month();
  return PensionModel.find({
    validityType: { $nin: [NON_VALID_PENSION] },
    pensionType: { $in: PENSION_TYPES },
    reactivationDate: {
      $gte: new Date(currentYear, currentMonth, 1),
      $lt: new Date(currentYear, currentMonth + 1, 1)
    },
    enabled: true
  }).lean();
};

const isSpecialCase = ({ pensionType, ChangeOfPensionTypeDueToCharges }) =>
  SPECIAL_PENSION_TYPES.some(regex => regex.test(pensionType)) && ChangeOfPensionTypeDueToCharges;

const getPensionsUntilConditionMeet = pensions => {
  const pensionsToUpdate = [];

  for (const pension of pensions) {
    if (!NON_VALID_PENSION.test(pension.validityType)) break;
    pensionsToUpdate.push(pension);
  }
  return pensionsToUpdate;
};

const setValidatedStudyPeriod = pensionList => {
  return pensionList
    .filter(pension => STUDY_PERIOD.test(pension.validatedStudyPeriod))
    .map(({ validatedStudyPeriod, ...data }) => {
      return { ...data, validatedStudyPeriod: 'Pagado' };
    });
};

const addUpReservedAmounts = pensionList => {
  const sumUp = pensionList.reduce((accSurvival, pension = {}) => {
    const { reservedAmounts = {}, validatedStudyPeriod = '' } = pension;
    if (STUDY_PERIOD.test(validatedStudyPeriod)) {
      return accSurvival + reservedAmounts.forSurvival;
    }
    return accSurvival;
  }, 0);

  return roundValue(sumUp);
};

const updateRetroactiveAmountForSurvival = async (
  { beneficiary, causant, retroactiveAmounts, ...data },
  pensionService,
  query = {}
) => {
  const historicalPensions = await pensionService.getPensionHistoryByMonth(
    beneficiary.rut,
    causant.rut,
    query
  );

  const previousPensionsToAdd = getPensionsUntilConditionMeet(historicalPensions);
  const pensionsToUpdate = setValidatedStudyPeriod(previousPensionsToAdd);
  const forSurvival = addUpReservedAmounts(previousPensionsToAdd);

  return [
    { ...data, beneficiary, causant, retroactiveAmounts: { ...retroactiveAmounts, forSurvival } },
    ...pensionsToUpdate
  ];
};

const service = {
  processRetroactiveAmountForSurvival: async pensionService => {
    const pensioners = await filterPensioners();

    const normalPensioner = pensioners.filter(pension => !isSpecialCase(pension));
    const specialPensioner = pensioners.filter(pension => isSpecialCase(pension));

    const specialCaseQuery = {
      createdAt: {
        $gte: moment()
          .subtract(2, 'years')
          .subtract(1, 'month')
          .startOf('month')
          .toDate(),
        $lt: moment()
          .startOf('month')
          .toDate()
      }
    };

    const updateNormalPensioners = await Promise.all(
      normalPensioner.map(pension => updateRetroactiveAmountForSurvival(pension, pensionService))
    );
    const updateSpecialPensioners = await Promise.all(
      specialPensioner.map(pension =>
        updateRetroactiveAmountForSurvival(pension, pensionService, specialCaseQuery)
      )
    );

    const flattenedUpdatedPensionList = [
      ...updateNormalPensioners,
      ...updateSpecialPensioners
    ].reduce((acc, curr) => [...acc, ...curr], []);

    const { completed, error } = await pensionService.updatePensionsByIdRetroActivoAmounts(
      flattenedUpdatedPensionList
    );
    return { completed, error };
  }
};

module.exports = { ...service };

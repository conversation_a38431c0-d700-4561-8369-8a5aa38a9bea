/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let temporaryService;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      historicalReactivationPensions: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      ),
      historicalInactivationPensions: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    temporaryService = {
      historicalReactivationPensions: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {};
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(service.historicalReactivationPensions).toBeCalled();
    expect(service.historicalInactivationPensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.historicalReactivationPensions).not.toBeCalled();
    expect(service.historicalInactivationPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('does not meet dependencyMarks', async () => {
    logService.existsLog = jest.fn(() => false);
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.historicalReactivationPensions).not.toBeCalled();
    expect(service.historicalInactivationPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('hitorical inactivation pensions fails', async () => {
    service.historicalInactivationPensions = jest.fn(() => Promise.resolve({ err: 'truthy' }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.historicalInactivationPensions).toHaveBeenCalledTimes(1);
    expect(service.historicalReactivationPensions).toHaveBeenCalledTimes(0);

    expect(logService.saveLog).not.toBeCalled();
  });

  it('hitorical reactivation pension fails', async () => {
    service.historicalReactivationPensions = jest.fn(() => Promise.resolve({ err: 'truthy' }));
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.historicalInactivationPensions).toHaveBeenCalledTimes(1);
    expect(service.historicalReactivationPensions).toHaveBeenCalledTimes(1);

    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      logService,
      pensionService,
      service,
      temporaryService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.historicalReactivationPensions).not.toBeCalled();
    expect(service.historicalInactivationPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

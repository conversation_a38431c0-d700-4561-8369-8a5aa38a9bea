const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const cronDescription = 'cálculo de descuentos de bonificación de activos:';
const cronMark = 'CALCULATE_ASSETS_BONUS_DISCOUNTS_WORKER';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';

const workerFn = async ({ job, Logger, logService, pensionService, service, done }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage,
        alreadyExecuted: true
      };
    }
    Logger.info(`${cronDescription} process started`);
    const { error } = await service.calculateAssetsBonusDiscounts(pensionService);
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};
module.exports = { cronMark, dependencyMark, workerFn };

const moment = require('moment');
const { roundValue, pipe } = require('../../sharedFiles/helpers');

const pensionTypes = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const isMatched = ({ pensionType }) => pensionTypes.some(type => type.test(pensionType));

const reajustBasePension = pension => {
  const { basePension, daysToPay } = pension;
  return {
    ...pension,
    basePension: roundValue(basePension * (daysToPay / moment().daysInMonth()))
  };
};

const reajustArticle40And41 = pension => {
  const { article40, article41, daysToPay } = pension;
  return isMatched(pension)
    ? {
        ...pension,
        article40: roundValue(article40 * (daysToPay / moment().daysInMonth())),
        article41: roundValue(article41 * (daysToPay / moment().daysInMonth()))
      }
    : { ...pension };
};

const modifier = obj => pipe(reajustBasePension, reajustArticle40And41)(obj);

module.exports = modifier;

const service = require('../services/dbService');
const pensionService = require('../../../pensions/services/pension.service');
const preWorkerModule = require('./pre.worker');
const postWorkerModule = require('./post.worker');
const logService = require('../../../sharedFiles/services/jobLog.service');

module.exports = {
  inactivatePensionsByRetirementPreWorker: {
    name: 'inactivatePensionsByRetirementPreWorker',
    worker: deps => preWorkerModule.workerFn({ service, pensionService, logService, ...deps }),
    description:
      'Marcar pensiones de invalidez a inactivar el mes siguiente al actual por jubilación',
    endPoint: 'inactivatebyretirementpreworker',
    cronMark: preWorkerModule.cronMark,
    dependencyMark: preWorkerModule.dependencyMark
  },
  inactivatePensionsByRetirementPostWorker: {
    name: 'inactivatePensionsByRetirementPostWorker',
    worker: deps => postWorkerModule.workerFn({ service, logService, ...deps }),
    description:
      'Inactivar pensiones marcadas en el mes anterior al actual para inactivar por jubilación',
    endPoint: 'inactivatebyretirementpostworker',
    cronMark: postWorkerModule.cronMark,
    dependencyMark: postWorkerModule.dependencyMark
  }
};

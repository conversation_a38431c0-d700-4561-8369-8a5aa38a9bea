const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'rejectedRetroactiveAmount',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_REJECTED_RETROACTIVE_AMOUNT_FREQUENCY,
  description:
    'Cron que calcula el monto retroactivo para pensionados rechazados por el banco o vale vista reintegrado',
  endPoint: 'rejectedretroactiveamount',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

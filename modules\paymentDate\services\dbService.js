/* eslint-disable no-unused-expressions */
const moment = require('moment-business-days');
const { getHolidays } = require('../../sharedFiles/services/holidays.service');
const PaymentDateModel = require('../models/paymentDate');

const service = {
  async createUpdatePaymentDates(paymentDateList) {
    const session = await PaymentDateModel.startSession();
    await session.startTransaction();
    try {
      const bulk = PaymentDateModel.collection.initializeOrderedBulkOp();
      paymentDateList.forEach(paymentDateObject => {
        const { year, month, paymentDate } = paymentDateObject;
        bulk
          .find({ year, month, enabled: true })
          .upsert()
          .replaceOne({
            year,
            month,
            paymentDate,
            enabled: true,
            createdAt: new Date(),
            updatedAt: new Date()
          });
      });
      await bulk.execute();
      await session.commitTransaction();
      return { completed: true };
    } catch (e) {
      await session.abortTransaction();
      return { completed: false, error: e };
    }
  },
  async generatePaymentDates(year) {
    moment.updateLocale('cl', {
      workingWeekdays: [1, 2, 3, 4, 5],
      holidays: getHolidays(year),
      holidayFormat: 'YYYY-MM-DD'
    });
    const months = [...Array(12).keys()].map(i => (i + 1).toString().padStart(2, '0'));
    const paymentDates = months.map(month => {
      let originDate = moment(`${year}-${month}-01`)
        .endOf('month')
        .startOf('day');
      let paymentDate;
      switch (month) {
        case '09':
          originDate = moment(`${year}-${month}-18`).startOf('day');
          paymentDate = moment(originDate)
            .businessSubtract(1)
            .toDate();
          break;
        case '12':
          paymentDate = moment(originDate)
            .businessSubtract(8)
            .toDate();
          break;
        default:
          paymentDate = moment(originDate)
            .businessSubtract(3)
            .toDate();
          break;
      }
      return {
        year,
        month,
        paymentDate
      };
    });
    const { completed, error } = await this.createUpdatePaymentDates(paymentDates);
    return { completed, error };
  }
};

module.exports = { ...service };

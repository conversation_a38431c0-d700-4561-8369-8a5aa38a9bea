const {
  getInactiveTemporaryHorphanhood
} = require('../../../orphanhood/services/orphanhood.service');
const { createUpdatePension } = require('../../../pensions/services/pension.service');

const NOT_VALID_TYPE = /No vigente/i;
const TYPE_PENSION = [/Pensi[oó]n por orfandad/i, /Pensi[oó]n de orfandad de padre y madre/i];
const VALIDITY_TRUE = true;

module.exports = {
  async activate() {
    try {
      const result = await getInactiveTemporaryHorphanhood(
        VALIDITY_TRUE,
        NOT_VALID_TYPE,
        TYPE_PENSION
      );
      if (result.length) {
        const pensionList = result.map(({ _id, endDate, ...pensionerData }) => {
          const { pension } = pensionerData;
          const { beneficiary, causant, pensionCodeId } = pension;

          return {
            beneficiary,
            causant,
            pensionCodeId,
            inactivationReason: '-',
            endDateOfValidity: endDate,
            validityType: 'Vigente Orfandad',
            reactivationDate: new Date()
          };
        });

        if (pensionList.length) {
          const { completed, error } = await createUpdatePension(pensionList);
          if (!completed) throw new Error(error);
        }
      }
    } catch (error) {
      throw new Error(error);
    }
  }
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionsModel = require('../../../../models/pension');
const service = require('./dbService');
const pensionService = require('../../../pensions/services/pension.service');
const liquidationService = require('../../../liquidation/services/liquidation.service');

const pensions = require('../../../../resources/previred/pensions.json');
const afps = require('../../../../resources/previred/afps.json');
const isapres = require('../../../../resources/previred/isapres.json');

describe('generator of previred files', () => {
  beforeAll(beforeAllTests);
  let afpService;
  let isapreService;
  let Logger;
  beforeEach(() => {
    jest.spyOn(Date, 'now').mockImplementation(() => new Date('06-01-2020 00:00:00'));
    afpService = { getAfps: jest.fn(() => Promise.resolve({ result: afps })) };
    isapreService = { getIsapres: jest.fn(() => Promise.resolve({ result: isapres })) };
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
  });

  it('should generate previred file', async () => {
    await PensionsModel.insertMany(pensions);

    await service.generatePreviredFile(
      pensionService,
      liquidationService,
      afpService,
      isapreService,
      Logger
    );

    expect(afpService.getAfps).toBeCalledTimes(1);
    expect(isapreService.getIsapres).toBeCalledTimes(1);
  });

  afterEach(() => {
    Date.now.mockRestore();
  });
  afterAll(afterAllTests);
});

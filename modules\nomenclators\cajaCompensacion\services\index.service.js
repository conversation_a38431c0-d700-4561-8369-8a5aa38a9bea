const cajaModel = require('../../../../models/compensationboxs');

const service = {
  async updateCaja({ name, ...cajaData }) {
    try {
      const data = await cajaModel
        .findOneAndUpdate(
          { name },
          { $set: { ...cajaData } },
          { returnNewDocument: true, upsert: true, new: true }
        )
        .exec();
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getCajas() {
    return cajaModel
      .find({})
      .lean()
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async getCajaByName(filter) {
    return cajaModel
      .findOne({ name: filter })
      .lean()
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

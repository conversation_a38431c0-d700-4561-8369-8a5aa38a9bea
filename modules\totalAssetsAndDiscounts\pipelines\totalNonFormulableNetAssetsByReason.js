const calculate = pension => {
  const totalNetAssetsByReason = {};
  const netAssetRegex = /l[ií<PERSON><PERSON>]qu[íìïi]do/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const netAssets = assetsNonFormulable.filter(({ assetType }) => netAssetRegex.test(assetType));
  netAssets.forEach(asset => {
    const { reason, amount } = asset;
    if (reason) {
      totalNetAssetsByReason[reason] = (totalNetAssetsByReason[reason] || 0) + amount;
    }
  });
  const netNonFormulableByReason = Object.entries(
    totalNetAssetsByReason
  ).map(([reason, amount]) => ({ reason, amount }));

  return { ...pension, assets: { ...pension.assets, netNonFormulableByReason } };
};

module.exports = calculate;

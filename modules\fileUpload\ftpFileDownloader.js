const downloadFTPFile = async ({
  client,
  remoteFilePath,
  connectToFTPServer,
  ftpCredentials,
  tmp
}) => {
  const { connected, error } = await connectToFTPServer(client, ftpCredentials);
  if (!connected) return { file: null, error };

  const remoteFile = await client.list(remoteFilePath);
  if (!remoteFile.length) return { file: null, error };

  const outputFilePath = tmp.fileSync({ postfix: '.txt' }).name;
  await client.downloadTo(outputFilePath, remoteFilePath);
  client.close();
  return { file: outputFilePath, error };
};
module.exports = downloadFTPFile;

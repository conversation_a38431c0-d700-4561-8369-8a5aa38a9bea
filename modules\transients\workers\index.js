const pensionService = require('../../pensions/services/pension.service');
const daysUtils = require('./daysUtils');
const sapService = require('./sapSinisterData');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'daysOfTranstientPension',
  worker: deps =>
    workerModule.workerFn({
      pensionService,
      sapService,
      daysUtils,
      logService,
      ...deps
    }),
  description: 'Calculo de días a pagar a las pensiones transitorias',
  endPoint: 'daysoftranstientpension',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

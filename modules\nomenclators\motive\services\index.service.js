/* eslint-disable no-unused-expressions */

const MotiveModel = require('../models/motive');

const service = {
  async updateMotive({ _id: id, ...motiveData }) {
    const criteria = {
      enabled: false,
      $or: [{ motive: motiveData.motive, option: motiveData.option }]
    };
    try {
      const oldMotive = await MotiveModel.findOne(criteria).exec();
      if (oldMotive && oldMotive.id) {
        await MotiveModel.remove({ _id: oldMotive.id }).exec();
      }
      const data = await MotiveModel.findOneAndUpdate(
        { _id: id, enabled: true, isDefault: false },
        { $set: { ...motiveData } },
        { returnNewDocument: true, upset: true, new: true }
      ).exec();
      return { result: data, isError: false };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async createMotive(motiveData) {
    const criteria = {
      enabled: false,
      $or: [{ motive: motiveData.motive, option: motiveData.option }]
    };
    try {
      const result = await MotiveModel.findOne(criteria).exec();
      if (result) {
        const savedMotive = await MotiveModel.findOneAndUpdate(
          { ...criteria, isDefault: false },

          { ...motiveData, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedMotive };
      }
      const data = await MotiveModel.create(motiveData);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async deleteMotive(id) {
    try {
      const data = await MotiveModel.update(
        { _id: id, enabled: true, isDefault: false },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getMotives(query = { enabled: true }) {
    return MotiveModel.find(query)
      .lean()
      .then(data => ({ result: data.map(({ __v, ...motive }) => ({ ...motive })) }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

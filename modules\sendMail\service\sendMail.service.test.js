/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const sgMail = require('@sendgrid/mail');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

process.env = Object.assign(process.env, {
  SENDGRID_API_KEY: 'SG.'
});
const service = require('./sendMail.service');

describe('sendmail service Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {
    jest.resetModules();
    jest.spyOn(sgMail, 'send').mockImplementationOnce(() => Promise.resolve());
  });
  // Solo para que pasen los test
  it('success send email service', async () => {
    // eslint-disable-next-line global-require
    const { error, completed } = await service.sendEmail(
      { to: '<EMAIL>', cc: '<EMAIL>' },
      {}
    );

    expect(error).toBeFalsy();
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const pensionsData = require('../../../resources/pensions.json');

const service = require('./dbService');
const PensionModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');
const pensionService = require('../../pensions/services/pension.service');

const activePension = {
  ...pensionsData[0],
  bankRejected: 'Sí',
  validityType: 'vigente',
  reservedAmounts: { forRejection: 15000 },
  enabled: true,
  createdAt: moment().toDate()
};

const pension1 = {
  ...activePension,
  enabled: false,
  createdAt: moment()
    .subtract(1, 'month')
    .toDate()
};

const pension2 = {
  ...activePension,
  enabled: false,
  createdAt: moment()
    .subtract(2, 'month')
    .toDate()
};

const pension3 = {
  ...activePension,
  bankRejected: 'No',
  enabled: false,
  createdAt: moment()
    .subtract(3, 'month')
    .toDate()
};

describe('rejected reserved amount test', () => {
  beforeAll(beforeAllTests);

  it('should get pensions and set the retroactive amounts for rejection', async () => {
    await PensionModel.insertMany([activePension]);
    await PensionHistoricModel.insertMany([pension1, pension2, pension3]);
    const { completed, error } = await service.calculateRetroactiveRejectedPensions(
      pensionService,
      PensionModel
    );
    const updatedPension = await PensionModel.findOne({ enabled: true }).lean();
    expect(completed).toBe(true);
    expect(error).toBeFalsy();
    expect(updatedPension.retroactiveAmounts.forRejection).toBe(45000);
  });

  it('should get pensions and set the retroactive amounts for pay checked', async () => {
    const paycheckPensions = [activePension].map(p => ({
      ...p,
      reservedAmounts: { forPayCheck: 15000 },
      retroactiveAmounts: { forPayCheck: 0 },
      paycheckRefunded: 'Sí'
    }));

    const paycheckPensionsHistory = [pension1, pension2, pension3].map((p, idx) => ({
      ...p,
      reservedAmounts: { forPayCheck: 15000 },
      retroactiveAmounts: { forPayCheck: 0 },
      paycheckRefunded: idx === 2 ? 'No' : 'Sí'
    }));

    await PensionModel.insertMany(paycheckPensions);
    await PensionHistoricModel.insertMany(paycheckPensionsHistory);
    const { completed, error } = await service.calculateRetroactivePayCheckRefundedPensions(
      pensionService,
      PensionModel
    );
    const updatedPension = await PensionModel.findOne({ enabled: true }).lean();
    expect(completed).toBe(true);
    expect(error).toBeFalsy();
    expect(updatedPension.retroactiveAmounts.forPayCheck).toBe(45000);
  });

  beforeEach(async () => {
    await PensionModel.deleteMany();
    await PensionHistoricModel.deleteMany();
  });
  afterAll(afterAllTests);
});

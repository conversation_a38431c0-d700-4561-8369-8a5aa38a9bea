const Model = require('../models/temporaryPensioner');
const mapperTemporary = require('./modifiers/discounts');
const logService = require('../../sharedFiles/services/jobLog.service');

const linkMark = 'LINK_PENSIONS';

const service = {
  populateFields: () => '',
  async bulkAndDelete(body) {
    try {
      await Model.deleteMany({}).exec();
      const result = await Model.insertMany(mapperTemporary(body));
      return { isError: false, result };
    } catch (error) {
      return { isError: true, error };
    }
  },
  async getAll(query = null, sort = null) {
    if (await logService.existsLog(linkMark)) return [];
    if (query && sort) {
      return Model.find(query).sort(sort);
    }
    return Model.find(query)
      .sort(sort)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = service;

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const discountsAndAssetsModel = require('../../../models/discountsAndAssets');
const discountsAndAssetsObject = require('../../../resources/discountsAndAssets.json');
const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./dbService');

describe('Total assets and discounts service', () => {
  beforeAll(beforeAllTests);
  it('should get pensions and calculate total assets and discounts', async () => {
    const discountsAndAssets = await discountsAndAssetsModel
      .create({
        ...discountsAndAssetsObject,
        causantRut: '11111111-1',
        beneficiaryRut: '22222222-2'
      })
      .catch(e => console.error(e));
    const pension = { ...pensionData, discountsAndAssets: discountsAndAssets._id };
    await PensionModel.create(pension).catch(e => console.error(e));
    await service.calculateTotalAssetsAndDiscounts().catch(e => console.error(e));
    const enabledPension = await PensionModel.findOne({ enabled: true }).catch(e =>
      console.error(e)
    );
    const {
      discounts: { totalNonFormulable },
      assets: { netTotalNonFormulable, taxableTotalNonFormulable },
      numberOfNonFormulableDiscounts,
      numberOfNetNonFormulableAssets,
      numberOfTaxableNonFormulableAssets
    } = enabledPension;
    expect(totalNonFormulable).toBe(60);
    expect(netTotalNonFormulable).toBe(60);
    expect(taxableTotalNonFormulable).toBe(40);
    expect(numberOfNonFormulableDiscounts).toBe(2);
    expect(numberOfNetNonFormulableAssets).toBe(2);
    expect(numberOfTaxableNonFormulableAssets).toBe(2);
  });

  afterAll(afterAllTests);
});

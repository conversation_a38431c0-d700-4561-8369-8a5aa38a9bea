/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const reservedPensionData = require('../../../resources/backpensionsToRejectedReservedAmount.json');
const service = require('./dbService');

describe('rejected reserved amount test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let Model;
  beforeEach(() => {
    Model = {
      aggregate: jest
        .fn(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
    };
    pensionService = {
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
  });

  it('should get pensions and set the back paycheck Refunded Pensions amount', async () => {
    Model = {
      aggregate: jest
        .fn(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => [reservedPensionData]) }))
    };

    const { completed, error } = await service.calculateReservedAmountBypaycheckRefunded(
      pensionService,
      Model
    );
    expect(completed).toBe(true);
    expect(error).toBe(undefined);
  });

  it('Error when should get pensions and set the back netpensions amount', async () => {
    pensionService = {
      updatePensions: jest.fn(() => Promise.reject(new Promise('error')))
    };

    Model = {
      aggregate: jest
        .fn(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => [reservedPensionData]) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
    };
    const { completed } = await service.calculateReservedAmountBypaycheckRefunded(
      pensionService,
      Model
    );

    expect(completed).toBeFalsy();
  });

  it('should get pensions and set the back rejected Pensions amount', async () => {
    Model = {
      aggregate: jest
        .fn(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => [reservedPensionData]) }))
    };
    const { completed, error } = await service.calculateReservedAmountByRejectedPensions(
      pensionService,
      Model
    );
    expect(completed).toBe(true);
    expect(error).toBeFalsy();
  });

  it('Error when should get pensions and set the back netpensions amount by rejected pensions', async () => {
    pensionService = {
      updatePensions: jest.fn(() => Promise.reject(new Promise('error')))
    };
    Model = {
      aggregate: jest
        .fn(() => ({ cursor: jest.fn(() => []) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => [reservedPensionData]) }))
        .mockImplementationOnce(() => ({ cursor: jest.fn(() => []) }))
    };
    const { completed } = await service.calculateReservedAmountByRejectedPensions(
      pensionService,
      Model
    );
    expect(completed).toBe(false);
  });

  afterAll(afterAllTests);
});

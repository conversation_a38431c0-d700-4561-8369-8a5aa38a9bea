/* eslint-disable prettier/prettier */
const moment = require('moment');

const PensionModel = require('../../../models/pension');
const { getPreviousMonthAndYear } = require('../../sharedFiles/helpers');
const pensionService = require('../../pensions/services/pension.service');
const CurrentCapitalReportModel = require('../models/CurrentCapitalReport');
const { getCurrentYearAndMonth } = require('../../inactivatePensions/sharedFiles/filesHelper');
const {
  createPrevAndCurrMonthReport,
  getReportObject,
  createTotalCapitalReportByPensionType,
  createReactivatedInCurrMonthReport,
  createInactPensionsInCurrMonthReport,
  createValidCurrMonthPensionsReport
} = require('./reportGenerator');
const {
  validPensionsWithPreviousMonthDocQuery,
  buildQuery,
  pensionsWithValidPreviousMonthDocQuery,
  pensionsInactInCurrMonthQuery
} = require('./queryBuilder');

const reportTypeMapper = {
  report1: 'Reporte 1',
  report2: 'Reporte 2',
  report3: 'Reporte 3',
  report4: 'Reporte 4',
  report5: 'Reporte 5'
};

const REPORT3_INACT_REASONS = [
  /Alta\s+m[ée]dica/i,
  /Expiraci[óo]n\s+de\s+a[ñn]o\s+de\s+pago/i,
  /Vencimiento\s+de\s+certificado\s+de\s+estudios/i
];

const categoryColumns = {
  report1: 'Estado Capital / Tipo de invalidez',
  report2: 'Estado Capital / Tipo de invalidez',
  report3: 'Motivo de reactivación',
  report4: 'Motivo de inactivación',
  report5: 'Capital / Tipo de Pensión'
};

const dataColumns = [
  { field: 'pensionporaccidentedetrabajo', header: 'Pensión por accidente de trabajo' },
  { field: 'pensionporaccidentedetrayecto', header: 'Pensión por accidente de trayecto' },
  { field: 'pensionporenfermedadprofesional', header: 'Pensión por enfermedad profesional' },
  { field: 'pensionpororfandad', header: 'Pensión por orfandad' },
  { field: 'pensiondeorfandaddepadreymadre', header: 'Pensión de orfandad de padre y madre' },
  { field: 'pensiondeviudezconhijos', header: 'Pensión de viudez con hijos' },
  { field: 'pensiondeviudezsinhijos', header: 'Pensión de viudez sin hijos' },
  {
    field: 'pensiondemadredehijodefiliacionnomatrimonialconhijos',
    header: 'Pensión de madre de hijo de filiación no matrimonial con hijos'
  },
  {
    field: 'pensiondemadredehijodefiliacionnomatrimonialsinhijos',
    header: 'Pensión de madre de hijo de filiación no matrimonial sin hijos'
  }
];

const report5DataColumns = [
  { field: 'monthColumnSummatory', header: 'MES' },
  { field: 'ipcColumnSummatory', header: 'IPC' },
  { field: 'capitalPlusIpc', header: 'Capital + IPC' }
];

const setCurrentMonthDocsCapitalStatus = pensions => {
  const updatedCurrentMonthPensions = [];
  pensions.forEach(pension => {
    const { previousMonthDoc, ...currentMonthDoc } = pension;
    if (!previousMonthDoc || previousMonthDoc.validityType.match(/No\s+vigente/i)) {
      updatedCurrentMonthPensions.push({ ...currentMonthDoc, capitalStatus: 'Nuevo' });
    } else {
      updatedCurrentMonthPensions.push({ ...currentMonthDoc, capitalStatus: 'Mantiene' });
    }
  });
  return updatedCurrentMonthPensions;
};

const setPreviousMonthDocsCapitalStatus = pensions => {
  const updatedPreviousMonthPensions = [];
  pensions.forEach(pension => {
    const { previousMonthDoc, ...currentMonthDoc } = pension;
    const { validityType } = currentMonthDoc;
    if (validityType.match(/No\s+vigente/i)) {
      updatedPreviousMonthPensions.push({ ...previousMonthDoc, capitalStatus: 'Salida' });
    } else {
      updatedPreviousMonthPensions.push({ ...previousMonthDoc, capitalStatus: 'Mantiene' });
    }
  });
  return updatedPreviousMonthPensions;
};

const getPensions = async queryFn => {
  const [month, year] = getPreviousMonthAndYear();
  const query = queryFn(month, year);
  const pensions = await PensionModel.aggregate(query);
  return pensions;
};

const getReportFileName = (type, date) => {
  const YYYY_MM = moment(date).format('YYYYMM');
  const currentMonth = moment(date)
    .locale('es')
    .format('MMMM');
  const previousMonth = moment(date)
    .date(0)
    .locale('es')
    .format('MMMM-YYYY');
  const filename = {
    report1: `Vigentes_${YYYY_MM}_mes_${currentMonth}`,
    report2: `Reporte2_vigentes_mes_${previousMonth}`,
    report3: `Reporte3_entradas_enlazados_nuevos_y_reactivados_mes_${currentMonth}`,
    report4: `Reporte4_salidas_${currentMonth}`,
    report5: `Reporte5_contabilizacion_de_capitales_vigentes_${YYYY_MM}`
  };
  return filename[type];
};

const objectToCsvString = (obj, columns) => {
  const csvRow = [];
  columns.forEach(key => {
    csvRow.push(`${obj[key]}`);
  });
  return csvRow.join(',');
};

const getDetailsCsvString = (details, columns) => {
  let csvString = '';
  details.forEach(detailObj => {
    csvString += `${objectToCsvString(detailObj, columns)}\n`;
  });
  return csvString;
};

const getCsvHeader = reportType => {
  const columsData = reportType === 'report5' ? report5DataColumns : dataColumns;
  const headerArr = [categoryColumns[reportType], ...columsData.map(col => col.header)];
  return `${headerArr.join(',')}\n`;
};

const getCsvPeriod = (type, date) => {
  if (type === 'report5') {
    return `Contabilización de Capitales Vigentes ${moment().format('YYYYMM')}`;
  }
  if (type === 'report2') {
    return moment(date)
      .date(0)
      .locale('es')
      .format('MMMM-YYYY');
  }
  return moment(date)
    .locale('es')
    .format('MMMM-YYYY');
};

const getColumns = type => {
  if (type === 'report5') {
    return ['category', ...report5DataColumns.map(col => col.field)];
  }
  return ['category', ...dataColumns.map(col => col.field)];
};

const getCsvReport = (reports, reportType) => {
  let csv = '';

  const csvPeriod = getCsvPeriod(reportType, reports[0].createdAt);
  const columns = getColumns(reportType);
  const csvHeader = getCsvHeader(reportType);
  csv += `${csvPeriod}\n`;
  csv += csvHeader;
  reports.forEach(report => {
    const { detailsByInvalidityType, ...reportData } = report;
    csv += `${objectToCsvString(reportData, columns)}\n`;
    if (detailsByInvalidityType.length)
      csv += getDetailsCsvString(detailsByInvalidityType, columns);
  });
  return csv;
};

const updatePreviousMonthPensionsWithCapitalStatus = async () => {
  try {
    const pensions = await getPensions(pensionsWithValidPreviousMonthDocQuery);
    const updatedPensions = setPreviousMonthDocsCapitalStatus(pensions);
    const { completed, error } = await pensionService.updatePensions(updatedPensions);
    return { completed, error, updatedPensions };
  } catch (error) {
    return { completed: false, error, updatedPensions: [] };
  }
};

const updateCurrentMonthPensionsWithCapitalStatus = async () => {
  try {
    const pensions = await getPensions(validPensionsWithPreviousMonthDocQuery);
    const updatedPensions = setCurrentMonthDocsCapitalStatus(pensions);
    const { completed, error } = await pensionService.updatePensions(updatedPensions);
    return { completed, error, updatedPensions };
  } catch (error) {
    return { completed: false, error, updatedPensions: [] };
  }
};

const createFilter = keyword => ({ capitalStatus }) => capitalStatus === keyword;

const getPensionsInfoForReportOneAndTwo = async reportType => {
  if (reportType === 'Reporte 1') {
    const { error, updatedPensions } = await updateCurrentMonthPensionsWithCapitalStatus();
    if (error) throw new Error(error);
    return updatedPensions.map(
      ({ pensionType, currentCapitalCalculation, capitalStatus, disabilityType }) => {
        return { pensionType, currentCapitalCalculation, capitalStatus, disabilityType };
      }
    );
  }

  if (reportType === 'Reporte 2') {
    const { error, updatedPensions } = await updatePreviousMonthPensionsWithCapitalStatus();
    if (error) throw new Error(error);
    return updatedPensions.map(
      ({ pensionType, currentCapitalCalculation, capitalStatus, disabilityType }) => {
        return { pensionType, currentCapitalCalculation, capitalStatus, disabilityType };
      }
    );
  }

  return [];
};

const getPensionsInfoForReportThree = async () => {
  const [year, month] = getCurrentYearAndMonth();
  const linkedInCurrMonthQuery = buildQuery({
    dateField: 'linkedDate',
    year,
    month,
    additionalQuery: {
      capitalStatus: /Nuev[ao]/i
    }
  });
  const reactivatedInCurrMonthQuery = buildQuery({
    dateField: 'reactivationDate',
    year,
    month,
    additionalQuery: {
      inactivationReason: { $in: REPORT3_INACT_REASONS },
      capitalStatus: /Nuev[ao]/i
    }
  });
  const totalCapitalReportFields = { pensionType: 1, currentCapitalCalculation: 1 };
  const reactivatedReportFields = {
    pensionType: 1,
    currentCapitalCalculation: 1,
    inactivationReason: 1
  };

  return Promise.all([
    PensionModel.find(linkedInCurrMonthQuery, totalCapitalReportFields).lean(),
    PensionModel.find(reactivatedInCurrMonthQuery, reactivatedReportFields).lean()
  ]);
};

const getPensionsInfoForReportFour = async () => {
  const pensions = await getPensions(pensionsInactInCurrMonthQuery);
  return pensions.map(({ pensionType, capitalStatus, inactivationReason, previousMonthDoc }) => ({
    pensionType,
    capitalStatus,
    inactivationReason,
    currentCapitalCalculation: previousMonthDoc.currentCapitalCalculation
  }));
};

const service = {
  async generateValidPensionsInPrevAndCurrMonthReport(typeAndCategoryObj) {
    const { firstCategory, secondCategory, reportType } = typeAndCategoryObj;
    const pensionsInfo = await getPensionsInfoForReportOneAndTwo(reportType);

    const firstCategoryReport = createPrevAndCurrMonthReport(
      pensionsInfo.filter(createFilter(firstCategory)),
      firstCategory,
      reportType
    );
    const secondCategoryReport = createPrevAndCurrMonthReport(
      pensionsInfo.filter(createFilter(secondCategory)),
      secondCategory,
      reportType
    );
    const generalReportObj = getReportObject([firstCategoryReport, secondCategoryReport]);
    const { detailsByInvalidityType, ...generalReport } = generalReportObj;
    const reportGeneral = { category: 'Total general', reportType, ...generalReport };
    await CurrentCapitalReportModel.deleteMany({ reportType });
    await CurrentCapitalReportModel.insertMany([
      firstCategoryReport,
      secondCategoryReport,
      reportGeneral
    ]);
  },

  async generatePensionsLinkedAndReactivatedInCurrentMonthReport() {
    const REPORT_THREE = 'Reporte 3';

    const [
      pensionsLinkedInCurrMonth,
      pensionsReactivatedInCurrMonth
    ] = await getPensionsInfoForReportThree();

    const linkedInCurrMonthReport = createTotalCapitalReportByPensionType(
      pensionsLinkedInCurrMonth,
      'Nuevos enlazados',
      REPORT_THREE
    );
    const reactivatedInCurrMonth = createReactivatedInCurrMonthReport(
      pensionsReactivatedInCurrMonth,
      REPORT_THREE
    );
    const generalReport = getReportObject([linkedInCurrMonthReport, ...reactivatedInCurrMonth]);
    const reportsList = [
      linkedInCurrMonthReport,
      ...reactivatedInCurrMonth,
      {
        category: 'Total general',
        reportType: REPORT_THREE,
        ...generalReport
      }
    ];
    await CurrentCapitalReportModel.deleteMany({ reportType: REPORT_THREE });
    await CurrentCapitalReportModel.insertMany(reportsList);
  },

  async generatePensionsInactInCurrMonthReport() {
    const REPORT_FOUR = 'Reporte 4';

    const pensionsInfo = await getPensionsInfoForReportFour();
    const outgoingPensionsReport = createTotalCapitalReportByPensionType(
      pensionsInfo.filter(({ capitalStatus }) => capitalStatus === 'Salida'),
      'Salida',
      'Reporte 4'
    );
    const inactInCurrMonthReport = createInactPensionsInCurrMonthReport(pensionsInfo, REPORT_FOUR);
    const reportsList = [outgoingPensionsReport, ...inactInCurrMonthReport];
    await CurrentCapitalReportModel.deleteMany({ reportType: REPORT_FOUR });
    await CurrentCapitalReportModel.insertMany(reportsList);
  },

  async generateValidPensionInCurrentMonthReport() {
    const REPORT_FIVE = 'Reporte 5';
    const [year, month] = getCurrentYearAndMonth();
    const query = buildQuery({ dateField: 'createdAt', year, month });
    const selectedFields = { pensionType: 1, currentCapitalCalculation: 1 };
    const pensions = await PensionModel.find(query, selectedFields).lean();
    const reportsList = createValidCurrMonthPensionsReport(pensions);
    await CurrentCapitalReportModel.deleteMany({ reportType: REPORT_FIVE });
    await CurrentCapitalReportModel.insertMany(reportsList);
  },

  async getCsvReportData(type) {
    try {
      const reportType = reportTypeMapper[type.toLowerCase()];
      const reports = await CurrentCapitalReportModel.find({ reportType }).lean();
      if (!reports.length) throw new Error(`No report data found for ${type}`);
      const csvReportData = getCsvReport(reports, type.toLowerCase());
      const filename = getReportFileName(type.toLowerCase(), reports[0].createdAt);
      return { csvReportData, filename, error: false };
    } catch (error) {
      return { csvReportData: null, error };
    }
  }
};

module.exports = service;

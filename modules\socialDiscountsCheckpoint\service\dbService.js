const pensionService = require('../../pensions/services/pension.service');
const liquidationService = require('../../liquidation/services/liquidation.service');
const afpService = require('../../nomenclators/afp/services/index.service');
const ufService = require('../../UFvalue/services/ufValue.service');
const cajaService = require('../../nomenclators/cajaCompensacion/services/index.service');

const { recursiveSum, recursiveCount, roundValue } = require('../../sharedFiles/helpers');
const {
  netPension: calculateNetPension
} = require('../../netPensionsLiquidationReports/netPensions/services/dbService');

const VALIDITY_TYPE = /No\s+vigente/i;
const percentage = 0.25;
const discountsPath = [
  'othersLosHeroes',
  'othersLosAndes',
  'socialCreditsLaAraucana',
  'socialCredits18',
  'socialCreditsLosHeroes',
  'socialCreditsLosAndes'
];

const validPensionFilter = pension => {
  const { liquidation } = pension;
  const { taxablePension } = liquidation;
  const totalDiscounts = recursiveSum(pension.discounts, discountsPath);
  const quarterTaxablePension = taxablePension * percentage;
  return totalDiscounts > quarterTaxablePension;
};

const decreaseDiscounts = ({ _id, liquidation, ...pension }) => {
  const { discounts } = pension;
  const { taxablePension } = liquidation;
  const quantityOfDiscounts = recursiveCount(discounts, discountsPath);
  const totalDiscounts = recursiveSum(discounts, discountsPath);
  const quarterTaxablePension = taxablePension * percentage;
  const updatedDiscounts = discountsPath.reduce((acc, key) => {
    if (discounts[key] && quantityOfDiscounts === 1) {
      acc[key] = roundValue(discounts[key] - (discounts[key] - quarterTaxablePension));
    } else {
      acc[key] = roundValue(discounts[key] * (quarterTaxablePension / totalDiscounts));
    }
    return acc;
  }, {});
  return { ...pension, discounts: { ...pension.discounts, ...updatedDiscounts } };
};

const service = {
  async recalculateSocialDiscountsCheckpoint() {
    const queryObj = { enabled: true, validityType: { $not: VALIDITY_TYPE } };
    const { result, error } = await pensionService.getPensionsWithLiquidation(queryObj);
    if (error) return { completed: false, error };
    const validPensions = result.filter(validPensionFilter);
    const updatedPensions = validPensions.map(decreaseDiscounts);
    const { completed, error: updateError } = await pensionService.createUpdatePension(
      updatedPensions
    );
    return { completed, error: updateError, updatedPensions };
  },

  async recalculateNetPension(pensions) {
    const { completed, error } = await calculateNetPension(
      pensionService,
      liquidationService,
      afpService,
      ufService,
      cajaService,
      pensions
    );
    return { completed, error };
  }
};

module.exports = service;

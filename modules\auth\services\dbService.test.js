const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./auth.service');

describe('worker Tesssst', () => {
  beforeAll(beforeAllTests);
  let res;

  beforeEach(() => {
    process.env = Object.assign(process.env, {
      JWTsecret: '+I0qp`&@MTc}I^%',
      JWTexpires: '1d'
    });
    res = {
      send: jest.fn(),
      status: jest.fn(() => res)
    };
  });

  it('login', async () => {
    const { complete, data } = await service.login({
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      id: 'fakeid'
    });
    expect(complete).toBe(true);
    expect(data.token).toBeDefined();
  });

  it('logout', async () => {
    const { data } = await service.login({
      name: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      id: 'fakeid'
    });
    const { complete } = await service.logout({ headers: { authorization: data.token } });

    expect(complete).toBe(true);
  });

  it('logoutUserById', async () => {
    await service.login({
      name: 'Cristian',
      email: '<EMAIL>',
      id: 'fakeid'
    });
    const { complete } = await service.logoutUserById('fakeid');

    expect(complete).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

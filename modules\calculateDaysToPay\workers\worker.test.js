/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let sharedHelper;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    service = {
      calculateDaysToPay: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    sharedHelper = { getCurrentYearAndMonth: jest.fn(() => ['2020', '02']) };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, sharedHelper, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(service.calculateDaysToPay).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already executed in the current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, sharedHelper, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateDaysToPay).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('dependency mark not previously executed', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, service, sharedHelper, logService, done });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(service.calculateDaysToPay).toHaveBeenCalledTimes(0);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('fail worker', async () => {
    service.calculateDaysToPay = jest.fn(() => Promise.resolve({ error: true }));
    await workerModule.workerFn({ Logger, service, sharedHelper, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(sharedHelper.getCurrentYearAndMonth).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(service.calculateDaysToPay).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

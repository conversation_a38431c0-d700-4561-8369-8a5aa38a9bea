/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const DiscountsAndAssetsModel = require('../../../models/discountsAndAssets');

const pensionService = require('../../pensions/services/pension.service');
const service = require('./dbService');

const discountsAndAssetsData = require('../../../resources/calculateAssetsBonusDiscounts/discountsAndAssets.json');
const pensionsData = require('../../../resources/calculateAssetsBonusDiscounts/pensions.json');

describe('test service', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should update a pension and its discounts and assets data associated', async done => {
    pensionsData[0].createdAt = new Date();
    await PensionModel.create(pensionsData[0]);
    const discountsAndAssetsDataOne = {
      ...discountsAndAssetsData[0],
      pensionCodeId: pensionsData[0].pensionCodeId
    };
    await DiscountsAndAssetsModel.create(discountsAndAssetsDataOne);

    await service.calculateAssetsBonusDiscounts(pensionService);

    const pension = await PensionModel.findOne({ enabled: true }).lean();

    await DiscountsAndAssetsModel.findById(pension.discountsAndAssets).lean();

    const {
      forBasePension,
      forArticle40,
      forArticle41,
      forBonuses,
      assetsNonFormulableTaxableTotalsByReason,
      assetsNonFormulableNetTotalsByReason,
      discountsNonFormulableTotalsByReason
    } = pension.reservedAmounts;

    expect(forBasePension).toBe(100000);
    expect(forArticle40).toBe(12000);
    expect(forArticle41).toBe(14000);
    expect(forBonuses).toBe(6000.66);
    expect(assetsNonFormulableTaxableTotalsByReason.length).toBe(1);
    expect(assetsNonFormulableNetTotalsByReason.length).toBe(2);
    expect(discountsNonFormulableTotalsByReason.length).toBe(2);
    done();
  });

  it('should update just the pension data if there are no discounts and assets associated', async done => {
    pensionsData[1].createdAt = new Date();
    await PensionModel.create(pensionsData[1]);

    const { completed, error } = await service.calculateAssetsBonusDiscounts(pensionService);

    const pension = await PensionModel.findOne({ enabled: true }).lean();

    const discountsAndAssetsList = await DiscountsAndAssetsModel.find({}).lean();

    const {
      forBasePension,
      forArticle40,
      forArticle41,
      forBonuses,
      discountsAndAssets
    } = pension.reservedAmounts;

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(forBasePension).toBe(10000.05);
    expect(forArticle40).toBe(0);
    expect(forArticle41).toBe(0);
    expect(forBonuses).toBe(0);
    expect(discountsAndAssets).toBeUndefined();
    expect(discountsAndAssetsList.length).toBe(0);
    done();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await DiscountsAndAssetsModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('unified worker total assets and discounts with net pension liquidations report', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let logService;
  let done;

  let netPensionsLiquidationReports;

  beforeEach(() => {
    done = jest.fn();

    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve()),
      allMarksExists: jest.fn(() => Promise.resolve(true))
    };

    netPensionsLiquidationReports = jest.fn(() => Promise.resolve({ alreadyExecuted: true }));
  });

  it('cron should be succesfully completed', async () => {
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(1);
  });

  it(' should fail if deps marks not exists', async () => {
    logService.allMarksExists = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(logService.allMarksExists).toHaveBeenCalledTimes(0);
    // expect(logService.saveLog).not.toHaveBeenCalled();
  });

  it(' should fail if child cron fails', async () => {
    netPensionsLiquidationReports = jest.fn(() => Promise.resolve({}));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.allMarksExists).toHaveBeenCalledTimes(0);
    expect(logService.saveLog).not.toHaveBeenCalled();
  });

  it(' cron was already executed ', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should not find the unified cron mark', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('he processes is not finished correctly', async () => {
    netPensionsLiquidationReports = jest.fn(() => {
      throw new Error('Errors while processing');
    });
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('should fail file upload', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject(new Error()));
    await workerModule.workerFn({
      Logger,
      logService,
      done,
      netPensionsLiquidationReports
    });

    expect(Logger.info).toHaveBeenCalledTimes(1);
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

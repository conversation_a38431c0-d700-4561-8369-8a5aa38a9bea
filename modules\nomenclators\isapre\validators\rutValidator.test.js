/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const {
  calculateCheckDigit,
  isValidRut,
  codeRule,
  codeFormatter,
  rutFormatter
} = require('./rutValidator');

describe('RUT validator tests', () => {
  beforeAll(beforeAllTests);

  it('should calculate the Check Digit when RUT body is given', () => {
    const rutBody = '1';
    const checkDigit = calculateCheckDigit(rutBody);
    const expected = '9';
    expect(checkDigit).toEqual(expected);

    const rutBody1 = '6';
    const checkDigit1 = calculateCheckDigit(rutBody1);
    const expected1 = 'K';
    expect(checkDigit1).toEqual(expected1);

    const rutBody2 = '14';
    const checkDigit2 = calculateCheckDigit(rutBody2);
    const expected2 = '0';
    expect(checkDigit2).toEqual(expected2);

    const rutBody3 = '18123634';
    const checkDigit3 = calculateCheckDigit(rutBody3);
    const expected3 = '5';
    expect(checkDigit3).toEqual(expected3);

    expect(calculateCheckDigit(10864629)).toEqual('2');
    expect(calculateCheckDigit(11726111)).toEqual('5');
    expect(calculateCheckDigit(13067971)).toEqual('4');
    expect(calculateCheckDigit(15223952)).toEqual('1');
    expect(calculateCheckDigit(15496120)).toEqual('8');
    expect(calculateCheckDigit(16003145)).toEqual('K');
    expect(calculateCheckDigit(16158088)).toEqual('0');
    expect(calculateCheckDigit(16931829)).toEqual('8');
    expect(calculateCheckDigit(17577561)).toEqual('7');
    expect(calculateCheckDigit(19791795)).toEqual('4');
    expect(calculateCheckDigit(20181773)).toEqual('0');
    expect(calculateCheckDigit(20309424)).toEqual('8');
    expect(calculateCheckDigit(21705755)).toEqual('8');
    expect(calculateCheckDigit(23023518)).toEqual('K');
    expect(calculateCheckDigit(23559651)).toEqual('2');
    expect(calculateCheckDigit(24261604)).toEqual('9');
    expect(calculateCheckDigit(24901269)).toEqual('6');
    expect(calculateCheckDigit(6709127)).toEqual('2');
    expect(calculateCheckDigit(8139919)).toEqual('0');
    expect(calculateCheckDigit(8702020)).toEqual('7');
  });

  it('should return true when a valid RUT is given, false otherwise', () => {
    const validRut = '18.123.634-5';
    const isValid = isValidRut(validRut);
    expect(isValid).toBe(true);

    const invalidRut = '18.123.634-6';
    const isValid1 = isValidRut(invalidRut);
    expect(isValid1).toBe(false);
  });

  it('should return false when RUT body is shorter than 3 characters', () => {
    const invalidRut = '19';
    const isValid = isValidRut(invalidRut);
    expect(isValid).toBe(false);

    const rutWithoutHyphen = '1.9';
    const isValid1 = isValidRut(rutWithoutHyphen);
    expect(isValid1).toBe(false);

    const shortRut = '1-9';
    const isValid2 = isValidRut(shortRut);
    expect(isValid2).toBe(false);
  });

  it('should return false when there is no hyphen in the given RUT', () => {
    const invalidRut = '18.123.6345';
    const isValid = isValidRut(invalidRut);
    expect(isValid).toBe(false);

    const validRut = '18.123.634-5';
    const isValid1 = isValidRut(validRut);
    expect(isValid1).toBe(true);
  });

  it('should format a 2 digit code', () => {
    const ruled1 = '10';
    const ruled2 = 'XX';
    const ruled3 = '0a0aaaa';
    const ruled4 = 'anything$%&00';
    expect(codeFormatter(ruled1)).toBe('10');
    expect(codeFormatter(ruled2)).toBe('');
    expect(codeFormatter(ruled3)).toBe('00');
    expect(codeFormatter(ruled4)).toBe('00');
  });
  it('should validate a 2 digit code', () => {
    const ruled1 = '10';
    const ruled2 = '000000';
    const ruled3 = 10;
    const ruled4 = 'anything$%&';
    expect(codeRule.test(ruled1)).toBe(true);
    expect(codeRule.test(ruled2)).toBe(true);
    expect(codeRule.test(ruled3)).toBe(true);
    expect(codeRule.test(ruled4)).toBe(false);
  });

  it('should format ruts', () => {
    const rut1 = '11.111.111-k';
    const rut2 = '11.11K.111-K';
    expect(rutFormatter(rut1)).toBe('11111111-K');
    expect(rutFormatter(rut2)).toBe('1111111-K');
  });

  afterAll(afterAllTests);
});

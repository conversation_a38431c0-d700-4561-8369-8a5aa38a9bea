trigger:
  - master

pool:
  vmImage: ubuntu-latest


steps:
- task: DockerInstaller@0
  displayName: 'Install Docker 17.09.0-ce'


- bash: |
   chmod -R 777  ./
   docker run --rm -v $(pwd):/zap/wrk/:rw -t owasp/zap2docker-stable zap-full-scan.py -t 'https://sadesapecstaticweb.z13.web.core.windows.net/' -g gen.conf -x OWASP-ZAP-Report.xml -r scan-report.html
   true
  displayName: 'Security Test Run'

- powershell: |
   $XslPath = "$($Env:SYSTEM_DEFAULTWORKINGDIRECTORY)/SecurityTest/OWASPToNUnit3.xslt"
   $XmlInputPath = "$($Env:SYSTEM_DEFAULTWORKINGDIRECTORY)/OWASP-ZAP-Report.xml"
   $XmlOutputPath = "$($Env:SYSTEM_DEFAULTWORKINGDIRECTORY)/Converted-OWASP-ZAP-Report.xml"
   $XslTransform = New-Object System.Xml.Xsl.XslCompiledTransform
   $XslTransform.Load($XslPath)
   $XslTransform.Transform($XmlInputPath, $XmlOutputPath)
  displayName: 'Convert ZAP Report to NUnit Report Format'


- task: UniversalPackages@0
  displayName: 'Universal Publish'
  inputs:
    command: publish
    publishDirectory: '$(System.DefaultWorkingDirectory)'
    vstsFeedPublish: "5438d7c5-1598-471f-aaf8-7e01727ed338/0f833596-65f1-4d52-95f6-1d2988a6afd0"
    vstsFeedPackagePublish: securitytestingresults

- task: PublishTestResults@2
  displayName: 'Publish Test Results'
  inputs:
    testResultsFormat: NUnit
    testResultsFiles: 'Converted-OWASP-ZAP-Report.xml'
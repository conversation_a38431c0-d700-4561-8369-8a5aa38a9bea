const validator = require('validator');

const { check } = require('express-validator');

const { roundValue } = require('../../../sharedFiles/helpers');

const MIN_NUMBER_ALLOWED = 1;
const MAX_NUMBER_ALLOWED = 1000000;

const PENSION_TYPE = 'pension';

const isValidInt = val =>
  val === undefined ||
  validator.isInt(`${val}`, {
    min: MIN_NUMBER_ALLOWED,
    max: MAX_NUMBER_ALLOWED,
    allow_leading_zeroes: false
  });

const isValidDecimal = val => {
  if (val === undefined) return true;

  const stringValue = `${val}`;
  if (!validator.isDecimal(stringValue, { decimal_digits: '0,2' })) return false;
  const fixedValue = roundValue(stringValue);
  if (fixedValue < MIN_NUMBER_ALLOWED || fixedValue > MAX_NUMBER_ALLOWED) return false;

  return true;
};

const applyIntValidation = values => values.every(val => isValidInt(val));
const applyDecimalValidation = values => values.every(val => isValidDecimal(val));

const validators = [
  check('data')
    .notEmpty()
    .withMessage('body should not be empty!')
    .isArray()
    .withMessage('body should not be empty!')
    .custom(values => {
      return values.every(({ type, value }) => {
        if (type === PENSION_TYPE) {
          const { minimun, law19403, law19539, law19953 } = value;
          return applyDecimalValidation([minimun, law19403, law19539, law19953]);
        }
        return applyIntValidation([value]);
      });
    })
    .withMessage('invalid values')
];

module.exports = validators;

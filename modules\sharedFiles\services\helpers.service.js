const moment = require('moment');

const getCurrentYearAndMonth = () => [moment().year(), moment().month()];
const getMonth = month => {
  const months = {
    0: '<PERSON><PERSON>',
    1: 'Febrer<PERSON>',
    2: '<PERSON><PERSON>',
    3: '<PERSON><PERSON><PERSON>',
    4: '<PERSON>',
    5: '<PERSON><PERSON>',
    6: '<PERSON>',
    7: '<PERSON><PERSON><PERSON>',
    8: 'Septiembre',
    9: 'Octubre',
    10: '<PERSON><PERSON><PERSON>',
    11: 'Diciembre'
  };
  return months[month];
};

module.exports = {
  getCurrentYearAndMonth,
  getMonth
};

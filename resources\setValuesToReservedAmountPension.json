[{"_doc": {"_id": "5eb5e47b3a70c47628f89b24", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": ""}, "beneficiary": {"rut": "8081440-2", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "numberOfCharges": 0, "institutionalPatient": false, "reservedAmountInstituionalPatient": 0, "liquidation": {"taxablePension": 19.87}, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosAndes": 0, "healthLoan": 0, "nonFormulable": 0}, "enabled": true, "basePension": 266544, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1996-06-26T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON> v<PERSON> ", "pensionType": "Pensión de viudez sin hijos ", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2019-12-31T03:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "2019-01-03T03:00:00.000Z", "article40": 234, "createdAt": "2020-05-08T23:00:11.639Z", "updatedAt": "2020-05-08T23:00:11.639Z", "endDateOfTheoricalValidity": "2020-12-31T03:00:00.000Z", "endDateOfValidity": "2020-12-31T03:00:00.000Z", "linkedDate": "2020-05-08T22:47:26.782Z"}}, {"_doc": {"_id": "5eb5e47b3a70c47628f89b25", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "5243050-K", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": ""}, "beneficiary": {"rut": "********-k", "name": "<PERSON>", "lastName": "Nu@ez", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forSurvival": 1, "forDisability": 1, "forInstitutionalPatient": 1, "forRejection": 1}, "assets": {"aps": 1, "healthDiscount": "SI", "healthExemption": "SI", "forFamilyAssignment": 1, "nonFormulable": 1, "christmasBonus": 1, "nationalHolidaysBonus": 1, "winterBonus": 1}, "retroactiveAmounts": {"forSurvival": 1, "forDisability": 1, "forInstitutionalPatient": 1, "forRejection": 1}, "numberOfCharges": 0, "institutionalPatient": false, "reservedAmountInstituionalPatient": 0, "liquidation": {"taxablePension": 19.005}, "discounts": {"onePercentLaAraucana": "SI", "socialCreditsLaAraucana": 0, "onePercent18": "si", "socialCredits18": 1, "onePercentLosAndes": "Si", "socialCreditsLosAndes": 1, "onePercentLosHeroes": "Si", "socialCreditsLosHeroes": 1, "othersLosAndes": 1, "healthLoan": 1, "nonFormulable": 1}, "enabled": true, "basePension": 266544, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1996-06-26T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON> v<PERSON> ", "pensionType": "Pensión de viudez sin hijos ", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2019-10-15T03:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "2019-01-03T03:00:00.000Z", "article40": 234, "createdAt": "2020-05-08T23:00:11.644Z", "updatedAt": "2020-05-08T23:00:11.644Z", "endDateOfTheoricalValidity": "2020-10-15T03:00:00.000Z", "endDateOfValidity": "2020-10-15T03:00:00.000Z", "linkedDate": "2020-05-08T22:47:26.783Z"}}]
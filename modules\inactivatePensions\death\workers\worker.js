/* eslint-disable no-console */
const cronDescription = ' inactive pensions by death';
const cronMark = 'INACTIVATE_PENSION_DEATH';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const emptyFile = 'El archivo recibido no contiene datos';
const dependencyMark = 'MODIFY_CIVIL_REGISTRY_DATA';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const workerFn = async ({
  Logger,
  done,
  service,
  sftpClient,
  sftpCredentials,
  connectToSFTPServer,
  logService,
  pensionService,
  filesHelper,
  tmp,
  job
}) => {
  try {
    Logger.info(`${cronMark}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark} process started`);
    const { completed, error } = await pensionService.updateDisableByDeath();
    if (!completed) {
      throw new Error(error);
    }

    Logger.info(`Checking SFTP server. Verifying second Civil Registration file`);
    const { lines, error: ftpError } = await filesHelper.readSFTPFile({
      sftpClient,
      connectToSFTPServer,
      sftpCredentials,
      filesHelper,
      tmp,
      dataType: 'death'
    });
    if (ftpError) throw new Error(ftpError);

    if (!lines.length) {
      Logger.info(emptyFile);
      throw new Error(emptyFile);
    }

    Logger.info(`File received, processing second Civil Registration file`);
    const { error: serviceError } = await service.createUpdateDeathPension(lines, pensionService);

    if (serviceError) throw new Error(serviceError);

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

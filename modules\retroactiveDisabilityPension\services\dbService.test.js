/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const ProcessedJobModel = require('../../sharedFiles/models/processedJob');
const PensionModel = require('../../../models/pension');

const service = require('./dbService');
const retroactivePension = require('../../../resources/retroactivePension.json');
const retroactiveHistoricPension = require('../../../resources/retroactivePensionHistoric.json');

describe('Pension serviceasda Model Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: [] })),
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('add only the first two amounts without validity', async () => {
    const a = await service.addretroactiveAmountsByDisability([
      { validityType: 'No vigente', reservedAmounts: { forDisability: 100 } },
      { validityType: 'No vigente', reservedAmounts: { forDisability: 100 } },
      { validityType: 'No ', reservedAmounts: { forDisability: 100 } },
      { validityType: 'No vigente', reservedAmounts: { forDisability: 100 } }
    ]);
    expect(a.sumValue).toBe(200);
  });

  it('retroactive Disability Pension', async () => {
    await PensionModel.create(retroactiveHistoricPension);
    pensionService.getAllAndFilter = jest.fn(() =>
      Promise.resolve({ result: [retroactivePension] })
    );
    const { completed, error } = await service.retroactiveDisabilityPension(pensionService);

    expect(completed).toBe(true);
    expect(error).toBe(null);
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
    await PensionModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

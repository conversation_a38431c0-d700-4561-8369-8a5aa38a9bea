const cronDescription = 'file upload previred';
const cronMark = 'GENERATE_AND_UPLOAD_PREVIRED_FILE_FREQUENCY';
const dependencyMark = 'GENERATE_AND_UPLOAD_BANK_FILE';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const getMissingDependencyMessage = dependency => `No se ha ejecutado la dependencia ${dependency}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  done,
  logService,
  generatorArgs,
  storageService,
  previredService,
  job,
  moment,
  emailService
}) => {
  try {
    Logger.info(`Cron ${cronMark}. Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`Execution start ${cronMark}. Generating previred file...`);

    const { filePath, fileName } = await previredService.generatePreviredFile(
      ...generatorArgs,
      Logger
    );

    const DATE_FOR_PATH = moment().format('YYYY/MM');

    Logger.info(`TEMPORARY FILE PATH: ${filePath}`);
    Logger.info(`File Generation completed. Uploading file to storage...`);
    const { status, data, fileSendName } = await storageService.uploadFileFromLocal(
      filePath,
      fileName
    );
    if (status !== 200) throw new Error(data);
    const virtualPath = `${DATE_FOR_PATH}/Previred/${fileSendName}`;
    const { error } = await storageService.saveFileRegistry(virtualPath, data);
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);

    const { error: sendNotificationEmailError } = await emailService.sendNotificationEmail();
    if (sendNotificationEmailError) throw new Error(sendNotificationEmailError);
    Logger.info(successMessage);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};
module.exports = { cronMark, dependencyMark, workerFn };

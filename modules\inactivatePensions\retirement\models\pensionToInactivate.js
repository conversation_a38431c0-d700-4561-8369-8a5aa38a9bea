const mongoose = require('mongoose');
const paginate = require('../../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const ToInactivateByRetirement = new Schema(
  {
    inactivationReason: { type: String, required: true },
    beneficiaryRut: { type: String, required: true },
    causantRut: { type: String, required: true },
    dateToInactivate: { type: Date, required: true }
  },
  { timestamps: true }
);

ToInactivateByRetirement.plugin(paginate);
ToInactivateByRetirement.index({ idCode: 1 });

module.exports = mongoose.model('ToInactivateByRetirement', ToInactivateByRetirement);

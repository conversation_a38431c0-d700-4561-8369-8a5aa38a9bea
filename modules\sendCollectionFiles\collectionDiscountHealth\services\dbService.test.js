/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const service = require('./dbService');

describe('Send collection discount health  service test', () => {
  beforeAll(beforeAllTests);

  const Sftp = {
    Client: jest.fn().mockImplementation(() => {
      return {
        uploadFrom: jest.fn(),
        close: jest.fn()
      };
    }),
    connectToSFTPServer: jest.fn().mockResolvedValue({
      connected: true,
      error: null
    })
  };

  const Model = {
    aggregate: jest
      .fn()
      .mockReturnValue({ cursor: jest.fn().mockReturnValue([{ matchedPensions: { result: [] } }]) })
  };

  const fileGenerationUtils = {
    getLine: jest.fn().mockReturnValue(''),
    getFileName: jest.fn().mockReturnValue('test-file.txt'),
    getZipFileName: jest.fn().mockReturnValue(''),
    compressFile: jest.fn().mockReturnValue('test-file.zip'),
    writeFileSync: jest.fn(),
    util: {
      promisify: jest.fn().mockReturnValue(
        jest.fn(() => ({
          catch: jest.fn()
        }))
      )
    }
  };

  it('should generate the file and return the file name', async () => {
    const fileName = await service.generateFile(fileGenerationUtils, Model);
    expect(Model.aggregate).toHaveBeenCalled();
    expect(fileGenerationUtils.getFileName).toHaveBeenCalled();
    expect(fileGenerationUtils.util.promisify).toHaveBeenCalled();
    expect(fileName).toBe('test-file.txt');
  });

  it('should upload file to SFTP server and return the file name', async () => {
    const uploadedFile = await service.uploadFileToSftpServer({
      fileName: 'test-file.txt',
      fileGenerationUtils,
      Sftp
    });
    expect(Sftp.Client).toHaveBeenCalled();
    expect(fileGenerationUtils.compressFile).toHaveBeenCalled();
    expect(fileGenerationUtils.getZipFileName).toHaveBeenCalled();
    expect(Sftp.connectToSFTPServer).toHaveBeenCalled();
    expect(uploadedFile).toBe('test-file.zip');
  });

  afterAll(afterAllTests);
});

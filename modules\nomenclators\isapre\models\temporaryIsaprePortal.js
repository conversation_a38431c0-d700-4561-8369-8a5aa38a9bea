const mongoose = require('mongoose');

const { Schema } = mongoose;

const TemporaryIsaprePortalSchema = new Schema(
  {
    affiliateRut: { type: String, required: true },
    totalDiscount: { type: Number, required: true },
    isapreId: { type: String, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

TemporaryIsaprePortalSchema.index({ affiliateRut: 1 }, { unique: true });

module.exports = mongoose.model('temporaryIsaprePortal', TemporaryIsaprePortalSchema);

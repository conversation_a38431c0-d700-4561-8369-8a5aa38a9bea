/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./setReservedAmount');
const reservedAmountPensions = require('../../../resources/setReservedAmounAssetDiscount.json');
const PensionModel = require('../../../models/pension');
const { roundValue } = require('../../sharedFiles/helpers');

describe('set reserved amount for discounts-assets pension Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  const WITHOUT_FIELDS = 0;
  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: [reservedAmountPensions[1]] })),
      createUpdatePension: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });
  it('should assign reserved amounts based on assets and discounts ', async () => {
    const pension = {
      ...reservedAmountPensions[0],
      reservedAmounts: {
        forNetTotalNonFormulableAssets: 10,
        forTotalNonFormulableDiscounts: 10,
        forTaxableTotalNonFormulableAssets: 10
      },
      assets: {
        netTotalNonFormulable: 100.034,
        taxableTotalNonFormulable: 100.045
      },
      discounts: { totalNonFormulable: 120.046, healthUF: 0 },
      validityType: 'No Vigente'
    };

    const result = service.setReservedAmounts(pension);
    expect(result.reservedAmounts.forNetTotalNonFormulableAssets).toBe(
      roundValue(pension.assets.netTotalNonFormulable)
    );
    expect(result.reservedAmounts.forTaxableTotalNonFormulableAssets).toBe(
      roundValue(pension.assets.taxableTotalNonFormulable)
    );
    expect(result.reservedAmounts.forTotalNonFormulableDiscounts).toBe(
      roundValue(pension.discounts.totalNonFormulable)
    );
  });

  it('calculate reserved amount for assets and discount pension without fields', async () => {
    const pension = {
      ...reservedAmountPensions[0]
    };
    const insertedPension = await PensionModel.create(pension._doc);
    const result = service.setReservedAmounts(insertedPension._doc);
    expect(result.reservedAmounts.forNetTotalNonFormulableAssets).toBe(WITHOUT_FIELDS);
    expect(result.reservedAmounts.forTaxableTotalNonFormulableAssets).toBe(WITHOUT_FIELDS);
    expect(result.reservedAmounts.forTotalNonFormulableDiscounts).toBe(WITHOUT_FIELDS);
  });

  it('reserved amount assets & discounts pensions success', async () => {
    const { completed, error } = await service.calculateReservedAmount(pensionService);
    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(pensionService.getAllAndFilter).toHaveBeenCalledTimes(1);
    expect(pensionService.createUpdatePension).toHaveBeenCalledTimes(1);
  });

  it('reserved amount assets & discounts pensions fails', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.reject(new Error()));
    const { completed, error } = await service.calculateReservedAmount(pensionService);
    expect(completed).toBe(false);
    expect(error).toStrictEqual(new Error());
  });

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

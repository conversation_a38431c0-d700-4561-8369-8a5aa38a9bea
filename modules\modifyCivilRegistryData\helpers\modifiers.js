const { setRetirement } = require('../../linkPensions/services/modifiers/retirement');
const { setWidowhood } = require('../../linkPensions/services/modifiers/widowhood');
const setEndDateByOrphanhood = require('../../linkPensions/services/modifiers/orphanhood');
const { setEndDateForLifePension } = require('../../linkPensions/services/modifiers/life');

const calculateEndDateOfValidity = async pension => {
  const retirement = setRetirement(pension, pension.pensionType, pension.validityType);
  const widowhood = await setWidowhood(retirement, pension.pensionType, pension.validityType);
  const orphanhood = setEndDateByOrphanhood(widowhood, pension.pensionType, pension.validityType);
  const life = setEndDateForLifePension(orphanhood, pension.validityType);

  return { ...retirement, ...widowhood, ...orphanhood, ...life };
};

module.exports = { calculateEndDateOfValidity };

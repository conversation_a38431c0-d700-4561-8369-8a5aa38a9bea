const tmp = require('tmp');
const moment = require('moment');
const util = require('util');
const { appendFile } = require('fs');

const { compressFile } = require('../../../sharedFiles/helpers');
const { getSplitedDate } = require('../../../inactivatePensions/sharedFiles/filesHelper');
const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');
const {
  getPensionerRUT,
  getPensionerLastName,
  getPensionerMothersLastName,
  getPensionerName
} = require('../../helpers');

const getPeriod = () => {
  const [year, month] = getSplitedDate();
  return `${year}${month}`;
};

const getFileName = (tempLib = tmp) =>
  `${tempLib.dirSync().name}/esalud${moment().format('YYYYMM')}.703601006.txt`;

const getZipFileName = () =>
  `esalud.703601006.ips_${moment().format('YYYYMM')}_${moment().format('YYYYMMDD_HHmm')}.zip`;

const isCurrenPension = pension => {
  const [year, month] = getSplitedDate();

  const { createdAt } = pension;
  const pensionMonth = moment(createdAt).month() + 1;
  const pensionYear = moment(createdAt).year();
  const isActual = +year === pensionYear && +month === pensionMonth;

  return isActual;
};

const getRightToBenefitPeriod = pension => {
  const [year, month] = getSplitedDate();
  const { createdAt } = pension;
  const [pensionYear, pensionMonth] = moment(createdAt)
    .format('YYYY-MM')
    .split('-');
  const isActual = isCurrenPension(pension);

  if (isActual) return `${year}${month}01`;

  return `${pensionYear}${pensionMonth}01`;
};

const getEntityRut = () => {
  const RUT_ACHS = '70360100';
  const DV_ACHS = '6';

  return RUT_ACHS + DV_ACHS;
};

const getBenefitType = pension => {
  const RUT_MAX_LENGTH = 2;
  const { apsInfo } = pension;
  const { apsOrigin } = apsInfo;

  return String(apsOrigin).padStart(RUT_MAX_LENGTH, '0');
};

const getPensionAmount = pension => {
  const AMOUNT_MAX_LENGTH = 8;
  const fields = ['basePension', 'article40', 'law19403', 'law19539', 'law19953', 'assets.aps'];
  const amount = recursiveSum(pension, fields);
  return String(Math.round(roundValue(amount))).padStart(AMOUNT_MAX_LENGTH, '0');
};

const getHealthBonusAmount = pension => {
  const AMOUNT_MAX_LENGTH = 8;
  const { rejectionHealthExemptionAmount = 0 } = pension;
  const isActual = isCurrenPension(pension);

  const bonusAmount = Math.round(roundValue(0.07 * getPensionAmount(pension)));

  if (isActual) return String(bonusAmount).padStart(AMOUNT_MAX_LENGTH, '0');

  return String(rejectionHealthExemptionAmount).padStart(AMOUNT_MAX_LENGTH, '0');
};

const getLine = pension => {
  const lineData = [getPeriod(), getRightToBenefitPeriod(pension), getEntityRut()];

  [
    getPensionerRUT,
    getPensionerLastName,
    getPensionerMothersLastName,
    getPensionerName,
    getBenefitType,
    getHealthBonusAmount,
    getPensionAmount
  ].forEach(fn => lineData.push(fn(pension)));

  return `${lineData.join('')}\n`;
};

module.exports = { getLine, getFileName, getZipFileName, compressFile, appendFile, util };

const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');

const FactoryController = require('../modules/monthlyExpenses/controllers/monthlyExpenses.controller');
const service = require('../modules/monthlyExpenses/services/dbService');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  const controller = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    service
  });

  router.get('/stats', validateAccess(), controller.getStats);
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./ftp.service');
const data = require('../../../resources/pensions.json');

describe('FTP Service', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let client;
  beforeEach(() => {
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
    client = {
      access: jest.fn(() => Promise.resolve()),
      uploadFrom: jest.fn(),
      close: jest.fn(),
      ftp: { verbose: false }
    };
  });

  it('success ftp access', async () => {
    const { connected } = await service.connectToFTPServer(client);
    expect(connected).toBe(true);
  });

  it('fail ftp access', async () => {
    client = {
      access: jest.fn(() => Promise.reject(new Error('error access')))
    };
    const { connected } = await service.connectToFTPServer(client);
    expect(connected).toBe(false);
  });

  it('success upload to ftp', async () => {
    const fileName = `dummy_file.txt`;
    service.getClientFTP = jest.fn().mockReturnValue({
      access: jest.fn(() => Promise.resolve()),
      uploadFrom: jest.fn(),
      close: jest.fn(),
      ftp: { verbose: false }
    });
    service.connectToFTPServer = jest.fn().mockReturnValue({
      connected: true
    });
    const { completed, error } = await service.uploadFileToFTP({
      fileName,
      data,
      Logger,
      ftpCredentials: {}
    });
    expect(completed).toBe(true);
    expect(error).toBeUndefined();
  });

  it('fail upload to ftp', async () => {
    service.getClientFTP = jest.fn().mockReturnValue({
      access: jest.fn(() => Promise.resolve()),
      uploadFrom: jest.fn(),
      close: jest.fn(),
      ftp: { verbose: false }
    });
    service.connectToFTPServer = jest.fn().mockReturnValue({
      connected: false,
      error: 'Server unreachable'
    });
    const fileName = `dummy_file.txt`;
    const { completed, error } = await service.uploadFileToFTP({
      fileName,
      data,
      Logger,
      ftpCredentials: {}
    });
    expect(completed).toBe(false);
    expect(error).toBeDefined();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  afterAll(afterAllTests);
});

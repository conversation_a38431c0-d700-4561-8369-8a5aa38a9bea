const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const assetsNonFormulableSchema = new Schema({
  label: { type: String, default: 'Haber' },
  key: { type: String, default: 'noformulable' },
  amount: { type: Number, default: 0 },
  reason: { type: String, default: '', lowercase: true },
  assetType: { type: String, enum: ['imponible', 'líquido'], default: 'líquido' },
  startDate: { type: String },
  endDate: { type: String },
  creationDate: { type: Date, default: new Date() }
});

const discountsNonFormulableSchema = new Schema({
  label: { type: String, default: 'Descuento' },
  key: { type: String, default: 'noformulable' },
  amount: { type: Number, default: 0 },
  reason: { type: String, default: '', lowercase: true },
  startDate: { type: String },
  endDate: { type: String },
  creationDate: { type: Date, default: new Date() },
  judicialRetention: {
    paymentInfo: {
      paymentGateway: { type: String },
      accountNumber: { type: String },
      bank: { type: String },
      branchOffice: { type: String }
    },
    collector: {
      rut: { type: String },
      name: { type: String },
      lastName: { type: String },
      mothersLastName: { type: String },
      address: { type: String },
      commune: { type: String },
      city: { type: String }
    },
    retention: {
      validity: { type: Boolean },
      type: { type: String },
      amount: { type: Number }
    }
  }
});

const DiscountsAndAssetSchema = new Schema(
  {
    beneficiaryRut: { type: String, required: true },
    causantRut: { type: String, required: true },
    pensionCodeId: { type: String, required: true },
    assetsNonFormulable: [assetsNonFormulableSchema],
    discountsNonFormulable: [discountsNonFormulableSchema]
  },
  { timestamps: true }
);
DiscountsAndAssetSchema.plugin(paginate);
DiscountsAndAssetSchema.index(
  { beneficiaryRut: 1, causantRut: 1, pensionCodeId: 1 },
  { unique: true }
);

module.exports = mongoose.model('DiscountsAndAsset', DiscountsAndAssetSchema);

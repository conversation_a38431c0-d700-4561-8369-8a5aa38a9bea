/* eslint-disable no-unused-expressions */
const moment = require('moment');
const Model = require('../../../../models/pension');
const InactivateMarriageModel = require('../models/inactivateMarriageModel');
const PensionHistoricModel = require('../../../../models/pensionHistoric');

const notValid = /No vigente/i;

const WIDOWS_PENSIONS_TYPE = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i
];
const isWidow = pensionType => WIDOWS_PENSIONS_TYPE.some(regex => regex.test(pensionType));

const diffDate = (start, end) => moment(start).diff(end) > 0;

const buildCurrentMonthAndYearQuery = (dateField, additionalCriteria) => {
  const date = moment();
  return {
    ...additionalCriteria,
    $expr: {
      $and: [
        { $eq: [{ $month: `$${dateField}` }, date.month() + 1] },
        { $eq: [{ $year: `$${dateField}` }, date.year()] }
      ]
    }
  };
};

const preService = {
  async getAllMarriagePensionToInactivate(query) {
    return InactivateMarriageModel.find(query)
      .lean()
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async findPensionSoonToInactivate(ruts = []) {
    try {
      const pensions = await Model.find({
        'beneficiary.rut': { $in: ruts },
        enabled: true,
        validityType: { $nin: [notValid] },
        manuallyReactivated: false,
        inactivateManually: false
      })
        .select('_id beneficiary.rut pensionType pensionStartDate causant.rut pensionCodeId')
        .exec();
      return { hasError: false, error: null, pensions };
    } catch (error) {
      return { hasError: true, error, pensions: [] };
    }
  },
  async saveMarriagePensionsToInactivate({ lines }) {
    const session = await InactivateMarriageModel.startSession();
    session.startTransaction();
    try {
      const bulk = InactivateMarriageModel.collection.initializeOrderedBulkOp();
      const ruts = lines.map(([rut]) => new RegExp(rut, 'i'));
      const { pensions } = await this.findPensionSoonToInactivate(ruts);
      const pensionsToEvaluate = [];

      const promiseFunctions = lines.map(line => async () => {
        const [rut, marriageDate] = line;
        if (rut && marriageDate) {
          pensions
            .filter(pension => pension.beneficiary.rut === rut)
            .forEach(({ _doc }) => {
              const { pensionType, pensionStartDate } = _doc;
              if (isWidow(pensionType) && diffDate(marriageDate, pensionStartDate)) {
                const { _id, ...data } = _doc;
                const dateToInactivate = moment()
                  .add(1, 'months')
                  .startOf('month');

                const endDateOfValidity = moment(marriageDate)
                  .subtract(1, 'days')
                  .toDate();

                pensionsToEvaluate.push({
                  ...data,
                  endDateOfValidity,
                  evaluationDate: new Date(),
                  inactivationReason: 'Matrimonio'
                });

                bulk.insert({
                  beneficiaryRut: data.beneficiary.rut,
                  causantRut: data.causant.rut,
                  inactivationReason: 'Matrimonio',
                  evaluationDate: new Date(),
                  endDateOfValidity,
                  dateToInactivate: dateToInactivate.toDate()
                });
              }
            });
        }
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }

      if (bulk.length) {
        await bulk.execute();
      }

      await session.commitTransaction();
      return { pensionsToEvaluate };
    } catch (e) {
      await session.abortTransaction();
      return { inactivationError: e };
    }
  },
  async createUpdateMarriagePension() {
    const query = buildCurrentMonthAndYearQuery('dateToInactivate');

    try {
      const markedPensions = await InactivateMarriageModel.find(query);
      if (!markedPensions.length) return { completed: true };
      const asyncFunctions = markedPensions.map(document => async () => {
        const {
          beneficiaryRut,
          causantRut,
          _id: markID,
          inactivationReason,
          endDateOfValidity
        } = document;

        const currentPension = await Model.findOne({
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          deathDate: { $exists: false },
          enabled: true,
          inactivateManually: false,
          manuallyReactivated: false
        }).lean();

        if (currentPension) {
          await Model.updateOne(
            {
              _id: currentPension.id
            },
            {
              $set: {
                enabled: true,
                validityType: notValid,
                inactivationDate: new Date(),
                inactivationReason,
                endDateOfValidity
              }
            }
          );

          const { _id: id, ...data } = currentPension;
          await PensionHistoricModel.create({
            ...data,
            enabled: false
          });
        }
        await InactivateMarriageModel.deleteOne({ _id: markID });
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of asyncFunctions) {
        await fn();
      }

      return { completed: true };
    } catch (error) {
      return { completed: false, inactivationError: error };
    }
  },

  async findOneAndDelete(id) {
    try {
      const result = await InactivateMarriageModel.findByIdAndDelete(id).exec();
      return { isError: false, result };
    } catch (e) {
      return { isError: true, error: e };
    }
  }
};

module.exports = { ...preService };

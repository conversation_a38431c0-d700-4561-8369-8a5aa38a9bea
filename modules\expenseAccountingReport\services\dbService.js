/* eslint-disable no-restricted-syntax */
const PensionModel = require('../../../models/pension');
const aggregation = require('./aggregation');
const reportsConfig = require('./reportsConfig');
const Model = require('../models/ExpenseAccountingReport');

const mapper = reportObj => item => {
  return {
    label: item.label,
    code: item.code,
    value: Math.round(reportObj[item.field] || 0)
  };
};

const service = {
  async generateReports() {
    try {
      const [result = {}] = await PensionModel.aggregate(aggregation).exec();
      const { document = {}, pensPP2102010001 = 0 } = result;
      const reportObject = { ...document, pensPP2102010001 };
      const reportsList = reportsConfig.map(mapper(reportObject));

      await Model.deleteMany({}).exec();
      await Model.insertMany(reportsList);

      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = service;

/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */
const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');
const tmp = require('tmp');
const path = require('path');

const StoreFilesModel = require('../../../models/storedFiles');

const { ALFRESCO_API, ALFRESCO_AF, MIDDLEWARE_SUBSCRIPTION_KEY } = process.env;

const fileHelper = require('../../sharedFiles/helpers');

const service = {
  async findFilesByPrefix(virtualPathPrefix = '') {
    try {
      const regexp = new RegExp(`^${virtualPathPrefix}`, 'i');
      const data = await StoreFilesModel.find({ virtualPath: regexp })
        .sort({ virtualPath: -1 })
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async findFilesByPartialPath(partialVirtualPath = '') {
    try {
      const regexp = new RegExp(`${partialVirtualPath}`, 'i');
      const data = await StoreFilesModel.find({ virtualPath: regexp })
        .sort({ virtualPath: -1 })
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async findFileRegistry(vp) {
    try {
      const data = await StoreFilesModel.findOne({
        $or: [{ virtualPath: `${vp}.zip` }, { virtualPath: `${vp}.pdf` }, { virtualPath: vp }]
      })
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async saveFileRegistry(virtualPath, uuid) {
    try {
      const data = await StoreFilesModel.findOneAndUpdate(
        { virtualPath },
        { $set: { uuid } },
        { upsert: true, new: true }
      ).exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async uploadFileFromLocal(filePath, filename) {
    const api = `${ALFRESCO_API}/GuardarDocumento?af=${ALFRESCO_AF}&np=pec`;
    const formData = new FormData();
    let fileSend = filePath;
    let fileSendName = filename;
    const sizeMB = await fileHelper.getFileSizeMB(filePath);
    if (sizeMB > 5) {
      fileSendName = `${path.parse(filename).name}.zip`;
      fileSend = fileHelper.compressFile(
        filePath,
        tmp.fileSync({ name: fileSendName, postfix: '.zip' }).name
      );
    }

    formData.append('', fs.readFileSync(fileSend), {
      contentType: 'multipart/form-data',
      filename: fileSendName
    });
    const config = {
      headers: {
        'Ocp-Apim-Subscription-Key': MIDDLEWARE_SUBSCRIPTION_KEY,
        'Ocp-Apim-Trace': 'true',
        ...formData.getHeaders()
      }
    };

    const res = await axios.post(api, formData.getBuffer(), config).catch(error => error.response);
    return { status: res.status, data: res.data, fileSendName };
  },
  async downloadFile(uuid) {
    const api = `${ALFRESCO_API}/ObtenerDocumento?uuid=${uuid}`;
    const config = {
      headers: {
        'Ocp-Apim-Subscription-Key': MIDDLEWARE_SUBSCRIPTION_KEY,
        'Ocp-Apim-Trace': 'true'
      },
      responseType: 'arraybuffer'
    };

    return axios.get(api, config).catch(error => error.response);
  }
};

module.exports = { ...service };

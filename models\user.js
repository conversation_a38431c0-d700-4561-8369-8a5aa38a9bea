/* eslint-disable func-names */
const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const {
  findPreviousRecord,
  trackingPostFindOneAndUpdate,
  previousSave,
  trakingPostSave
} = require('../modules/trackingUserActivity/services/trackingUserActivity.service');

const ACTION_CREATE = 'crear usuario';
const ACTION_UPDATE = 'actualizar usuario';
const MODEL_NAME = 'users';

const { Schema } = mongoose;

const UserSchema = new Schema(
  {
    name: { type: String, maxlength: 70, required: true },
    role: { type: Schema.Types.ObjectId, required: true, ref: 'Role' },
    email: { type: String, required: true, primary: true, trim: true },
    isActive: { type: Boolean, default: true },
    enabled: { type: Boolean, default: true }
  },

  { timestamps: true }
);

UserSchema.pre(/findOneAndUpdate|updateOne/, findPreviousRecord);
UserSchema.post(/findOneAndUpdate|updateOne/, async function(doc) {
  return trackingPostFindOneAndUpdate(doc, this)(ACTION_UPDATE);
});
UserSchema.pre('save', previousSave);
UserSchema.post('save', async function(doc) {
  return trakingPostSave(doc, MODEL_NAME)(ACTION_CREATE);
});

UserSchema.index({ email: 1 }, { unique: true, collation: { locale: 'es', strength: 1 } });
UserSchema.plugin(paginate);

module.exports = mongoose.model('User', UserSchema);

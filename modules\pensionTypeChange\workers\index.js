const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/setPensionTypeChange');

module.exports = {
  name: 'set-pension-type-change',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  description: 'Cambio de tipo de pensión',
  endPoint: 'setpensiontypechange',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

<?php

namespace App\Domain\Pension\Services;

use App\Domain\Pension\Models\Pension;
use App\Domain\Shared\Services\UfValueService;
use App\Domain\Shared\Services\RulesEngineService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class AssetsCalculationService
{
    public function __construct(
        private UfValueService $ufService,
        private RulesEngineService $rulesEngine
    ) {}

    /**
     * Calcula todos los beneficios de una pensión
     */
    public function calculateAssets(Pension $pension): array
    {
        $assets = [
            'aps' => $this->calculateAps($pension),
            'family_assignment' => $this->calculateFamilyAssignment($pension),
            'christmas_bonus' => $this->calculateChristmasBonus($pension),
            'national_holidays_bonus' => $this->calculateNationalHolidaysBonus($pension),
            'winter_bonus' => $this->calculateWinterBonus($pension),
            'marriage_bonus' => $this->calculateMarriageBonus($pension),
            'health_exemption' => $this->calculateHealthExemption($pension),
            'rebsal' => $this->calculateRebsal($pension),
            'non_formulable_net' => $this->calculateNonFormulableAssets($pension, 'líquido'),
            'non_formulable_taxable' => $this->calculateNonFormulableAssets($pension, 'imponible'),
        ];

        return array_filter($assets, fn($value) => $value > 0);
    }

    /**
     * Calcula APS (Aporte Previsional Solidario)
     */
    private function calculateAps(Pension $pension): float
    {
        if (!$this->isEligibleForAps($pension)) {
            return 0;
        }

        $apsRules = $this->rulesEngine->getApsRules();
        $basePension = $pension->total_base_pension;
        
        foreach ($apsRules as $rule) {
            if ($basePension >= $rule['min_pension'] && $basePension <= $rule['max_pension']) {
                return $rule['aps_amount'];
            }
        }

        return 0;
    }

    /**
     * Calcula asignación familiar
     */
    private function calculateFamilyAssignment(Pension $pension): float
    {
        if ($pension->number_of_charges <= 0) {
            return 0;
        }

        $familyAssignmentRules = Cache::remember('family_assignment_rules', 3600, function () {
            return $this->rulesEngine->getFamilyAssignmentRules();
        });

        $basePension = $pension->total_base_pension;
        $charges = $pension->number_of_charges;

        foreach ($familyAssignmentRules as $rule) {
            if ($basePension >= $rule['min_pension'] && $basePension <= $rule['max_pension']) {
                return $rule['amount_per_charge'] * $charges;
            }
        }

        return 0;
    }

    /**
     * Calcula bono de navidad
     */
    private function calculateChristmasBonus(Pension $pension): float
    {
        if (!$this->shouldReceiveBonus($pension, 'christmas')) {
            return 0;
        }

        $currentMonth = now()->month;
        if ($currentMonth !== 12) {
            return 0; // Solo se paga en diciembre
        }

        $bonusRules = $this->rulesEngine->getBonusRules('christmas');
        $basePension = $pension->total_base_pension;

        foreach ($bonusRules as $rule) {
            if ($basePension >= $rule['min_pension'] && $basePension <= $rule['max_pension']) {
                return $rule['bonus_amount'];
            }
        }

        return 0;
    }

    /**
     * Calcula bono de fiestas patrias
     */
    private function calculateNationalHolidaysBonus(Pension $pension): float
    {
        if (!$this->shouldReceiveBonus($pension, 'national_holidays')) {
            return 0;
        }

        $currentMonth = now()->month;
        if ($currentMonth !== 9) {
            return 0; // Solo se paga en septiembre
        }

        $bonusRules = $this->rulesEngine->getBonusRules('national_holidays');
        $basePension = $pension->total_base_pension;

        foreach ($bonusRules as $rule) {
            if ($basePension >= $rule['min_pension'] && $basePension <= $rule['max_pension']) {
                return $rule['bonus_amount'];
            }
        }

        return 0;
    }

    /**
     * Calcula bono de invierno
     */
    private function calculateWinterBonus(Pension $pension): float
    {
        if (!$this->shouldReceiveBonus($pension, 'winter')) {
            return 0;
        }

        $currentMonth = now()->month;
        if (!in_array($currentMonth, [5, 6, 7, 8])) {
            return 0; // Solo se paga en meses de invierno
        }

        $bonusRules = $this->rulesEngine->getBonusRules('winter');
        $basePension = $pension->total_base_pension;

        foreach ($bonusRules as $rule) {
            if ($basePension >= $rule['min_pension'] && $basePension <= $rule['max_pension']) {
                return $rule['bonus_amount'];
            }
        }

        return 0;
    }

    /**
     * Calcula bono matrimonio
     */
    private function calculateMarriageBonus(Pension $pension): float
    {
        // Solo para pensiones de supervivencia en el mes del matrimonio
        if ($pension->pension_type !== 'SUPERVIVENCIA' || !$pension->marriage_date) {
            return 0;
        }

        $marriageMonth = Carbon::parse($pension->marriage_date);
        if (!$marriageMonth->isSameMonth(now())) {
            return 0;
        }

        return $this->rulesEngine->getMarriageBonusAmount();
    }

    /**
     * Calcula exención de salud ajustada
     */
    private function calculateHealthExemption(Pension $pension): float
    {
        $assets = $pension->assets ?? [];
        
        if (($assets['health_exemption'] ?? 'No') !== 'Si') {
            return 0;
        }

        $taxablePension = $pension->currentLiquidation?->taxable_pension ?? 0;
        $ufValue = $this->ufService->getCurrentUfValue();
        
        // Lógica específica de exención de salud
        $exemptionAmount = min($taxablePension * 0.07, $ufValue * 2.78);
        
        return round($exemptionAmount, 2);
    }

    /**
     * Calcula REBSAL (Rebaja de Salud)
     */
    private function calculateRebsal(Pension $pension): float
    {
        $assets = $pension->assets ?? [];
        
        if (($assets['health_discount'] ?? 'No') !== 'Si') {
            return 0;
        }

        $taxablePension = $pension->currentLiquidation?->taxable_pension ?? 0;
        
        // REBSAL es 7% de la pensión imponible
        return round($taxablePension * 0.07, 2);
    }

    /**
     * Calcula haberes no formulables
     */
    private function calculateNonFormulableAssets(Pension $pension, string $assetType): float
    {
        $discountsAndAssets = $pension->discountsAndAssets;
        
        if (!$discountsAndAssets) {
            return 0;
        }

        $assets = $discountsAndAssets->getAssetsByType($assetType);
        $currentDate = now();
        $total = 0;

        foreach ($assets as $asset) {
            // Verificar vigencia
            $startDate = $asset['start_date'] ? Carbon::parse($asset['start_date']) : null;
            $endDate = $asset['end_date'] ? Carbon::parse($asset['end_date']) : null;

            if ($startDate && $currentDate->lt($startDate)) {
                continue;
            }

            if ($endDate && $currentDate->gt($endDate)) {
                continue;
            }

            $total += $asset['amount'] ?? 0;
        }

        return round($total, 2);
    }

    /**
     * Verifica elegibilidad para APS
     */
    private function isEligibleForAps(Pension $pension): bool
    {
        return in_array($pension->pension_type, [
            'INVALIDEZ_TOTAL',
            'INVALIDEZ_PARCIAL',
            'SUPERVIVENCIA'
        ]);
    }

    /**
     * Verifica si debe recibir bonos
     */
    private function shouldReceiveBonus(Pension $pension, string $bonusType): bool
    {
        $assets = $pension->assets ?? [];
        return ($assets['pay_bonus'] ?? 'SI') === 'SI';
    }
}

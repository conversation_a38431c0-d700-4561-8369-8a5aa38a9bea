/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const Model = require('../models/ExpenseAccountingReport');
const DiscountsAndAssetsModel = require('../../../models/discountsAndAssets');

const service = require('./dbService');

const discountsAndAssetsData = require('../../../resources/calculateAssetsBonusDiscounts/discountsAndAssets.json');
const pensionsData = require('../../../resources/calculateAssetsBonusDiscounts/pensions.json');

describe('test service', () => {
  beforeAll(beforeAllTests);

  it('should generate 28 reports document', async done => {
    await PensionModel.create({ ...pensionsData[0], enabled: true, validityType: 'Vigente' });
    await DiscountsAndAssetsModel.create({
      ...discountsAndAssetsData[0],
      pensionCodeId: pensionsData[0].pensionCodeId,
      beneficiaryRut: pensionsData[0].beneficiary.rut,
      causantRut: pensionsData[0].causant.rut
    });

    const { completed, error } = await service.generateReports();
    const results = await Model.find({}).lean();
    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(results.length).toBe(28);

    done();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await DiscountsAndAssetsModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

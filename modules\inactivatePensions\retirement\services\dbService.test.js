/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const ToInactivateByRetirementModel = require('../models/pensionToInactivate');
const PensionModel = require('../../../../models/pension');

const service = require('./dbService');

const pensionsList = require('../../../../resources/pensionSoonToInactivate.json');

describe('Inactivate by retirement test', () => {
  beforeAll(beforeAllTests);
  let mocks;

  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn()
    };
    jest.spyOn(ToInactivateByRetirementModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should return empty array if there is no pensions to mark', async () => {
    const { pensions } = await service.findPensionToInactivate();
    expect(pensions.length).toBe(0);
  });

  it('should return {completed: true} if list of pensions to mark is empty', async () => {
    const { pensionsToEvaluate, error } = await service.markPensionToInactivate([]);
    expect(pensionsToEvaluate).toBeDefined();
    expect(error).toBeUndefined();
  });

  it('should create a bulk insert if list of pensions is NOT empty', async () => {
    const { pensionsToEvaluate } = await service.markPensionToInactivate(pensionsList);
    expect(mocks.startTransaction).toBeCalled();
    expect(mocks.commitTransaction).toBeCalled();
    expect(mocks.abortTransaction).not.toBeCalled();
    expect(pensionsToEvaluate).toBeDefined();
  });

  it('should abort the transaction if there is an error', async () => {
    await ToInactivateByRetirementModel.create({
      causantRut: '5e502569b20a4257fc0ece70',
      beneficiaryRut: '6e502569b20a4257fc0ece70',
      inactivationReason: 'Jubilación',
      dateToInactivate: '2020-04-01T03:00:00.000Z'
    });
    ToInactivateByRetirementModel.collection.initializeOrderedBulkOp = jest
      .fn()
      .mockImplementation(() => {
        throw new Error();
      });
    const { pensionsToEvaluate } = await service.markPensionToInactivate(pensionsList);
    expect(mocks.abortTransaction).toBeCalled();
    expect(pensionsToEvaluate).toBeDefined();
  });

  it('should find and inactivate pension by retirement', async () => {
    const pension = await PensionModel.create({
      ...pensionsList[0],
      pensionType: 'Jubilación',
      endDateOfValidity: new Date(),
      inactivateManually: false,
      manuallyReactivated: false,
      enabled: true
    });

    await ToInactivateByRetirementModel.create({
      causantRut: pension.causant.rut,
      beneficiaryRut: pension.beneficiary.rut,
      inactivationReason: 'Por jubilación',
      dateToInactivate: new Date()
    });

    const { completed, inactivationError } = await service.inactivatePensionsByRetirement();

    // Now pension collection should have two documents

    expect(inactivationError).toBeUndefined();
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    await ToInactivateByRetirementModel.deleteMany({});
    await PensionModel.deleteMany({});
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const { escapeChars } = require('../regex-utils');

const isString = obj =>
  obj !== 'true' && obj !== 'false' && Object.prototype.toString.call(obj) === '[object String]';

const isBooleanString = obj =>
  Object.prototype.toString.call(obj) === '[object String]' && (obj === 'true' || obj === 'false');

const separator = ';';

const getSearchValueInMultipleFields = (value, ...fields) => {
  const regex = new RegExp(value, 'i');
  const $or = fields.map(field => {
    const statement = {};
    statement[field] = regex;
    return statement;
  });
  return {
    $or
  };
};

const getFilterFields = value => {
  /*
        Exclude all params for paginations and other issues
    */
  const { select, sort, page, size, filter, ...others } = value;
  return others;
};

const getFilterValueInFields = values => {
  let $and = [];
  if (values) {
    $and = Object.keys(values).map(field => {
      const statement = {};
      const value = values[field];
      if (isString(value)) {
        if (value.indexOf(separator) === -1) {
          statement[field] = new RegExp(escapeChars(value), 'i');
        } else {
          // sector: { $in: ["Banks", "Chemicals"] }
          const terms = value.split(separator).map(e => new RegExp(escapeChars(e), 'i'));

          statement[field] = { $in: terms };
        }
      } else if (isBooleanString(value)) {
        statement[field] = value === 'true';
      } else {
        statement[field] = value;
      }

      return statement;
    });
  }
  return {
    $and
  };
};

const multiFieldCriteriaBuilder = {
  /**
   * Search value in multiple fields
   * @param {String} value - search value
   * @param {Array} fields - field names to search into
   */

  build(value, ...fields) {
    const filter = value.filter ? escapeChars(value.filter) : '';
    // eslint-disable-next-line no-param-reassign
    value.filter = filter;

    const searchValueInMultipleFields = getSearchValueInMultipleFields(value.filter, ...fields);
    const valueFields = getFilterFields(value);
    const filterValueInFields = getFilterValueInFields(valueFields);
    const query = {
      $and: []
    };
    if (filterValueInFields.$and.length > 0) {
      query.$and.push(filterValueInFields);
    }
    if (searchValueInMultipleFields.$or.length > 0) {
      query.$and.push(searchValueInMultipleFields);
    }

    return query;
  }
};

module.exports = multiFieldCriteriaBuilder;

/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
const {
  MAX_AGE,
  ruleFactory,
  valueLaw19403,
  valueLaw19539A,
  valueLaw19539B,
  valueLaw19953A,
  payLawBonds,
  pensionTypesRuler
} = require('./helper');
const { roundValue } = require('../../../sharedFiles/helpers');

const rulesDefinition = [
  (pension, basePensionData) => {
    const basePension =
      pension.basePension < basePensionData.minimun ? basePensionData.minimun : pension.basePension;
    const payLaw = payLawBonds(pension);

    if (
      basePension <
      Math.max(roundValue(basePensionData.minimun) + roundValue(basePensionData.law19403), 0)
    ) {
      return {
        ...pension,
        basePension,
        law19403: payLaw ? valueLaw19403(basePension, basePensionData) : 0,
        law19539: payLaw ? valueLaw19539A(basePension, basePensionData) : 0,
        law19953: payLaw ? valueLaw19953A(basePension, basePensionData) : 0
      };
    }
    return false;
  },
  (pension, basePensionData) => {
    const basePension =
      pension.basePension < basePensionData.minimun ? basePensionData.minimun : pension.basePension;
    const payLaw = payLawBonds(pension);

    if (
      basePension >
      Math.max(roundValue(basePensionData.minimun) + roundValue(basePensionData.law19403), 0)
    ) {
      return {
        ...pension,
        basePension,
        law19403: 0,
        law19539: payLaw ? valueLaw19539B(basePension, basePensionData) : 0,
        law19953: payLaw ? valueLaw19953A(basePension, basePensionData) : 0
      };
    }
    return false;
  }
];

const getRules = age => (age >= MAX_AGE ? rulesDefinition : false);

module.exports = async pension =>
  ruleFactory({
    getNomenclatorValue: value => value.law19953,
    getRules,
    pension,
    pensionTypes: pensionTypesRuler
  });

module.exports = ({
  HttpStatus,
  discountsFonasaService,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  pensionsService,
  Logger
}) => {
  const service = discountsFonasaService;
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }
  return {
    processFonasaDiscounts: async (req, res) => {
      const { body } = req;
      if (!body.length) {
        res.status(HttpStatus.BAD_REQUEST).json({ result: 'Datos no recibidos para el proceso.' });
        return;
      }
      const { isError, error } = await service.bulkInsert(body);
      if (isError) {
        Logger.error(`Error on fonasa discounts process, ${error}`);
        manageError(res, error);
      }
      const processResult = await service.processFonasaDiscounts(pensionsService);
      if (processResult.isError) {
        Logger.error(`Error on updating pensions to set fonasa discounts, ${error}`);
        res.status(401).json({ result: processResult.message });
      } else {
        Logger.info(`Fonasa discounts processed successfully`);
        res.status(HttpStatus.CREATED).json({ result: 'El proceso se completó correctamente.' });
      }
    },

    getMonthYear: async (req, res) => {
      const currentDate = await service.getCurrentTime();

      Logger.info('getting Month and Year for file validation');
      res.status(HttpStatus.OK).json(currentDate);
    }
  };
};

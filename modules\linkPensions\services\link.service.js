/* eslint-disable no-underscore-dangle */
/* eslint no-return-await: "error" */
const PensionModel = require('../../../models/pension');
const TemporaryPensionModel = require('../models/temporaryPensioner');
const validatePensioners = require('./validations/tempPensionsValidations');
const { getMonthHolidays } = require('./businessDays.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const PensionHistoricModel = require('../../../models/pensionHistoric');

const processMark = 'LINK_PENSIONS';

const service = {
  async getAllAndFilter(query) {
    return PensionModel.find(query)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async getAll() {
    return PensionModel.find()
      .lean()
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async findAll(filter = {}, select = {}, sort = {}) {
    return PensionModel.find(filter)
      .lean()
      .select(select)
      .sort(sort)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async update(id, json) {
    try {
      const result = await this.PensionModel.findOnePensionAndUpdate(
        { _id: id, enabled: true },
        json,
        {
          new: true
        }
      );
      return { isError: false, result };
    } catch (e) {
      return { isError: true, error: e };
    }
  },
  async alreadyLinked() {
    return logService.existsLog(processMark);
  },
  async totalTemporaryPensionsOnCurrentCount() {
    const currentCount = await TemporaryPensionModel.countDocuments();
    return currentCount;
  },
  async linkPensions() {
    let result = 0;

    if (await logService.existsLog(processMark)) {
      return {
        isError: true,
        error: { code: 401, message: 'Este proceso ya se ejecutó en el mes actual.' },
        result
      };
    }

    if ((await this.totalTemporaryPensionsOnCurrentCount()) === 0) {
      return {
        isError: true,
        error: { code: 404, message: 'No se ha realizado el proceso de importación' },
        result
      };
    }

    try {
      result = await TemporaryPensionModel.count().exec();
      await logService.saveLog(processMark);
      return { isError: false, result };
    } catch (e) {
      return { isError: true, error: e };
    }
  },
  async cancelLinkedPensions(_date, checkActionPermissionFn) {
    const { actionIsAllowed, inDayRange } = await checkActionPermissionFn(_date, getMonthHolidays);

    if (!actionIsAllowed) {
      return {
        isError: false,
        isCancelled: false,
        inDayRange,
        message: 'No se puede realizar esta acción en la fecha actual'
      };
    }
    if (!(await this.alreadyLinked())) {
      return {
        isError: false,
        isCancelled: false,
        inDayRange,
        message: 'No se han encontrado registro del mes actual'
      };
    }
    const session = await TemporaryPensionModel.startSession();
    session.startTransaction();
    try {
      await TemporaryPensionModel.deleteMany({}).exec();
      await session.commitTransaction();
      await logService.removeMark(processMark);
      return {
        isError: false,
        isCancelled: true,
        message: 'Anulación exitosa'
      };
    } catch (e) {
      await session.abortTransaction();
      return { isError: true, error: e };
    }
  },
  async validateData(inputPensions) {
    const filter = {
      'beneficiary.rut': { $in: inputPensions.map(({ beneficiaryRut }) => beneficiaryRut) }
    };
    const select = 'beneficiary causant pensionType';
    const { result: actualPensioners = [] } = await this.findAll(filter, select);

    return inputPensions.map((_, i) => validatePensioners(inputPensions, i, actualPensioners));
  }
};

module.exports = { processMark, ...service, PensionModel, PensionHistoricModel };

const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/transferPensionerInfo.services');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'transfer-pensioner-info',
  worker: deps =>
    workerModule.workerFn({
      pensionService,
      logService,
      service,
      ...deps
    }),
  repeatInterval: process.env.CRON_TRANSFER_PENSIONER_INFO_FREQUENCY,
  description:
    'Traspaso de modificaciones realizadas a partir del día 13 del mes anterior al actual de pesionados desde el perfil del beneficiaro',
  priority: 'highest',
  endPoint: 'updatepensionerinfo',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.cronDependency
};

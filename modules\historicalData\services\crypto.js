const crypto = require('crypto');
const { isValidRutPattern } = require('./validators');

const { SECRET_KEY_DECRYPT_HISTORICAL_PENSION_DATA: secret } = process.env;
const algorithm = 'aes-256-ctr';

const a32byteKey = crypto
  .createHash('sha512')
  .update(String(secret))
  .digest('base64')
  .substr(0, 32);

const encrypt = text => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv(algorithm, a32byteKey, iv);
  const encrypted = Buffer.concat([cipher.update(text), cipher.final()]);

  return {
    iv: iv.toString('hex'),
    content: encrypted.toString('hex')
  };
};

const decrypt = hash => {
  const decipher = crypto.createDecipheriv(algorithm, a32byteKey, Buffer.from(hash.iv, 'hex'));
  const decrpyted = Buffer.concat([
    decipher.update(Buffer.from(hash.content, 'hex')),
    decipher.final()
  ]);
  return decrpyted.toString();
};

const objDecryption = (obj, index) => {
  const { rut, name, lastName, mothersLastName, ...otherFields } = obj;

  const decryptedRut = !rut.iv ? rut : decrypt(rut).toUpperCase();

  if (!isValidRutPattern(decryptedRut))
    throw new Error(`Wrong decryption at register: ${index} ${decryptedRut} `);

  return {
    ...otherFields,
    rut: decryptedRut,
    name,
    lastName,
    mothersLastName
  };
};

module.exports = {
  encrypt,
  decrypt,
  objDecryption
};

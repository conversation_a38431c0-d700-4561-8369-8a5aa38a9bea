const moment = require('moment');
const FonasaDiscountModel = require('../models/discounts');
const logService = require('../../../sharedFiles/services/jobLog.service');

const processMark = 'BULK_LOAD_FONASA';

const getMappedDiscountsData = discounts => {
  const rutRegexList = [];
  const dataMappedToObj = {};
  discounts.forEach(({ rut, discount }) => {
    if (rut) {
      const formatedRut = rut.replace(/^0+/, '');
      rutRegexList.push(new RegExp(formatedRut, 'i'));
      dataMappedToObj[formatedRut] = discount;
    }
  });
  return [rutRegexList, dataMappedToObj];
};

const getMappedPensions = (pensions, dataMappedToObj) => {
  const list = pensions.map(({ _id, __v, ...pension }) => {
    const { rut } = pension.beneficiary;
    return {
      ...pension,
      discounts: { ...pension.discounts, healthLoan: dataMappedToObj[rut] }
    };
  });
  return list;
};

const service = {
  async bulkInsert(discounts) {
    try {
      await FonasaDiscountModel.deleteMany({}).exec();
      await FonasaDiscountModel.insertMany(discounts);
      return { isError: false, error: null };
    } catch (error) {
      return { isError: true, error };
    }
  },
  async getCurrentTime() {
    return moment().format('MMYY');
  },
  async processFonasaDiscounts(pensionService) {
    try {
      const markDependency = 'CRON_BASE_MINIMUN_PENSION_WORKER';
      const missingDepMsg = `No se ha ejecutado la dependencia ${markDependency}`;
      const alreadyExecutedMessage = `Este proceso ya se ejecutó para el mes actual`;

      if (!(await logService.existsLog(markDependency))) {
        return { isError: true, error: 'UNAUTHORIZED', message: missingDepMsg };
      }

      if (await logService.existsLog(processMark)) {
        return { isError: true, error: 'UNAUTHORIZED', message: alreadyExecutedMessage };
      }
      const fonasaDiscounts = await FonasaDiscountModel.find({}).exec();
      const [rutList, dataMappedToObj] = getMappedDiscountsData(fonasaDiscounts);
      const pensions = await pensionService.findValidPensionsByRut(rutList);
      const mappedPensions = getMappedPensions(pensions, dataMappedToObj);
      const { completed, error } = await pensionService.createUpdatePension(mappedPensions);
      await logService.saveLog(processMark);
      return { isError: !completed, error };
    } catch (error) {
      return { isError: true, error };
    }
  }
};
module.exports = { processMark, ...service };

{"idSiniestro": "0002960515", "cunInterno": "4199011701", "cunExterno": "0000000000", "ceSanitario": "A000", "bpEmpresa": "2000325969", "rutEmpresa": "78408960-6", "institucionPaciente": "", "bpTrabajador": "1001669766", "tipoSiniestro": 2, "tipoLey": 1, "fechaPresentacion": "1999-01-11T00:00:00", "horaPresentacion": "123613", "datoHistorico": "X", "coberturaSoap": "X", "modificadoEl": "2019-06-05T00:00:00", "hora": "100209", "timestamp": "20190605100209", "indBorrado": "", "borradoEl": "0001-01-01T00:00:00", "borradoPor": "", "antecedentesAccidente": {"idSiniestro": "0002960515", "fechaCreacion": "0001-01-01T00:00:00", "horaCreacion": "000000", "fechaModificacion": "2019-06-05T00:00:00", "horaModificacion": "100209", "timeStamp": "20190605100209", "fechaSiniestro": "1999-01-11T00:00:00", "horaSiniestro": "081500", "calle": "CARRETERA 5 SUR ENTRE LOS PINOS Y LA VARA", "numero": "1111", "codigoPostal": "", "comuna": "SAN BERNARDO", "codigoCiudad": "000000013401", "pais": "CL", "region": "13", "codigoLugar": 0, "lugar": "AV COLON PANAMERICANA", "antecedentes": "NO ESPECIFICADO", "codigoForma": 11, "textoAntecedente": "es impactado por cola de camion al viajar en moto", "codigoAgente": 234, "codigoUbicacionLesion": "521", "criterioGravedad": "1", "trabajoHabitual": "X", "tipoAccidenteTrayecto": "1", "intencion": "", "accidenteOcurrioSucursal": "1", "cunInterno": "4199011701", "claseDenunciante": "8", "rutDenunciante": "13818363-7", "telefonoDenunciante": "008416599", "nombreDenunciante": "RAMOS TORRES ALEX"}, "antecedentesEnfermedadProfesional": null, "ocupacionJornada": {"idSiniestro": "0002960515", "fechaCreacion": "2019-06-05T00:00:00", "horaCreacion": "100209", "fechaModificacion": "0001-01-01T00:00:00", "horaModificacion": "000000", "timeStamp": "20190605100209", "sede": "0000000176", "direccionSede": "JOFRE 038", "diasTrabajados": "0", "diasLibres": "0", "horaInicioTurno": "080000", "horaFinTurno": "170000", "codigoRubro": "400", "actividadEmpresa": "0000452010", "tipoJornada": "1", "ocupacion": "ADMINISTRATIVO", "codigoProfesion": "9622", "puestoTrabajo": "ADMINISTRATIVO", "fechaIngreso": "1995-06-30T00:00:00", "duracionContrato": "1", "categoriaOcupacional": "0001-01-01T00:00:00", "dependencia": "1", "remuneracion": "1", "previsionSalud": "2000025835", "afp": "001", "cajaCompensacion": "2000131904"}, "reposos": [{"idSiniestro": "0002960515", "idSecuencia": "0001", "fechaInicioReposo": "1999-01-11T00:00:00", "fechaAlta": "2000-06-30T00:00:00", "fechaIndicacionReposo": "1999-01-11T00:00:00", "fechaIndicacionAlta": "2000-06-30T00:00:00", "respAlta": "", "diasReposo": "537", "tipoAlta": 1, "numeroLicencia": "0000000000", "creadoPor": "", "creadoEl": null, "horaCreacion": null, "timeStamp": "20190605100209"}, {"idSiniestro": "0002960515", "idSecuencia": "0002", "fechaInicioReposo": "2012-09-12T00:00:00", "fechaAlta": "2012-09-12T00:00:00", "fechaIndicacionReposo": "2012-09-12T00:00:00", "fechaIndicacionAlta": "2012-09-12T00:00:00", "respAlta": "", "diasReposo": "1", "tipoAlta": 2, "numeroLicencia": "0000000000", "creadoPor": "", "creadoEl": null, "horaCreacion": null, "timeStamp": "20190605100209"}], "calificaciones": [{"idSiniestro": "0002960515", "idSecuencia": "0002", "fechaCalificacion": "1999-01-18T00:00:00", "tipoSiniestro": 2, "tipoCalificacion": 2, "tipoCobertura": 2, "calificador": "", "indicadorReposoFuturo": "", "numeroLicencia": "0000000000", "fechaInicioLicencia": "00000000", "fechaFinLicencia": "00000000", "justificaionEstadoCalificacion": "", "codigoOITFormaAccidente": "", "codigoOITAgenteMaterial": "", "codigoOITCausaExterna": "", "codigoOITCausalEnfermedad": "", "tipoReca": "00", "enviaSuseso": "", "excepcion": "", "tipoDoc": "000", "indicacionPendiente": "", "creadoEl": null, "horaCreacion": null, "timeStamp": "0"}, {"idSiniestro": "0002960515", "idSecuencia": "0001", "fechaCalificacion": "1999-01-11T00:00:00", "tipoSiniestro": 2, "tipoCalificacion": 2, "tipoCobertura": 1, "calificador": "", "indicadorReposoFuturo": "", "numeroLicencia": "0000000000", "fechaInicioLicencia": "00000000", "fechaFinLicencia": "00000000", "justificaionEstadoCalificacion": "", "codigoOITFormaAccidente": "", "codigoOITAgenteMaterial": "", "codigoOITCausaExterna": "", "codigoOITCausalEnfermedad": "", "tipoReca": "00", "enviaSuseso": "", "excepcion": "", "tipoDoc": "000", "indicacionPendiente": "", "creadoEl": null, "horaCreacion": null, "timeStamp": "0"}], "incapacidad": [{"idSiniestro": "0002960515", "idIncapacidad": "0001", "numeroResolucion": "041036800", "fechaResolucion": "2000-07-14T00:00:00", "comisionEvaluadora": "01", "fechaSolicitudEvaluacion": "2000-07-14T00:00:00", "solicitanteEvaluacion": "", "tipoEvaluacion": "", "ocurrioUltimaEmpresa": "", "idEmpresaResponsable": "", "ponderacionSexo": 0.0, "ponderacionEdad": 0.0, "ponderacionProfesion": 0.0, "ponderacionOtro": 0.0, "indicadorGranInvalido": "", "fechaInicioIncapacidad": "2000-07-14T00:00:00", "modalidad": "", "porcentajeIncapacidad": 50.0, "indicadorVigencia": "", "observaciones": "", "nombreMinistroFe": "MIGRACION", "rutMinistroFe": "", "nombrePresidenteComision": "SALUD", "rutPresidenteComision": "", "otroSolicitanteEvaluacion": null, "nombreEmpresaAnterior": null, "rutEmpresaAnterior": null, "nombreEmpresaAnteriorAnterior": null, "rutEmpresaAnteriorAnterior": null, "indicacionPendiente": null, "anulado": null, "idDocumento": null, "usuarioRegistraDocumento": null, "fechaRegistraDocumento": null, "horaRegistraDocumento": null, "tipoObjetoSAP": null, "claseDocumentoSAP": null, "idDoctoArchivo": null, "fechaCreacion": "0001-01-01T00:00:00", "fechaModificacion": "0001-01-01T00:00:00", "horaCreacion": null, "horaModificacion": null, "timeStamp": null, "indicadorBorrado": null, "borrado": null}, {"idSiniestro": "0002960515", "idIncapacidad": "0002", "numeroResolucion": "041042104", "fechaResolucion": "2004-08-27T00:00:00", "comisionEvaluadora": "05", "fechaSolicitudEvaluacion": "2004-08-27T00:00:00", "solicitanteEvaluacion": "", "tipoEvaluacion": "", "ocurrioUltimaEmpresa": "", "idEmpresaResponsable": "", "ponderacionSexo": 0.0, "ponderacionEdad": 0.0, "ponderacionProfesion": 0.0, "ponderacionOtro": 0.0, "indicadorGranInvalido": "", "fechaInicioIncapacidad": "2000-07-14T00:00:00", "modalidad": "", "porcentajeIncapacidad": 70.0, "indicadorVigencia": "X", "observaciones": "", "nombreMinistroFe": "MIGRACION", "rutMinistroFe": "", "nombrePresidenteComision": "SALUD", "rutPresidenteComision": "", "otroSolicitanteEvaluacion": null, "nombreEmpresaAnterior": null, "rutEmpresaAnterior": null, "nombreEmpresaAnteriorAnterior": null, "rutEmpresaAnteriorAnterior": null, "indicacionPendiente": null, "anulado": null, "idDocumento": null, "usuarioRegistraDocumento": null, "fechaRegistraDocumento": null, "horaRegistraDocumento": null, "tipoObjetoSAP": null, "claseDocumentoSAP": null, "idDoctoArchivo": null, "fechaCreacion": "0001-01-01T00:00:00", "fechaModificacion": "0001-01-01T00:00:00", "horaCreacion": null, "horaModificacion": null, "timeStamp": null, "indicadorBorrado": null, "borrado": null}, {"idSiniestro": "0002960515", "idIncapacidad": null, "numeroResolucion": null, "fechaResolucion": "0001-01-01T00:00:00", "comisionEvaluadora": null, "fechaSolicitudEvaluacion": "0001-01-01T00:00:00", "solicitanteEvaluacion": null, "tipoEvaluacion": null, "ocurrioUltimaEmpresa": null, "idEmpresaResponsable": null, "ponderacionSexo": 0.0, "ponderacionEdad": 0.0, "ponderacionProfesion": 0.0, "ponderacionOtro": 0.0, "indicadorGranInvalido": null, "fechaInicioIncapacidad": "0001-01-01T00:00:00", "modalidad": null, "porcentajeIncapacidad": 0.0, "indicadorVigencia": null, "observaciones": null, "nombreMinistroFe": null, "rutMinistroFe": null, "nombrePresidenteComision": null, "rutPresidenteComision": null, "otroSolicitanteEvaluacion": "", "nombreEmpresaAnterior": "", "rutEmpresaAnterior": "", "nombreEmpresaAnteriorAnterior": "", "rutEmpresaAnteriorAnterior": "", "indicacionPendiente": "", "anulado": "", "idDocumento": "", "usuarioRegistraDocumento": "", "fechaRegistraDocumento": "00000000", "horaRegistraDocumento": "000000", "tipoObjetoSAP": "", "claseDocumentoSAP": "", "idDoctoArchivo": "", "fechaCreacion": "0001-01-01T00:00:00", "fechaModificacion": "0001-01-01T00:00:00", "horaCreacion": "000000", "horaModificacion": "000000", "timeStamp": "0", "indicadorBorrado": "", "borrado": "00000000"}, {"idSiniestro": "0002960515", "idIncapacidad": null, "numeroResolucion": null, "fechaResolucion": "0001-01-01T00:00:00", "comisionEvaluadora": null, "fechaSolicitudEvaluacion": "0001-01-01T00:00:00", "solicitanteEvaluacion": null, "tipoEvaluacion": null, "ocurrioUltimaEmpresa": null, "idEmpresaResponsable": null, "ponderacionSexo": 0.0, "ponderacionEdad": 0.0, "ponderacionProfesion": 0.0, "ponderacionOtro": 0.0, "indicadorGranInvalido": null, "fechaInicioIncapacidad": "0001-01-01T00:00:00", "modalidad": null, "porcentajeIncapacidad": 0.0, "indicadorVigencia": null, "observaciones": null, "nombreMinistroFe": null, "rutMinistroFe": null, "nombrePresidenteComision": null, "rutPresidenteComision": null, "otroSolicitanteEvaluacion": "", "nombreEmpresaAnterior": "", "rutEmpresaAnterior": "", "nombreEmpresaAnteriorAnterior": "", "rutEmpresaAnteriorAnterior": "", "indicacionPendiente": "", "anulado": "", "idDocumento": "", "usuarioRegistraDocumento": "", "fechaRegistraDocumento": "00000000", "horaRegistraDocumento": "000000", "tipoObjetoSAP": "", "claseDocumentoSAP": "", "idDoctoArchivo": "", "fechaCreacion": "0001-01-01T00:00:00", "fechaModificacion": "0001-01-01T00:00:00", "horaCreacion": "000000", "horaModificacion": "000000", "timeStamp": "0", "indicadorBorrado": "", "borrado": "00000000"}], "episodios": [{"idSiniestro": "0002960515", "centroSanitario": "A000", "idEpisodio": "0004139496", "fechaCreacion": "2019-06-24T00:00:00", "fechaModificacion": "0001-01-01T00:00:00", "horaCreacion": "000000", "horaModificacion": "000000", "creadoPor": "YCARMONA", "anulado": "", "anuladoPor": "", "anuladoEl": "00000000", "timestamp": "20190624112005"}], "alertaCalificacion": null, "alertaCalificacionTrayecto": [{"idSiniestro": "0002960515", "dirigenteSindical": "", "trabajoDistancia": "", "fuerzaMayorExtrana": "", "accidenteControlMedico": "", "noRegistraAlerta": "X", "creadoEl": "2019-06-05T00:00:00", "horaCreado": "100209", "modificadoEl": "0001-01-01T00:00:00", "horaModificado": "000000", "timeStamp": "20190605100209"}], "alertaCalificacionEP": null}
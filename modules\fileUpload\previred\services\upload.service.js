const moment = require('moment');

const downloadFTPFile = require('../../ftpFileDownloader');
const { compressFile } = require('../../../sharedFiles/helpers');

const {
  PREVIRED_LOGIN_PAGE_URL,
  PREVIRED_LOGIN_PAGE_USER,
  PREVIRED_LOGIN_PAGE_PASSWORD,
  PREVIRED_ACHS_EMAIL,
  PREVIRED_PAYROLL_TYPE,
  PREVIRED_PAYROLL_FILE_FORMAT
} = process.env;

const rutField = '#web_rut2';
const passwordField = '#web_password';
const loginButton = '#login';
const enterprise = '#empresa';
const resultsTable = '#tablaResultados';
const enterButton = 'button.ingresar';
const goBackButton = '#btnVolver';
const mutualPensionsButton = 'a.boton';
const addPayrollButton = '#agregar_nomina_goliath';
const payrollNameField = '#web_nombre_nomina';
const emailField = '#web_email';
const payrollTypeField = '#web_tipo_pago';
const fileFormatField = '#web_formato_sel';
const continueButton = '#continuar';

const fill = async (page, selector, fillValue) => {
  await page.waitFor(selector);
  await page.$eval(
    selector,
    (element, value) => {
      const el = element;
      el.value = value;
    },
    fillValue
  );
};

const uploadFile = async ({
  file,
  puppeteer,
  fillField = fill,
  compressFileToUpload = compressFile,
  tmp
}) => {
  const browser = await puppeteer.launch({
    headless: true,
    defaultViewport: {
      width: 1024,
      height: 768
    },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  try {
    const page = await browser.newPage();
    await page.goto(PREVIRED_LOGIN_PAGE_URL, { waitUntil: 'networkidle2' });

    await fillField(page, rutField, PREVIRED_LOGIN_PAGE_USER);
    await fillField(page, passwordField, PREVIRED_LOGIN_PAGE_PASSWORD);

    await page.click(loginButton);
    await page.waitForSelector(enterprise);
    await page.click(enterprise);
    await page.waitForSelector(resultsTable);
    await page.click(enterButton);
    await page.waitForSelector(goBackButton);
    await page.click(mutualPensionsButton);
    await page.waitForSelector(addPayrollButton);
    await page.click(addPayrollButton);

    await fillField(page, payrollNameField, `Pensionados ${moment().format('MMYYYY')}`);
    await fillField(page, emailField, PREVIRED_ACHS_EMAIL);
    await page.select(payrollTypeField, PREVIRED_PAYROLL_TYPE);
    await page.select(fileFormatField, PREVIRED_PAYROLL_FILE_FORMAT);

    const fileToUpload = compressFileToUpload(
      file,
      `${tmp.dirSync().name}/prev${moment().format('MMYYYY')}.zip`
    );
    const fileUploadInputField = await page.$('input[type=file]');
    fileUploadInputField.uploadFile(fileToUpload);

    await page.click(continueButton);
    await page.waitFor(15000);
    browser.close();
  } catch (error) {
    browser.close();
    throw new Error(`Error in previred -> uploadFile() ${error}`);
  }
};

module.exports = {
  downloadFTPFile,
  compressFile,
  uploadFile,
  fill
};

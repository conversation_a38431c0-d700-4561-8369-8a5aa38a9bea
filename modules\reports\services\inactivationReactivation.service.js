const ReactivationModel = require('../../historicPensions/models/temporaryReactivationPension');
const InactivationModel = require('../../historicPensions/models/temporaryInactivationPension');

const CLEAN_RESPONSE = '-_id -__v -enabled';

const service = {
  getInactivationReactivationReport: async () => {
    const inactivation = await InactivationModel.find({}, CLEAN_RESPONSE).exec();

    const reactivation = await ReactivationModel.find({}, CLEAN_RESPONSE).exec();

    return {
      isError: false,
      result: { inactivations: inactivation, reactivations: reactivation },
      error: null
    };
  }
};

module.exports = { ...service };

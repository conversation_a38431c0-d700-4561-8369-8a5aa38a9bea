const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'set-amount-pensioners-national-holidays-bonus',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SET_AMOUNT_PENSIONERS_NATIONAL_HOLIDAYS_BONUS,
  description: 'Asignar bono de aguinaldo por fiestas patrias a los pensionados que apliquen',
  endPoint: 'assignnationalholidaysbonus',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

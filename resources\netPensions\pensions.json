[{"_id": "fakeiD", "institutionalPatient": false, "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34.827, "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionCodeId": "17153", "article40": 10000, "article41": 10000, "marriageBonus": 1000, "assets": {"aps": 1200, "healthDiscount": "Si", "healthExemption": "Si", "forFamilyAssignment": 12313, "nonFormulable": 21312, "taxableTotalNonFormulable": 1000, "nationalHolidaysBonus": 10000, "christmasBonus": 3333.33, "netTotalNonFormulable": 7394.11, "winterBonus": 1000}, "retroactiveAmounts": {"taxableTotalNonFormulableAssets": 1000, "survival": 2300, "forDisability": 2132, "forInstitutionalPatient": 12312, "forRejection": 1232, "forTotalNonFormulableDiscounts": 1111.11, "forNetTotalNonFormulableAssets": 2222.22, "forBonuses": 2000, "forFamilyAssignment": 12313, "forPayCheck": 12313}, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 2500, "onePercent18": "No", "socialCredits18": 2500, "onePercentLosAndes": "Si", "socialCreditsLosAndes": 2500, "othersLosAndes": 6789, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 2500, "healthLoan": 500, "nonFormulable": 0, "health": 0, "afp": 0, "onePercentAdjusted": 0, "healthUF": 2.78, "totalNonFormulable": 10000}}, {"_id": "fakeiD", "institutionalPatient": false, "enabled": true, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34.827, "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "1111111-1", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "Pensión por accidente de trabajo", "pensionType": "Pensión por orfandad", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "endDateOfTheoricalValidity": "2020-10-02T03:00:00.000Z", "pensionCodeId": "17153"}]
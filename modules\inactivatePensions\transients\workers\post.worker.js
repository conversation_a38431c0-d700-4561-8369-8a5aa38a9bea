const cronDescription = ' inactivate transients post worker';
const cronMark = 'INACTIVATE_TRANSIENTS_POST_WORKER';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'INACTIVATE_PENSION_DEATH';

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const logError = (logger, error) => {
  logger.error(`Error cron inactivar transitorias: ${error}`);
  return { message: `Error cron inactivar transitorias: ${error}` };
};

const workerFn = async ({ Logger, transientService, done, logService, job }) => {
  try {
    Logger.info(`${cronMark}:Inicio de ejecucion worker de inactivación Nº 3`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: process started`);
    const { inactivationError } = await transientService.createUpdateTransientPension();

    if (inactivationError) {
      Logger.error(`Error cron inactivar transitorias: ${inactivationError}`);
      throw new Error(`Error cron inactivar transitorias: ${inactivationError}`);
    }

    Logger.info(`Fin de ejecucion worker de inactivación Nº 3`);
    await logService.saveLog(cronMark);

    Logger.info(`${cronMark} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return logError(Logger, error);
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

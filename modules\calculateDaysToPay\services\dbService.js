const moment = require('moment');
const PensionModel = require('../../../models/pension');

const TRANSIENT = /^s[iíìîï]$/i;
const VALIDITY_TYPE = /^No\s+vigente$/i;
const RETIRED = /Jubilaci[oó]n/i;
const MARRIAGE = /Matrimonio/i;
const EXPIRATION = /Expiraci[oó]n de año de pago/i;
const DEATH = /Fallecimiento/i;

const isInMonth = (currentDate, dateToTest) => {
  if (dateToTest) {
    return currentDate.diff(moment(dateToTest).startOf('month'), 'months') === 0;
  }
  return false;
};

const composeValidations = (...props) => [...props].every(x => x);

const getInactivationDays = (currentDate, dateOfValidity, plusDay) => {
  const endDate = moment(dateOfValidity).startOf('day');
  const daysInMonth = currentDate.daysInMonth();
  const days = endDate.diff(currentDate, 'days') + plusDay;
  if (days < 0) return 0;
  if (days > daysInMonth) return daysInMonth;
  return days;
};

const casesToCalculate = {
  transient: data => (TRANSIENT.test(data.transient) ? data.totalTransitoryDaysToPay : NaN),
  inactRetirement: (data, currentDate) => {
    if (
      composeValidations(
        isInMonth(currentDate, data.evaluationDate),
        !VALIDITY_TYPE.test(data.validityType),
        RETIRED.test(data.inactivationReason),
        data.endDateOfValidity
      )
    ) {
      return getInactivationDays(currentDate, data.endDateOfValidity, 1);
    }
    return NaN;
  },
  inactMarriage: (data, currentDate) => {
    if (
      composeValidations(
        isInMonth(currentDate, data.evaluationDate),
        !VALIDITY_TYPE.test(data.validityType),
        MARRIAGE.test(data.inactivationReason),
        data.endDateOfValidity
      )
    ) {
      return getInactivationDays(currentDate, data.endDateOfValidity, 0);
    }
    return NaN;
  },
  inactWidow: (data, currentDate) => {
    if (
      composeValidations(EXPIRATION.test(data.inactivationReason), data.endDateOfTheoricalValidity)
    ) {
      return getInactivationDays(currentDate, data.endDateOfTheoricalValidity, 0);
    }
    return NaN;
  },
  inactManually: (data, currentDate) => {
    if (
      data.inactivateManually &&
      data.endDateOfValidity &&
      isInMonth(currentDate, data.endDateOfValidity)
    ) {
      return getInactivationDays(currentDate, data.endDateOfValidity, 0);
    }
    return NaN;
  },

  inactdeath: (data, currentDate) => {
    if (
      composeValidations(
        isInMonth(currentDate, data.inactivationDate),
        !VALIDITY_TYPE.test(data.validityType),
        DEATH.test(data.inactivationReason),
        data.deathDate,
        data.endDateOfValidity
      )
    ) {
      return getInactivationDays(currentDate, data.endDateOfValidity, 1);
    }
    return NaN;
  },
  totalDays: () => moment().daysInMonth()
};

const calculateAllCases = (data, currentDate) => {
  const daysToPayArr = Object.keys(casesToCalculate)
    .map(functionName => casesToCalculate[functionName](data, currentDate))
    .filter(num => !Number.isNaN(Number(num)));
  const daysToPay = Math.min(...daysToPayArr);
  return { ...data, daysToPay };
};

const service = {
  calculateDaysToPay: async (month, year) => {
    try {
      const session = await PensionModel.startSession();
      session.startTransaction();

      const currentDate = moment(new Date(year, month, 1));

      const pensions = await PensionModel.find({ enabled: true })
        .lean()
        .exec();
      const pensionsWithDaysToPay = pensions.map(pension =>
        calculateAllCases(pension, currentDate)
      );

      const bulk = PensionModel.collection.initializeOrderedBulkOp();

      pensionsWithDaysToPay.forEach(({ _id, ...pension }) => {
        bulk
          .find({
            _id
          })
          .update({ $set: { ...pension, updatedAt: new Date() } });
      });

      await bulk.execute();
      await session.commitTransaction();

      return {
        completed: true,
        error: null
      };
    } catch (error) {
      return {
        completed: false,
        error
      };
    }
  }
};

module.exports = { ...service };

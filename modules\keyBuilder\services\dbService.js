/* eslint-disable no-console */

const moment = require('moment');
/* eslint-disable no-restricted-syntax */
const NOT_VALID = /no vigente/i;
const cleanRut = (rut = '') => rut.replace(/[^0-9kK-]/g, '').toUpperCase();

const CAUSAL_TERM_MAPPER = [
  { regex: /Vigente hasta la jubilaci[óo]n/i, value: 'Jubilacion' },
  { regex: /Vigente vitalicia/i, value: 'Vitalicio' },
  { regex: /Vigente orfandad/i, value: 'Acreditación de estudios' }
];

const VALIDITY_MAPPER = [/Vigente vitalicia/i, /No vigente/i];

const SURVIVAL_MAPPER = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
  /Pensi[oó]n por orfandad/i,
  /Pensi[oó]n de orfandad de padre y madre/i
];

const DISABILITY_TYPE_MAPPER = [{ regex: /No inv[aá]lido/i, value: 0 }];

const ORPHANDHOOD_PENSIONS_MAPPER = [
  /Pensi[óo]n por orfandad/i,
  /Pensi[óo]n de orfandad de padre y madre/i
];

const WIDOWS_PENSIONS_MAPPER = [
  /Pensi[oó]n de viudez con hijos/i,
  /Pensi[oó]n de viudez sin hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
  /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i
];

const findInRegex = (array, toSearch, defaultValue = '') => {
  const result = array.find(({ regex }) => regex.test(toSearch));
  return result ? result.value : defaultValue;
};

const startingYear = 1970;

const calculateAge = birthday => {
  const ageDifMs = Date.now() - birthday.getTime();
  const ageDate = new Date(ageDifMs);
  return Math.abs(ageDate.getUTCFullYear() - startingYear);
};

const keysMapperPensions = (item, orphandhoodPensions) => {
  const {
    pensionType,
    gender,
    dateOfBirth,
    disabilityType,
    validityType,
    causant: { rut: causantRut },
    endDateOfValidity,
    familyGroup: pensionFamilyGroup
  } = item;

  const causalTerm = findInRegex(CAUSAL_TERM_MAPPER, validityType);
  const disabilityIndicator = findInRegex(DISABILITY_TYPE_MAPPER, disabilityType, 1);
  const age = calculateAge(dateOfBirth);

  if (
    SURVIVAL_MAPPER.some(pension => pension.test(pensionType)) &&
    age < 45 &&
    !VALIDITY_MAPPER.some(validity => validity.test(validityType))
  ) {
    if (ORPHANDHOOD_PENSIONS_MAPPER.some(pension => pension.test(pensionType))) {
      return {
        ...item,
        currentCapitalKeys: {
          F118: `${gender}Orfandad${age}`,
          F218: `${gender}Orfandad${age + 1}`,
          F124: `${gender}${age}OrfandadAcreditación de Estudios0`,
          F224: `${gender}${age + 1}OrfandadAcreditación de Estudios0`
        }
      };
    }
    if (WIDOWS_PENSIONS_MAPPER.some(pension => pension.test(pensionType))) {
      const matchedOrphandhood = orphandhoodPensions.filter(
        ({ causant, familyGroup }) =>
          cleanRut(causant.rut) === cleanRut(causantRut) && familyGroup === pensionFamilyGroup
      );
      const lowerAge = matchedOrphandhood.reduce((prev, current) => {
        return prev.dateOfBirth && moment(prev.dateOfBirth).diff(moment(current.dateOfBirth)) > 0
          ? prev
          : current;
      }, {});

      if (Object.keys(lowerAge).length) {
        const widowsAge18 = 18 - calculateAge(lowerAge.dateOfBirth) + age;
        const widowsAge24 = 24 - calculateAge(lowerAge.dateOfBirth) + age;

        return {
          ...item,
          currentCapitalKeys: {
            F118: `Orfandad${widowsAge18}Viuda${age}`,
            F218: `Orfandad${widowsAge18}Viuda${age + 1}`,
            F124: `Orfandad${widowsAge24}Viuda${age}`,
            F224: `Orfandad${widowsAge24}Viuda${age + 1}`
          }
        };
      }

      const ageEndOfValidity = moment().diff(endDateOfValidity, 'years') + age + 1;

      return {
        ...item,
        currentCapitalKeys: {
          F118: `Orfandad${ageEndOfValidity}Viuda${age}`,
          F218: `Orfandad${ageEndOfValidity}Viuda${age + 1}`,
          F124: `Orfandad${ageEndOfValidity}Viuda${age}`,
          F224: `Orfandad${ageEndOfValidity}Viuda${age + 1}`
        }
      };
    }
  }

  return {
    ...item,
    currentCapitalKeys: {
      F1: `${gender}${age}${causalTerm}${disabilityIndicator}`,
      F2: `${gender}${age + 1}${causalTerm}${disabilityIndicator}`
    }
  };
};

const service = {
  async generateKeys(pensionService) {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    try {
      const { result } = await pensionService.getAllAndFilter({
        validityType: { $not: NOT_VALID },
        enabled: true,
        $or: [
          { evaluationDate: { $exists: false } },
          {
            $or: [
              { evaluationDate: { $lt: new Date(currentYear, currentMonth, 1) } },
              { evaluationDate: { $gte: new Date(currentYear, currentMonth + 1, 1) } }
            ]
          }
        ]
      });

      const pensions = result.map(({ _doc: { ...pensionData } }) => pensionData);

      const orphandhoodPensions = pensions.filter(({ pensionType }) =>
        ORPHANDHOOD_PENSIONS_MAPPER.some(type => type.test(pensionType))
      );

      const processes = pensions.map(item => keysMapperPensions(item, orphandhoodPensions));

      const mappedPension = await Promise.all(processes);

      return { completed: true, err: false, result: mappedPension.map(({ _id, ...item }) => item) };
    } catch (error) {
      return { completed: false, err: error };
    }
  }
};

module.exports = { ...service };

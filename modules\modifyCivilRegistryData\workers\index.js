const tmp = require('tmp');
const workerModule = require('./worker');
const { Client, connectToSFTPServer } = require('../../sharedFiles/sftpClient');
const service = require('../services/dbService');
const pensionsService = require('../../pensions/services/pension.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const fileHelpers = require('../helpers/fileHelpers');
const sharedHelpers = require('../../sharedFiles/helpers');

const FOLDER_PATH = process.env.CIVIL_REGISTRATION_SFTP_OUTPUT_FOLDER;
const {
  CIVIL_REGISTRATION_SFTP_HOST,
  CIVIL_REGISTRATION_SFTP_USER,
  CIVIL_REGISTRATION_SFTP_PASS,
  CIVIL_REGISTRATION_SFTP_PORT
} = process.env;
const sftpCredentials = {
  host: CIVIL_REGISTRATION_SFTP_HOST,
  user: CIVIL_REGISTRATION_SFTP_USER,
  password: CIVIL_REGISTRATION_SFTP_PASS,
  port: CIVIL_REGISTRATION_SFTP_PORT
};

module.exports = {
  name: 'modifyCivilRegistryData',
  worker: deps => {
    const sftpClient = new Client();
    return workerModule.workerFn({
      sftpCredentials,
      sftpClient,
      connectToSFTPServer,
      service,
      pensionsService,
      logService,
      fileHelpers,
      FOLDER_PATH,
      sharedHelpers,
      tmp,
      ...deps
    });
  },
  description:
    'Actualizar data del pensionado con la que recibe del txt respuesta de Registro civil',
  endPoint: 'modifycivilregistrydata',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

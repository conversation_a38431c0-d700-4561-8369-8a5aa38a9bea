const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const validatePensioners = require('./tempPensionsValidations');
const pensionInput = require('../../../../resources/validationsTransfer.json');

describe('Doing some calculations for the end of date validity date ..', () => {
  beforeAll(beforeAllTests);

  it('Pension type condition to set end of date by widowhood ...', () => {
    const received = validatePensioners(pensionInput, 0, []);
    expect(received.length).toBe(1);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

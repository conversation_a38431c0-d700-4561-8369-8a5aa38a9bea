const mongoose = require('mongoose');

const { percentageDefined, valueDefined } = require('../modules/ipc/validator');

const { Schema } = mongoose;

const ipc = new Schema({
  date: { type: Date, required: true },
  value: { type: String, required: true, validate: valueDefined },
  percentage: { type: String, required: true, validate: percentageDefined },
  isLastPercentageChange: { type: Boolean, required: true }
});

ipc.index({ createdAt: 1 });

module.exports = mongoose.model('ipc', ipc);

{"getAllAndFilter": [{"_id": "5ed02f8c94bb95d801599460", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "NIÑO CANTANDO", "lastName": "JESUS", "mothersLastName": "AVENDAÑO", "address": ""}, "beneficiary": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "nonFormulable": 0}, "numberOfCharges": 0, "institutionalPatient": false, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": "No pago", "afp": "No pago"}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 266544, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 34.827, "dateOfBirth": "1968-05-31T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "ISAPRE", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por Enfermedad Profesional", "disabilityDegree": 50, "disabilityType": "Invalidez parcial", "resolutionNumber": 906, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1900-01-01T00:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "2010-01-01T03:00:00.000Z", "article40": 123, "createdAt": "2020-05-28T21:39:24.538Z", "updatedAt": "2020-05-28T21:39:24.538Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2033-05-30T04:00:00.000Z", "endDateOfValidity": "2020-05-20T04:00:00.000Z", "linkedDate": "2020-05-26T21:05:44.921Z"}, {"_id": "5ed02f8c94bb95d801599462", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "5674920-9", "name": "IGNACIO MIGUEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-7", "name": "NIÑO CANTANDO", "lastName": "JESUS", "mothersLastName": "AVENDAÑO", "address": ""}, "beneficiary": {"rut": "********-4", "name": "LUZ DEL CARMEN", "lastName": "PABLAZA", "mothersLastName": "VARGAS", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "nonFormulable": 0}, "numberOfCharges": 0, "institutionalPatient": false, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosAndes": 0, "healthLoan": 0, "nonFormulable": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 31, "enabled": true, "basePension": 266544, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 34.827, "dateOfBirth": "1968-05-31T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "ISAPRE", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por Enfermedad Profesional", "disabilityDegree": 50, "disabilityType": "Invalidez parcial", "resolutionNumber": 906, "accidentNumber": 5261396, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1900-01-01T00:00:00.000Z", "pensionCodeId": "23129", "pensionStartDate": "2010-01-01T03:00:00.000Z", "article40": 123, "createdAt": "2020-05-28T21:39:24.551Z", "updatedAt": "2020-05-28T21:39:24.551Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2033-05-30T04:00:00.000Z", "endDateOfValidity": "2033-05-30T04:00:00.000Z", "linkedDate": "2020-05-26T21:05:44.921Z"}, {"_id": "5ed02f8c94bb95d801599463", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": ""}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "nonFormulable": 0}, "numberOfCharges": 0, "institutionalPatient": false, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosAndes": 0, "healthLoan": 0, "nonFormulable": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 255564, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trayecto", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 1234, "createdAt": "2020-05-28T21:39:24.557Z", "updatedAt": "2020-05-28T21:39:24.557Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-05-26T21:05:44.922Z"}, {"_id": "5ed02f8c94bb95d801599464", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-7", "name": "LESLIE", "lastName": "PALOMINOS", "mothersLastName": "GONZALEZ"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": ""}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "nonFormulable": 0}, "numberOfCharges": 0, "institutionalPatient": false, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosAndes": 0, "healthLoan": 0, "nonFormulable": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 255564, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "No vigente", "pensionType": "Pensión por accidente de trayecto", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 1288639, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 12345, "createdAt": "2020-05-28T21:39:24.564Z", "updatedAt": "2020-05-28T21:39:24.564Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2037-08-10T04:00:00.000Z", "endDateOfValidity": "2020-02-02T00:00:00.000Z", "linkedDate": "2020-05-26T21:05:44.922Z", "deathDate": "2020-02-02T00:00:00.000Z", "inactivationReason": "Fallecimiento", "inactivationDate": "2020-05-26T21:18:02.380Z"}], "transientPensions": [{"beneficiary": {"rut": "********-7", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "email": ""}, "causant": {"rut": "********-7", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "accidentNumber": 6456159, "pensionCodeId": "12345"}, {"beneficiary": {"rut": "********-4", "name": "LUZ DEL CARMEN", "lastName": "PABLAZA", "mothersLastName": "VARGAS", "email": ""}, "causant": {"rut": "5674920-9", "name": "IGNACIO MIGUEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "accidentNumber": 5261396, "pensionCodeId": "123456"}, {"beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "causant": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "accidentNumber": 2960515, "pensionCodeId": "1234567"}, {"beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "causant": {"rut": "********-7", "name": "LESLIE", "lastName": "PALOMINOS", "mothersLastName": "GONZALEZ"}, "accidentNumber": 1288639, "pensionCodeId": "212345"}], "estimatedDaysOnCurrentMonth": [{"idSiniestro": 6456159, "daysToPay": 2}, {"idSiniestro": 5261396, "daysToPay": 2}, {"idSiniestro": 2960515, "daysToPay": 2}, {"idSiniestro": 1288639, "daysToPay": 16}], "previusMonthPensions": [{"_id": {"beneficiaryRut": "********-7", "causantRut": "********-7", "accidentNumber": 2960515}, "createdAt": "2020-04-26T21:05:36.431Z"}, {"_id": {"beneficiaryRut": "********-7", "causantRut": "********-7", "accidentNumber": 1288639}, "createdAt": "2020-04-26T21:18:01.337Z"}], "concatenatedArraysOfPensions": [{"accidentNumber": 6456159, "beneficiaryRut": "********-7", "causantRut": "********-7", "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0}, {"accidentNumber": 5261396, "beneficiaryRut": "********-4", "causantRut": "5674920-9", "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0}, {"accidentNumber": 2960515, "beneficiaryRut": "********-7", "causantRut": "********-7", "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0}, {"accidentNumber": 1288639, "beneficiaryRut": "********-7", "causantRut": "********-7", "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0}], "daysToPay": [{"beneficiary": {"rut": "********-7"}, "causant": {"rut": "********-7"}, "totalTransitoryDaysToPay": 2, "totalEstimatedDaysToPay": 2}, {"beneficiary": {"rut": "********-7"}, "causant": {"rut": "********-7"}, "totalTransitoryDaysToPay": 2, "totalEstimatedDaysToPay": 2}, {"beneficiary": {"rut": "********-7"}, "causant": {"rut": "********-7"}, "totalTransitoryDaysToPay": 2, "totalEstimatedDaysToPay": 2}, {"beneficiary": {"rut": "********-7"}, "causant": {"rut": "********-7"}, "totalTransitoryDaysToPay": 16, "totalEstimatedDaysToPay": 16}], "sinisterSapServiceData": [[{"fechaInicioReposo": "2019-07-15", "fechaAlta": "2019-07-15", "tipoAlta": 1, "idSiniestro": "0006456159"}, {"fechaInicioReposo": "2019-07-25", "fechaAlta": "2019-07-25", "tipoAlta": 1, "idSiniestro": "0006456159"}], [{"fechaInicioReposo": "2016-02-26", "fechaAlta": "2018-02-05", "tipoAlta": 2, "idSiniestro": "0005261396"}, {"fechaInicioReposo": "2018-02-06", "fechaAlta": "0001-01-01", "tipoAlta": 0, "idSiniestro": "0005261396"}], [{"fechaInicioReposo": "1999-01-11", "fechaAlta": "2000-06-30", "tipoAlta": 1, "idSiniestro": "0002960515"}, {"fechaInicioReposo": "2012-09-12", "fechaAlta": "2012-09-12", "tipoAlta": 2, "idSiniestro": "0002960515"}], [{"fechaInicioReposo": "2017-11-20", "fechaAlta": "2017-11-20", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2016-11-28", "fechaAlta": "2016-11-28", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2017-05-08", "fechaAlta": "2017-05-08", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2017-09-04", "fechaAlta": "2017-09-04", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2017-05-29", "fechaAlta": "2017-05-29", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2017-10-02", "fechaAlta": "2017-10-02", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2017-11-23", "fechaAlta": "2017-11-23", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2016-02-12", "fechaAlta": "2016-07-10", "tipoAlta": 3, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2013-10-27", "fechaAlta": "2015-04-26", "tipoAlta": 3, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2018-03-19", "fechaAlta": "2018-03-19", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2018-03-26", "fechaAlta": "2018-03-26", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2019-04-17", "fechaAlta": "2019-04-28", "tipoAlta": 2, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2018-04-16", "fechaAlta": "2019-04-16", "tipoAlta": 6, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2019-04-29", "fechaAlta": "2019-08-17", "tipoAlta": 3, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2019-09-03", "fechaAlta": "2019-09-03", "tipoAlta": 1, "idSiniestro": "0001288639"}, {"fechaInicioReposo": "2019-09-13", "fechaAlta": "2019-09-13", "tipoAlta": 1, "idSiniestro": "0001288639"}]], "appliedConditionsResult": [{"idSiniestro": 6456159, "estimatedDaysToPay": 0}, {"idSiniestro": 5261396, "estimatedDaysToPay": 31}, {"idSiniestro": 2960515, "estimatedDaysToPay": 0}, {"idSiniestro": 1288639, "estimatedDaysToPay": 0}], "estimatedDaysForPreviousMonth": [{"idSiniestro": 6456159, "daysToPay": 0}, {"idSiniestro": 5261396, "daysToPay": 0}, {"idSiniestro": 2960515, "daysToPay": 0}, {"idSiniestro": 1288639, "daysToPay": 0}]}
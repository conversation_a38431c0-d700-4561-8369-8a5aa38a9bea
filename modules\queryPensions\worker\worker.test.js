/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker for transfering pensioners', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let pensionService;
  beforeEach(() => {
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    pensionService = {
      createUpdatePension: jest.fn(() => Promise.resolve({ error: null }))
    };

    service = {
      transferPensionerData: jest.fn(() =>
        Promise.resolve({ pensionersToUpdate: [], error: null })
      ),
      deleteTemporalData: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };

    done = jest.fn();
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(pensionService.createUpdatePension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already executed at current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject(new Error()));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    pensionService.createUpdatePension = jest.fn(() => Promise.reject(new Error('Error service')));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(pensionService.createUpdatePension).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker dependency mark not founded', async () => {
    logService = {
      existsLog: jest
        .fn(() => Promise.resolve(false))
        .mockImplementationOnce(() => Promise.resolve(false))
        .mockImplementationOnce(() => Promise.resolve(false)),
      saveLog: jest.fn(() => Promise.resolve())
    };

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(2);
  });
  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const {
  areRutsUnique,
  mapperTemporaryIsapres,
  isValidTotalDiscount,
  isValidIsapre
} = require('./isaprePortalValidator');

const isapresResources = require('../../../../resources/isaprePortal.json');

describe('RUT validator tests', () => {
  it('should return true when there are not duplicated RUTs', () => {
    const uniqueRuts = areRutsUnique(isapresResources);
    expect(uniqueRuts).toBe(true);
  });

  it('should return false when there are duplicated RUTs', () => {
    const rut = '11111111-1';
    const sameRuts = isapresResources.map(resource => ({ ...resource, affiliateRut: rut }));
    const uniqueRuts = areRutsUnique(sameRuts);
    expect(uniqueRuts).toBe(false);
  });

  it('should return mapped data', () => {
    const mappedData = mapperTemporaryIsapres(isapresResources[0]);
    expect(mappedData.totalDiscount).toBeDefined();
    expect(mappedData.affiliateRut).toBeDefined();
    expect(mappedData.isapreId).toBeDefined();
  });

  it('should return true when a numeric value is given', () => {
    const numericValue = 23.25;
    const nonNumericValue = 'Non numeric';
    const validDiscount = isValidTotalDiscount({
      ...isapresResources[0],
      totalDiscount: numericValue
    });
    const invalidDiscount = isValidTotalDiscount({
      ...isapresResources[0],
      totalDiscount: nonNumericValue
    });
    expect(validDiscount).toBe(true);
    expect(invalidDiscount).toBe(false);
  });

  it('should return true when a value is given', () => {
    const isapre = 'Isapre';
    const blankIsapre = ' ';
    const emptyIsapre = '';

    const validIsapre = isValidIsapre(isapre);
    const invalidIsapre = isValidIsapre(blankIsapre);
    const anotherInvalidIsapre = isValidIsapre(emptyIsapre);

    expect(validIsapre).toBe(true);
    expect(invalidIsapre).toBe(false);
    expect(anotherInvalidIsapre).toBe(false);
  });
});

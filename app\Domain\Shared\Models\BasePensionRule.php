<?php

namespace App\Domain\Shared\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Modelo que representa las reglas de cálculo de pensión base
 * Equivalente a la colección basePensionRules del sistema anterior
 */
class BasePensionRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'pension_types',
        'min_amount',
        'max_amount',
        'adjustment_factor',
        'adjustment_type',
        'valid_from',
        'valid_to',
        'priority',
        'enabled',
        'conditions',
        'formula'
    ];

    protected $casts = [
        'pension_types' => 'array',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'enabled' => 'boolean',
        'conditions' => 'array',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'adjustment_factor' => 'decimal:4',
        'priority' => 'integer'
    ];

    // Scopes
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeActive($query)
    {
        return $query->where('valid_from', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('valid_to')
                          ->orWhere('valid_to', '>=', now());
                    });
    }

    // Business Logic
    public function appliesTo($pension): bool
    {
        // Verificar tipo de pensión
        if ($this->pension_types && !in_array($pension->pension_type, $this->pension_types)) {
            return false;
        }

        // Verificar rango de montos
        if ($this->min_amount && $pension->base_pension < $this->min_amount) {
            return false;
        }

        if ($this->max_amount && $pension->base_pension > $this->max_amount) {
            return false;
        }

        // Verificar condiciones adicionales
        if ($this->conditions) {
            return $this->evaluateConditions($pension);
        }

        return true;
    }

    private function evaluateConditions($pension): bool
    {
        foreach ($this->conditions as $condition) {
            $field = $condition['field'] ?? null;
            $operator = $condition['operator'] ?? '=';
            $value = $condition['value'] ?? null;

            if (!$field || $value === null) {
                continue;
            }

            $pensionValue = data_get($pension, $field);

            switch ($operator) {
                case '=':
                    if ($pensionValue != $value) return false;
                    break;
                case '>':
                    if ($pensionValue <= $value) return false;
                    break;
                case '<':
                    if ($pensionValue >= $value) return false;
                    break;
                case '>=':
                    if ($pensionValue < $value) return false;
                    break;
                case '<=':
                    if ($pensionValue > $value) return false;
                    break;
                case 'in':
                    if (!in_array($pensionValue, (array)$value)) return false;
                    break;
            }
        }

        return true;
    }
}

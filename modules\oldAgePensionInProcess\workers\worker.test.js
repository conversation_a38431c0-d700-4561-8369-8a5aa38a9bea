const workerModule = require('./worker');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('old-age-pension-in-process', () => {
  beforeAll(beforeAllTests);
  let service;
  let logService;
  let Logger;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      markPensionersForRetirement: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    logService = {
      existsLogAndRetry: jest.fn(() => true).mockImplementationOnce(() => ({ existsLog: false })),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.markPensionersForRetirement).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current year', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.markPensionersForRetirement).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.markPensionersForRetirement).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

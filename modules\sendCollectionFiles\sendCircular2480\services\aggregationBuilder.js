const buildAggregation = () => [
  {
    $match: {
      enabled: false,
      validityType: { $not: /No vigente/i }
    }
  },
  {
    $project: {
      beneficiary: 1,
      causant: 1,
      dateOfBirth: 1,
      gender: 1,
      disabilityType: 1,
      pensionType: 1,
      basePension: 1,
      article40: 1,
      law19403: 1,
      law19539: 1,
      law19953: 1
    }
  },
  {
    $group: {
      _id: { beneficiary: '$beneficiary.rut', causant: '$causant.rut' },
      last: { $last: '$$ROOT' }
    }
  },
  { $replaceRoot: { newRoot: '$last' } }
];

module.exports = { buildAggregation };

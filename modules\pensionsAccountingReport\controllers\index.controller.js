module.exports = ({ HttpStatus, Logger, dbService, reportService }) => {
  return {
    getReport: async (req, res) => {
      try {
        const fileName = 'reporte_de_conciliacion';
        const { data, error } = await dbService.getReportData();
        if (error) throw new Error(error);

        const { workbook, error: csvError } = await reportService.getWorkBook(data, fileName);
        if (csvError) throw new Error(csvError);

        res.header(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );
        res.header('Content-Disposition', `attachment; filename=${fileName}.xlsx`);

        workbook.xlsx.write(res).then(() => {
          res.status(HttpStatus.OK).end();
        });
      } catch (error) {
        Logger.error(`Current capital report error ${error}`);
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ error: true, message: error.message });
      }
    },

    existsData: async (req, res) => {
      try {
        const { doesDataExists, error } = await dbService.existsData();

        if (error) throw new Error(error);

        res.status(HttpStatus.OK).json({ doesDataExists });
      } catch (error) {
        Logger.error(`Current capital report error ${error}`);
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ error: true, message: error.message });
      }
    }
  };
};

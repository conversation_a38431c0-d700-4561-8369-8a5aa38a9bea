const DB_ERRORS = {
  CastError: {
    message: 'Error de validación',
    errors: ['there was validation error']
  },
  MongoError: {
    message: 'Registro duplicado',
    errors: ['There was a duplicate key error']
  },
  ValidationError: {
    message: 'Error de validación',
    errors: []
  }
};

const ERROR_EXCEPTION_CODE = {
  MongoError: 11000
};

const DEFAULT_MESSAGE = 'Internal Server Error';

const VALIDATION_MESSAGE = 'Error de validación';

const DEFAULT_ERROR = {
  message: DEFAULT_MESSAGE,
  errors: []
};

const ErrorNameCte = {
  DB_ERRORS,
  ERROR_EXCEPTION_CODE,
  VALIDATION_MESSAGE,
  DEFAULT_ERROR
};

module.exports = ErrorNameCte;

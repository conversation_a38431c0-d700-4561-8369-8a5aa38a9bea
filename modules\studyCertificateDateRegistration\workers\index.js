/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const familyAssignmentService = require('../../familyAssignment/services/assignment.service');

const workerModule = require('./worker');

module.exports = {
  worker: deps =>
    workerModule.workerFn({
      service,
      familyAssignmentService,
      logService,
      ...deps
    }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'inactivateOrReactivateAFCronsGroup',
  description: 'Registrar fechas de certificados de estudio',
  endPoint: 'registerstartandenddatestudycertificate',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

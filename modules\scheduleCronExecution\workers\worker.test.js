/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let logService;
  let Logger;
  let done;
  beforeEach(() => {
    service = {
      getJobListWithExecutionDate: jest.fn(() =>
        Promise.resolve({ jobListWithExecutionDate: true, errorJobListWithExecutionDate: false })
      ),
      scheduleJobs: jest.fn(() => Promise.resolve({ errorSchedulingJobs: false })),
      areAllJobsFinished: jest.fn(() =>
        Promise.resolve({ areAllJobsFinished: true, errorGettingMarks: null })
      )
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      service,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.getJobListWithExecutionDate).toBeCalled();
    expect(service.scheduleJobs).toBeCalled();
    expect(service.areAllJobsFinished).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      service,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.getJobListWithExecutionDate).not.toBeCalled();
    expect(service.scheduleJobs).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      service,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.getJobListWithExecutionDate).not.toBeCalled();
    expect(service.scheduleJobs).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('not all workers are finished', async () => {
    service.areAllJobsFinished = jest.fn(() =>
      Promise.resolve({ areAllJobsFinished: false, errorGettingMarks: null })
    );
    await workerModule.workerFn({
      service,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.getJobListWithExecutionDate).toBeCalled();
    expect(service.scheduleJobs).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(0);
  });

  it('areAllJobsFinished throws an error', async () => {
    service.areAllJobsFinished = jest.fn(() =>
      Promise.resolve({ areAllJobsFinished: false, errorGettingMarks: 'brand new Error' })
    );
    await workerModule.workerFn({
      service,
      Logger,
      logService,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.getJobListWithExecutionDate).toBeCalled();
    expect(service.scheduleJobs).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

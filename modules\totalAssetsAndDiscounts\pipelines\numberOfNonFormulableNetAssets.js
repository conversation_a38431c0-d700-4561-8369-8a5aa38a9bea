const countUniqueAssetsReason = require('./countUniqueKeyValue');

const count = pension => {
  const netAssetTypeRegex = /l[iíì<PERSON>]qu[íìïi]do/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const netAssets = assetsNonFormulable.filter(({ assetType }) =>
    netAssetTypeRegex.test(assetType)
  );
  const numberOfNetNonFormulableAssets = countUniqueAssetsReason(netAssets);
  return { ...pension, numberOfNetNonFormulableAssets };
};

module.exports = count;

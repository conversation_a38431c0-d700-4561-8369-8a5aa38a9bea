const { check } = require('express-validator');
const { regRule, emailMatchRule } = require('./validator');

const validators = [
  check('user.name')
    .notEmpty()
    .withMessage('name should not be empty')
    .isLength({ min: 1, max: 70 })
    .withMessage('max length should be 70')
    .matches(regRule)
    .withMessage('name should match the format'),
  check('user.email')
    .notEmpty()
    .withMessage('email should not be empty')
    .custom(value => emailMatchRule(value))
    .withMessage('role does not exist')
];

module.exports = validators;

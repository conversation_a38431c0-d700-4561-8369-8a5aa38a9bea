/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests, Logger } = require('../../../testsHelper');
const { marriage<PERSON>reworker } = require('.');

describe('Worker Innactivate Pensions by Marriage', () => {
  beforeAll(beforeAllTests);

  let service;
  let sftpClient;
  let connectToSFTPServer;
  let filesHelper;
  let done;
  let logService;
  let pensionService;

  beforeEach(() => {
    done = jest.fn();
    service = {};

    sftpClient = {
      Client: jest.fn()
    };
    filesHelper = {
      readSFTPFile: jest.fn(() => Promise.resolve({ lines: [{}], error: null }))
    };
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    pensionService = {
      createUpdatePension: jest.fn().mockResolvedValue({ error: false, completed: true })
    };
  });
  it('worker fail read file', async () => {
    filesHelper = {
      readFTPFile: jest.fn(() => Promise.resolve({ lines: [], error: true })),
      getCurrentYearAndMonth: jest.fn(() => ['2020', '02'])
    };

    service = {
      saveMarriagePensionsToInactivate: jest.fn(() => Promise.resolve())
    };

    await marriagePreworker.worker({
      Logger,
      filesHelper,
      sftpClient,
      connectToSFTPServer,
      service,
      done,
      logService
    });

    const { lines, error } = await filesHelper.readFTPFile();

    expect(lines).toStrictEqual([]);
    expect(error).toBe(true);
    expect(service.saveMarriagePensionsToInactivate).not.toHaveBeenCalled();
    expect(logService.saveLog).not.toHaveBeenCalled();
  });

  it('success worker marriage', async () => {
    filesHelper = {
      readSFTPFile: jest.fn(() =>
        Promise.resolve({ lines: [['126559275', '20200101']], error: false })
      ),
      getCurrentYearAndMonth: jest.fn(() => ['2020', '02'])
    };

    service = {
      saveMarriagePensionsToInactivate: jest.fn(() =>
        Promise.resolve({ completed: true, inactivationError: null })
      )
    };

    await marriagePreworker.worker({
      Logger,
      filesHelper,
      sftpClient,
      connectToSFTPServer,
      service,
      done,
      logService,
      pensionService
    });

    const { lines, error } = await filesHelper.readSFTPFile();
    const { completed, inactivationError } = await service.saveMarriagePensionsToInactivate();

    expect(lines).toStrictEqual([['126559275', '20200101']]);
    expect(error).toBe(false);
    expect(completed).toBe(true);
    expect(inactivationError).toBe(null);
    expect(logService.saveLog).toBeCalledTimes(1);
  });

  it('worker read file with empty result', async () => {
    filesHelper = {
      readSFTPFile: jest.fn(() => Promise.resolve({ lines: [], error: false })),
      getCurrentYearAndMonth: jest.fn(() => ['2020', '02'])
    };

    service = {
      saveMarriagePensionsToInactivate: jest.fn(() => Promise.resolve())
    };

    await marriagePreworker.worker({
      Logger,
      filesHelper,
      sftpClient,
      connectToSFTPServer,
      service,
      done,
      logService,
      pensionService
    });

    const { lines, error } = await filesHelper.readSFTPFile();

    expect(lines).toStrictEqual([]);
    expect(error).toBe(false);
    expect(service.saveMarriagePensionsToInactivate).not.toHaveBeenCalled();
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('throw error ', async () => {
    logService.existsLog = () => {
      throw Error('service error');
    };
    await marriagePreworker.worker({
      Logger,
      filesHelper,
      sftpClient,
      connectToSFTPServer,
      service,
      done,
      logService,
      pensionService
    });
    expect(Logger.error).toHaveBeenCalled();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });
  afterAll(afterAllTests);
});

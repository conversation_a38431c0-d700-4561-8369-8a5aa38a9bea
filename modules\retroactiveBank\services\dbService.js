/* eslint-disable consistent-return */
/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */
const moment = require('moment');
const PensionHistoricModel = require('../../../models/pensionHistoric');

const PENSION_TYPE = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];
const isNotValid = validityType => /No\s+vigente/i.test(validityType);
const isMatchPensionType = pensionType => PENSION_TYPE.some(regex => regex.test(pensionType));
const toLowerCase = (text = '') => text.toLowerCase();

const getReasonAndAmount = totalsByReason => {
  const savedArray = [];
  totalsByReason.forEach(item => {
    const isFounded = savedArray.findIndex(
      element => toLowerCase(element.reason) === toLowerCase(item.reason)
    );
    if (isFounded < 0) {
      savedArray.push({ reason: toLowerCase(item.reason), amount: item.amount });
      return;
    }
    savedArray[isFounded].reason = item.reason;
    savedArray[isFounded].amount += item.amount;
  });
  return savedArray;
};
const sumAllvaluesByAmount = array =>
  array.length
    ? array.reduce((accumulator, currentValue) => accumulator.amount + currentValue.amount)
    : 0;

const addReservedForPensionType = pensions => {
  let sumForArticle40 = 0;
  let sumForArticle41 = 0;
  let isValid = false;
  for (let i = 0; pensions[i] && isNotValid(pensions[i].validityType); i += 1) {
    isValid = true;
    const { forArticle40 = 0, forArticle41 = 0 } = pensions[i].reservedAmounts;
    sumForArticle41 = forArticle41 + sumForArticle41;
    sumForArticle40 = forArticle40 + sumForArticle40;
  }
  return {
    isValid,
    sumForArticle40: Math.round((sumForArticle40 + Number.EPSILON) * 100) / 100,
    sumForArticle41: Math.round((sumForArticle41 + Number.EPSILON) * 100) / 100
  };
};

const addReservedForRestPensions = async pensions => {
  let sumForBasePension = 0;
  let sumForBonuses = 0;
  let sumAssetsNonFormulableNetTotals;
  let sumAssetsNonFormulableTaxableTotals;
  let sumDiscountsNonFormulableTotals;

  let isValid = false;

  for (let i = 0; pensions[i] && isNotValid(pensions[i].validityType); i += 1) {
    isValid = true;
    const { reservedAmounts = {} } = pensions[i];
    const {
      forBonuses = 0,
      forBasePension = 0,
      assetsNonFormulableTaxableTotalsByReason = [],
      assetsNonFormulableNetTotalsByReason = [],
      discountsNonFormulableTotalsByReason = []
    } = reservedAmounts;

    sumForBonuses = forBonuses + sumForBonuses;
    sumForBasePension = forBasePension + sumForBasePension;

    sumAssetsNonFormulableNetTotals = getReasonAndAmount(assetsNonFormulableNetTotalsByReason);
    sumAssetsNonFormulableTaxableTotals = getReasonAndAmount(
      assetsNonFormulableTaxableTotalsByReason
    );
    sumDiscountsNonFormulableTotals = getReasonAndAmount(discountsNonFormulableTotalsByReason);
  }
  const forTotalNonFormulableDiscounts = sumAllvaluesByAmount(sumDiscountsNonFormulableTotals);
  const forNetTotalNonFormulableAssets = sumAllvaluesByAmount(sumAssetsNonFormulableNetTotals);
  const forTaxableTotalNonFormulableAssets = sumAllvaluesByAmount(
    sumAssetsNonFormulableTaxableTotals
  );
  return {
    sumAssetsNonFormulableNetTotals,
    sumAssetsNonFormulableTaxableTotals,
    sumDiscountsNonFormulableTotals,
    isValid,
    sumForBasePension: Math.round((sumForBasePension + Number.EPSILON) * 100) / 100,
    sumForBonuses: Math.round((sumForBonuses + Number.EPSILON) * 100) / 100,
    forTotalNonFormulableDiscounts,
    forNetTotalNonFormulableAssets,
    forTaxableTotalNonFormulableAssets
  };
};

const evaluateBackPensions = async pensions => {
  const calculatedPensions = [];

  for await (const {
    _doc: { _id, ...pension }
  } of pensions) {
    const {
      beneficiary: { rut: beneficiaryRut },
      causant: { rut: causantRut },
      pensionType,
      retroactiveAmounts
    } = pension;

    let newRetroactiveAmount = retroactiveAmounts;

    const backPensions = await PensionHistoricModel.aggregate([
      {
        $match: {
          enabled: false,
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          createdAt: {
            $lt: moment()
              .startOf('month')
              .toDate()
          }
        }
      },
      { $sort: { createdAt: -1 } },
      {
        $group: {
          _id: { month: { $month: '$createdAt' }, year: { $year: '$createdAt' } },
          date: { $first: '$createdAt' },
          data: { $first: '$$ROOT' }
        }
      },
      { $replaceRoot: { newRoot: '$data' } },
      { $sort: { createdAt: -1 } }
    ]);

    if (!backPensions.length) {
      return [];
    }

    if (isMatchPensionType(pensionType)) {
      const {
        isValid,
        sumForArticle40: forArticle40,
        sumForArticle41: forArticle41
      } = addReservedForPensionType(backPensions);

      if (!isValid) {
        return [];
      }

      newRetroactiveAmount = { ...newRetroactiveAmount, forArticle41, forArticle40 };
    }
    const {
      sumAssetsNonFormulableNetTotals,
      sumAssetsNonFormulableTaxableTotals,
      sumDiscountsNonFormulableTotals,
      isValid: isOk,
      sumForBasePension,
      sumForBonuses,
      forTotalNonFormulableDiscounts,
      forNetTotalNonFormulableAssets,
      forTaxableTotalNonFormulableAssets
    } = await addReservedForRestPensions(backPensions);

    if (!isOk) {
      return [];
    }

    calculatedPensions.push({
      ...pension,
      retroactiveAmounts: {
        ...newRetroactiveAmount,
        forNetTotalNonFormulableAssetsByReason: sumAssetsNonFormulableNetTotals,
        forTaxableTotalNonFormulableAssetsByReason: sumAssetsNonFormulableTaxableTotals,
        forTotalNonFormulableDiscountsByReason: sumDiscountsNonFormulableTotals,
        forBasePension: sumForBasePension,
        forBonuses: sumForBonuses,
        forNetTotalNonFormulableAssets,
        forTaxableTotalNonFormulableAssets,
        forTotalNonFormulableDiscounts
      }
    });
  }

  return calculatedPensions;
};

const service = {
  async retroactiveBankFile(pensionService) {
    try {
      const date = new Date();
      const firstDayofCurrentMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const lastDayofCurrentMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const { result: pensions } = await pensionService.getAllAndFilter({
        validityType: { $nin: [/No vigente/i] },
        enabled: true,
        reactivationDate: {
          $gte: firstDayofCurrentMonth,
          $lt: lastDayofCurrentMonth
        }
      });

      const evaluatedPensions = await evaluateBackPensions(pensions);
      const { completed, error } = await pensionService.updatePensions(evaluatedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service, evaluateBackPensions };

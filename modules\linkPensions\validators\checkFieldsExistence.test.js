const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const {
  requiredFieldsExistence,
  getField,
  sanitizeNumericalFields,
  sanitizeRuts,
  sanitizeDisabilityTypeField
} = require('./checkFieldsExistence');
const temporaryPensionerModel = require('../models/temporaryPensioner');
const pensions = require('../../../resources/pensions.json');

describe('Checking field existence Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(temporaryPensionerModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should get fields', () => {
    const temporaryData = pensions[0];
    const fields = getField('beneficiary.rut', temporaryData);
    const requiredFields = requiredFieldsExistence(temporaryData);
    expect(fields).toBe('19685004-K');
    expect(requiredFields).toBe(true);
  });

  it('should be a undefined an unexistence nested field', async () => {
    const temporaryData = pensions[0];
    const fields = getField('beneficiary.rut.enabled', temporaryData);
    const requiredFields = requiredFieldsExistence(temporaryData);
    expect(fields).toBeUndefined();
    expect(requiredFields).toBe(true);
  });

  it('should set default values for numerical values when they are null or empty', async () => {
    const numericalValues = {
      familyGroup: '',
      increasingInLaw19953: '',
      increasingInLaw19578: '',
      increasingInLaw20102: '',
      basePensionWithoutIncreases: 3
    };
    const {
      familyGroup,
      increasingInLaw19953,
      increasingInLaw19578,
      increasingInLaw20102,
      basePensionWithoutIncreases
    } = sanitizeNumericalFields(numericalValues);

    expect(familyGroup).toBe(1);
    expect(increasingInLaw19953).toBe(0);
    expect(increasingInLaw19578).toBe(0);
    expect(increasingInLaw20102).toBe(0);
    expect(basePensionWithoutIncreases).toBe(3);
  });

  it('should sanitize values for valid ruts ', async () => {
    const temporaryData = {
      beneficiary: { rut: '1.000.000-7' },
      causant: { rut: '10000007' },
      collector: { rut: ' 10000007 ' }
    };
    const sanitizedRut = sanitizeRuts(temporaryData);

    expect(sanitizedRut.beneficiary.rut).toBe('1000000-7');
    expect(sanitizedRut.causant.rut).toBe('1000000-7');
    expect(sanitizedRut.collector.rut).toBe('1000000-7');
  });

  it('should set values for disabilityType for total disability depending on pensionType', async () => {
    const disabilityValues = {
      disabilityType: '',
      pensionType: 'Pensión por accidente de trabajo'
    };
    const { disabilityType } = sanitizeDisabilityTypeField(disabilityValues);

    expect(disabilityType).toBe('Invalidez total');
  });

  it('should set values for disabilityType when pensionType is not a disability pension ', async () => {
    const disabilityValues = {
      disabilityType: '',
      pensionType: 'Pensión de viudez con hijos'
    };
    const { disabilityType } = sanitizeDisabilityTypeField(disabilityValues);

    expect(disabilityType).toBe('No invalida');
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

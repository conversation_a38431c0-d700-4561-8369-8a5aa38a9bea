[{"enabled": true, "pension": "5e32e3f7e16de8c95b6ec40e", "inactivationReason": "Por alta médica", "endDateOfValidity": "2018-10-19T03:00:00.000Z", "dateToInactivate": "2020-03-01T03:00:00.000Z", "createdAt": "2020-02-13T13:55:01.330Z", "updatedAt": "2020-02-13T13:55:01.330Z", "__v": 0}, {"enabled": false, "pension": "5e32e3f7e16de8c95b6ec40c", "inactivationReason": "Por alta médica", "endDateOfValidity": "1993-10-23T03:00:00.000Z", "dateToInactivate": "2020-03-01T03:00:00.000Z", "createdAt": "2020-02-13T13:55:01.336Z", "updatedAt": "2020-02-13T13:55:01.336Z", "__v": 0}]
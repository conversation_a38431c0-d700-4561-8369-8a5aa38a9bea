const mongoose = require('mongoose');
const paginate = require('../../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const ServipagSchema = new Schema(
  {
    id: { type: String, required: true },
    name: { type: String, required: true },
    city: { type: String, required: true },
    address: { type: String, required: true },
    code: { type: String, minlength: 3, maxlength: 3, required: true },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

ServipagSchema.plugin(paginate);
ServipagSchema.index({ id: 1 }, { unique: true });
ServipagSchema.index({ code: 1 }, { unique: true });
ServipagSchema.index({ name: 1 }, { unique: true, collation: { locale: 'es', strength: 1 } });

module.exports = mongoose.model('Servipag', ServipagSchema);

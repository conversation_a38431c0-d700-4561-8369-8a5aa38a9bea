/* eslint-disable no-console */
const mongoose = require('mongoose');
const { v4 } = require('uuid');

jest.mock('../lib/logger.js');
const beforeAllTests = async () => {
  const dbName = process.env.MONGO_URL.replace(/template-db-name/, `${v4()}`);
  await mongoose.connect(dbName, { useNewUrlParser: true, useUnifiedTopology: true }).catch(err => {
    if (err) {
      console.error(err);
      process.exit(1);
    }
  });
};

const afterAllTests = async () => {
  try {
    const collections = Object.keys(mongoose.connection.collections).map(
      name => mongoose.connection.collections[name]
    );
    await Promise.all(collections.map(collection => collection.deleteMany()));
    // Closing the DB connection allows Jest to exit successfully.
    await mongoose.connection.close();
  } catch (err) {
    console.error(err);
  }
};

const Logger = { info: jest.fn(), error: jest.fn() };

module.exports = { beforeAllTests, afterAllTests, Logger };

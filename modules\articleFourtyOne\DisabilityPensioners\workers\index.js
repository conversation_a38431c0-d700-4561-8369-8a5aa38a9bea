const workerModule = require('./worker');

const service = require('../services/dbService');
const pensionsService = require('../../../pensions/services/pension.service');
const logService = require('../../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'article-fourty-one',
  worker: deps =>
    workerModule.workerFn({
      service,
      pensionsService,
      logService,
      ...deps
    }),
  description: 'Actualizar por articulo 41',
  endPoint: 'calculatearticle41',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

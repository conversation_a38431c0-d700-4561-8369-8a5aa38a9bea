const util = require('util');
const { soap } = require('strong-soap');

const workerModule = require('./worker');
const service = require('../services/ufValue.service');
const fetchSoapData = require('../services/soapFetcher');
const { getLastDayOfMonth } = require('../../sharedFiles/helpers');

const client = util.promisify(soap.createClient);
const keys = ['GetSeriesResult', 'Series', 'fameSeries', '0', 'obs'];
const {
  USER_UF_SOAP_SERVICE,
  PASSWORD_UF_SOAP_SERVICE,
  SERIE_ID_UF_SOAP_SERVICE,
  URL_UF_SOAP_SERVICE,
  CRON_UFVALUE_FREQUENCY
} = process.env;

const logService = require('../../sharedFiles/services/jobLog.service');

const url = URL_UF_SOAP_SERVICE || 'https://si3.bcentral.cl/sietews/sietews.asmx?wsdl';
const requestArgs = {
  user: USER_UF_SOAP_SERVICE || '764587839',
  password: PASSWORD_UF_SOAP_SERVICE,
  seriesIds: { string: SERIE_ID_UF_SOAP_SERVICE || 'F073.UFF.PRE.Z.D' },
  firstDate: getLastDayOfMonth(),
  lastDate: getLastDayOfMonth()
};

module.exports = {
  name: 'obtainUfValue',
  worker: deps =>
    workerModule.workerFn({
      service,
      fetchSoapData,
      util,
      logService,
      client,
      requestArgs,
      url,
      keys,
      ...deps
    }),
  repeatInterval: CRON_UFVALUE_FREQUENCY,
  description: 'Obtener valor de la UF',
  endPoint: 'obtainufvalue',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

module.exports = ({
  HttpStatus,
  cajaLosAndesDiscountsService,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger,
  validationResult
}) => {
  const service = cajaLosAndesDiscountsService;
  return {
    getCurrentMonthYear: async (req, res) => {
      const { result } = await service.getCurrentMonthYear();
      Logger.info('Getting month and year for file validation');
      res.status(HttpStatus.OK).json({ result });
    },
    wasUploadedThisMonth: async (req, res) => {
      const { isError, error, result } = await service.wasUploadedThisMonth();
      Logger.info('Getting if it was uploaded the data of caja los andes');
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.OK).json({ error, isError });
      } else {
        res.status(HttpStatus.OK).json({ result });
      }
    },
    isActionAllowedOnCurrentDate: async (req, res) => {
      const { error, result } = await service.isActionAllowedOnCurrentDate();
      Logger.info('Checking if action is allowed on current date');
      if (error) {
        Logger.error(error);
        res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ error });
      } else {
        res.status(HttpStatus.OK).json({ result });
      }
    },

    processCajaLosAndesDiscounts: async (req, res) => {
      Logger.info('Bulk caja los andes discounts');

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(HttpStatus.OK).json({
          result: {
            errors: errors.array(),
            message: 'Hubo un problema realizando la validación de los datos.',
            completed: false
          },
          isError: true
        });
        return;
      }
      const {
        body: { cajaLosAndes }
      } = req;

      const { isError, error } = await service.insertMany(cajaLosAndes);
      const processResult = await service.processCajaLosAndesDiscounts();

      if (isError || processResult.isError) {
        Logger.error(error || processResult.error);
        res.status(401).json({
          result: {
            message: 'Hubo un problema realizando la operación.',
            completed: false
          },
          isError: true
        });
      } else {
        res.status(HttpStatus.OK).json({
          result: {
            message: 'El proceso se completó correctamente.',
            completed: true
          },
          isError: false
        });
      }
    }
  };
};

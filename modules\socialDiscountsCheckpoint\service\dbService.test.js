/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const LiquidationModel = require('../../../models/liquidation');
const service = require('./dbService');
const pensionsData = require('../../../resources/pensions.json');
const liquidations = require('../../../resources/liquidationsPayRoll.json');

describe('Verify checkpoint after calculate discounts', () => {
  beforeAll(beforeAllTests);

  const pensionOne = {
    ...pensionsData[0],
    discounts: {
      othersLosHeroes: 5000,
      othersLosAndes: 5000,
      socialCreditsLaAraucana: 5000,
      socialCredits18: 5000,
      socialCreditsLosHeroes: 5000,
      socialCreditsLosAndes: 5000
    }
  };
  const liquidationOne = {
    ...liquidations[0],
    pensionCodeId: pensionOne.pensionCodeId,
    taxablePension: 100000,
    netPension: 0,
    beneficiaryRut: pensionOne.beneficiary.rut,
    causantRut: pensionOne.causant.rut
  };

  const pensionTwo = {
    ...pensionsData[1],
    discounts: {
      othersLosHeroes: 0,
      othersLosAndes: 0,
      socialCreditsLaAraucana: 30000,
      socialCredits18: 0,
      socialCreditsLosHeroes: 0,
      socialCreditsLosAndes: 0
    }
  };
  const liquidationTwo = {
    ...liquidations[1],
    pensionCodeId: pensionTwo.pensionCodeId,
    netPension: 0,
    taxablePension: 100000,
    beneficiaryRut: pensionTwo.beneficiary.rut,
    causantRut: pensionTwo.causant.rut
  };

  it('should recalculate social discounts', async () => {
    await PensionModel.insertMany([pensionOne, pensionTwo]);
    await LiquidationModel.insertMany([liquidationOne, liquidationTwo]);
    const { completed, error } = await service.recalculateSocialDiscountsCheckpoint();
    const enabledPensions = await PensionModel.find({ enabled: true });
    const [updatedPensionOne, updatedPensionTwo] = enabledPensions;

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPensionOne.discounts.socialCreditsLaAraucana).toBe(4166.67);
    expect(updatedPensionTwo.discounts.socialCreditsLaAraucana).toBe(25000);
    expect(updatedPensionTwo.discounts.socialCredits18).toBe(0);
  });

  it('should not recalculate social discounts when total discounts is lesser than quarter of taxable pension', async () => {
    await PensionModel.create(pensionOne);
    await LiquidationModel.create({ ...liquidationOne, taxablePension: 200000 });
    const { completed, error } = await service.recalculateSocialDiscountsCheckpoint();
    const enabledPensions = await PensionModel.find();
    const updatedPension = enabledPensions[0];

    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(enabledPensions.length).toBe(1);
    expect(updatedPension.discounts.socialCreditsLaAraucana).toBe(5000);
  });

  afterEach(async () => {
    await PensionModel.deleteMany().catch(error => console.error(error));
    await LiquidationModel.deleteMany().catch(error => console.error(error));
  });

  afterAll(afterAllTests);
});

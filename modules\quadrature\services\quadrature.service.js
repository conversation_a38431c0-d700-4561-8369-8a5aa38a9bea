const FactorModel = require('../models/factor');
const ConcurrencyModel = require('../models/concurrency');

const factorCols = [
  { field: 'key', name: '<PERSON><PERSON><PERSON>' },
  { field: 'factor', name: 'factor' }
];

const concurrenciesCols = [
  { field: 'beneficiaryRut', name: 'Rut beneficiario' },
  { field: 'concurrencyPercentage', name: '% Concurrencias' },
  { field: 'concurrencyReceivable', name: 'concurr por cobrar' },
  { field: 'mutualPercentage', name: 'porcent mutual' },
  { field: 'istPercentage', name: 'porcent ist' },
  { field: 'islPercentage', name: 'porcent isl' }
];

const dataToCSV = ({ data, headers }) => {
  const rows = [];
  const header = headers.map(({ name }) => name).join(',');
  rows.push(header);
  const body = data.map(value => headers.map(({ field }) => value[field]).join(','));
  rows.push(...body);
  return rows.join('\n');
};

const services = {
  insertConcurrencies: async data => {
    try {
      await ConcurrencyModel.deleteMany({}).exec();
      const insertedConcurrencies = await ConcurrencyModel.insertMany(data);
      return { completed: !!insertedConcurrencies };
    } catch (error) {
      return { error };
    }
  },
  insertFactors: async data => {
    try {
      await FactorModel.deleteMany({}).exec();
      const insertedFactors = await FactorModel.insertMany(data);
      return { completed: !!insertedFactors };
    } catch (error) {
      return { error };
    }
  },
  wasDataUploaded: async ({ month, year }) => {
    try {
      const factors = !!(await FactorModel.estimatedDocumentCount());
      const concurrencies = !!(
        await ConcurrencyModel.find({
          createdAt: {
            $gte: new Date(+year, +month - 1, 1),
            $lt: new Date(+year, +month, 1)
          }
        })
      ).length;
      return { wasDataUploaded: { factors, concurrencies } };
    } catch (error) {
      return { wasDataUploaded: {}, error };
    }
  },
  generateFactorCSV: async () => {
    try {
      const data = await FactorModel.find({})
        .lean()
        .exec();

      const csvData = dataToCSV({ data, headers: factorCols });

      return { csvData };
    } catch (error) {
      return { csvData: '', error };
    }
  },
  generateConcurrencyCSV: async ({ month, year }) => {
    try {
      const data = await ConcurrencyModel.find({
        createdAt: {
          $gte: new Date(+year, +month - 1, 1),
          $lt: new Date(+year, +month, 1)
        }
      })
        .lean()
        .exec();

      const csvData = dataToCSV({ data, headers: concurrenciesCols });

      return { csvData };
    } catch (error) {
      return { csvData: '', error };
    }
  }
};

module.exports = { ...services };

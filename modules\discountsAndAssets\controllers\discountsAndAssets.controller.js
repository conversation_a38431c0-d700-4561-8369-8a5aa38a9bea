/* eslint-disable no-unused-vars */
const service = require('../services/dbService');

module.exports = ({ HttpStatus, Logger }) => {
  function manageError(res, error) {
    const { code = HttpStatus.INTERNAL_SERVER_ERROR, message = 'Internal server error' } = error;
    const statusCode = code === 11000 ? HttpStatus.ALREADY_EXIST : code;
    res.status(statusCode).json({ message });
  }

  return {
    update: async (req, res) => {
      const { user = {} } = req;
      const { body } = req;
      const { _id, ...data } = body;
      Logger.info('updating discountAndAsset: ');
      const { error, result } = await service.findOneAndUpdate({ _id, data, user });
      if (error) {
        Logger.error(`Error updating discount and asset: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json({ result, error });
      }
    },

    create: async (req, res) => {
      const { user = {} } = req;
      const { body } = req;
      const { _id, ...data } = body;
      Logger.info('creating discountAndAsset: ');
      const { error, result } = await service.create({ data, user });
      if (error) {
        Logger.error(`Error creating discount and asset: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json({ result, error });
      }
    },
    isReadyToUpdate: async (req, res) => {
      Logger.info('is Ready To Update discountAndAsset: ');
      const { error, result } = await service.isReadyToUpdate();
      if (error) {
        Logger.error(`Error getting response to update discount and asset: ${error}`);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json({ result, error });
      }
    },
    isReadyToUpdateNonFormulable: async (req, res) => {
      Logger.info('is Ready To Update discountAndAsset non formulable: ');
      const { error, result } = await service.isReadyToUpdateNonFormulable();
      if (error) {
        Logger.error(
          `Error getting response to update discount and asset non formulable: ${error}`
        );
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json({ result, error });
      }
    }
  };
};

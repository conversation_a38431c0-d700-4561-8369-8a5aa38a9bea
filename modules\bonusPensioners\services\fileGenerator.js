const moment = require('moment');
const { removeAccents } = require('../../sharedFiles/helpers');

const PAY_BONUS_OPTIONS = [/S[ií]/i, /N[oó]/i];

const formatTextBonus = (value = '', num = 0) =>
  removeAccents(value)
    .toUpperCase()
    .padEnd(num, ' ')
    .slice(0, num);

const formatNumberBonus = (value = '', num = 0) =>
  value
    .toString()
    .padStart(num, '0')
    .slice(0, num);

const isValidPayBonus = value => PAY_BONUS_OPTIONS.some(option => option.test(value));

const isNumeric = (value = '') => !Number.isNaN(+value);

const getTextLine = pension => {
  const result = [];
  result.push(formatNumberBonus(pension.pensionerRut, 8));
  result.push(formatTextBonus(pension.pensionerCheckDigit, 1));
  result.push(formatTextBonus(pension.pensionerName, 40));
  result.push(formatNumberBonus(moment(pension.dateOfBirth).format('YYYYMMDD'), 8));
  result.push(formatNumberBonus(pension.institutionCode, 2));
  result.push(formatNumberBonus(pension.institutionRut, 13));
  result.push(formatTextBonus(pension.institutionCheckDigit, 1));
  result.push(formatNumberBonus(moment(pension.pensionStartDate).format('YYYYMMDD'), 8));
  result.push(formatNumberBonus(pension.pensionAmount, 7));
  result.push(formatNumberBonus(pension.numberOfCharges, 2));
  result.push(formatNumberBonus(pension.pensionType, 1));
  result.push(formatTextBonus(pension.payBonus, 2));

  return result.join('');
};

const generateBonusFileContent = (pensionerBonusList = []) => {
  const textFile = [];
  pensionerBonusList.forEach(pension => textFile.push(getTextLine(pension)));

  return textFile.join('\n');
};

const downloadFileAsBase64 = (content = '') => {
  try {
    const fileString = Buffer.from(content).toString('base64');
    if (!fileString.length)
      return {
        error: {
          code: 404,
          message: 'No existen datos para descargar.'
        }
      };
    return { result: fileString, error: null };
  } catch (error) {
    return { result: null, error };
  }
};

const decodeBase64File = fileString => {
  try {
    const fileContent = Buffer.from(fileString, 'base64').toString();
    return { result: fileContent, error: null };
  } catch (error) {
    return { result: null, error };
  }
};

const readFileContent = fileContent => {
  const lines = [];
  const fileLines = fileContent.trimEnd().split('\n');

  fileLines.forEach(line => {
    lines.push({
      pensionerRut: line.slice(0, 8),
      pensionerCheckDigit: line.slice(9, 10),
      name: line.slice(11, 51),
      dateBirth: line.slice(52, 60),
      institutionCode: line.slice(61, 63),
      institutionRut: line.slice(64, 77),
      institutionCheckDigit: line.slice(78, 79),
      informationPARGP: line.slice(80, 81),
      informationPARNB: line.slice(82, 84),
      startDate: line.slice(85, 93),
      amountOtherPension: line.slice(94, 101),
      numberLoads: line.slice(102, 104),
      pensionType: line.slice(105, 106),
      otherAssets: line.slice(107, 114),
      payBonus: line.slice(115, 117)
    });
  });

  return lines;
};

const isEmpty = (value = '') => value.trim().length === 0;

const validatePensionerRut = pensionerRut => {
  if (isEmpty(pensionerRut))
    return { field: 'pensionerRut', message: 'El campo "Rut pensionado" está vacío' };

  if (!isNumeric(pensionerRut))
    return {
      field: 'pensionerRut',
      message: 'El campo "Rut pensionado" debe ser numérico'
    };

  return null;
};

const validatePayBonus = payBonus => {
  if (isEmpty(payBonus))
    return { field: 'payBonus', message: 'El campo "Paga Aguinaldo" está vacío' };

  if (!isValidPayBonus(payBonus))
    return {
      field: 'payBonus',
      message: 'El campo "Paga Aguinaldo" debe tener valor "SI" o "NO"'
    };

  return null;
};

const validateInstitutionCode = institutionCode => {
  if (isEmpty(institutionCode))
    return { field: 'institutionCode', message: 'El campo "Código de Institución" está vacío' };

  if (institutionCode.trim().length !== 2)
    return {
      field: 'institutionCode',
      message: 'El campo "Código de Institución" debe tener dos dígitos'
    };

  if (!isNumeric(institutionCode))
    return {
      field: 'institutionCode',
      message: 'El campo "Código de Institución" debe ser numérico'
    };

  return null;
};

const validateAmountOtherPension = amountOtherPension => {
  if (isEmpty(amountOtherPension))
    return { field: 'amountOtherPension', message: 'El campo "Monto otra pensión" está vacío' };

  if (!isNumeric(amountOtherPension))
    return {
      field: 'amountOtherPension',
      message: 'El campo "Monto otra pensión" debe ser numérico'
    };

  return null;
};

const validateFileLines = fileLines => {
  const errorLines = [];

  fileLines.forEach((line, index) => {
    const errorFields = [];
    const { pensionerRut, payBonus, institutionCode, amountOtherPension } = line;

    const pensionerRutError = validatePensionerRut(pensionerRut);
    if (pensionerRutError) errorFields.push(pensionerRutError);

    const payBonusError = validatePayBonus(payBonus);
    if (payBonusError) errorFields.push(payBonusError);

    const institutionCodeError = validateInstitutionCode(institutionCode);
    if (institutionCodeError) errorFields.push(institutionCodeError);

    const amountOtherPensionError = validateAmountOtherPension(amountOtherPension);
    if (amountOtherPensionError) errorFields.push(amountOtherPensionError);

    if (errorFields.length) errorLines.push({ lineNumber: index + 1, errorFields });
  });

  return errorLines;
};

module.exports = {
  generateBonusFileContent,
  downloadFileAsBase64,
  decodeBase64File,
  readFileContent,
  validateFileLines
};

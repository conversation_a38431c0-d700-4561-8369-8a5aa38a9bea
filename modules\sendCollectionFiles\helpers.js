const getPensionerRUT = pension => {
  const RUT_MAX_LENGTH = 9;
  const { beneficiary } = pension;
  const { rut } = beneficiary;
  let formatedRut = rut.split('-').join('');
  formatedRut = formatedRut.padStart(RUT_MAX_LENGTH, 0);
  return formatedRut;
};

const getPensionerLastName = pension => {
  const LAST_NAME_MAX_LENGTH = 20;
  const { beneficiary } = pension;
  const { lastName } = beneficiary;
  const formatedLastName = lastName.padEnd(LAST_NAME_MAX_LENGTH).substring(0, LAST_NAME_MAX_LENGTH);
  return formatedLastName;
};

const getPensionerMothersLastName = pension => {
  const MOTHER_LAST_NAME_MAX_LENGTH = 20;
  const { beneficiary } = pension;
  const { mothersLastName } = beneficiary;
  const formatedMothersLastName = mothersLastName
    .padEnd(MOTHER_LAST_NAME_MAX_LENGTH)
    .substring(0, MOTHER_LAST_NAME_MAX_LENGTH);
  return formatedMothersLastName;
};

const getPensionerName = pension => {
  const NAME_MAX_LENGTH = 30;
  const { beneficiary } = pension;
  const { name } = beneficiary;
  const formatedName = name.padEnd(NAME_MAX_LENGTH).substring(0, NAME_MAX_LENGTH);
  return formatedName;
};

module.exports = {
  getPensionerRUT,
  getPensionerLastName,
  getPensionerMothersLastName,
  getPensionerName
};

const { check, body } = require('express-validator');
const { isValidRut } = require('./rutValidator');
const {
  areRutsUnique,
  mapperTemporaryIsapres,
  isValidTotalDiscount,
  isValidIsapre
} = require('./isaprePortalValidator');

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const regRule = new RegExp(regex, 'i');

const codeRegex = /(\d{2})/;
const codeRule = new RegExp(codeRegex, 'i');
const isapreValidators = [
  check('isapre.name')
    .notEmpty()
    .withMessage('el nombre no debe estar vacío')
    .isLength({ min: 1, max: 255 })
    .withMessage('la longitud máxima debe ser 255')
    .matches(regRule)
    .withMessage('el nombre debe coincidir con el formato'),
  check('isapre.rut')
    .notEmpty()
    .withMessage('RUT no debe estar vacío')
    .isLength({ min: 8, max: 12 })
    .withMessage('la longitud máxima de RUT debe ser 12, incluidos los puntos y el guión')
    .custom(value => {
      if (!isValidRut(value)) {
        throw new Error('formato de RUT no válido');
      }
      return true;
    })
    .withMessage('RUT debe coincidir con el formato'),
  check('isapre.code')
    .notEmpty()
    .withMessage('el código no debe estar vacío')
    .isLength({ min: 2, max: 2 })
    .withMessage('la longitud mínima y máxima debe ser 2')
    .matches(codeRule)
    .withMessage('el código debe coincidir con el formato')
];
const isaprePortalValidators = [
  body('*.affiliateRut')
    .notEmpty()
    .withMessage("El campo 'rut' no debe estar vacío")
    .isLength({ min: 9, max: 10 })
    .withMessage("El campo 'rut' debe tener entre 9 y 10 caracteres, sin puntos y con guión")
    .custom(value => {
      if (!isValidRut(value)) {
        throw new Error('Rut inválido');
      }
      return true;
    })
    .withMessage('Formato de rut inválido'),
  body('*.isapre')
    .custom(value => isValidIsapre(value))
    .withMessage("El campo 'isapre' no debe estar vacío"),
  body('*.totalDiscount')
    .notEmpty()
    .withMessage("El campo 'total a descontar' no debe estar vacío"),
  body('*')
    .custom(value => {
      const temporaryIsapre = mapperTemporaryIsapres(value);
      if (!isValidTotalDiscount(temporaryIsapre))
        throw new Error("El campo 'total a descontar' debe ser numérico");
      return true;
    })
    .withMessage('El archivo tiene valores inválidos'),
  body().custom(value => {
    if (!areRutsUnique(value)) throw new Error('Existen ruts duplicados en el archivo');
    return true;
  })
];

module.exports = { isapreValidators, isaprePortalValidators };

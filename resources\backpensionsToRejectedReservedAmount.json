{"_id": "600759beb323852d19390899", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 10, "forDisability": 10, "forInstitutionalPatient": 10, "forRejection": 10, "forBasePension": 10, "forArticle40": 10, "forArticle41": 10, "forTaxableTotalNonFormulableAssets": 10, "forNetTotalNonFormulableAssets": 10, "forTotalNonFormulableDiscounts": 10, "forBonuses": 10, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 10, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 10, "marriageBonus": 10, "christmasBonus": 10, "nationalHolidaysBonus": 10, "winterBonus": 10, "nonFormulable": 10, "rebsal": 11.2, "adjustedHealthExemption": 10, "netTotalNonFormulable": 10, "taxableTotalNonFormulable": 200, "netNonFormulableByReason": [], "taxableNonFormulableByReason": [{"reason": "dos", "amount": 200}]}, "retroactiveAmounts": {"forSurvival": 10, "forDisability": 10, "forInstitutionalPatient": 10, "forRejection": 10, "forBonuses": 10, "forTaxableTotalNonFormulableAssets": 10, "forNetTotalNonFormulableAssets": 10, "forTotalNonFormulableDiscounts": 10, "forBasePension": 10, "forArticle40": 10, "forArticle41": 10, "forFamilyAssignment": 10, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 10, "apsResolutionDate": 10, "apsPaymentUniqueId": 10, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 10, "numberOfNonFormulableDiscounts": 10, "numberOfNetNonFormulableAssets": 10, "numberOfTaxableNonFormulableAssets": 1, "institutionalPatient": false, "fixedBasePension": 10, "fixedArticle40": 10, "fixedArticle41": 10, "daysToPay": 10, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 10, "increasingInLaw19953": 10, "increasingInLaw20102": 10, "basePensionWithoutIncreases": 10, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 10, "onePercent18": "No", "socialCredits18": 10, "onePercentLosAndes": "No", "socialCreditsLosAndes": 10, "othersLosAndes": 10, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 10, "healthLoan": 10, "nonFormulable": 10, "health": 10, "afp": 10, "onePercentAdjusted": 10, "totalNonFormulable": 10, "nonFormulableByReason": []}, "capitalStatus": "Nuevo", "totalCapital": 10, "rejectionHealthExemptionAmount": 10, "rejectionHealthReductionAmount": 10, "rejectionIPS": true, "law19403": 10, "law19539": 10, "law19953": 10, "enabled": true, "checkpoint": false, "taxablePension": 10, "netPension": 10, "basePension": 100, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2021-01-19T22:14:22.843Z", "updatedAt": "2021-01-19T22:14:22.843Z", "validatedStudyPeriod": "No", "article41": 10, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c", "matchedPensions": [{"_id": "5fd79dd39fbb0d1d6d23d78a", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 5, "numberOfNetNonFormulableAssets": 5, "numberOfTaxableNonFormulableAssets": 2, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 0, "increasingInLaw19953": 0, "increasingInLaw20102": 0, "basePensionWithoutIncreases": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": 21.04, "afp": 0, "onePercentAdjusted": 0}, "capitalStatus": "Nuevo", "totalCapital": 0, "enabled": true, "taxablePension": 0, "netPension": 0, "basePension": 250.621, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2020-10-14T17:16:03.438Z", "updatedAt": "2020-10-16T20:00:41.062Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c", "rejectionIPS": false}, {"_id": "5fda6769d10730505da0d779", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 400, "healthDiscount": "Si", "healthExemption": "Si", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 5, "numberOfNetNonFormulableAssets": 5, "numberOfTaxableNonFormulableAssets": 2, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 0, "increasingInLaw19953": 0, "increasingInLaw20102": 0, "basePensionWithoutIncreases": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": 21.04, "afp": 0, "onePercentAdjusted": 0}, "capitalStatus": "Nuevo", "totalCapital": 0, "rejectionHealthExemptionAmount": 0, "rejectionHealthReductionAmount": 0, "law19403": 0, "law19539": 0, "law19953": 0, "enabled": true, "taxablePension": 0, "netPension": 0, "basePension": 250.621, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2020-11-16T20:00:41.069Z", "updatedAt": "2020-11-17T15:33:34.030Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c", "rejectionIPS": true}, {"_id": "5fdb7a4e3fb54159f0edc493", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 0, "healthDiscount": "Si", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 11.2, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 200, "netNonFormulableByReason": [], "taxableNonFormulableByReason": [{"reason": "dos", "amount": 200}]}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 1, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 0, "increasingInLaw19953": 0, "increasingInLaw20102": 0, "basePensionWithoutIncreases": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": 11.2, "afp": 0, "onePercentAdjusted": 0, "totalNonFormulable": 0, "nonFormulableByReason": []}, "capitalStatus": "Nuevo", "totalCapital": 0, "rejectionHealthExemptionAmount": 0, "rejectionHealthReductionAmount": 0, "law19403": 0, "law19539": 0, "law19953": 0, "enabled": false, "taxablePension": 0, "netPension": 0, "basePension": 100, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2020-12-17T15:33:34.035Z", "updatedAt": "2021-01-06T14:46:45.889Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c", "rejectionIPS": true}, {"_id": "5ff5cd55042d206fc2ec9b0b", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "checkpoint": false, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 11.2, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 200, "netNonFormulableByReason": [], "taxableNonFormulableByReason": [{"reason": "dos", "amount": 200}]}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 1, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 0, "increasingInLaw19953": 0, "increasingInLaw20102": 0, "basePensionWithoutIncreases": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": 0, "afp": 0, "onePercentAdjusted": 0, "totalNonFormulable": 0, "nonFormulableByReason": []}, "capitalStatus": "Nuevo", "totalCapital": 0, "rejectionHealthExemptionAmount": 0, "rejectionHealthReductionAmount": 0, "rejectionIPS": true, "law19403": 0, "law19539": 0, "law19953": 0, "enabled": false, "taxablePension": 0, "netPension": 0, "basePension": 100, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2021-01-06T14:46:45.896Z", "updatedAt": "2021-01-19T22:14:22.822Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c"}, {"_id": "600b15e8346e1233d384519f", "paymentInfo": {}, "causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBonuses": 0, "assetsNonFormulableTaxableTotalsByReason": [], "assetsNonFormulableNetTotalsByReason": [], "discountsNonFormulableTotalsByReason": []}, "assets": {"aps": 0, "healthDiscount": "Si", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 11.2, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 200, "netNonFormulableByReason": [], "taxableNonFormulableByReason": [{"reason": "dos", "amount": 200}]}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": [], "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": []}, "currentCapitalCalculation": {}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "", "apsOrigin": ""}, "ChangeOfPensionTypeDueToCharges": false, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 1, "institutionalPatient": false, "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "familyGroup": 1, "increasingInLaw19578": 0, "increasingInLaw19953": 0, "increasingInLaw20102": 0, "basePensionWithoutIncreases": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": 0, "health": 11.2, "afp": 0, "onePercentAdjusted": 0, "totalNonFormulable": 0, "nonFormulableByReason": []}, "capitalStatus": "Nuevo", "totalCapital": 0, "rejectionHealthExemptionAmount": 0, "rejectionHealthReductionAmount": 0, "law19403": 0, "law19539": 0, "law19953": 0, "enabled": false, "taxablePension": 0, "netPension": 0, "basePension": 100, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON>", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2020-12-11T15:33:34.035Z", "updatedAt": "2021-01-06T14:46:45.889Z", "validatedStudyPeriod": "No", "article41": 0, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "reactivationDate": "2020-10-02T11:22:40.844Z", "discountsAndAssets": "5f99cb9bb04453b2b57f794c", "rejectionIPS": true}]}
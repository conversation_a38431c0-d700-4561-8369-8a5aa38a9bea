const moment = require('moment');
const { pipe } = require('../../sharedFiles/helpers');

const AGE_LIMIT = 45;

const updatePensionersGreaterOrEqualTo45Years = pensioner => {
  const { dateOfBirth } = pensioner;
  const age = moment().diff(new Date(dateOfBirth), 'years');
  if (age < AGE_LIMIT) return pensioner;
  const endDate = moment(new Date(dateOfBirth))
    .add(110, 'years')
    .toDate();
  return {
    ...pensioner,
    validityType: 'Vigente vitalicia',
    endDateOfTheoricalValidity: endDate,
    endDateOfValidity: endDate,
    reactivationDate: moment().toDate()
  };
};

const updatePensionerLessThan45Years = pensioner => {
  const { dateOfBirth } = pensioner;
  const age = moment().diff(new Date(dateOfBirth), 'years');
  if (age >= AGE_LIMIT) return pensioner;
  const endDate = new Date(
    moment()
      .endOf('year')
      .format()
  );

  return {
    ...pensioner,
    validityType: 'Vigente viudez',
    endDateOfTheoricalValidity: endDate,
    endDateOfValidity: endDate,
    reactivationDate: moment().toDate()
  };
};

const reactivate = obj =>
  pipe(updatePensionersGreaterOrEqualTo45Years, updatePensionerLessThan45Years)(obj);

module.exports = reactivate;

/* eslint-disable consistent-return */

const cronDependencyOrphanhood = 'REACTIVATE_TRANSIENT_PRE_WORKER';
const cronMarkOrphanhood = 'INACTIVATE_OR_REACTIVATE_ORPHANHOOD_PROCESS';
const cronDescriptionOrphanhood = 'inactivate or reactivate orphanhood:';
const alreadyExecutedMsg = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMarkOrphanhood} se completo correctamente`;
const notImportedMsg = 'No se ha importado el archivo de orfandad';
const getDependencyMessage = `No se ha ejecutado dependencia ${cronDependencyOrphanhood}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  done,
  logService,
  TemporaryHorphanhoodService,
  service,
  job
}) => {
  try {
    Logger.info('Inicio trabajo revisando importacion archivo de orfandad');

    const { existsLog, message } = await logService.existsLogAndRetry(cronMarkOrphanhood);
    if (existsLog) {
      Logger.info(alreadyExecutedMsg);
      return { message, status: 'UNAUTHORIZED' };
    }
    if (!(await TemporaryHorphanhoodService.horphanhoodFileAlreadyImported())) {
      Logger.info(notImportedMsg);
      return { message: notImportedMsg, status: 'NOTFOUND' };
    }

    Logger.info('inactivacion-reactivacion orfandad: revisando ejecucion dependencia');
    if (!(await logService.existsLog(cronDependencyOrphanhood))) {
      Logger.info(getDependencyMessage);
      return { message: getDependencyMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`Inicia proceso:  inactivación-Reactivación orfandades`);
    const { error } = await service.inactivationReactivationProcess();
    if (error) throw new Error(error);

    await logService.saveLog(cronMarkOrphanhood);
    Logger.info(`${cronDescriptionOrphanhood} proceso finalizado`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescriptionOrphanhood} ${error}`);
    await logService.retryLog(cronMarkOrphanhood);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescriptionOrphanhood} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMarkOrphanhood, cronDependencyOrphanhood, workerFn };

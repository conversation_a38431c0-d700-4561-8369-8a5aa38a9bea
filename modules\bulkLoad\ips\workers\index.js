const sftp = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const pensionService = require('../../../pensions/services/pension.service');
const ftpHelper = require('../../sharedFiles/ftpFileDownloader');
const { getParsedLinesFromIpsFiles } = require('../../sharedFiles/filesParser');
const workerModule = require('./worker');

const { IPS_FTP_HOST, IPS_FTP_USER, IPS_FTP_PASS, IPS_FTP_PORT } = process.env;
const ftpCredentials = {
  host: IPS_FTP_HOST,
  user: IPS_FTP_USER,
  password: IPS_FTP_PASS,
  port: IPS_FTP_PORT
};

module.exports = {
  name: 'ips-bulkLoad',
  worker: deps =>
    workerModule.workerFn({
      ftpCredentials,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      pensionService,
      service,
      logService,
      sftp,
      ...deps
    }),
  description: 'Carga masiva IPS',
  endPoint: 'ipsbulkload',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

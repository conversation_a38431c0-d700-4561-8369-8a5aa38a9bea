/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker payment dates Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let pensionsService;
  let Logger;
  let done;
  let logService;
  let fileHelpers;
  beforeEach(() => {
    done = jest.fn();
    service = {
      modifyCivilRegistryData: jest.fn(() => Promise.resolve([]))
    };
    pensionsService = {
      updatePensionsById: jest.fn(() => Promise.resolve({ error: false })),
      updatePensions: jest.fn(() => Promise.resolve({ error: false }))
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    fileHelpers = {
      downloadCivilRegistryFileFromSFTP: jest.fn(() => Promise.resolve({ file: 'File received' })),
      readSFTPFile: jest.fn(() => Promise.resolve({ lines: [{}] }))
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      service,
      pensionsService,
      logService,
      fileHelpers,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.modifyCivilRegistryData).toBeCalled();
    expect(pensionsService.updatePensionsById).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('should call save log when file is empty', async () => {
    fileHelpers.readSFTPFile = jest.fn(() => Promise.resolve({ lines: [] }));

    await workerModule.workerFn({
      Logger,
      service,
      pensionsService,
      logService,
      fileHelpers,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.modifyCivilRegistryData).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should return satus UNAUTHORIZED when has been already executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    const { status } = await workerModule.workerFn({ Logger, service, logService, done });

    expect(status).toBe('UNAUTHORIZED');
    expect(logService.existsLog).not.toBeCalled();
    expect(service.modifyCivilRegistryData).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should return satus UNAUTHORIZED when there is a missing dependency', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    const { status } = await workerModule.workerFn({ Logger, service, logService, done });

    expect(status).toBe('UNAUTHORIZED');
    expect(logService.existsLog).toBeCalled();
    expect(service.modifyCivilRegistryData).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should fail when there is an error', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      service,
      logService,
      done
    });

    expect(executionCompleted).toBe(undefined);
    expect(logService.existsLog).toBeCalled();
    expect(service.modifyCivilRegistryData).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

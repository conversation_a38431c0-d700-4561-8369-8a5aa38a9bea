/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
module.exports = ({ HttpStatus, listOfWorkers, Logger }) => {
  return {
    execute: async (req, res) => {
      try {
        const { cronName } = req.params;
        const done = () => true;
        const job = {
          repeatEvery: () => true,
          save: async () => true
        };
        const { functionToExecute: execute, dependencies: dependencyList } = listOfWorkers[
          cronName.toLowerCase()
        ];
        if (dependencyList) {
          for await (const dependency of dependencyList) {
            const dependencyFn = listOfWorkers[dependency.toLowerCase()].functionToExecute;
            const {
              executionCompleted,
              message = '',
              status = 'INTERNAL_SERVER_ERROR'
            } = await dependencyFn({
              Logger,
              done,
              job
            });
            const alreadyExecuted =
              message.includes('already executed') || message.includes('fue ejecutado');
            if (!executionCompleted && !alreadyExecuted)
              return res.status(HttpStatus[status]).json({
                executionCompleted: false,
                error: true,
                message
              });
          }
        }
        const { executionCompleted, message, status = 'INTERNAL_SERVER_ERROR' } = await execute({
          Logger,
          done,
          job
        });
        if (executionCompleted) {
          return res.status(HttpStatus.OK).json({ error: false, executionCompleted, message });
        }
        return res.status(HttpStatus[status]).json({
          executionCompleted: false,
          error: true,
          message
        });
      } catch (error) {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          executionCompleted: false,
          error: true,
          message: error.message
        });
      }
    }
  };
};

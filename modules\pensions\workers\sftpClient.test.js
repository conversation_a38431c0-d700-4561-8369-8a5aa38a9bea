/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const textTable = require('text-table');
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const uploadToSFTP = require('./sftpClient');
const result = require('../../../resources/pensions.json');
const mapperData = require('./mapper');

describe('Worker registro civil Test', () => {
  beforeAll(beforeAllTests);
  let sftpClient;
  let fs;
  let Logger;
  let connectToSFTPServer;
  beforeEach(() => {
    sftpClient = {
      uploadFrom: jest.fn(),
      close: jest.fn(),
      ftp: { verbose: false }
    };
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
    fs = {
      writeFile: jest.fn(() => Promise.resolve()),
      unlink: jest.fn(() => Promise.resolve())
    };
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
  });
  it('uploadftp with error conection ftp', async () => {
    connectToSFTPServer = Promise.reject(new Error('error conection ftp'));
    const fileName = `Achs_${moment().format('YYYYMM')}.txt`;
    await uploadToSFTP({ fileName, result, Logger, sftpClient, fs, mapperData, table: textTable });
    expect(connectToSFTPServer).rejects.toThrow('error conection ftp');
  });

  it('should return completed true when there are no errors', async () => {
    const fileName = `Achs_${moment().format('YYYYMM')}.txt`;
    const { completed } = await uploadToSFTP({
      fileName,
      result,
      Logger,
      sftpClient,
      connectToSFTPServer,
      fs,
      mapperData,
      table: textTable
    });
    expect(completed).toBe(true);
  });

  afterAll(afterAllTests);
});

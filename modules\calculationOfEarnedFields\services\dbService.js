const PensionModel = require('../../../models/pension');
const pensionService = require('../../pensions/services/pension.service');
const afpService = require('../../nomenclators/afp/services/index.service');

const SIN_AFILIACION = /Sin afiliaci[oó]n/i;

const nameAfpFormat = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[aáàâäãå]/gi, 'a')
    .replace(/[eéèêë]/gi, 'e')
    .replace(/[iíìîï]/gi, 'i')
    .replace(/[oóòôöõ]/gi, 'o')
    .replace(/[uúùûü]/gi, 'u')
    .toLowerCase()
    .trim();

const afpFilterByName = (afp, name) => {
  const specialCharactersValidation = new RegExp(nameAfpFormat(afp.name));
  return specialCharactersValidation.test(nameAfpFormat(name));
};

const service = {
  async getPensionsAllEnabled() {
    return PensionModel.aggregate([
      {
        $match: { enabled: true }
      },
      {
        $project: {
          beneficiary: 1,
          causant: 1,
          pensionType: 1,
          'retroactiveConstitution.totalPensionAccrued': 1,
          'retroactiveConstitution.indemnityDiscount': 1,
          'retroactiveConstitution.strennaRetroConstitution': 1,
          'retroactiveConstitution.otherLink': 1,
          'retroactiveConstitution.settlement': 1,
          'retroactiveConstitution.healthDiscountAccrued': 1,
          'retroactiveConstitution.afpDiscountAccrued': 1,
          afpAffiliation: 1,
          healthAffiliation: 1
        }
      }
    ]);
  },

  async calculateAccruedFields() {
    const pensioners = await this.getPensionsAllEnabled();
    const { result: afpsList } = await afpService.getAfpsWithFilters(
      { enabled: true },
      { name: 1, percentage: 1 }
    );
    return pensioners.map(pensioner => {
      const {
        retroactiveConstitution = {},
        afpAffiliation = 'Sin afiliacion',
        healthAffiliation = 'Sin afiliacion'
      } = pensioner;
      const {
        totalPensionAccrued = 0,
        indemnityDiscount = 0,
        strennaRetroConstitution = 0,
        otherLink = 0
      } = retroactiveConstitution;
      let afpDiscountAccrued = 0;
      let healthDiscountAccrued = 0;

      const afp = afpsList.find(x => afpFilterByName(x, afpAffiliation));

      if (healthAffiliation && !SIN_AFILIACION.test(healthAffiliation)) {
        healthDiscountAccrued = totalPensionAccrued * 0.07;
      }

      if (afpAffiliation && !SIN_AFILIACION.test(afpAffiliation) && afp) {
        const { percentage = 0 } = afp;
        afpDiscountAccrued = totalPensionAccrued * (percentage / 100);
      }

      const settlement =
        totalPensionAccrued -
        indemnityDiscount -
        healthDiscountAccrued -
        afpDiscountAccrued +
        strennaRetroConstitution +
        otherLink;

      return {
        ...pensioner,
        retroactiveConstitution: {
          ...retroactiveConstitution,
          totalPensionAccrued,
          indemnityDiscount,
          strennaRetroConstitution,
          otherLink,
          settlement: parseInt(settlement, 10),
          healthDiscountAccrued: parseInt(healthDiscountAccrued, 10),
          afpDiscountAccrued: parseInt(afpDiscountAccrued, 10)
        }
      };
    });
  },

  async setAccruedFields() {
    try {
      const pensioners = await this.calculateAccruedFields();
      const { completed, error } = await pensionService.updatePensionsById(pensioners);
      return { completed, error, pensioners };
    } catch (error) {
      return { completed: false, error, pensioners: [] };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');

const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./dbService');
const pensionService = require('../../pensions/services/pension.service');

describe('Set Reserved Amount for Institutional Patient', () => {
  beforeAll(beforeAllTests);

  it('should get pensions and set the reservedAmounts.forInstitutionalPatient field', async () => {
    const pension1 = {
      ...pensionData,
      pensionType: 'Pension por accidente de trabajo',
      validityType: 'vigente',
      enabled: true,
      reservedAmounts: {
        ...pensionData.reservedAmounts,
        forInstitutionalPatient: 100
      },
      institutionalPatient: false,
      createdAt: '2020-05-19'
    };
    const pension2 = {
      ...pension1,
      enabled: false,
      createdAt: '2020-03-25',
      institutionalPatient: true
    };
    const pension3 = {
      ...pension1,
      enabled: false,
      createdAt: '2020-03-19',
      institutionalPatient: true
    };
    const pension4 = {
      ...pension1,
      enabled: false,
      createdAt: '2020-02-19',
      institutionalPatient: false
    };
    const pension5 = {
      ...pension1,
      enabled: false,
      createdAt: '2020-04-19',
      institutionalPatient: true
    };
    const pension6 = {
      ...pension1,
      enabled: false,
      createdAt: '2020-01-19',
      institutionalPatient: true
    };

    await PensionModel.insertMany([pension1]).catch(e => console.error(e));

    await PensionHistoricModel.insertMany([
      pension2,
      pension3,
      pension4,
      pension5,
      pension6
    ]).catch(e => console.error(e));

    const { completed, error } = await service.setTotalRetroactiveAmounts(pensionService);
    const [updatedPension] = await PensionModel.find({ enabled: true });

    expect(error).toBe(null);
    expect(completed).toBe(true);

    expect(updatedPension.retroactiveAmounts.forInstitutionalPatient).toBe(200);
  });

  afterAll(afterAllTests);
});

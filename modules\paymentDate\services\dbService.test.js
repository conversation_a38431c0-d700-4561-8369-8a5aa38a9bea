/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PaymentDateModel = require('../models/paymentDate');

const service = require('./dbService');

describe('test service', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PaymentDateModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should create and save a single object into payment dates', async done => {
    const paymentDate = new Date(2021, 0, 1);
    const paymentList = [{ year: 2021, month: 1, paymentDate }];
    const { completed, error } = await service.createUpdatePaymentDates(paymentList);
    const docsPaymentDate = await PaymentDateModel.find({}).lean();
    expect(completed).toBe(true);
    expect(error).toBeUndefined();
    expect(docsPaymentDate.length).toBe(1);
    expect(docsPaymentDate[0].year).toBe(2021);
    expect(docsPaymentDate[0].month).toBe(1);
    expect(docsPaymentDate[0].paymentDate).toStrictEqual(paymentDate);
    done();
  });

  it('should create and save all payment dates for year 2021', async done => {
    const { completed, error } = await service.generatePaymentDates(2021);
    const docsPaymentDate = await PaymentDateModel.find({}).lean();
    expect(completed).toBe(true);
    expect(error).toBeUndefined();
    expect(docsPaymentDate.length).toBe(12);
    expect(docsPaymentDate[0].paymentDate).toStrictEqual(new Date(2021, 0, 27));
    expect(docsPaymentDate[1].paymentDate).toStrictEqual(new Date(2021, 1, 24));
    expect(docsPaymentDate[2].paymentDate).toStrictEqual(new Date(2021, 2, 26));
    expect(docsPaymentDate[3].paymentDate).toStrictEqual(new Date(2021, 3, 27));
    expect(docsPaymentDate[4].paymentDate).toStrictEqual(new Date(2021, 4, 26));
    expect(docsPaymentDate[5].paymentDate).toStrictEqual(new Date(2021, 5, 24));
    expect(docsPaymentDate[6].paymentDate).toStrictEqual(new Date(2021, 6, 28));
    expect(docsPaymentDate[7].paymentDate).toStrictEqual(new Date(2021, 7, 26));
    expect(docsPaymentDate[8].paymentDate).toStrictEqual(new Date(2021, 8, 16));
    expect(docsPaymentDate[9].paymentDate).toStrictEqual(new Date(2021, 9, 27));
    expect(docsPaymentDate[10].paymentDate).toStrictEqual(new Date(2021, 10, 25));
    expect(docsPaymentDate[11].paymentDate).toStrictEqual(new Date(2021, 11, 21));
    done();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PaymentDateModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

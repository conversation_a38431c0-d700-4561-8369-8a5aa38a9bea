/* eslint-disable no-unused-expressions */
const AfpsModel = require('../models/afp');

const service = {
  async updateAfp({ id, ...afpData }) {
    const criteria = { enabled: false, $or: [{ code: afpData.code }, { rut: afpData.rut }] };
    try {
      const oldAfp = await AfpsModel.findOne(criteria).exec();
      if (oldAfp && oldAfp.id) {
        await AfpsModel.remove({ id: oldAfp.id }).exec();
      }
      const data = await AfpsModel.findOneAndUpdate(
        { id, enabled: true },
        { $set: { ...afpData } },
        { returnNewDocument: true, upset: true, new: true }
      ).exec();
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async createAfp(afpData) {
    const criteria = { enabled: false, $or: [{ code: afpData.code }, { rut: afpData.rut }] };
    try {
      const result = await AfpsModel.findOne(criteria).exec();
      if (result) {
        const savedAfp = await AfpsModel.findOneAndUpdate(
          criteria,
          { ...afpData, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedAfp };
      }
      const data = await AfpsModel.create(afpData);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async deleteAfp(id) {
    try {
      const data = await AfpsModel.update(
        { id, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getAfps(query = { enabled: true }) {
    return AfpsModel.find(query)
      .lean()
      .then(data => ({ result: data.map(({ _id, __v, ...afp }) => ({ ...afp })) }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async getAfpsWithFilters(query, selectedFields) {
    return AfpsModel.find(query, selectedFields)
      .lean()
      .then(data => ({ result: data.map(({ _id, __v, ...afp }) => ({ ...afp })) }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

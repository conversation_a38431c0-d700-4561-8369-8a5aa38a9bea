/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const sapResponse = require('../../../../resources/sapRequest.json');
const incapacities = require('../../../../resources/incapacities.json');
const transientsHelper = require('./transientsHelper');

describe('Temporary Pensioner service Model Test', () => {
  beforeAll(beforeAllTests);

  it('fecha de alta in the range', () => {
    const reposes = sapResponse[0].reposos;
    reposes[0].fechaAlta = '2018-08-03T00:00:00';
    const { result } = transientsHelper.isAvailableToInactivate(reposes);

    expect(result).toBe(true);
  });

  it('isnt available to inactivate', () => {
    const { result } = transientsHelper.isAvailableToInactivate(sapResponse[2].reposos);

    expect(result).toBe(false);
  });

  it('is available to inactivate by higher alta than actually', () => {
    const { result } = transientsHelper.isAvailableToInactivate(sapResponse[3].reposos);

    expect(result).toBe(true);
  });

  it('sap available to inactivate', () => {
    const { result } = transientsHelper.isAvailableToInactivate(sapResponse[0].reposos);

    expect(result).toBe(true);
  });

  it('get inactivation json', () => {
    const date = new Date();
    const pension = transientsHelper.inactivateService({
      causantRut: 'fakeRut',
      beneficiaryRut: 'fakeRut',
      endDateOfValidity: date,
      reason: 'fake reason'
    });

    expect(pension.inactivationReason).toBe('fake reason');
    expect(pension.causantRut).toBe('fakeRut');
    expect(pension.beneficiaryRut).toBe('fakeRut');
    expect(pension.endDateOfValidity).toBe(date);
  });

  it('sap not available to inactivate by date higher than actually', () => {
    const reposes = sapResponse[0].reposos;
    const date = new Date();
    reposes[0].fechaInicioReposo = date.setUTCFullYear(date.getUTCFullYear() + 1);
    const { result } = transientsHelper.isAvailableToInactivate(reposes);
    expect(result).toBe(false);
  });

  it('fecha de alta isnt the range', () => {
    const reposes = sapResponse[0].reposos;
    const date = new Date();
    reposes[0].fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);
    const { result } = transientsHelper.isAvailableToInactivate(reposes);

    expect(result).toBe(false);
  });

  it('resolution date is in the range', () => {
    const result = incapacities[0].reduce(transientsHelper.getHigherResolution);

    expect(result.fechaResolucion).toBe('2020-01-01T00:00:00');
  });

  it('get incativation by Resolución definitiva  ', () => {
    const incapacity = incapacities[2];
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: incapacity,
      id: 'fakeid',
      fechaAlta
    });

    expect(result.reason).toBe('Resolución definitiva');
  });

  it('get null inactivation', () => {
    const incapacity = [
      {
        fechaResolucion: '0001-07-18T00:00:00',
        indicadorBorrado: 'null'
      },
      {
        fechaResolucion: '0001-07-18T00:00:00',
        indicadorBorrado: 'null'
      }
    ];
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: incapacity,
      id: 'fakeid',
      fechaAlta
    });

    expect(result).toBe(null);
  });

  it('get incativation by alta medica  ', () => {
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: null,
      id: 'fakeid',
      fechaAlta
    });

    expect(result.reason).toBe('Alta médica');
  });

  it('isnt inactivated by indicador borrado ', () => {
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.isInactivatedByResolution({
      incapacityData: { indicadorBorrado: true },
      fechaAlta
    });

    expect(result).toBe(null);
  });

  it('isnt inactivated by fecha resolución ', () => {
    const date = new Date();
    const fechaResolucion = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.isInactivatedByResolution({
      incapacityData: { indicadorBorrado: true, fechaResolucion },
      fechaAlta: date
    });

    expect(result).toBe(null);
  });

  it('get incativation by alta medica  and  wrong incapacities', () => {
    const incapacity = incapacities[1];
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() - 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: incapacity,
      id: 'fakeid',
      fechaAlta
    });

    expect(result.reason).toBe('Alta médica');
  });

  it('get null inactivation, wrong incapacities with alta medica higher than current year', () => {
    const incapacity = incapacities[1];
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: incapacity,
      id: 'fakeid',
      fechaAlta
    });

    expect(result).toBe(null);
  });

  it('get null inactivation, wrong incapacities withs alta medica higher than current year', () => {
    const incapacity = incapacities[4];
    const date = new Date();
    const fechaAlta = date.setUTCFullYear(date.getUTCFullYear() + 1);

    const result = transientsHelper.inactivatePension({
      incapacidad: incapacity,
      id: 'fakeid',
      fechaAlta
    });

    expect(result).toBe(null);
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');

describe('Social Discounts Checkpoint Worker Test', () => {
  beforeAll(beforeAllTests);

  let Logger;
  let done;
  let logService;
  let service;

  beforeEach(() => {
    done = jest.fn();
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = {
      recalculateSocialDiscountsCheckpoint: jest.fn().mockResolvedValue({ error: null }),
      recalculateNetPension: jest.fn().mockResolvedValue({ error: null })
    };
  });

  it('should execute worker correctly', async () => {
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      done,
      service,
      logService
    });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(service.recalculateSocialDiscountsCheckpoint).toHaveBeenCalled();
    expect(service.recalculateNetPension).toHaveBeenCalled();
    expect(executionCompleted).toBe(true);
  });

  it('should return if process was previously executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.recalculateSocialDiscountsCheckpoint).not.toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('should throw error if there is any in recalculateSocialDiscountsCheckpoint', async () => {
    service.recalculateSocialDiscountsCheckpoint = jest.fn().mockResolvedValue({ error: true });
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.recalculateSocialDiscountsCheckpoint).toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('should throw error if there is any in recalculateNetPension', async () => {
    service.recalculateNetPension = jest.fn().mockResolvedValue({ error: true });
    await workerModule.workerFn({ Logger, done, service, logService });
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.recalculateNetPension).toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

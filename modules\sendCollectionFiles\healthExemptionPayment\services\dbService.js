/* eslint-disable no-restricted-syntax */
const tmp = require('tmp');

const { buildAggregation } = require('./aggregationBuilder');
const { PensionModel } = require('../../../linkPensions/services/link.service');
const { getCurrentYearAndMonth } = require('../../../inactivatePensions/sharedFiles/filesHelper');

const { env } = process;
const { CIRCULAR_SFTP_OUTPUT_FOLDER } = env;
const sftpCredentials = {
  host: env.SFTP_HOST,
  port: env.SFTP_PORT,
  username: env.SFTP_USER,
  password: env.SFTP_PASS
};

const service = {
  async generateFile(fileGenerationUtils, Model = PensionModel) {
    const { getLine, getFileName, appendFile, util } = fileGenerationUtils;
    const [year, month] = getCurrentYearAndMonth();
    const aggregation = buildAggregation(year, month, 'assets.healthExemption');
    const cursor = Model.aggregate(aggregation).cursor();
    const fileName = getFileName();
    const append = util.promisify(appendFile);
    /* eslint-disable no-restricted-syntax */
    let fileCreated = false;
    for await (const doc of cursor) {
      const pensionsLines = [];
      const { matchedPensions } = doc;
      const { result } = matchedPensions;

      pensionsLines.push(getLine(doc));
      result.forEach(nestedPension => pensionsLines.push(getLine(nestedPension)));

      await append(fileName, pensionsLines.join('')).catch(error => {
        throw error;
      });
      fileCreated = true;
    }

    return fileCreated ? fileName : null;
  },

  async uploadFileToSftpServer({ fileName, fileGenerationUtils, Sftp }) {
    const { compressFile, getZipFileName } = fileGenerationUtils;
    const zipFileName = getZipFileName();
    const fileToUpload = compressFile(fileName, `${tmp.dirSync().name}/${zipFileName}`);
    const sftpClient = new Sftp.Client();
    const { connected, error } = await Sftp.connectToSFTPServer(sftpClient, sftpCredentials);
    if (!connected) throw new Error(error);
    await sftpClient.uploadFrom(fileToUpload, `${CIRCULAR_SFTP_OUTPUT_FOLDER}/${zipFileName}`);
    sftpClient.close();
    return fileToUpload;
  }
};

module.exports = service;

const mongoose = require('mongoose');

const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'POST_LIQUIDATION_CHECKPOINT_REPORT';
const dependencyMark = 'SOCIAL_DISCOUNTS_CHECK_POINT';
const missingDepMsg = `No se ha ejecutado la dependencia ${dependencyMark}`;
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const cronDescription = 'post liquidation checkpoint';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, logService, service, job }) => {
  try {
    Logger.info(`Cron execution start: ${cronMark}. Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDepMsg);
      return { message: missingDepMsg, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark} process started`);
    const { error, updatedFailedPensions } = await service.updateCheckPointPensions();
    if (error) throw new Error(error);
    if (updatedFailedPensions && updatedFailedPensions.length) {
      const { error: recalculateError } = await service.recalculateNetPension(
        updatedFailedPensions
      );
      if (recalculateError) throw new Error(recalculateError);
    }
    const reevaluatedPensions = await service.reevaluateCheckPointPensions();
    const { db } = mongoose.connection;
    if (reevaluatedPensions.length) {
      await db.collection('failedcheckpointpensions').insertMany(reevaluatedPensions);
    }

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark}: process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

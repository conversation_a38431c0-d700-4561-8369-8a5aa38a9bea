const Model = require('../models/views.model');
const utilsService = require('./utils.service');

const service = {
  ...utilsService,
  createView: async ({ view, ...viewData }) => {
    try {
      const result = await Model.findOne({ view, enabled: false }).exec();

      if (result) {
        const savedUser = await Model.findOneAndUpdate(
          { view, enabled: true },
          { ...viewData, enabled: true },
          { new: true, runValidators: true }
        ).exec();

        return { completed: !!savedUser };
      }

      await Model.create({ ...viewData, view, enabled: true });

      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  readViews: async () => {
    try {
      const viewDocs = await Model.find({ enabled: true })
        .lean()
        .exec();
      return { views: viewDocs };
    } catch (error) {
      return { error };
    }
  },

  updateView: async ({ view, ...viewData }) => {
    try {
      const oldView = await Model.findOne({ view, enabled: true }).exec();

      if (oldView && oldView.id) {
        await Model.deleteOne({ id: oldView.id }).exec();
      }
      await Model.findOneAndUpdate(
        { view, enabled: true },
        { $set: { ...viewData } },
        { returnNewDocument: true, upsert: true, new: true }
      ).exec();

      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  deleteView: async ({ view }) => {
    try {
      const deletedView = await Model.updateOne(
        { view, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { completed: !!deletedView };
    } catch (error) {
      return { error };
    }
  }
};

module.exports = { ...service, Model };

const VALIDITY_TYPE = /^No\s+vigente$/i;

const buildAggregation = (actualMonth, query) => [
  {
    $match: {
      validityType: { $not: VALIDITY_TYPE },
      ...query.match,
      enabled: true
    }
  },
  {
    $lookup: {
      from: 'pensionhistorics',
      let: {
        causantRut: '$causant.rut',
        beneficiaryRut: '$beneficiary.rut',
        pensionCode: '$pensionCodeId'
      },
      pipeline: [
        {
          $match: {
            enabled: false,
            createdAt: { $lt: actualMonth },
            $expr: {
              $and: [
                { $eq: ['$causant.rut', '$$causantRut'] },
                { $eq: ['$beneficiary.rut', '$$beneficiaryRut'] },
                { $eq: ['$pensionCodeId', '$$pensionCode'] }
              ]
            }
          }
        },
        {
          $group: {
            _id: {
              month: { $month: '$createdAt' },
              year: { $year: '$createdAt' }
            },
            date: { $first: '$createdAt' },
            resultGroup: { $first: '$$ROOT' }
          }
        },
        { $replaceRoot: { newRoot: '$resultGroup' } },
        { $sort: { createdAt: -1 } }
      ],
      as: 'matchedPensions'
    }
  },
  {
    $addFields: {
      matchedPensions: {
        $reduce: {
          input: '$matchedPensions',
          initialValue: {
            results: [],
            status: true
          },
          in: {
            $cond: [
              {
                $and: [
                  {
                    $or: [
                      { $eq: [`$$this.${query.targetField}`, 'Sí'] },
                      { $eq: [`$$this.${query.targetField}`, 'Si'] },
                      { $eq: [`$$this.${query.targetField}`, 'si'] },
                      { $eq: [`$$this.${query.targetField}`, 'sí'] }
                    ]
                  },
                  { $eq: ['$$value.status', true] }
                ]
              },
              {
                results: { $concatArrays: ['$$value.results', ['$$this']] },
                status: '$$value.status'
              },
              {
                results: '$$value.results',
                status: false
              }
            ]
          }
        }
      }
    }
  }
];

module.exports = { buildAggregation };

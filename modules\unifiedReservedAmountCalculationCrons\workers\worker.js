/* eslint-disable no-unused-expressions */

const cronDescription = 'calcular importes reservados';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = 'Proceso completado con éxito.';
const dependencyMark = 'POST_LIQUIDATION_CHECKPOINT_REPORT';
const cronMark = 'RESERVED_ASSETS_AND_DISCOUNTS_AMOUNT_CALCULATION';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const getMissingDependencyMessage = dep => `Dependencia "${dep}" aún no ejecutada`;

const workerFn = async ({
  job,
  Logger,
  done,
  logService,
  calculateReservedAmountDisabilityPension,
  calculateReservedAssetsAndDiscounts,
  setReservedAmountForInstitutionalPatient,
  setReservedAmountForSurvival
}) => {
  try {
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { message: getMissingDependencyMessage(dependencyMark) };
    }
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage,
        alreadyExecuted: true
      };
    }

    Logger.info(`${cronDescription} process started`);
    const {
      executionCompleted: amountDisabilityPensionCompleted,
      message: amountDisabilityPensionMessage,
      alreadyExecuted: amountDisabilityPensionAlreadyExecuted
    } = await calculateReservedAmountDisabilityPension({ Logger, done });
    const {
      executionCompleted: assetsAndDiscountsCompleted,
      message: assetsAndDiscountsMessage,
      alreadyExecuted: assetsAndDiscountsAlreadyExecuted
    } = await calculateReservedAssetsAndDiscounts({ Logger, done });
    const {
      executionCompleted: institutionalPatientCompleted,
      message: institutionalPatientMessage,
      alreadyExecuted: institutionalPatientAlreadyExecuted
    } = await setReservedAmountForInstitutionalPatient({ Logger, done });
    const {
      executionCompleted: reservedBySurvivalCompleted,
      message: reservedBySurvivalMessage,
      alreadyExecuted: reservedBySurvivalAlreadyExecuted
    } = await setReservedAmountForSurvival({ Logger, done });

    const messageOutput = {
      amountDisabilityPensionMessage,
      assetsAndDiscountsMessage,
      institutionalPatientMessage,
      reservedBySurvivalMessage
    };

    const allCompleted =
      (amountDisabilityPensionCompleted || amountDisabilityPensionAlreadyExecuted) &&
      (assetsAndDiscountsCompleted || assetsAndDiscountsAlreadyExecuted) &&
      (institutionalPatientCompleted || institutionalPatientAlreadyExecuted) &&
      (reservedBySurvivalCompleted || reservedBySurvivalAlreadyExecuted);

    if (!allCompleted) throw new Error(JSON.stringify(messageOutput));

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

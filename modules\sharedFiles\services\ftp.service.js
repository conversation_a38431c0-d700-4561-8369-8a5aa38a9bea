const fsClient = require('fs').promises;
const ftp = require('basic-ftp');

const service = {
  async getClientFTP() {
    return new ftp.Client();
  },

  async connectToFTPServer(client, host = '', user = '', password = '', port = '') {
    try {
      await client.access({
        host,
        user,
        password,
        port
      });
      return { connected: true, error: false };
    } catch (error) {
      return { connected: false, error };
    }
  },

  async uploadFileToFTP({
    fileName,
    folderPath,
    data,
    Logger,
    ftpCredentials: { host, user, password, port }
  }) {
    const client = await this.getClientFTP();
    if (!client) {
      return { completed: false, error: 'Cannot get client' };
    }
    client.ftp.verbose = true;
    const { connected, error } = await this.connectToFTPServer(client, host, user, password, port);
    if (!connected || error) {
      return { completed: false, error };
    }

    await fsClient.writeFile(`${__dirname}/${fileName}`, data).catch(err => Logger.error(err));

    try {
      await client.uploadFrom(`${__dirname}/${fileName}`, `${folderPath}/${fileName}`);
      return { completed: true };
    } catch (err) {
      return { completed: false, error: err };
    } finally {
      await fsClient.unlink(`${__dirname}/${fileName}`).catch(e => Logger.error(e));
      client.close();
    }
  }
};

module.exports = service;

const Model = require('../../../../models/user');
const utilsService = require('../../rolesAndPermissions/services/utils.service');

const service = {
  ...utilsService,
  async findByEmail(email) {
    const criteria = {
      email: { $regex: new RegExp(`^${email}$`), $options: 'i' },
      enabled: true
    };

    return Model.findOne(criteria).populate([
      {
        path: 'role',
        model: 'Role',
        select: 'roleName views.permission',
        populate: {
          path: 'views.view',
          model: 'View',
          select: 'view module viewNumber'
        }
      }
    ]);
  },
  async updateUser(id, userData) {
    const criteria = { enabled: false, $or: [{ email: userData.email }] };
    try {
      const oldUser = await Model.findOne(criteria).exec();
      if (oldUser && oldUser.id) {
        await Model.deleteOne({ id: oldUser.id }).exec();
      }
      await Model.findOneAndUpdate(
        { _id: id, enabled: true },
        { $set: { ...userData } },
        { returnNewDocument: true, upsert: true, new: true }
      ).exec();
      return { completed: true };
    } catch (error) {
      return { error };
    }
  },
  async createUser(user) {
    const criteria = { enabled: false, $or: [{ name: user.name }] };
    try {
      const result = await Model.findOne(criteria).exec();
      if (result) {
        const savedUser = await Model.findOneAndUpdate(
          criteria,
          { ...user, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedUser };
      }
      const data = await Model.create(user);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async deleteUser(id) {
    try {
      const data = await Model.updateOne(
        { _id: id, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getUsers(query = { enabled: true }) {
    return Model.find(query)
      .lean()
      .then(data => ({ result: data.map(({ __v, ...user }) => ({ ...user })) }))
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service, Model };

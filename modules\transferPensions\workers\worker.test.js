/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    service = {
      transferPensions: jest.fn(() => Promise.resolve({ isError: false, error: null }))
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('should execute successfuly', async () => {
    const { executionCompleted } = await workerModule.workerFn({
      Logger,
      service,
      logService,
      done
    });

    expect(executionCompleted).toBe(true);
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.transferPensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('should return when has been already executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    const { status } = await workerModule.workerFn({ Logger, service, logService, done });

    expect(status).toBe('UNAUTHORIZED');
    expect(Logger.info).toHaveBeenCalledTimes(1);
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.transferPensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should return an error message when an error is thrown', async () => {
    service.transferPensions = jest.fn(() => Promise.reject());

    await workerModule.workerFn({ Logger, service, logService, done });

    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

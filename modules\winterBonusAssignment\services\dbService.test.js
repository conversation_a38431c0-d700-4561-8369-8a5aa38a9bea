const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const resourcePensions = require('../../../resources/pensionsWinterBonus.json');
const resourceRules = require('../../../resources/basePensionRules.json');

describe('Set winter bonus service test', () => {
  beforeAll(beforeAllTests);

  it('get rules for calculation', async () => {
    service.getRules = jest.fn(() => Promise.resolve(resourceRules));
    service.filterPensionersWinterBonus = jest.fn(() => Promise.resolve(resourcePensions));

    const { completed, pensioners } = await service.setWinterBonus();
    expect(pensioners.length).toBe(3);
    expect(completed).toBe(true);
    expect(pensioners[0].payBonus).toBe('Si');
    expect(pensioners[0].assets.winterBonus).toBe(1000000);
    expect(pensioners[1].payBonus).toBe('No');
    expect(pensioners[1].assets.winterBonus).toBe(0);
    expect(pensioners[2].payBonus).toBe('No');
    expect(pensioners[2].assets.winterBonus).toBe(0);
  });

  afterAll(afterAllTests);
});

const Model = require('../../../nomenclators/motive/models/motive');
const { escapeChars } = require('../../../../lib/regex-utils');

const service = {
  readMotives: async () => {
    try {
      const motivesDocs = await Model.find({ enabled: true })
        .lean()
        .exec();
      return { motives: motivesDocs };
    } catch (error) {
      return { error };
    }
  },
  bulkDefaultMotives: async arr => {
    const bulks = Model.collection.initializeUnorderedBulkOp();

    arr.forEach(elem => {
      const criteria = {
        motive: { $regex: new RegExp(escapeChars(elem.motive), 'i') },
        option: elem.option
      };

      bulks
        .find(criteria)
        .upsert()
        .replaceOne({
          ...elem
        });
    });

    return bulks.execute();
  }
};

module.exports = { ...service, Model };

const { roundValue } = require('../../sharedFiles/helpers');

const VALIDITY_TYPE = /^No\s+vigente$/i;

const PENSION_TYPES = [
  /Pensi[óo]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+sin\s+hijos/i,
  /Pensi[óo]n\s+por\s+orfandad/i,
  /Pensi[óo]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];

const getSurvivalPensions = async pensionService => {
  const queryObj = {
    pensionType: { $in: PENSION_TYPES },
    validityType: VALIDITY_TYPE,
    enabled: true
  };
  const { result, error } = await pensionService.getPensionsWithLiquidation(queryObj);

  if (error) throw new Error(error);

  return result;
};

const getMappedPensions = pensions => {
  return pensions.map(({ liquidation, ...item }) => ({
    ...item,
    reservedAmounts: {
      ...item.reservedAmounts,
      forSurvival: roundValue(liquidation.taxablePension) || 0
    }
  }));
};

const service = {
  async calculateReservedAmountsBySurvival(pensionService) {
    try {
      const pensions = await getSurvivalPensions(pensionService);
      const mappedPensions = getMappedPensions(pensions);
      const { completed, error } = await pensionService.updatePensions(mappedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = {
  ...service
};

/* eslint-disable no-underscore-dangle */
const SftpClient = require('ssh2-sftp-client');

class Client extends SftpClient {
  async downloadTo(remoteFile, localFile) {
    return this.fastGet(remoteFile, localFile);
  }

  async downloadToSpecial(remoteFile, localFile, options) {
    return this.get(remoteFile, localFile, options);
  }

  async _downloadToFile(localFile, remoteFile) {
    return this.fastGet(remoteFile, localFile);
  }

  async uploadFrom(localFile, remoteFile) {
    return this.fastPut(localFile, remoteFile);
  }

  close() {
    return this.end();
  }
}

const Sftp = {
  Client,
  async connectToSFTPServer(client, credentials) {
    try {
      await client.connect({ ...credentials });
      return { connected: true, error: false };
    } catch (error) {
      return { connected: false, error };
    }
  }
};

module.exports = Sftp;

const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');

const paths = [
  'discounts.afp',
  'discounts.health',
  'discounts.healthLoan',
  'discounts.othersLosAndes',
  'discounts.othersLosHeroes',
  'discounts.totalNonFormulable',
  'discounts.onePercentAdjusted',
  'retroactiveAmounts.forTotalNonFormulableDiscounts',
  'discounts.socialCredits18',
  'discounts.socialCreditsLaAraucana',
  'discounts.socialCreditsLosHeroes',
  'discounts.socialCreditsLosAndes'
];

const calculate = pension => ({
  ...pension,
  liquidation: {
    ...pension.liquidation,
    totalDiscounts: roundValue(recursiveSum(pension, paths))
  }
});

module.exports = calculate;

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      generateKeys: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {
      createUpdatePension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(pensionService.createUpdatePension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject(new Error('erro')));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail generateKeys', async () => {
    service.generateKeys = jest.fn(() => Promise.resolve({ err: 'error' }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(service.generateKeys).toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail createUpdatePension', async () => {
    pensionService.createUpdatePension = jest.fn(() => Promise.resolve({ error: 'error' }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(pensionService.createUpdatePension).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependencies have not yet run', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(service.generateKeys).not.toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('mark has run in this month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.generateKeys).not.toBeCalled();
    expect(pensionService.createUpdatePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  afterAll(afterAllTests);
});

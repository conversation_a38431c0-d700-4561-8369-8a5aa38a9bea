/* eslint-disable consistent-return */

const cronMark = 'INACTIVATE_RETIREMENT_POST_WORKER';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const cronDescription = 'post worker de inactivación por jubilación No. 5:';
const dependencyMark = 'DAYS_OF_TRANSIENT_PENSION';

const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia ${dep}`;

const logError = (logger, error) => {
  logger.error(`Error cron inactivatePensionsByRetirementPostWorker: ${error}, Nº 5`);
  return {
    message: `Error cron inactivatePensionsByRetirementPostWorker: ${error}`
  };
};
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const workerFn = async ({ Logger, service, done, logService, job }) => {
  try {
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }
    Logger.info(`Inicio de ejecucion worker de inactivatePensionsByRetirementPostWorker Nº 5`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage
      };
    }

    Logger.info(`${cronDescription}: process started`);
    const { inactivationError } = await service.inactivatePensionsByRetirement();

    if (inactivationError) {
      Logger.error(
        `Error cron inactivatePensionsByRetirementPostWorker: ${inactivationError}, Nº 5`
      );
      throw new Error(`Error cron inactivatePensionsByRetirementPostWorker: ${inactivationError}`);
    }

    Logger.info(`Fin ejecucion worker de inactivatePensionsByRetirementPostWorker Nº 5`);
    await logService.saveLog(cronMark);

    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return logError(Logger, error);
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./dbService');
const taxablePensions = require('../../../resources/taxablePension.json');
const ProcessedJobModel = require('../../sharedFiles/models/processedJob');

describe('taxable pension Test', () => {
  beforeAll(beforeAllTests);

  let liquidationService;
  beforeEach(() => {
    liquidationService = {
      createUpdateLiquidation: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('calculate taxable pension', async () => {
    const result = service.calculateTaxablePension(taxablePensions[0]._doc);
    expect(result.taxablePension).toBe(200084);
  });

  it('calculate taxable pension without fields', async () => {
    const result = service.calculateTaxablePension(taxablePensions[4]._doc);
    expect(result.taxablePension).toBe(0);
  });

  it('calculate taxable pension with dot', async () => {
    const result = service.calculateTaxablePension(taxablePensions[3]._doc);
    expect(result.taxablePension).toBe(244454.06);
  });

  it('processed taxable pensions success', async () => {
    const result = await service.taxablePension(taxablePensions, liquidationService);
    expect(result.completed).toBe(true);
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

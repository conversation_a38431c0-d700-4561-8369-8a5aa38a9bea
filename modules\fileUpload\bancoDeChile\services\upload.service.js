// const puppeteer = require('puppeteer');
const moment = require('moment');

const { BANCOCHILE_LOGIN_PAGE_URL, BANCOCHILE_USER, BANCOCHILE_PASSWORD } = process.env;

const ENTER_KEY = 'Enter';
const TIME_INTERVAL_BEFORE_NEXT_SELECTION = 5000;
const TIME_INTERVAL_BEFORE_PRESSING_ENTER_KEY = 1000;
const userNameInputField = 'input#iduserName';
const passwordField = 'input[type=password]';
const loginButton = 'button#idIngresar';
const paymentsAndTranferenceTab = 'a#nivel1-2000';
const agreementSelectField = 'div[name=convenioSelect] span.ng-binding.ng-scope';
const paymentTypeSelectField = 'div[name=tipoPagoSelect] span.ng-binding.ng-scope';
const templateTypeSelectField = 'div[name=tipoPlantillaSelect] span.ng-binding.ng-scope';
const payrollNameInputField = 'input[name=nombreNominaInput]';
const fileInputField = 'input[type=file]';
const addPayrollButton = "button[ng-click='agregarNomina.confirmarNomina()']";
const massivePayHover = 'li#nivel2-2400';
const addPayroll = '[ui-sref="portalProveedores.agregarNomina"]';
const uploadFile = async ({ puppeteer, fileToUpload }) => {
  const browser = await puppeteer.launch({
    headless: true,
    defaultViewport: {
      width: 1024,
      height: 768
    },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  try {
    const page = await browser.newPage();
    await page.goto(BANCOCHILE_LOGIN_PAGE_URL, {
      waitUntil: 'networkidle2'
    });
    await page.waitFor(1000);
    await page.waitForSelector(userNameInputField);
    await page.type(userNameInputField, BANCOCHILE_USER);
    await page.type(passwordField, BANCOCHILE_PASSWORD);
    await page.waitFor(1000);
    await page.click(loginButton);
    await page.waitForSelector(paymentsAndTranferenceTab);
    await page.click(paymentsAndTranferenceTab);
    await page.waitForSelector(massivePayHover);
    await page.hover(massivePayHover);
    await page.waitForSelector(addPayroll);
    await page.click(addPayroll);

    await page.$eval(agreementSelectField, el => el.click());
    await page.waitFor(TIME_INTERVAL_BEFORE_PRESSING_ENTER_KEY);
    await page.keyboard.press(ENTER_KEY);
    await page.waitFor(TIME_INTERVAL_BEFORE_NEXT_SELECTION);

    await page.$eval(paymentTypeSelectField, el => el.click());
    await page.waitFor(TIME_INTERVAL_BEFORE_PRESSING_ENTER_KEY);
    await page.keyboard.press(ENTER_KEY);
    await page.waitFor(TIME_INTERVAL_BEFORE_NEXT_SELECTION);

    await page.$eval(templateTypeSelectField, el => el.click());
    await page.waitFor(TIME_INTERVAL_BEFORE_PRESSING_ENTER_KEY);
    await page.keyboard.press(ENTER_KEY);
    await page.waitFor(TIME_INTERVAL_BEFORE_NEXT_SELECTION);

    await page.type(payrollNameInputField, `Pago ${moment().format('MMYYYY')}`);
    await page.waitFor(TIME_INTERVAL_BEFORE_NEXT_SELECTION);

    const fileUploadInputField = await page.$(fileInputField);
    await fileUploadInputField.uploadFile(fileToUpload);
    await page.waitForSelector(`${addPayrollButton}:not([disabled])`, { timeout: 600000 });
    await page.click(addPayrollButton);
    await page.waitFor(TIME_INTERVAL_BEFORE_NEXT_SELECTION);
    browser.close();
  } catch (error) {
    browser.close();
    throw new Error(`Error al subir archivo banco de chile: ${error}`);
  }
};

module.exports = { uploadFile };

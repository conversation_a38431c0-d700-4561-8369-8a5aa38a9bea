const cronDescription = 'modificar datos registro civil';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'MODIFY_CIVIL_REGISTRY_DATA';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'DATA_TO_CIVIL_REGISTRATION';
const emptyFile = 'El archivo recibido no contiene datos';

const getMissingDependencyMessage = depMark => `No se ha ejecutado la dependencia ${depMark}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  sftpCredentials,
  sftpClient,
  connectToSFTPServer,
  service,
  pensionsService,
  logService,
  fileHelpers,
  FOLDER_PATH,
  sharedHelpers,
  tmp,
  job,
  done
}) => {
  try {
    Logger.info(`${cronMark}: checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: start dependency verification`);

    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: process started`);
    Logger.info(`Check SFTP server. Verify file for ${cronMark}.`);
    const { file, error } = await fileHelpers.downloadCivilRegistryFileFromSFTP({
      sftpClient,
      connectToSFTPServer,
      sftpCredentials,
      FOLDER_PATH,
      fileHelpers,
      tmp
    });

    if (error) {
      Logger.error(`File error on ${cronMark}. ${error}`);
      throw new Error(`File error on ${cronMark}. ${error}`);
    }

    if (!file) {
      Logger.error(`Verification finished. The file has not been received yet. ${cronMark} `);
      throw new Error(`Verification finished. The file has not been received yet. ${cronMark} `);
    }

    Logger.info(`File received. Start file process for ${cronMark}`);
    const { lines, error: fileReadError } = await fileHelpers.readSFTPFile({
      file,
      fileHelpers,
      sharedHelpers
    });

    if (fileReadError) throw new Error(fileReadError);

    if (!lines.length) {
      Logger.info(emptyFile);
      throw new Error(emptyFile);
    }

    Logger.info('Modifying Civil Registration data');
    const pensionsToUpdate = await service.modifyCivilRegistryData(lines);

    const { error: updateError } = await pensionsService.updatePensionsById(pensionsToUpdate);
    if (updateError) throw new Error(updateError);

    await logService.saveLog(cronMark);

    Logger.info(successMessage);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

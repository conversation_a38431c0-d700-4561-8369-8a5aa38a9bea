const cronMark = 'SET_RETROACTIVE_AMOUNT_FOR_SURVIVAL';
const cronDescription = 'establecer monto retroactivo para la supervivencia';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const dependencyMark = '';

const workerFn = async ({ done, Logger, logService, pensionService, service }) => {
  try {
    Logger.info(`Inicio procesamiento: setear valor Monto retroacivo por supervivencia`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    Logger.info(`Inicio procesamiento: setear valor Monto retroacivo por supervivencia`);
    const { error } = await service.processRetroactiveAmountForSurvival(pensionService);
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

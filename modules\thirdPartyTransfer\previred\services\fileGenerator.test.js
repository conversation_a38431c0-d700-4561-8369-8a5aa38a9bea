/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const fs = require('fs');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const { getTextLine, getExtraTextLines } = require('./fileGenerator');

const pensions = require('../../../../resources/previred/pensions.json');
const afps = require('../../../../resources/previred/afps.json');
const isapres = require('../../../../resources/previred/isapres.json');

describe('generator of previred files', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});

  it('generate txt file for Pension with no extra lines', async () => {
    const result = getTextLine({
      pension: pensions[0],
      type: 1,
      afps,
      isapres,
      taxablePension: 100000
    });

    const data = fs
      .readFileSync(`${__dirname}/../../../../resources/previred/result1.txt`)
      .toString();

    expect(result).toBe(data);
  });

  it('generate txt file for Pension with extra lines', async () => {
    const result = getExtraTextLines({
      pension: pensions[0],
      taxablePension: 50000
    });

    const data = fs
      .readFileSync(`${__dirname}/../../../../resources/previred/result2.txt`)
      .toString()
      .replace('\r', '')
      .split('\n');

    expect(result).toStrictEqual(data);
  });

  afterEach(() => {});
  afterAll(afterAllTests);
});

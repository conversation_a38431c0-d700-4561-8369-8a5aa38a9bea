const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const pensionsData = require('../../../../resources/pensionObjectForLiquidation.json');
const sumFields = require('./pipeline');

describe('Liquidation Report Field Sumatory Pipeline', () => {
  beforeAll(beforeAllTests);

  it('should go through functions pipeline and sum fields', async () => {
    const result = sumFields(pensionsData);
    const {
      liquidation: {
        totalAssets,
        totalSocialCreditDiscounts,
        totalOnePercentDiscounts,
        numberOfAssets,
        numberOfDiscounts,
        totalDiscounts
      }
    } = result;
    expect(totalAssets).toBe(250731.05);
    expect(totalSocialCreditDiscounts).toBe(20);
    expect(totalOnePercentDiscounts).toBe(450);
    expect(numberOfAssets).toBe(11);
    expect(numberOfDiscounts).toBe(9);
    expect(totalDiscounts).toBe(540);
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
const { codeFormatter } = require('../validators/validator');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  banksService,
  validationResult,
  Logger
}) => {
  return {
    updateBank: async (req, res) => {
      Logger.info('Add or Update nomenclator bank: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, city, name, direction, code } = req.body.bank;
      const { result, isError, error } = await banksService.updateBank({
        id,
        city,
        name,
        direction,
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator banks successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    createBank: async (req, res) => {
      Logger.info('create nomenclator Bank: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, city, name, direction, code } = req.body.bank;
      const { result, isError, error } = await banksService.createBank({
        id,
        city,
        name,
        direction,
        code: codeFormatter(code)
      });
      if (isError) {
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Banks successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    deleteBank: async (req, res) => {
      Logger.info('Delete nomenclator bank: ', req.params.id);
      const { result, isError, error } = await banksService.deleteBank(req.params.id);
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.OK).json({ error, isError });
      } else {
        Logger.info('Delete nomenclator bank successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    getBanks: async (req, res) => {
      Logger.info('Get all banks: ');
      const { result } = await banksService.getBanks();
      res.status(HttpStatus.OK).json({ result });
    }
  };
};

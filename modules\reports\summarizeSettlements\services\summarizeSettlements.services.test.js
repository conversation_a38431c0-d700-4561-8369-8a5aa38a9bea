/* eslint-disable no-plusplus */
/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const summarizeSettlementsService = require('./summarizeSettlements.services');
const PensionModel = require('../../../../models/pension');
const liquidationHistoric = require('../../../../models/liquidationHistoric');
const data = require('../../../../resources/summarizeSettlementsExcel.json');
const { excelService } = require('./excel.service');

describe('test service', () => {
  beforeAll(beforeAllTests);

  it('should return pensions linked in a time interval of one month', async done => {
    const startingDate = new Date(2020, 1, 1);
    const endingDate = new Date(2020, 4, 1);

    const pension = data[1];
    await PensionModel.insertMany([pension]).catch(err => console.error(err));
    const liquidation = data[0];
    await liquidationHistoric.insertMany([liquidation]).catch(err => console.error(err));

    const {
      isError,
      result,
      error,
      badRequestError
    } = await summarizeSettlementsService
      .matchLiquidationPension(startingDate, endingDate)
      .catch(err => console.log(err));

    expect(error).toBe('');
    expect(badRequestError).toBe('');
    expect(isError).toBe(false);
    expect(result[0].beneficiaryRut).toBe('17176176-8');
    expect(result[0].causantRut).toBe('17175175-8');
    expect(result[0].pension.beneficiary.rut).toBe('17176176-8');
    expect(result[0].pension.causant.rut).toBe('17175175-8');
    expect(result[0].pension.article40).toBe(30000);

    done();
  });

  it('Should export an excel', async () => {
    const result = excelService([data[2]]);

    expect(result.getStringIndex('Mes')).toBe(0);
    expect(result.getStringIndex('Año')).toBe(1);
    expect(result.getStringIndex('Número de pensión')).toBe(2);
    expect(result.getStringIndex('Rut beneficiario')).toBe(3);
    expect(result.getStringIndex('Tipo de pensión')).toBe(4);
    expect(result.getStringIndex('Tipo de vigencia')).toBe(5);
    expect(result.getStringIndex('Pensión base')).toBe(6);
    expect(result.getStringIndex('Pensión base fija')).toBe(7);
    expect(result.getStringIndex('Días a pagar')).toBe(8);
    expect(result.getStringIndex('Nombre beneficiario')).toBe(9);
    expect(result.getStringIndex('Pensión imponible')).toBe(10);
    expect(result.getStringIndex('Pensión líquida')).toBe(11);
    expect(result.getStringIndex('Artículo 40')).toBe(12);
    expect(result.getStringIndex('Artículo 41')).toBe(13);
    expect(result.getStringIndex('Haber asignación familiar')).toBe(14);
    expect(result.getStringIndex('Haber APS')).toBe(15);
    expect(result.getStringIndex('REBSAL (rebaja de salud ajustado)')).toBe(16);
    expect(result.getStringIndex('Monto retroactivo por pensión base')).toBe(17);
    expect(result.getStringIndex('Monto retroactivo por artículo 40')).toBe(18);
    expect(result.getStringIndex('Monto retroactivo por artículo 41')).toBe(19);
    expect(
      result.getStringIndex(
        'Monto retroactivo por haber no formulable líquido comidaTotalNonFormulable1'
      )
    ).toBe(43);
    expect(
      result.getStringIndex(
        'Monto retroactivo por haber no formulable líquido comidaTotalNonFormulable2'
      )
    ).toBe(44);
    expect(
      result.getStringIndex('Monto retroactivo imponible por haber no formulable comidaTaxable1')
    ).toBe(45);
    expect(
      result.getStringIndex('Monto retroactivo imponible por haber no formulable comidaTaxable2')
    ).toBe(46);
  });

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.error(err));
    await liquidationHistoric.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

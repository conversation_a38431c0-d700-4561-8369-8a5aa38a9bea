import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { JsonbOperationsService } from './jsonb-operations.service';
import {
  PensionAssets,
  PensionDiscounts,
  RetroactiveAmounts,
  CalculationConfig,
  AuditEntry,
  JsonbUpdateOperation
} from '../types/pension-jsonb.types';

/**
 * Servicio de cálculo de pensiones optimizado para JSONB
 * Aprovecha las capacidades de PostgreSQL para cálculos eficientes
 */
@Injectable()
export class PensionCalculationJsonbService {
  private readonly logger = new Logger(PensionCalculationJsonbService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly jsonbOps: JsonbOperationsService
  ) {}

  /**
   * Calcula y actualiza beneficios usando operaciones JSONB atómicas
   */
  async calculateAndUpdateAssets(pensionId: string): Promise<PensionAssets> {
    const pension = await this.prisma.pension.findUniqueOrThrow({
      where: { id: pensionId },
      include: { discountsAndAssets: true }
    });

    // Calcular assets usando datos JSONB existentes
    const currentAssets = pension.assets as PensionAssets || {};
    const calculationConfig = pension.calculationConfig as CalculationConfig || {};

    const newAssets: PensionAssets = {
      ...currentAssets,
      aps: await this.calculateAps(pension),
      familyAssignment: await this.calculateFamilyAssignment(pension),
      christmasBonus: await this.calculateSeasonalBonus(pension, 'christmas'),
      nationalHolidaysBonus: await this.calculateSeasonalBonus(pension, 'national_holidays'),
      winterBonus: await this.calculateSeasonalBonus(pension, 'winter'),
      healthExemption: await this.calculateHealthExemption(pension),
      rebsal: await this.calculateRebsal(pension),
    };

    // Calcular assets no formulables desde JSONB
    if (pension.discountsAndAssets?.assetsNonFormulable) {
      const nonFormulableAssets = this.calculateNonFormulableAssets(
        pension.discountsAndAssets.assetsNonFormulable as any[]
      );
      newAssets.nonFormulableNet = nonFormulableAssets.net;
      newAssets.nonFormulableTaxable = nonFormulableAssets.taxable;
    }

    // Actualizar usando operación JSONB atómica
    await this.jsonbOps.updateJsonbField(pensionId, [
      {
        field: 'assets',
        operation: 'set',
        value: newAssets
      }
    ]);

    return newAssets;
  }

  /**
   * Calcula descuentos con validaciones JSONB
   */
  async calculateAndUpdateDiscounts(pensionId: string): Promise<PensionDiscounts> {
    const pension = await this.prisma.pension.findUniqueOrThrow({
      where: { id: pensionId },
      include: { discountsAndAssets: true, liquidations: { take: 1 } }
    });

    const currentDiscounts = pension.discounts as PensionDiscounts || {};
    const taxablePension = pension.liquidations[0]?.taxablePension || 0;

    const newDiscounts: PensionDiscounts = {
      ...currentDiscounts,
      afp: await this.calculateAfpDiscount(pension, Number(taxablePension)),
      health: await this.calculateHealthDiscount(pension),
      healthLoan: currentDiscounts.healthLoan || 0,
      onePercentAdjusted: await this.calculateOnePercentAdjusted(pension),
    };

    // Procesar retenciones judiciales desde JSONB
    if (currentDiscounts.judicialRetentions) {
      const activeRetentions = currentDiscounts.judicialRetentions.filter(r => r.validity);
      const totalJudicialRetentions = this.calculateJudicialRetentions(
        activeRetentions,
        Number(taxablePension)
      );
      newDiscounts.nonFormulable = (newDiscounts.nonFormulable || 0) + totalJudicialRetentions;
    }

    // Validar límites usando JSONB query
    const validatedDiscounts = await this.validateDiscountLimits(pensionId, newDiscounts);

    await this.jsonbOps.updateJsonbField(pensionId, [
      {
        field: 'discounts',
        operation: 'set',
        value: validatedDiscounts
      }
    ]);

    return validatedDiscounts;
  }

  /**
   * Procesamiento masivo usando JSONB queries optimizadas
   */
  async calculateBatchWithJsonbOptimization(
    filters: {
      pensionType?: string;
      hasAssets?: boolean;
      updatedBefore?: Date;
    }
  ): Promise<{ processed: number; errors: string[] }> {
    // Usar query JSONB optimizada para filtrar pensiones
    let sqlConditions = ['enabled = true'];
    const params: any[] = [];

    if (filters.pensionType) {
      sqlConditions.push('pension_type = $' + (params.length + 1));
      params.push(filters.pensionType);
    }

    if (filters.hasAssets) {
      sqlConditions.push('assets IS NOT NULL AND assets != \'{}\'::jsonb');
    }

    if (filters.updatedBefore) {
      sqlConditions.push('updated_at < $' + (params.length + 1));
      params.push(filters.updatedBefore);
    }

    const pensions = await this.prisma.$queryRawUnsafe(`
      SELECT id, pension_code_id, assets, discounts, calculation_config
      FROM pensions 
      WHERE ${sqlConditions.join(' AND ')}
      ORDER BY updated_at ASC
      LIMIT 1000
    `, ...params);

    let processed = 0;
    const errors: string[] = [];

    // Procesar en chunks usando transacciones JSONB
    const chunks = this.chunkArray(pensions as any[], 100);

    for (const chunk of chunks) {
      try {
        await this.prisma.$transaction(async (tx) => {
          for (const pension of chunk) {
            await this.calculateSinglePensionJsonb(tx, pension.id);
            processed++;
          }
        });
      } catch (error) {
        errors.push(`Chunk error: ${error.message}`);
        this.logger.error('Batch calculation error:', error);
      }
    }

    return { processed, errors };
  }

  /**
   * Cálculo individual optimizado con JSONB
   */
  private async calculateSinglePensionJsonb(tx: any, pensionId: string): Promise<void> {
    // Usar SQL raw para cálculos complejos en una sola query
    await tx.$executeRaw`
      UPDATE pensions 
      SET 
        assets = jsonb_set(
          COALESCE(assets, '{}'),
          '{calculatedAt}',
          to_jsonb(NOW())
        ),
        calculation_config = jsonb_set(
          COALESCE(calculation_config, '{}'),
          '{lastCalculation}',
          jsonb_build_object(
            'timestamp', NOW(),
            'version', '2.0',
            'method', 'jsonb_optimized'
          )
        )
      WHERE id = ${pensionId}
    `;
  }

  /**
   * Búsqueda avanzada usando operadores JSONB
   */
  async findPensionsWithComplexCriteria(criteria: {
    minAps?: number;
    hasJudicialRetentions?: boolean;
    bonusConfig?: string;
    calculationVersion?: string;
  }): Promise<any[]> {
    const conditions: string[] = ['enabled = true'];
    const params: any[] = [];

    if (criteria.minAps) {
      conditions.push(`(assets->>'aps')::numeric >= $${params.length + 1}`);
      params.push(criteria.minAps);
    }

    if (criteria.hasJudicialRetentions) {
      conditions.push(`discounts->'judicialRetentions' @> '[{"validity": true}]'`);
    }

    if (criteria.bonusConfig) {
      conditions.push(`assets->'bonusConfig'->>'payBonus' = $${params.length + 1}`);
      params.push(criteria.bonusConfig);
    }

    if (criteria.calculationVersion) {
      conditions.push(`calculation_config->>'version' = $${params.length + 1}`);
      params.push(criteria.calculationVersion);
    }

    return await this.prisma.$queryRawUnsafe(`
      SELECT 
        id,
        pension_code_id,
        pension_type,
        assets->>'aps' as aps_amount,
        discounts->'judicialRetentions' as judicial_retentions,
        calculation_config->>'version' as calc_version
      FROM pensions 
      WHERE ${conditions.join(' AND ')}
      ORDER BY updated_at DESC
    `, ...params);
  }

  /**
   * Reportes usando agregaciones JSONB
   */
  async generateJsonbReport(): Promise<any> {
    return await this.prisma.$queryRaw`
      SELECT 
        pension_type,
        COUNT(*) as total_pensions,
        
        -- Estadísticas de Assets
        AVG((assets->>'aps')::numeric) as avg_aps,
        AVG((assets->>'familyAssignment')::numeric) as avg_family_assignment,
        COUNT(*) FILTER (WHERE assets->>'christmasBonus' IS NOT NULL) as with_christmas_bonus,
        
        -- Estadísticas de Discounts  
        AVG((discounts->>'afp')::numeric) as avg_afp_discount,
        COUNT(*) FILTER (WHERE discounts->'judicialRetentions' @> '[{"validity": true}]') as with_judicial_retentions,
        
        -- Configuraciones
        COUNT(*) FILTER (WHERE calculation_config->>'useMinimumPension' = 'true') as using_minimum_pension,
        
        -- Agregaciones complejas
        jsonb_object_agg(
          'bonus_distribution',
          jsonb_build_object(
            'christmas', COUNT(*) FILTER (WHERE (assets->>'christmasBonus')::numeric > 0),
            'winter', COUNT(*) FILTER (WHERE (assets->>'winterBonus')::numeric > 0),
            'national_holidays', COUNT(*) FILTER (WHERE (assets->>'nationalHolidaysBonus')::numeric > 0)
          )
        ) as bonus_stats
        
      FROM pensions 
      WHERE enabled = true 
        AND assets IS NOT NULL
      GROUP BY pension_type
      ORDER BY total_pensions DESC
    `;
  }

  // Métodos auxiliares privados
  private async calculateAps(pension: any): Promise<number> {
    // Implementación específica usando datos JSONB
    const config = pension.calculationConfig as CalculationConfig;
    if (config?.manualOverrides?.aps) {
      return config.manualOverrides.aps.value;
    }
    // Lógica normal de cálculo APS
    return 0;
  }

  private async calculateFamilyAssignment(pension: any): Promise<number> {
    return pension.numberOfCharges * 12364; // Simplificado
  }

  private async calculateSeasonalBonus(pension: any, bonusType: string): Promise<number> {
    const assets = pension.assets as PensionAssets;
    if (assets?.bonusConfig?.payBonus === 'NO') {
      return 0;
    }
    // Lógica específica por tipo de bono
    return 30000; // Simplificado
  }

  private async calculateHealthExemption(pension: any): Promise<number> {
    return 0; // Implementación específica
  }

  private async calculateRebsal(pension: any): Promise<number> {
    return 0; // Implementación específica
  }

  private calculateNonFormulableAssets(assets: any[]): { net: number; taxable: number } {
    const net = assets
      .filter(a => a.assetType === 'líquido' && a.isActive)
      .reduce((sum, a) => sum + (a.amount || 0), 0);
    
    const taxable = assets
      .filter(a => a.assetType === 'imponible' && a.isActive)
      .reduce((sum, a) => sum + (a.amount || 0), 0);

    return { net, taxable };
  }

  private async calculateAfpDiscount(pension: any, taxablePension: number): Promise<number> {
    return taxablePension * 0.1; // 10% simplificado
  }

  private async calculateHealthDiscount(pension: any): Promise<number> {
    const discounts = pension.discounts as PensionDiscounts;
    return (discounts?.healthUF || 0) * 35000; // UF simplificada
  }

  private async calculateOnePercentAdjusted(pension: any): Promise<number> {
    return pension.basePension * 0.01; // 1% simplificado
  }

  private calculateJudicialRetentions(retentions: any[], netPension: number): number {
    return retentions.reduce((total, retention) => {
      if (retention.type === 'percentage') {
        return total + (netPension * retention.amount / 100);
      } else {
        return total + retention.amount;
      }
    }, 0);
  }

  private async validateDiscountLimits(pensionId: string, discounts: PensionDiscounts): Promise<PensionDiscounts> {
    // Validación usando consulta JSONB para obtener pensión neta
    const result = await this.prisma.$queryRaw`
      SELECT 
        (assets->>'aps')::numeric + 
        (assets->>'familyAssignment')::numeric + 
        base_pension as estimated_net_pension
      FROM pensions 
      WHERE id = ${pensionId}
    `;

    // Aplicar límites si es necesario
    return discounts;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

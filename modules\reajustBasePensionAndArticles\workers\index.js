/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const modifier = require('../modifiers');
const workerModule = require('./worker');

module.exports = {
  name: 'reajustBasePensionAndArticles',
  worker: deps => workerModule.workerFn({ logService, service, modifier, ...deps }),
  repeatInterval: process.env.CRON_REAJUST_BASEPENSION_AND_ARTICLES_FREQUENCY,
  description: 'Reajuste de Pensión base, art. 40 y 41',
  endPoint: 'reajustbasepensionandarticles',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependenciesArray
};

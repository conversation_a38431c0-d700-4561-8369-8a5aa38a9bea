/* eslint-disable no-underscore-dangle */
const numberOfNonFormulableDiscounts = require('./numberOfNonFormulableDiscounts');
const numberOfNonFormulableNetAssets = require('./numberOfNonFormulableNetAssets');
const numberOfNonFormulableTaxableAssets = require('./numberOfNonFormulableTaxableAssets');
const totalNonFormulableDiscounts = require('./totalNonFormulableDiscounts');
const totalNonFormulableNetAssets = require('./totalNonFormulableNetAssets');
const totalNonFormulableTaxableAssets = require('./totalNonFormulableTaxableAssets');
const totalNonFormulableDiscountsByReason = require('./totalNonFormulableDiscountsByReason');
const totalNonFormulableNetAssetsByReason = require('./totalNonFormulableNetAssetsByReason');
const totalNonFormulableTaxableAssetsByReason = require('./totalNonFormulableTaxableAssetsByReason');
const { pipe } = require('../../sharedFiles/helpers');

const replacePopulatedValue = pension => {
  const { discountsAndAssets = {} } = pension;
  const { _id } = discountsAndAssets;
  return {
    ...pension,
    discountsAndAssets: _id
  };
};

module.exports = obj =>
  pipe(
    numberOfNonFormulableDiscounts,
    numberOfNonFormulableNetAssets,
    numberOfNonFormulableTaxableAssets,
    totalNonFormulableDiscounts,
    totalNonFormulableNetAssets,
    totalNonFormulableTaxableAssets,
    totalNonFormulableDiscountsByReason,
    totalNonFormulableNetAssetsByReason,
    totalNonFormulableTaxableAssetsByReason,
    replacePopulatedValue // should always be at last position
  )(obj);

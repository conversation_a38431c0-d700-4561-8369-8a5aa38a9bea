/* eslint-disable no-unused-expressions */
/* eslint-disable consistent-return */
const cronDescription = 'calcular la pensión base';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const dependencyMarkOne = 'REAJUST_BY_IPC';
const dependencyMarkTwo = 'CALCULATE_DAYS_TO_PAY_WORKER';
const cronMark = 'CRON_BASE_MINIMUN_PENSION_WORKER';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const noPensionsMessage = `${cronMark}: sin pensiones activas`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `La dependencia ${dep} aún no se ha ejecutado`;

const workerFn = async ({ Logger, service, pensionService, logService, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMarkOne))) {
      Logger.info(getMissingDependencyMessage(dependencyMarkOne));
      return {
        message: getMissingDependencyMessage(dependencyMarkOne)
      };
    }
    if (!(await logService.existsLog(dependencyMarkTwo))) {
      Logger.info(getMissingDependencyMessage(dependencyMarkTwo));
      return {
        message: getMissingDependencyMessage(dependencyMarkTwo)
      };
    }

    Logger.info(`${cronDescription} process started`);
    const pensions = await service.obtainActivePensions(pensionService);

    if (pensions.length === 0) {
      Logger.info(noPensionsMessage);
      return {
        message: noPensionsMessage
      };
    }

    const newPensions = await service.calculateBasePension(pensions);
    const { completed, error } = await pensionService.updatePensionAndInsertHistory(newPensions);
    if (error) throw new Error(error);
    completed && (await logService.saveLog(cronMark));
    Logger.info(`Calculation of base pensions is finished`);

    return {
      executionCompleted: true,
      message: successMessage
    };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMarkOne, workerFn };

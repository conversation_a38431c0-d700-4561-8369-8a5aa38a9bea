/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
const {
  MAX_AGE,
  ruleFactory,
  valueLaw19403,
  valueLaw19539A,
  valueLaw19539B,
  payLawBonds,
  pensionTypesRuler
} = require('./helper');
const { roundValue } = require('../../../sharedFiles/helpers');

const rulesDefinition = [
  (pension, basePensionData) => {
    const basePension =
      pension.basePension < basePensionData.minimun ? basePensionData.minimun : pension.basePension;
    const payLaw = payLawBonds(pension);

    if (
      pension.basePension <
      Math.max(roundValue(basePensionData.minimun) + roundValue(basePensionData.law19403), 0)
    ) {
      return {
        ...pension,
        basePension,
        law19403: payLaw ? valueLaw19403(pension.basePension, basePensionData) : 0,
        law19539: payLaw ? valueLaw19539A(pension.basePension, basePensionData) : 0,
        law19953: 0
      };
    }
    return false;
  },
  (pension, basePensionData) => {
    const basePension =
      pension.basePension < basePensionData.minimun ? basePensionData.minimun : pension.basePension;
    const payLaw = payLawBonds(pension);

    if (
      pension.basePension >
      Math.max(roundValue(basePensionData.minimun) + roundValue(basePensionData.law19403), 0)
    ) {
      return {
        ...pension,
        basePension,
        law19403: 0,
        law19539: payLaw ? valueLaw19539B(pension.basePension, basePensionData) : 0,
        law19953: 0
      };
    }
    return false;
  }
];

const getRules = age => (age < MAX_AGE ? rulesDefinition : false);

module.exports = pension =>
  ruleFactory({
    getNomenclatorValue: value => value.law19539,
    getRules,
    pension,
    pensionTypes: pensionTypesRuler
  });

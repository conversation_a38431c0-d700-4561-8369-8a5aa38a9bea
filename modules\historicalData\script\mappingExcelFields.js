const defaultDate = '01-01-1900';
const defaultCreatedAt = '01-04-2021';
const defaultEmpty = '';

const Types = {
  array: 'array',
  boolean: 'boolean',
  date: 'date',
  encrypt: 'encrypt',
  number: 'number',
  word: 'word',
  forcedValue: 'forcedValue',
  forcedBooleanValue: 'forcedBooleanValue',
  forcedDateValue: 'forcedDateValue'
};

const excelFields = [
  {
    path: 'beneficiary.rut',
    nameInXlsx: 'RUT BENEFICIARIO',
    type: Types.encrypt
  },
  {
    path: 'basePension',
    nameInXlsx: 'PENSION BASE',
    type: Types.number,
    defaultValue: 10000,
    minValue: 10000
  },
  { path: 'pensionType', nameInXlsx: 'Tipo de pension', type: Types.word },
  { path: 'validityType', nameInXlsx: 'TIPO DE VIGENCIA', type: Types.word },
  {
    path: 'pensionCodeId',
    nameInXlsx: 'CODIGO IDENTIFICADOR DE PENSION',
    type: Types.word
  },
  {
    path: 'dateOfBirth',
    nameInXlsx: 'FECHA DE NACIMIENTO',
    type: Types.date,
    defaultValue: defaultDate
  },
  { path: 'gender', nameInXlsx: 'GÉNERO', type: Types.word },
  { path: 'afpAffiliation', nameInXlsx: 'AFILIACIÓN AFP', type: Types.word },
  {
    path: 'healthAffiliation',
    nameInXlsx: 'AFILIACIÓN SALUD',
    type: Types.word
  },
  {
    path: 'paymentInfo.branchOffice',
    nameInXlsx: 'SUCURSAL DEL COBRANTE',
    type: Types.word
  },
  { path: 'causant.rut', nameInXlsx: 'RUT CAUSANTE', type: Types.encrypt },
  { path: 'causant.name', nameInXlsx: 'NOMBRE CAUSANTE', type: Types.word },
  {
    path: 'causant.lastName',
    nameInXlsx: 'APELLIDO PATERNO CAUSANTE',
    type: Types.word
  },
  {
    path: 'causant.mothersLastName',
    nameInXlsx: 'APELLIDO MATERNO CAUSANTE',
    type: Types.word,
    defaultValue: ''
  },
  { path: 'collector.rut', nameInXlsx: 'RUT COBRANTE', type: Types.encrypt },
  {
    path: 'collector.name',
    nameInXlsx: 'NOMBRE COBRANTE',
    type: Types.word
  },
  {
    path: 'collector.lastName',
    nameInXlsx: 'APELLIDO PATERNO COBRANTE',
    type: Types.word
  },
  {
    path: 'collector.mothersLastName',
    nameInXlsx: 'APELLIDO MATERNO COBRANTE',
    type: Types.word,
    defaultValue: ''
  },
  {
    path: 'beneficiary.name',
    nameInXlsx: 'NOMBRE BENEFICIARIO',
    type: Types.word
  },
  {
    path: 'beneficiary.lastName',
    nameInXlsx: 'APELLIDO PATERNO BENEFICIARIO',
    type: Types.word
  },
  {
    path: 'beneficiary.mothersLastName',
    nameInXlsx: 'APELLIDO MATERNO BENEFICIARIO',
    type: Types.word,
    defaultValue: ''
  },
  {
    path: 'disabilityDegree',
    nameInXlsx: 'GRADO DE INCAPACIDAD',
    type: Types.number,
    defaultValue: 0
  },
  {
    path: 'disabilityType',
    nameInXlsx: 'TIPO DE INCAPACIDAD',
    type: Types.word
  },
  {
    path: 'resolutionNumber',
    nameInXlsx: 'NUMERO DE RESOLUCION',
    type: Types.number,
    defaultValue: 0
  },
  {
    path: 'resolutionDate',
    nameInXlsx: 'FECHA DE RESOLUCION',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    path: 'accidentDate',
    nameInXlsx: 'FECHA DE ACCIDENTE',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    path: 'beneficiary.email',
    nameInXlsx: 'Correo electrónico beneficiario',
    type: Types.word,
    defaultValue: defaultEmpty
  },
  {
    path: 'paymentInfo.paymentGateway',
    nameInXlsx: 'Vía de pago',
    type: Types.word
  },
  {
    path: 'paymentInfo.accountNumber',
    nameInXlsx: 'Número de cuenta',
    type: Types.word
  },
  { path: 'paymentInfo.bank', nameInXlsx: 'Banco', type: Types.word },
  {
    path: 'collector.address',
    nameInXlsx: 'Dirección cobrante',
    type: Types.word,
    defaultValue: defaultEmpty
  },
  {
    path: 'accidentNumber',
    nameInXlsx: 'Número de siniestro',
    type: Types.number,
    defaultValue: 0
  },
  {
    path: 'disabilityStartDate',
    nameInXlsx: 'Fecha de inicio de incapacidad',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    path: 'institutionalPatient',
    nameInXlsx: 'Paciente institucional',
    type: Types.boolean,
    defaultValue: false
  },
  {
    path: 'transient',
    nameInXlsx: 'Transitoria',
    type: Types.word,
    defaultValue: 'No'
  },
  { path: 'country', nameInXlsx: 'Nacionalidad', type: Types.word },
  { path: 'cun', nameInXlsx: 'CUN', type: Types.word },
  {
    path: 'initialBasePension',
    nameInXlsx: 'PENSION BASE INICIAL',
    type: Types.number,
    defaultValue: 10000,
    minValue: 10000
  },
  {
    path: 'pensionStartDate',
    nameInXlsx: 'Fecha de inicio de Pension',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    path: 'article40',
    nameInXlsx: 'Artículo 40',
    type: Types.number,
    defaultValue: 0
  },
  {
    path: 'discounts.healthUF',
    nameInXlsx: 'Descuento salud UF',
    type: Types.number,
    defaultValue: 0
  },
  {
    path: 'collector.commune',
    nameInXlsx: 'Comuna Cobrante',
    type: Types.word
  },
  {
    path: 'collector.city',
    nameInXlsx: 'Ciudad cobrante',
    type: Types.word,
    defaultValue: defaultEmpty
  },
  {
    path: 'beneficiary.phone',
    nameInXlsx: 'Telefono Beneficiario',
    type: Types.word,
    defaultValue: defaultEmpty
  },
  {
    path: 'inactivationDate',
    nameInXlsx: 'Fecha de inactivación',
    type: Types.date
  },
  {
    path: 'inactivationReason',
    nameInXlsx: 'Motivo de inactivación',
    type: Types.word
  },
  { path: 'deathDate', nameInXlsx: 'Fecha de fallecimiento', type: Types.date },
  {
    nameInXlsx: 'Fecha de fin de vigencia',
    path: 'endDateOfValidity',
    type: Types.date,
    defaultValue: defaultDate
  },

  {
    nameInXlsx: 'Fecha de reactivación',
    path: 'reactivationDate',
    type: Types.date
  },
  {
    nameInXlsx: 'Fecha de evaluación',
    path: 'evaluationDate',
    type: Types.date
  },
  {
    nameInXlsx: 'Fecha de enlace',
    path: 'linkedDate',
    type: Types.forcedDateValue,
    defaultValue: defaultCreatedAt
  },
  {
    nameInXlsx: 'Fecha de creación',
    path: 'createdAt',
    type: Types.forcedDateValue,
    defaultValue: defaultCreatedAt
  },
  {
    nameInXlsx: 'Fecha de actualización',
    path: 'updatedAt',
    type: Types.forcedDateValue,
    defaultValue: defaultCreatedAt
  },
  {
    nameInXlsx: 'Fecha de fin de vigencia teorica',
    path: 'endDateOfTheoricalValidity',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    nameInXlsx: 'ID de habers y descuentos no formulables',
    path: 'discountsAndAssets',
    type: Types.word
  },
  {
    nameInXlsx: 'Monto Rechazo Exención de Salud',
    path: 'rejectionHealthExemptionAmount',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto Rechazo Rebaja de Salud ',
    path: 'rejectionHealthReductionAmount',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Rechazo IPS',
    path: 'rejectionIPS',
    type: Types.boolean,
    defaultValue: false
  },
  {
    nameInXlsx: 'Número de Resolución APS',
    path: 'apsInfo.apsResolutionNumber',
    type: Types.word,
    defaultValue: ''
  },
  {
    nameInXlsx: 'Fecha de emisión de la Resolución APS',
    path: 'apsInfo.apsResolutionDate',
    type: Types.word,
    defaultValue: ''
  },
  {
    nameInXlsx: 'Identificador único de pago APS',
    path: 'apsInfo.apsPaymentUniqueId',
    type: Types.word,
    defaultValue: ''
  },
  {
    nameInXlsx: 'Código de Transferencia APS',
    path: 'apsInfo.apsTransferCode',
    type: Types.word,
    defaultValue: ''
  },
  {
    nameInXlsx: 'Origen APS',
    path: 'apsInfo.apsOrigin',
    type: Types.word,
    defaultValue: ''
  },
  {
    nameInXlsx: 'Cambio tipo de pensión por cargas',
    path: 'ChangeOfPensionTypeDueToCharges',
    type: Types.boolean,
    defaultValue: false
  },
  {
    nameInXlsx: 'Ley bono 19403',
    path: 'law19403',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Ley bono 19539',
    path: 'law19539',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Ley bono 19953',
    path: 'law19953',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Artículo 41',
    path: 'article41',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Periodo de estudio validado',
    path: 'validatedStudyPeriod',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Pensión base fija',
    path: 'fixedBasePension',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Artículo 40 fijo',
    path: 'fixedArticle40',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Artículo 41 fijo',
    path: 'fixedArticle41',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Dias a pagar',
    path: 'daysToPay',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Inactivar Manualmente',
    path: 'inactivateManually',
    type: Types.boolean,
    defaultValue: false
  },
  {
    nameInXlsx: 'Estado de capital',
    path: 'capitalStatus',
    type: Types.word,
    defaultValue: 'Nuevo'
  },
  {
    nameInXlsx: 'Capital total',
    path: 'totalCapital',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Numero de cargas',
    path: 'numberOfCharges',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Habilitado',
    path: 'enabled',
    type: Types.forcedBooleanValue,
    defaultValue: true
  },
  {
    path: 'discounts.onePercentLaAraucana',
    nameInXlsx: 'Descuento 1% caja La Araucana',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    path: 'discounts.onePercentLosHeroes',
    nameInXlsx: 'Descuento 1% caja Los Heroes',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    path: 'discounts.onePercent18',
    nameInXlsx: 'Descuento 1% caja 18',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    path: 'discounts.onePercentLosAndes',
    nameInXlsx: 'Descuento 1% caja Los Andes',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Descuento créditos sociales caja La Araucana',
    path: 'discounts.socialCreditsLaAraucana',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento créditos sociales Caja 18',
    path: 'discounts.socialCredits18',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento créditos sociales Caja Los Andes',
    path: 'discounts.socialCreditsLosAndes',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento créditos sociales Caja Los Héroes',
    path: 'discounts.socialCreditsLosHeroes',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Otros descuentos Caja Los Andes',
    path: 'discounts.othersLosAndes',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Otros descuentos Caja Los Heroes',
    path: 'discounts.othersLosHeroes',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento Prestamos de Salud',
    path: 'discounts.healthLoan',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento salud',
    path: 'discounts.health',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuentos no formulables totales',
    path: 'discounts.totalNonFormulable',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento 1% ajustado',
    path: 'discounts.onePercentAdjusted',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuento AFP',
    path: 'discounts.afp',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Descuentos no formulables por motivo',
    path: 'discounts.nonFormulableByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Monto retroactivo de invalidez',
    path: 'retroactiveAmounts.forDisability',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por invalidez',
    path: 'reservedAmounts.forDisability',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por paciente institucional',
    path: 'retroactiveAmounts.forInstitutionalPatient',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por paciente institucional',
    path: 'reservedAmounts.forInstitutionalPatient',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por rechazo',
    path: 'retroactiveAmounts.forRejection',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto reservado por rechazo',
    path: 'reservedAmounts.forRejection',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo supervivencia',
    path: 'retroactiveAmounts.forSurvival',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por supervivencia',
    path: 'reservedAmounts.forSurvival',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto retroactivo por pensión base',
    path: 'retroactiveAmounts.forBasePension',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por pensión base',
    path: 'reservedAmounts.forBasePension',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por artículo 40',
    path: 'retroactiveAmounts.forArticle40',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por artículo 40',
    path: 'reservedAmounts.forArticle40',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por artículo 41',
    path: 'retroactiveAmounts.forArticle41',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por artículo 41',
    path: 'reservedAmounts.forArticle41',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto retroactivo por aguinaldos',
    path: 'retroactiveAmounts.forBonuses',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por aguinaldos',
    path: 'reservedAmounts.forBonuses',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto retroactivo por haberes no formulables totales imponibles',
    path: 'retroactiveAmounts.forTaxableTotalNonFormulableAssets',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por haberes no formulables totales imponibles',
    path: 'reservedAmounts.forTaxableTotalNonFormulableAssets',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por haberes no formulables totales líquidos',
    path: 'retroactiveAmounts.forTaxableTotalNonFormulableAssets',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por haberes no formulables totales líquidos',
    path: 'reservedAmounts.forNetTotalNonFormulableAssets',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto retroactivo por descuentos no formulables totales',
    path: 'retroactiveAmounts.forTotalNonFormulableDiscounts',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto reservado por descuentos no formulables totales',
    path: 'reservedAmounts.forTotalNonFormulableDiscounts',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto retroactivo por asignación familiar',
    path: 'retroactiveAmounts.forFamilyAssignment',
    type: Types.number,
    defaultValue: 0
  },

  {
    nameInXlsx: 'Monto reservado por asignación familiar',
    path: 'reservedAmounts.forNonFormulableDiscount',
    type: Types.number
  },
  {
    nameInXlsx: 'Monto reservado descuento no formulable por motivo (arreglo)',
    path: 'reservedAmounts.discountsNonFormulableTotalsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Monto reservado por haber no formulable imponible por motivo (arreglo)',
    path: 'reservedAmounts.assetsNonFormulableTaxableTotalsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Monto reservado por haber no formulable líquido por motivo (arreglo)',
    path: 'reservedAmounts.assetsNonFormulableNetTotalsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Haber APS',
    path: 'assets.aps',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber Exención de Salud',
    path: 'assets.healthExemption',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Haber Rebaja de Salud',
    path: 'assets.healthDiscount',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Haber exención de salud ajustado',
    path: 'assets.adjustedHealthExemption',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'REBSAL (Haber rebaja de salud ajustada)',
    path: 'assets.rebsal',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber Asignación Familiar',
    path: 'assets.forFamilyAssignment',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber bono por matrimonio',
    path: 'assets.marriageBonus',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber aguinaldo de navidad',
    path: 'assets.christmasBonus',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber aguinaldo fiestas patrias',
    path: 'assets.nationalHolidaysBonus',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haber bono invierno',
    path: 'assets.winterBonus',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haberes no formulables totales imponibles',
    path: 'assets.taxableTotalNonFormulable',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haberes no formulables totales líquidos',
    path: 'assets.netTotalNonFormulable',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Haberes no formulables líquidos por motivo (arreglo)',
    path: 'assets.netNonFormulableByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Haberes no formulables imponibles por motivo (arreglo)',
    path: 'assets.taxableNonFormulableByReason',
    type: Types.array,
    defaultValue: []
  },

  {
    nameInXlsx: 'Monto retroactivo por haber no formulable imponible por motivo (arreglo)',
    path: 'retroactiveAmounts.forTaxableTotalNonFormulableAssetsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Monto retroactivo por haber no formulable líquido por motivo  (arreglo)',
    path: 'retroactiveAmounts.forNetTotalNonFormulableAssetsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Monto retroactivo por descuento no formulable por motivo (arreglo)',
    path: 'retroactiveAmounts.forTotalNonFormulableDiscountsByReason',
    type: Types.array,
    defaultValue: []
  },
  {
    nameInXlsx: 'Rechazado por el banco',
    path: 'bankRejected',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Vale vista reintegrado',
    path: 'paycheckRefunded',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Monto reservado por vale vista reintegrado',
    path: 'reservedAmounts.forPayCheck',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Monto retroactivo por vale vista reintegrado',
    path: 'retroactiveAmounts.forPayCheck',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Reactivado manualmente',
    path: 'manuallyReactivated',
    type: Types.boolean,
    defaultValue: false
  },
  {
    nameInXlsx: 'Incremento Ley 19.578',
    path: 'increasingInLaw19578',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Incremento Ley 19.953',
    path: 'increasingInLaw19953',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Incremento Ley 20.102',
    path: 'increasingInLaw20102',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Pensión Base sin incrementos',
    path: 'basePensionWithoutIncreases',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Grupo familiar',
    path: 'familyGroup',
    type: Types.number,
    defaultValue: 1
  },
  {
    nameInXlsx: 'Paga Aguinaldo',
    path: 'payBonus',
    type: Types.word,
    defaultValue: 'SI'
  },
  {
    nameInXlsx: 'Estado Civil',
    path: 'maritalStatus',
    type: Types.word,
    defaultValue: 'S'
  },
  {
    nameInXlsx: 'Otra Pension',
    path: 'otherPension',
    type: Types.word,
    defaultValue: 'No'
  },
  {
    nameInXlsx: 'Regimen Otra Pension',
    path: 'regimenOtherPension',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Inicio Otra Pension',
    path: 'startAnotherPension',
    type: Types.date,
    defaultValue: defaultDate
  },
  {
    nameInXlsx: 'Monto Otra Pension',
    path: 'amountOtherPension',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Ingreso Base',
    path: 'baseIncome',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'DL1026',
    path: 'dl1026',
    type: Types.number,
    defaultValue: 0
  },
  {
    nameInXlsx: 'Jubilación',
    path: 'retirement',
    type: Types.word,
    defaultValue: 'N'
  }
];

module.exports = excelFields;

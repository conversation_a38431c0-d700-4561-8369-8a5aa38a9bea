const COMMONS_VARS = {
  RUT_ACHS: '070360100',
  DV_ACHS: '6',
  HEADER_PAYROLL_NUMBER: '00001',
  HEADER_PAYROLL_NAME: 'PENSIONES ACHS LEY 16.744',
  CURRENCY_CODE: '01',
  AGREEMENT_CODE: '004'
};

const LIMIT_SERVIPAG = 1000000;

const parseStringValue = (word, length, caracter = ' ') => {
  return word
    .toString()
    .padEnd(length, caracter)
    .substr(0, length);
};

const validationServipag = netPension => (netPension > LIMIT_SERVIPAG ? '02' : '12');

const parseNumberValue = (word, length) => {
  const [number, decimal = '00'] = word.toString().split('.');
  const parseNumber = `${number}${decimal.padEnd(2, '0').substr(0, 2)}`;

  return parseNumber.padStart(length, '0').substr(0, length);
};

const addOrCutWord = (length, word = '', type = 'text', caracter = ' ') => {
  return type === 'text'
    ? parseStringValue(word, length, caracter)
    : parseNumberValue(word, length);
};
const repeatedWords = (length, word) => ''.padEnd(length, word);

const startWith = (length, word, repeatWord) => word.padStart(length, repeatWord).substr(0, length);
const findInRegex = (array, toSearch) => {
  const result = array.find(({ regex }) => regex.test(toSearch));
  return result ? result.value : '';
};

const findInRegexSalud = (array, toSearch) => {
  const result = array.find(({ regex }) => regex.test(toSearch));
  return result ? result.value : null;
};

const dynamicWordBuilder = (array, mapper, prefix) => {
  let word = '';
  let index = 1;

  array.forEach(item => {
    const foundedWord = findInRegex(mapper, item.reason);
    if (foundedWord) {
      word = word + foundedWord + addOrCutWord(10, item.value, 'number');
      return;
    }
    word = word + addOrCutWord(3, `${prefix}${index}`) + addOrCutWord(10, item.value, 'number');
    index += 1;
  });
  return word;
};
const transformDiacritic = value =>
  value
    .replace(/[aàáâãäå]/gi, '[aàáâãäå]')
    .replace(/[eèéêë]/gi, '[eèéêë]')
    .replace(/[iìíîï]/gi, '[iìíîï]')
    .replace(/[oòóôõö]/gi, '[oòóôõö]')
    .replace(/[uùúûü]/gi, '[uùúûü]')
    .replace(/[nñ]/gi, '[nñ]');
const isString = obj => Object.prototype.toString.call(obj) === '[object String]';
const escapeSpecialChars = value =>
  value && isString(value) ? value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : '';
const escapeChars = value =>
  value && isString(value) ? transformDiacritic(escapeSpecialChars(value)) : value;
const searchInBankOrServipag = (name, array) => {
  const regex = new RegExp(escapeChars(name), 'i');
  const { code = '000' } = (name && array.find(item => regex.test(item.name))) || {};
  return code;
};

const ASSETS_MAPPER = [
  { regex: /basePension/i, value: '101', name: 'Pensión base' },
  { regex: /article40/i, value: '102', name: 'Articulo 40' },
  { regex: /article41/i, value: '103', name: 'Articulo 41' },
  { regex: /forFamilyAssignment/i, value: '104', name: 'Asignación familiar' },
  { regex: /aps/i, value: '105', name: 'Aporte prev Sol' },
  { regex: /rebsal/i, value: '106', name: 'Rebaja de salud' },
  { regex: /adjustedHealthExemption/i, value: '107', name: 'Exención de salud' },
  { regex: /christmasBonus/i, value: '108', name: 'Aguinaldo de navidad' },
  { regex: /nationalHolidaysBonus/i, value: '109', name: 'Ag Fiestas patrias' },
  { regex: /winterBonus/i, value: '110', name: 'Bono invierno' },
  { regex: /marriageBonus/i, value: '111', name: 'Bono matrimonio' },
  { regex: /forBasePension/i, value: '112', name: 'Pensión base retro' },
  { regex: /forArticle40/i, value: '113', name: 'Artículo 40 retro' },
  { regex: /forArticle41/i, value: '114', name: 'Artículo 41 retro' },
  { regex: /forBonuses/i, value: '115', name: 'Aguinaldos retro' },
  { regex: /retroactiveFamilyAssignment/i, value: '116', name: 'Asig familiar retro' },
  { regex: /forRejection/i, value: '117', name: 'Monto rechaz retro' },
  { regex: /forInstitutionalPatient/i, value: '118', name: 'Paciente insti retro' },
  { regex: /law19403/i, value: '119', name: 'Bono Viudez L19403' },
  { regex: /law19539/i, value: '120', name: 'Bono Viudez L19539' },
  { regex: /law19953/i, value: '121', name: 'Bono Viudez L19953' },
  { regex: /totalPensionAccrued/i, value: '122', name: 'Monto devengado' },
  { regex: /strennaRetroConstitution/i, value: '123', name: 'Aguinaldo devengado' },
  { regex: /otherLink/i, value: '124', name: 'Otros Enlazar' }
];

const DISCOUNTS_MAPPER = [
  { regex: /onePercentAdjusted/i, value: '301', name: 'Descuento 1% CCAF' },
  { regex: /socialCreditsLosAndes/i, value: '302', name: 'Créditos sociales LAN' },
  { regex: /socialCredits18/i, value: '303', name: 'Créditos sociales CSC18' },
  { regex: /socialCreditsLaAraucana/i, value: '304', name: 'Créditos sociales CSCLA' },
  { regex: /socialCreditsLosHeroes/i, value: '305', name: 'Créditos sociales CSCLH' },
  { regex: /othersLosHeroes/i, value: '306', name: 'Otro descuento CLH' },
  { regex: /othersLosAndes/i, value: '307', name: 'Otro descuento CLA' },
  { regex: /healthLoan/i, value: '308', name: 'Préstamo de salud' },
  { regex: /health/i, value: '309', name: 'Descuento de salud' },
  { regex: /afp/i, value: '310', name: 'Descuento AFP' },
  { regex: /indemnityDiscount/i, value: '311', name: 'Descuento por indemnizaciones' },
  { regex: /settlement/i, value: '312', name: 'Finiquito' }
];

const PAY_METHODS = [
  { regex: /Dep[oó]sito cuenta corriente Banco de Chile/i, value: '01' },
  { regex: /Vale vista Banco de Chile/i, value: '02' },
  { regex: /Vale vista entregado directamente a la empresa/i, value: '04' },
  { regex: /Dep[oó]sito cuenta corriente otros bancos/i, value: '07' },
  { regex: /Dep[oó]sito cuenta ahorro otros bancos/i, value: '08' },
  { regex: /Cheque electr[oó]nico Banco de Chile/i, value: '11' },
  { regex: /SERVIPAG/i, value: '12' }
];

const HEALTH_TYPES = [
  { regex: /Sin Afiliaci[oó]n/i, value: repeatedWords(6, ' ') },
  { regex: /Fonasa/i, value: 'FONASA' }
];

const HEALTH_AFFILIATIONS = [
  { regex: /Sin Afiliaci[oó]n/i, value: repeatedWords(15, ' ') },
  { regex: /Fonasa/i, value: 'FONASA' }
];

const BANKS = [
  { regex: /BANCO DE CHILE-EDWARDS-CITI/i, value: '001' },
  { regex: /BANCO ESTADO/i, value: '012' },
  { regex: /BANCO SCOTIABANK SUD AMERICANO/i, value: '014' },
  { regex: /BANCO BCI - TBANC - NOVA/i, value: '016' },
  { regex: /CORPBANCA/i, value: '027' },
  { regex: /BANCO BICE/i, value: '028' },
  { regex: /BANCO DE SANTIAGO/i, value: '035' },
  { regex: /BANCO SANTANDER/i, value: '037' },
  { regex: /BANCO ITAU CHILE/i, value: '039' },
  { regex: /BANCO SECURITY/i, value: '049' },
  { regex: /BANCO FALABELLA/i, value: '051' },
  { regex: /BANCO RIPLEY/i, value: '053' },
  { regex: /BANCO BILBAO VIZCAY - BBVA/i, value: '504' },
  { regex: /BANCO DEL DESARROLLO/i, value: '507' },
  { regex: /SIN BANCO/i, value: '000' }
];

const PENSIONS_TYPES = [
  { regex: /Pensi[oó]n por accidente de trabajo/i, value: 'Accidente' },
  { regex: /Pensi[oó]n por accidente de trayecto/i, value: 'Accidente' },
  { regex: /Pensi[oó]n por enfermedad profesional/i, value: 'Enfermedad' },
  { regex: /Pensi[oó]n de viudez con hijos/i, value: 'Supervivencia' },

  { regex: /Pensi[oó]n de viudez sin hijos/i, value: 'Supervivencia' },
  {
    regex: /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial con hijos/i,
    value: 'Supervivencia'
  },
  {
    regex: /Pensi[oó]n de madre de hijo de filiaci[oó]n no matrimonial sin hijos/i,
    value: 'Supervivencia'
  },
  { regex: /Pensi[oó]n por orfandad/i, value: 'Supervivencia' },
  { regex: /Pensi[oó]n de orfandad de padre y madre/i, value: 'Supervivencia' }
];

const COMPENSATION_BOX_YES = /Si/i;

const getCompensationBox = discounts => {
  const { onePercentLaAraucana, onePercentLosHeroes, onePercent18, onePercentLosAndes } = discounts;
  if (COMPENSATION_BOX_YES.test(onePercentLaAraucana)) {
    return 'CAJA LA ARAUCANA';
  }
  if (COMPENSATION_BOX_YES.test(onePercentLosHeroes)) {
    return 'CAJA LOS HEROES';
  }
  if (COMPENSATION_BOX_YES.test(onePercent18)) {
    return 'CAJA 18';
  }
  if (COMPENSATION_BOX_YES.test(onePercentLosAndes)) {
    return 'CAJA LOS ANDES';
  }

  return '';
};

const getDiscounts = (pensionData, { extraAssets, dictionary }) => {
  const { discounts, retroactiveConstitution = {} } = pensionData;

  const {
    onePercentAdjusted,
    socialCreditsLosAndes,
    socialCredits18,
    socialCreditsLaAraucana,
    socialCreditsLosHeroes,
    othersLosHeroes,
    othersLosAndes,
    healthLoan
  } = discounts;

  let { afp } = discounts;

  const { afpDiscountAccrued = 0, indemnityDiscount = 0, settlement = 0 } = retroactiveConstitution;

  afp += afpDiscountAccrued;

  const discountsData = {
    onePercentAdjusted,
    socialCreditsLosAndes,
    socialCredits18,
    socialCreditsLaAraucana,
    socialCreditsLosHeroes,
    othersLosHeroes,
    othersLosAndes,
    healthLoan,
    afp,
    indemnityDiscount,
    settlement
  };

  const entriesDiscounts = Object.entries(discountsData);
  const totalDiscounts = entriesDiscounts
    .map(([reason, value]) => value && { reason, value })
    .filter(item => item);

  const finalDiscount = [...totalDiscounts, ...extraAssets];

  const wordAssets = dynamicWordBuilder(finalDiscount, [...DISCOUNTS_MAPPER, ...dictionary], 'DC');
  return wordAssets;
};

const getAssets = (pensionData, { extraAssets, dictionary }) => {
  const {
    assets,
    retroactiveAmounts,
    article40,
    article41,
    law19403,
    law19539,
    law19953,
    retroactiveConstitution = {}
  } = pensionData;

  const {
    totalPensionAccrued = 0,
    strennaRetroConstitution = 0,
    otherLink = 0
  } = retroactiveConstitution;

  const {
    forBasePension,
    forArticle40,
    forArticle41,
    forBonuses,
    forFamilyAssignment: retroactiveFamilyAssignment,
    forInstitutionalPatient,
    forRejection
  } = retroactiveAmounts;
  const {
    aps,
    rebsal,
    adjustedHealthExemption,
    christmasBonus,
    nationalHolidaysBonus,
    winterBonus,
    marriageBonus
  } = assets;

  const assetsFormulables = {
    article40,
    article41,
    aps,
    rebsal,
    adjustedHealthExemption,
    christmasBonus,
    nationalHolidaysBonus,
    winterBonus,
    marriageBonus,
    forBasePension,
    forArticle40,
    forArticle41,
    forBonuses,
    retroactiveFamilyAssignment,
    forRejection,
    forInstitutionalPatient,
    law19403,
    law19539,
    law19953,
    totalPensionAccrued,
    strennaRetroConstitution,
    otherLink
  };

  const entriesAssetsFormulables = Object.entries(assetsFormulables);
  const totalAssetsFormulables = entriesAssetsFormulables
    .map(([reason, value]) => value && { reason, value })
    .filter(item => item);

  const totalAssets = [...totalAssetsFormulables, ...extraAssets];

  const wordAssets = dynamicWordBuilder(totalAssets, [...ASSETS_MAPPER, ...dictionary], 'HB');
  return wordAssets;
};

const mapperHeader = (dateToPay, totalSumLiquidations) => {
  const {
    RUT_ACHS,
    DV_ACHS,
    AGREEMENT_CODE,
    HEADER_PAYROLL_NUMBER,
    HEADER_PAYROLL_NAME,
    CURRENCY_CODE
  } = COMMONS_VARS;

  const HEADER_RECORD_TYPE = '01';
  const PAYMENT_DATE = addOrCutWord(8, dateToPay);
  const AMMOUNT_TO_PAY = addOrCutWord(13, totalSumLiquidations, 'number');
  const ENDORSEMENT_TYPE = 'N';
  const PAYMENT_METHOD = 'PEN';
  const HEADER =
    '---- LIQUIDACION DE PAGO DE PENSION ------- ASOCIACION CHILENA DE  SEGURIDAD ----- www.achs.cl  -  FONO: 600.600.2247 --';

  return [
    `${HEADER_RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${HEADER_PAYROLL_NUMBER}${HEADER_PAYROLL_NAME}${CURRENCY_CODE}${PAYMENT_DATE}${AMMOUNT_TO_PAY}${repeatedWords(
      3,
      ' '
    )}${ENDORSEMENT_TYPE}${repeatedWords(18, '0')}${PAYMENT_METHOD}${HEADER}${repeatedWords(
      187,
      ' '
    )}`
  ];
};

const mapperCollectorLine = (
  { collector, pensionCodeId, paymentInfo, oldAgePensionInProcess, gender },
  { servipags = [], liquidation = {}, banks = [] }
) => {
  const { rut, name, lastName, mothersLastName, address, commune, city } = collector;
  const { netPension = '' } = liquidation;
  const { paymentGateway, bank, accountNumber, branchOffice = '' } = paymentInfo;
  const { RUT_ACHS, DV_ACHS, AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');
  const NAME = addOrCutWord(60, `${lastName} ${mothersLastName} ${name}`);
  const ADDRESS = addOrCutWord(65, address);
  const COMMUNE = addOrCutWord(15, commune);
  const CITY = addOrCutWord(15, city);
  const PENSION_NUMBER = startWith(8, pensionCodeId, '0');
  let CODE_MESSAGE = repeatedWords(4, '0');
  const ACCOUNT_NUMBER = addOrCutWord(22, accountNumber);
  const BANK_CODE = findInRegex(BANKS, bank) || '001';
  const paymentType = findInRegex(PAY_METHODS, paymentGateway) || '01';
  const PAYMENT_METHOD = paymentType === '12' ? validationServipag(netPension) : paymentType;
  const TOTAL_TO_PAY =
    addOrCutWord(13, Math.max(0, netPension), 'number') || repeatedWords(13, '0');
  const ECONOMIC_ACTIVITY = 'BC';
  const ADDRESS_TYPE = '0';
  const RECORD_TYPE = '02';
  const BRANCH_OFFICE = addOrCutWord(
    3,
    searchInBankOrServipag(branchOffice, [...banks, ...servipags])
  );

  if (oldAgePensionInProcess === 1 && gender === 'M') CODE_MESSAGE = '0001';
  else if (oldAgePensionInProcess === 1 && gender === 'F') CODE_MESSAGE = '0002';

  return [
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${PAYMENT_METHOD}${startWith(
      9,
      RUT,
      '0'
    )}${DV}${NAME}${ADDRESS_TYPE}${ADDRESS}${COMMUNE}${CITY}${repeatedWords(
      7,
      ' '
    )}${ECONOMIC_ACTIVITY}${BANK_CODE}${ACCOUNT_NUMBER}${BRANCH_OFFICE}${TOTAL_TO_PAY}${PENSION_NUMBER}${repeatedWords(
      111,
      ' '
    )}${CODE_MESSAGE}${repeatedWords(37, ' ')}`
  ];
};

const mapperCollectorRetentionLine = ({ pensionCodeId }, { collectorRetention = {} }) => {
  const {
    collectorRut: rut,
    name,
    lastName,
    mothersLastName,
    address,
    commune,
    city,
    paymentGateway,
    bank,
    accountNumber,
    amount
  } = collectorRetention;
  const netPension = '';
  const toUpperCase = (text = '') => text.toUpperCase();

  const { RUT_ACHS, DV_ACHS, AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');
  const NAME = toUpperCase(addOrCutWord(60, `${lastName} ${mothersLastName} ${name}`));
  const ADDRESS = toUpperCase(addOrCutWord(65, address));
  const COMMUNE = toUpperCase(addOrCutWord(15, commune));
  const CITY = toUpperCase(addOrCutWord(15, city));
  const PENSION_NUMBER = startWith(8, pensionCodeId, '0');
  const CODE_MESSAGE = repeatedWords(4, '0');
  const ACCOUNT_NUMBER = addOrCutWord(22, accountNumber);
  const BANK_CODE = findInRegex(BANKS, bank) || '001';
  const paymentType = findInRegex(PAY_METHODS, paymentGateway) || '01';
  const PAYMENT_METHOD = paymentType === '12' ? validationServipag(netPension) : paymentType;
  const TOTAL_TO_PAY = addOrCutWord(13, Math.max(0, amount), 'number') || repeatedWords(13, '0');
  const ECONOMIC_ACTIVITY = 'BC';
  const ADDRESS_TYPE = '0';
  const RECORD_TYPE = '02';
  const BRANCH_OFFICE = '000';

  return [
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${PAYMENT_METHOD}${startWith(
      9,
      RUT,
      '0'
    )}${DV}${NAME}${ADDRESS_TYPE}${ADDRESS}${COMMUNE}${CITY}${repeatedWords(
      7,
      ' '
    )}${ECONOMIC_ACTIVITY}${BANK_CODE}${ACCOUNT_NUMBER}${BRANCH_OFFICE}${TOTAL_TO_PAY}${PENSION_NUMBER}${repeatedWords(
      111,
      ' '
    )}${CODE_MESSAGE}${repeatedWords(37, ' ')}`
  ];
};

const getMapper = (dictionary, count) => {
  const withCodes = dictionary.map((item, i) => ({
    regex: new RegExp(escapeChars(item.motive), 'i'),
    name: item.motive,
    value: `${i + count}`
  }));
  return withCodes;
};

const mapperBeneficiaryLine = (
  { beneficiary, causant, assets, discounts, ...pension },
  { dateToPay, liquidation = {}, assetMapper }
) => {
  const { rut } = beneficiary;
  const { totalAssets = '', numberOfAssets = '', taxablePension = '' } = liquidation;
  const {
    pensionType,
    pensionCodeId,
    afpAffiliation,
    numberOfCharges = 0,
    basePension = 0,
    retroactiveConstitution = {}
  } = pension;
  const {
    forFamilyAssignment,
    netNonFormulableByReason = [],
    taxableNonFormulableByReason = []
  } = assets;

  const {
    totalPensionAccrued = 0,
    strennaRetroConstitution = 0,
    otherLink = 0
  } = retroactiveConstitution;

  const totalAssetsPlusRetroactive =
    totalAssets + totalPensionAccrued + strennaRetroConstitution + otherLink;
  const taxablePensionPlusRetroactive = taxablePension + totalPensionAccrued;
  const NAME_CAJA_COMPENSACION = getCompensationBox(discounts);
  const { name, lastName, mothersLastName, rut: causantRut } = causant;
  const { AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '04';
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');
  const [CAUSANT_RUT, CAUSANT_DV] = causantRut.replace(/[^0-9kK-]/g, '').split('-');
  const PENSION_NUMBER = startWith(7, pensionCodeId, '0');
  const FAMILY_ASSIGMENT_AMOUNT = addOrCutWord(10, forFamilyAssignment, 'number');
  const CAUSANT_NAME = addOrCutWord(40, `${lastName} ${mothersLastName} ${name}`);
  const TAXABLE_AMOUNT = '0000000000';
  const LIQUIDATION_TYPE = '000';
  const PAYMENT_CURRENCY = '1';
  const TOTAL_PENSION = addOrCutWord(10, basePension, 'number');
  const AFP = addOrCutWord(23, afpAffiliation);
  const CHARGES_NUMBER = startWith(2, numberOfCharges.toString(), '0');
  const NEXT_PAY = addOrCutWord(8, dateToPay);
  const BRANCH_OFFICE = repeatedWords(5, '0');
  const PENSION_TYPE = addOrCutWord(15, findInRegex(PENSIONS_TYPES, pensionType) || '');
  const TAXABLE_PENSION =
    addOrCutWord(10, taxablePensionPlusRetroactive, 'number') || repeatedWords(10, '0');
  const TOTAL_ASSETS =
    addOrCutWord(10, totalAssetsPlusRetroactive, 'number') || repeatedWords(10, '0');
  const ASSETS_NUMBER = startWith(2, numberOfAssets.toString(), '0');

  const toLowerCase = (text = '') => text.toLowerCase();

  const ASSETS = addOrCutWord(
    130,
    getAssets(
      { assets, ...pension },
      {
        dictionary: assetMapper,
        extraAssets: [
          ...netNonFormulableByReason.map(item => ({
            reason: toLowerCase(item.reason),
            value: item.amount
          })),
          ...taxableNonFormulableByReason.map(item => ({
            reason: toLowerCase(item.reason),
            value: item.amount
          }))
        ]
      }
    ),
    'text',
    '0'
  );

  return [
    `${RECORD_TYPE}${startWith(9, RUT, '0')}${DV}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${PENSION_NUMBER}${BRANCH_OFFICE}${CAUSANT_NAME}${startWith(
      9,
      CAUSANT_RUT,
      '0'
    )}${CAUSANT_DV}${NEXT_PAY}${PENSION_TYPE}${CHARGES_NUMBER}${TAXABLE_AMOUNT}${TAXABLE_PENSION}${TOTAL_ASSETS}${FAMILY_ASSIGMENT_AMOUNT}${TOTAL_PENSION}${LIQUIDATION_TYPE}${repeatedWords(
      13,
      ' '
    )}${PAYMENT_CURRENCY}${AFP}${ASSETS_NUMBER}${ASSETS}${repeatedWords(39, ' ')}${addOrCutWord(
      20,
      NAME_CAJA_COMPENSACION
    )}${repeatedWords(10, ' ')}`
  ];
};

const mapperBeneficiaryRetentionLine = (
  { causant, ...pension },
  { dateToPay, collectorRetention = {} }
) => {
  const { pensionType, pensionCodeId } = pension;
  const toUpperCase = (text = '') => text.toUpperCase();

  const {
    collectorRut: rut,
    amount: basePension,
    name,
    lastName,
    mothersLastName
  } = collectorRetention;
  const { rut: causantRut } = causant;
  const { AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '04';
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');
  const [CAUSANT_RUT, CAUSANT_DV] = causantRut.replace(/[^0-9kK-]/g, '').split('-');
  const PENSION_NUMBER = startWith(7, pensionCodeId, '0');
  const FAMILY_ASSIGMENT_AMOUNT = repeatedWords(10, '0');
  const CAUSANT_NAME = toUpperCase(addOrCutWord(40, `${lastName} ${mothersLastName} ${name}`));
  const TAXABLE_AMOUNT = '0000000000';
  const LIQUIDATION_TYPE = '000';
  const PAYMENT_CURRENCY = '1';
  const TOTAL_PENSION = addOrCutWord(10, basePension, 'number');
  const AFP = addOrCutWord(23, '');
  const CHARGES_NUMBER = '00';
  const NEXT_PAY = addOrCutWord(8, dateToPay);
  const BRANCH_OFFICE = repeatedWords(5, '0');
  const PENSION_TYPE = addOrCutWord(15, findInRegex(PENSIONS_TYPES, pensionType) || '');
  const TAXABLE_PENSION = repeatedWords(10, '0');
  const TOTAL_ASSETS = repeatedWords(10, '0');
  const ASSETS_NUMBER = repeatedWords(2, '0');
  const ASSETS = repeatedWords(130, '0');

  return [
    `${RECORD_TYPE}${startWith(9, RUT, '0')}${DV}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${PENSION_NUMBER}${BRANCH_OFFICE}${CAUSANT_NAME}${startWith(
      9,
      CAUSANT_RUT,
      '0'
    )}${CAUSANT_DV}${NEXT_PAY}${PENSION_TYPE}${CHARGES_NUMBER}${TAXABLE_AMOUNT}${TAXABLE_PENSION}${TOTAL_ASSETS}${FAMILY_ASSIGMENT_AMOUNT}${TOTAL_PENSION}${LIQUIDATION_TYPE}${repeatedWords(
      13,
      ' '
    )}${PAYMENT_CURRENCY}${AFP}${ASSETS_NUMBER}${ASSETS}${repeatedWords(69, ' ')}`
  ];
};

const mapperCollectorData = (
  { collector, discounts, healthAffiliation, pensionCodeId, ...pension },
  { liquidation = {}, ufValue, discountMapper }
) => {
  const { rut } = collector;
  const { health, nonFormulableByReason = [] } = discounts;
  const { numberOfDiscounts = 0, totalDiscounts = 0 } = liquidation;
  const { retroactiveConstitution = {} } = pension;
  const {
    healthDiscountAccrued = 0,
    afpDiscountAccrued = 0,
    settlement = 0,
    indemnityDiscount = 0
  } = retroactiveConstitution;

  const totalDiscountsPlusRetroactive =
    totalDiscounts + healthDiscountAccrued + afpDiscountAccrued + settlement + indemnityDiscount;
  const healthPlusRetroactive = health + healthDiscountAccrued;

  const { AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '05';
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');

  const TOTAL_TAX_AMOUNT = repeatedWords(10, '0');
  const HEALTH_DISCOUNT = addOrCutWord(10, healthPlusRetroactive, 'number');
  const UF_VALUE = addOrCutWord(10, ufValue, 'number') || repeatedWords(10, '0');
  const DISCOUNTS_TAX_AMOUNT = addOrCutWord(10, totalDiscountsPlusRetroactive, 'number');
  const PAID_DUES = repeatedWords(8, '0');
  const PENDING_DUES = repeatedWords(8, '0');
  const SECUENCE_NUMBER = startWith(6, pensionCodeId, '0');
  const HEALTH_TYPE_AUX = findInRegexSalud(HEALTH_TYPES, healthAffiliation) || 'ISAPRE';
  const HEALTH_TYPE = addOrCutWord(6, HEALTH_TYPE_AUX);
  const DISCOUNTS_NUMBER = startWith(2, numberOfDiscounts.toString(), '0');
  const HEALTH_AFFILIATION_AUX =
    findInRegexSalud(HEALTH_AFFILIATIONS, healthAffiliation) || healthAffiliation || ' ';
  const HEALTH_AFFILIATION = addOrCutWord(15, `${HEALTH_AFFILIATION_AUX}`.toUpperCase());

  const DISCOUNTS = addOrCutWord(
    130,
    getDiscounts(
      { discounts, ...pension },
      {
        dictionary: discountMapper,
        extraAssets: [
          ...nonFormulableByReason.map(item => ({ reason: item.reason, value: item.amount }))
        ]
      }
    ),
    'text',
    '0'
  );

  return [
    `${RECORD_TYPE}${startWith(9, RUT, '0')}${DV}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${HEALTH_DISCOUNT}${HEALTH_TYPE}${HEALTH_AFFILIATION}${TOTAL_TAX_AMOUNT}${DISCOUNTS_TAX_AMOUNT}${UF_VALUE}${repeatedWords(
      40,
      ' '
    )}${DISCOUNTS_NUMBER}${DISCOUNTS}${PAID_DUES}${PENDING_DUES}${repeatedWords(
      123,
      ' '
    )}${SECUENCE_NUMBER}`
  ];
};

const mapperCollectorRetentionData = ({ pensionCodeId }, { ufValue, collectorRetention = {} }) => {
  const { collectorRut: rut } = collectorRetention;
  const health = 0;
  const totalDiscounts = 0;

  const { AGREEMENT_CODE, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '05';
  const [RUT, DV] = rut.replace(/[^0-9kK-]/g, '').split('-');

  const TOTAL_TAX_AMOUNT = repeatedWords(10, '0');
  const HEALTH_DISCOUNT = addOrCutWord(10, health, 'number');
  const UF_VALUE = addOrCutWord(10, ufValue, 'number') || repeatedWords(10, '0');
  const DISCOUNTS_TAX_AMOUNT = addOrCutWord(10, totalDiscounts, 'number');
  const PAID_DUES = repeatedWords(8, '0');
  const PENDING_DUES = repeatedWords(8, '0');
  const SECUENCE_NUMBER = startWith(6, pensionCodeId, '0');
  const HEALTH_TYPE = addOrCutWord(6, ' ');
  const DISCOUNTS_NUMBER = '00';
  const HEALTH_AFFILIATION = addOrCutWord(15, ' ');

  const DISCOUNTS = repeatedWords(130, '0');

  return [
    `${RECORD_TYPE}${startWith(9, RUT, '0')}${DV}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${HEALTH_DISCOUNT}${HEALTH_TYPE}${HEALTH_AFFILIATION}${TOTAL_TAX_AMOUNT}${DISCOUNTS_TAX_AMOUNT}${UF_VALUE}${repeatedWords(
      40,
      ' '
    )}${DISCOUNTS_NUMBER}${DISCOUNTS}${PAID_DUES}${PENDING_DUES}${repeatedWords(
      123,
      ' '
    )}${SECUENCE_NUMBER}`
  ];
};

const mapperRegisterMessage = () => {
  const dictionary = [];
  const { AGREEMENT_CODE, RUT_ACHS, DV_ACHS, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '06';
  const CODE_MESSAGE = '0000';
  const CODE_MESSAGE_MAN = '0001';
  const CODE_MESSAGE_WOMAN = '0002';
  const COMMENT =
    'Sr(a) pensionado(a), si usted recibe el pago de su pensión con depósito en bancuenta Banco de Chile y tiene tarjeta sin chip, le solicitamos acercarse a la oficina CrediChile más cercana a su domicilio para hacer el cambio a una con chip. Saludos!';
  const COMMENT_RETIREMENT_MAN =
    'Sr. Pensionado, usted está a menos de seis meses para pensionarse por edad (65 años), por lo que debe iniciar los trámites para obtener su pensión de vejez en la oficina del organismo previsional al que está afiliado, más cercana a su domicilio.';
  const COMMENT_RETIREMENT_WOMAN =
    'Sra. Pensionada, usted está a menos de seis meses para pensionarse por edad (60 años), por lo que debe iniciar los trámites para obtener su pensión de vejez en la oficina del organismo previsional al que está afiliado, más cercana a su domicilio.';

  dictionary.push([
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${CODE_MESSAGE}${addOrCutWord(250, COMMENT)}${repeatedWords(
      124,
      ' '
    )}`
  ]);
  dictionary.push([
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${CODE_MESSAGE_MAN}${addOrCutWord(
      250,
      COMMENT_RETIREMENT_MAN
    )}${repeatedWords(124, ' ')}`
  ]);
  dictionary.push([
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${CODE_MESSAGE_WOMAN}${addOrCutWord(
      250,
      COMMENT_RETIREMENT_WOMAN
    )}${repeatedWords(124, ' ')}`
  ]);

  return [...dictionary];
};
const buildDictionary = array => {
  const dictionary = [];
  const { AGREEMENT_CODE, RUT_ACHS, DV_ACHS, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '80';

  array.forEach(item => {
    dictionary.push([
      `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
        2,
        '0'
      )}${HEADER_PAYROLL_NUMBER}${addOrCutWord(3, item.value)}${addOrCutWord(
        20,
        item.name
      )}${repeatedWords(355, ' ')}`
    ]);
  });
  return dictionary;
};
const mapperType80 = glosses => {
  const glossesSort = glosses.sort((a, b) => (a.value < b.value ? -1 : Number(a.value > b.value)));
  const COMMENTS = buildDictionary(glossesSort);

  return [...COMMENTS];
};

const mapperType81 = () => {
  const { AGREEMENT_CODE, RUT_ACHS, DV_ACHS, HEADER_PAYROLL_NUMBER } = COMMONS_VARS;
  const RECORD_TYPE = '81';
  const SUBSIDIARY_CODE = '00000';
  const SUBSIDIARY_NAME = addOrCutWord(20, 'SIN FILIAL');

  return [
    `${RECORD_TYPE}${RUT_ACHS}${DV_ACHS}${AGREEMENT_CODE}${repeatedWords(
      2,
      '0'
    )}${HEADER_PAYROLL_NUMBER}${SUBSIDIARY_CODE}${SUBSIDIARY_NAME}${repeatedWords(353, ' ')}`
  ];
};

module.exports = {
  mapperHeader,
  mapperCollectorLine,
  mapperCollectorRetentionLine,
  mapperBeneficiaryLine,
  mapperBeneficiaryRetentionLine,
  mapperCollectorData,
  mapperCollectorRetentionData,
  mapperRegisterMessage,
  mapperType81,
  mapperType80,
  getMapper,
  assetsAndDiscountMappers: [...ASSETS_MAPPER, ...DISCOUNTS_MAPPER]
};

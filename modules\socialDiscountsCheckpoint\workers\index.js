/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../service/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'socialDiscountsCheckPoint',
  worker: async deps => workerModule.workerFn({ service, logService, ...deps }),
  repeatInterval: process.env.CRON_SOCIAL_DISCOUNTS_CHECK_POINT_FREQUENCY,
  description:
    'Punto de control para rebajar descuentos de cajas sociales si su sumatoria supera el 25% de la pensión imponible',
  endPoint: 'socialdiscountscheckpoint',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

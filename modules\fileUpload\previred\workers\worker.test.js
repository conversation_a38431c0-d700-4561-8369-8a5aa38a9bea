/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

describe('Previred worker Test', () => {
  beforeAll(beforeAllTests);

  let generatorArgs;
  let previredService;
  let Logger;
  let logService;
  let done;
  let storageService;
  beforeEach(() => {
    done = jest.fn();

    previredService = {
      generatePreviredFile: jest.fn(() => Promise.resolve(true))
    };

    storageService = {
      uploadFileFromLocal: jest.fn(() => Promise.resolve({ status: 200, data: '2443' })),
      saveFileRegistry: jest.fn(() => Promise.resolve({ result: 'qwerty' }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    generatorArgs = [];
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      done,
      logService,
      previredService,
      storageService,
      generatorArgs,
      moment
    });
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(previredService.generatePreviredFile).toBeCalled();
    expect(storageService.uploadFileFromLocal).toBeCalled();
    expect(storageService.saveFileRegistry).toBeCalled();
    expect(logService.saveLog).toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(4);
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      done,
      storageService,
      logService,
      previredService,
      generatorArgs
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(previredService.generatePreviredFile).not.toBeCalled();
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(2);
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      done,
      storageService,
      logService,
      previredService,
      generatorArgs
    });

    expect(logService.existsLog).toBeCalled();
    expect(previredService.generatePreviredFile).not.toBeCalled();
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('dependency mark not already created ', async () => {
    logService.existsLog = jest
      .fn(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false))
      .mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({
      Logger,
      done,
      logService,
      storageService,
      previredService,
      generatorArgs
    });

    expect(logService.existsLog).toBeCalled();
    expect(previredService.generatePreviredFile).not.toBeCalled();
    expect(storageService.uploadFileFromLocal).not.toBeCalled();
    expect(storageService.saveFileRegistry).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(2);
  });

  afterAll(afterAllTests);
});

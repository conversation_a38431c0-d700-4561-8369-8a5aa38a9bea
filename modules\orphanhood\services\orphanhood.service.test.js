/* eslint no-underscore-dangle: 0 */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const TemporaryHorphanhoodModel = require('../models/temporaryHorphanhood');
const service = require('./orphanhood.service');
const horphanhoodsData = require('../../../resources/horphanhoods.json');

const inactivationReasonOrphanhood = /Vencimiento de certificado de estudios/i;
const validityTypeOnDesactivationOrphanhood = /No vigente/i;

describe('Temporary Horphanhood Test', () => {
  beforeAll(beforeAllTests);
  it('should delete all previous documents on new bulk operation', async () => {
    const horphanhood = new TemporaryHorphanhoodModel(horphanhoodsData[0]);
    await horphanhood.save();
    const { result } = await service.bulkAndDeleteOrphanhood([...horphanhoodsData]);
    expect(result.length).toBe(horphanhoodsData.length);
  });

  it('should create and save temporary horphanhood successfully', async done => {
    const savehorphanhood = await TemporaryHorphanhoodModel.create(horphanhoodsData[0]);

    expect(savehorphanhood._id).toBeDefined();
    expect(savehorphanhood.pensionId).toBe(parseInt(horphanhoodsData[0].pensionId, 10));
    done();
  });

  it('should return true on wasInactivated service on empty model', async () => {
    await TemporaryHorphanhoodModel.deleteMany({});
    const { wasInactivated, isError, error } = await service.wasInactivatedOrphanhood(
      inactivationReasonOrphanhood,
      validityTypeOnDesactivationOrphanhood
    );
    expect(wasInactivated).toBe(true);
    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  afterAll(afterAllTests);
});

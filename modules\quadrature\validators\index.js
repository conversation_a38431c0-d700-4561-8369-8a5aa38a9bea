const { check } = require('express-validator');
const { escapeChars } = require('../../../lib/regex-utils');

const checkDigitValidation = rutWithDV => {
  const [rut, currentDV] = rutWithDV.replace(/\./, '').split('-');
  const module = 11;
  const multipliers = '234567'.split('').map(x => +x);
  const sumOfElements = rut
    .replace(/[^\d]/g, '')
    .split('')
    .reverse()
    .map((value, index) => +value * multipliers[index % multipliers.length])
    .reduce((accumulator, currentValue) => accumulator + currentValue);
  let actualDV = module - (sumOfElements % module);
  if (actualDV === module - 1) {
    actualDV = 'k';
    return actualDV === currentDV.toLowerCase();
  }
  if (actualDV === module) actualDV = 0;
  return actualDV === +currentDV;
};

const insertHyphen = rut => {
  if (rut.includes('-')) return rut;
  return rut.replace(/^(\d+)(\d|K)$/i, '$1-$2');
};

const validateKeyUniqueness = (key, index, allRows = []) => {
  const rowRegex = new RegExp(`^${escapeChars(key)}$`, 'i');
  return allRows.some((row, i) => {
    if (index !== i) {
      return rowRegex.test(row.key);
    }
    return false;
  });
};

const trimValue = (str = '') => str.trim();

const MAX_VALUE_FACTORS = 999.999;
const MAX_VALUE_CONCURRENCIES = 100;
const isNumberInRange = (number, maxValue) => number >= 0 && number <= maxValue;

const areKeysUnique = (data = []) =>
  !data.some((row, index) => validateKeyUniqueness(row.key, index, data));

const rutSanitizer = rut => rut.replace(/[^\dKk-]/g, '').toUpperCase();
const moneySanitizer = (amount = '') =>
  `${amount}`
    .replace(/,/, '.')
    .replace(/^(\d+)\.?(\d{0,2})?\d*$/, '$1.$2')
    .replace(/(\.0+|\.)$/, '');

const factorsSanitizer = (amount = 0) =>
  `${amount}`
    .replace(/,/, '.')
    .replace(/^(\d+)\.?(\d{0,3})?\d*$/, '$1.$2')
    .replace(/(\.0+|\.)$/, '');

const RUT_PATTERN = /^[1-9][0-9]?\d{6}-[0-9Kk]$/;
const MONEY_PATTERN = /^\d+(\.\d{0,2})?$/;
const FACTOR_PATTERN = /^\d+(\.\d{0,3})?$/;
const ALPHANUMERIC_PATTERN = /^([0-9a-záéíóúàèìòùãẽĩõũỹñäöüëïâêîôûçğş]+\s*)+$/i;

const factorValidators = [
  check('data')
    .isArray()
    .notEmpty()
    .custom(data => areKeysUnique(data))
    .withMessage('hay al menos un valor clave duplicado'),
  check('data.*.key')
    .notEmpty()
    .customSanitizer(key => trimValue(key))
    .matches(ALPHANUMERIC_PATTERN)
    .withMessage('la clave debe ser alfanumérica'),
  check('data.*.factor')
    .notEmpty()
    .customSanitizer(value => factorsSanitizer(value))
    .isNumeric()
    .matches(FACTOR_PATTERN)
    .withMessage('el número debe tener un patrón válido D + .DDD')
    .custom(factor => isNumberInRange(factor, MAX_VALUE_FACTORS))
    .withMessage('el número debe estar en el rango 0-100')
];

const concurrencyValidators = [
  check('data')
    .isArray()
    .notEmpty(),
  check('data.*.beneficiaryRut')
    .notEmpty()
    .customSanitizer(value => rutSanitizer(value))
    .customSanitizer(value => insertHyphen(value))
    .matches(RUT_PATTERN)
    .withMessage('rut beneficiario debe coincidir con el formato')
    .custom(value => checkDigitValidation(value))
    .withMessage('dígito verificador invalido'),
  check('data.*.concurrencyPercentage')
    .notEmpty()
    .withMessage('porcentaje de concurrencia vacio')
    .customSanitizer(value => moneySanitizer(value))
    .isNumeric()
    .withMessage('porcentaje de concurrencia debe ser numerico')
    .matches(MONEY_PATTERN)
    .withMessage('porcentaje de concurrencia debe tener un patrón válido D + .DD')
    .custom(concurrency => isNumberInRange(concurrency, MAX_VALUE_CONCURRENCIES))
    .withMessage('porcentaje de concurrencia debe estar en el rango 0-100'),
  check('data.*.concurrencyReceivable')
    .notEmpty()
    .withMessage('concurrencia por cobrar vacio')
    .customSanitizer(value => moneySanitizer(value))
    .isNumeric()
    .withMessage('concurrencia por cobrar debe ser numerico'),
  check('data.*.mutualPercentage')
    .notEmpty()
    .withMessage('porcentaje mutual vacio')
    .customSanitizer(value => moneySanitizer(value))
    .isNumeric()
    .withMessage('porcentaje mutual debe ser numerico'),
  check('data.*.istPercentage')
    .notEmpty()
    .withMessage('porcentaje ist vacio')
    .customSanitizer(value => moneySanitizer(value))
    .isNumeric()
    .withMessage('porcentaje ist debe ser numerico'),
  check('data.*.islPercentage')
    .notEmpty()
    .withMessage('porcentaje isl vacio')
    .customSanitizer(value => moneySanitizer(value))
    .isNumeric()
    .withMessage('porcentaje isl debe ser numerico')
];

module.exports = {
  concurrencyValidators,
  factorValidators,
  checkDigitValidation
};

const ipcModel = require('../../../models/ipcs');

const service = {
  async getLastPercentageChange() {
    const [lastPercentage] = await ipcModel
      .find({ isLastPercentageChange: true })
      .sort({ date: -1 })
      .limit(1)
      .exec();

    return lastPercentage;
  },
  async insertIPC(data) {
    try {
      const { date, isLastPercentageChange, value, percentage } = data;
      const result = await ipcModel.create({
        value: `${value}`,
        percentage: `${percentage}`,
        date: new Date(date),
        isLastPercentageChange
      });
      return {
        result,
        error: null
      };
    } catch (error) {
      return { result: {}, error };
    }
  },

  async getLastPercentage() {
    return new Promise(resolve => {
      ipcModel
        .find({})
        .sort({ date: -1 })
        .limit(1)
        .lean()
        .then(function(result) {
          resolve(result.find(reg => reg.date));
        });
    });
  }
};

module.exports = { ...service };

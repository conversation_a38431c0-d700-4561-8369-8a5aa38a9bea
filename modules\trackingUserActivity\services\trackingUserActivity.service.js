/* eslint-disable no-underscore-dangle */
const { detailedDiff } = require('deep-object-diff');
const { getNamespace } = require('cls-hooked');

const trackUserActivityModel = require('../../../models/trackingUserActivity');

const isBSONType = (obj = {}) => obj && obj._bsontype;
const isDate = date => date && typeof date.getTime === 'function' && !Number.isNaN(date.getTime());
const buildObjectForBSON = (parentObject, bsonKeys = []) =>
  bsonKeys.reduce((acc, currentKey) => {
    return {
      ...acc,
      [currentKey]: parentObject[currentKey].toString()
    };
  }, {});

const addPathToKey = (key, path) => (path ? `${path}-${key}` : key);

const filterKeysThatAreNotObjects = (parentObject = {}, arr = []) =>
  arr.filter(
    key =>
      (typeof parentObject[key] !== 'object' ||
        parentObject[key] instanceof Array ||
        isDate(parentObject[key])) &&
      !isBSONType(parentObject[key])
  );
const filterKeysThatContainsObjects = (parentObject = {}, arr = []) =>
  arr.filter(
    key =>
      parentObject[key] instanceof Object &&
      !isBSONType(parentObject[key]) &&
      !isDate(parentObject[key])
  );
const filterBSONKeys = (parentObject, keys = []) =>
  keys.filter(key => isBSONType(parentObject[key]));

const sortObjectByKeys = (obj = {}) => {
  const allKeys = Object.keys(obj);
  const sortedKeys = [...allKeys].sort((a, b) => a.localeCompare(b));

  return sortedKeys.reduce((acc, currentKey) => {
    return { ...acc, [currentKey]: obj[currentKey] };
  }, {});
};

const flattenObject = (parentObject, path, flattenedStorage = {}) => {
  if (!parentObject) return flattenedStorage;
  const allObjectKeys = Object.keys(parentObject);

  const keysThatAreNotObjects = filterKeysThatAreNotObjects(parentObject, allObjectKeys);
  const keysThatAreObjects = filterKeysThatContainsObjects(parentObject, allObjectKeys);
  const keysThatAreBSON = filterBSONKeys(parentObject, allObjectKeys);

  const currentNonObjectValues = keysThatAreNotObjects.reduce((acc, currentKey) => {
    return { ...acc, [`${addPathToKey(currentKey, path)}`]: parentObject[currentKey] };
  }, flattenedStorage);

  const bsonObject = buildObjectForBSON(parentObject, keysThatAreBSON);

  if (!keysThatAreObjects.length) return { ...currentNonObjectValues, ...bsonObject };

  return keysThatAreObjects.reduce(
    (acc, currentKey) => {
      return {
        ...acc,
        ...flattenObject(parentObject[currentKey], addPathToKey(currentKey, path), {
          ...currentNonObjectValues
        })
      };
    },
    { ...currentNonObjectValues, ...bsonObject }
  );
};

const computeDiff = (previousValue, currentValue) => {
  const {
    added: addedToCurrent,
    updated: updatedToCurrent,
    deleted: deletedToCurrent
  } = detailedDiff(previousValue, currentValue);

  const {
    added: addedToPrevious,
    updated: updatedToPrevious,
    deleted: deletedToPrevious
  } = detailedDiff(currentValue, previousValue);

  const previousFlattenedObject = [addedToPrevious, updatedToPrevious, deletedToPrevious].reduce(
    (acc, currentObj) => {
      return { ...acc, ...flattenObject(currentObj) };
    },
    {}
  );

  const currentFlattenedObject = [addedToCurrent, updatedToCurrent, deletedToCurrent].reduce(
    (acc, currentObj) => {
      return { ...acc, ...flattenObject(currentObj) };
    },
    {}
  );

  return {
    previousFlattenedObject: sortObjectByKeys(previousFlattenedObject),
    currentFlattenedObject: sortObjectByKeys(currentFlattenedObject)
  };
};

const getDataFromUser = ({ name, email, endpoint }) => {
  return { name, email, endpoint };
};

const getField = (path, data) =>
  path.split('-').reduce((object, nestedField) => object && object[nestedField], data);

const mapModelToUniqueId = {
  discountsandassets: ['beneficiaryRut', 'causantRut'],
  pensions: ['beneficiary-rut', 'causant-rut'],
  processedjobs: ['name', 'year', 'month'],
  roles: ['roleName'],
  updatepensionerinfos: ['beneficiaryRut', 'causantRut', 'pensionCodeId'],
  updatepensiontypes: ['beneficiaryRut', 'causantRut', 'pensionCodeId'],
  users: ['email']
};

const getUniqueIdentifier = (identifierArr, document) =>
  identifierArr.reduce((acc, currentPath) => {
    return { ...acc, [currentPath]: getField(currentPath, document) };
  }, {});

const service = {
  findOneAndUpdateWithTracking: async ({
    model,
    user,
    fullQuery,
    dbToTrack = trackUserActivityModel,
    customDiffAlgorithm = computeDiff,
    loggerForError
  }) => {
    const [findQuery, valuesToUpgrade, optionals = {}] = fullQuery;

    const previousDocument = await model.findOne(findQuery).lean();
    const updatedDocument = await model
      .findOneAndUpdate(findQuery, valuesToUpgrade, { ...optionals, new: true })
      .lean();

    const modelName = model.collection.name;
    const uniqueIdentifiersInModel = mapModelToUniqueId[modelName] || [];

    const documentUniqueIdentifier = getUniqueIdentifier(uniqueIdentifiersInModel, updatedDocument);

    if (user) {
      const changes = customDiffAlgorithm(previousDocument, updatedDocument);

      await dbToTrack
        .create({
          ...getDataFromUser(user),
          changes,
          action: 'actualizar documento',
          documentUniqueIdentifier,
          modelName
        })
        .catch(error => loggerForError && loggerForError.error(error));
    }

    return updatedDocument;
  },
  createWithTracking: async ({
    model,
    user,
    document,
    dbToTrack = trackUserActivityModel,
    loggerForError
  }) => {
    const createdDocument = await model.create(document);

    const modelName = model.collection.name;
    const uniqueIdentifiersInModel = mapModelToUniqueId[modelName] || [];

    const documentUniqueIdentifier = getUniqueIdentifier(uniqueIdentifiersInModel, createdDocument);

    if (user) {
      await dbToTrack
        .create({
          ...getDataFromUser(user),
          changes: createdDocument,
          documentUniqueIdentifier,
          modelName,
          action: 'crear documento'
        })
        .catch(error => loggerForError && loggerForError.error(error));
    }

    return createdDocument;
  },

  async findPreviousRecord() {
    try {
      const session = getNamespace('session');

      if (session) {
        const query = this.getQuery();
        const { _doc: model } = await this.model.findOne(query);

        this._previousModel = model;

        this._user = session.get('user');
      }
      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  trackingPostFindOneAndUpdate: (
    doc,
    context,
    customDiffAlgorithm = computeDiff
  ) => async action => {
    try {
      const { _previousModel: previousModel, _user: user } = context || {};
      if (user) {
        const { _doc: currentModel } = doc;
        const { name, email, endpoint } = user;

        const modelName = context._collection.collectionName;
        const uniqueIdentifiersInModel = mapModelToUniqueId[modelName] || [];

        const documentUniqueIdentifier = getUniqueIdentifier(
          uniqueIdentifiersInModel,
          currentModel
        );

        const changes = customDiffAlgorithm(previousModel, currentModel);

        await trackUserActivityModel.create({
          name,
          email,
          endpoint,
          modelName,
          documentUniqueIdentifier,
          changes,
          action
        });
      }
      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  previousSave: async () => {
    try {
      const session = getNamespace('session');

      if (session) {
        this._user = session.get('user');
      }
      return { completed: true };
    } catch (error) {
      return { error };
    }
  },

  trakingPostSave: (doc, modelName) => async action => {
    const user = this._user || doc._user;
    try {
      if (user) {
        const { _doc: currentModel } = doc;
        const { name, email, endpoint } = user;

        const uniqueIdentifiersInModel = mapModelToUniqueId[modelName] || [];

        const documentUniqueIdentifier = getUniqueIdentifier(
          uniqueIdentifiersInModel,
          currentModel
        );

        await trackUserActivityModel.create({
          name,
          email,
          endpoint,
          modelName,
          documentUniqueIdentifier,
          changes: { currentModel },
          action
        });
      }
      return { completed: true };
    } catch (error) {
      return { error };
    }
  },
  customDiffForAssetsAndDiscounts: (previousDocument, currentDocument) => {
    const {
      assetsNonFormulable: previousAssets,
      discountsNonFormulable: previousDiscounts,
      ...restOfPrevious
    } = previousDocument;

    const {
      assetsNonFormulable: currentAssets,
      discountsNonFormulable: currentDiscounts,
      ...restOfCurrent
    } = currentDocument;

    const diffAssets = computeDiff(previousAssets, currentAssets);
    const diffDiscounts = computeDiff(previousDiscounts, currentDiscounts);
    const diffOfRest = computeDiff(restOfPrevious, restOfCurrent);

    return { diffAssets, diffDiscounts, diffOfRest };
  },
  customDiffRoles: (previousDocument, currentDocument) => {
    const { views: previousViews } = previousDocument;
    const { views: currentViews } = currentDocument;

    const viewDifferencePrevious = previousViews.toObject().filter(({ view, permission }) => {
      const arrOfCurrentViews = currentViews.toObject();
      return arrOfCurrentViews.find(
        currentView =>
          currentView.permission !== permission && currentView.view.toString() === view.toString()
      );
    });

    const viewDifferenceCurrent = currentViews.toObject().filter(({ view, permission }) => {
      const arrOfPreviousViews = previousViews.toObject();
      return arrOfPreviousViews.find(
        previousView =>
          previousView.permission !== permission && previousView.view.toString() === view.toString()
      );
    });

    return { viewDifferencePrevious, viewDifferenceCurrent };
  }
};

module.exports = { ...service, computeDiff, flattenObject };

/* eslint-disable no-console */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
const Agenda = require('agenda');
const mongoose = require('mongoose');
const workers = require('../modules/workers');
const config = require('../lib/conf/config');
const { LoggerFactory } = require('../lib/logger');
const dbService = require('./db.service');

let Logger;
const validateCircularWorkers = graph => {
  let queue = Object.keys(graph).map(node => [node]);
  while (queue.length) {
    let batch = [];
    for (const path of queue) {
      const { 0: first, [path.length - 1]: last } = path;
      const parents = graph[first] || [];
      if (parents.find(child => child === last)) {
        return [last, ...path];
      }
      batch = [...batch, ...parents.map(child => [child, ...path])];
    }
    queue = batch;
  }
  return [];
};

const defineWorker = async ({ workers: workersDefinition, job, done, name, worker }) => {
  const dependencies = await dbService.getDependencies(name, Logger);
  const workerDependencies = Object.entries(dependencies).map(([workerName, isProcessed]) => ({
    workerName,
    isProcessed
  }));
  Logger.debug(` >>> worker execution start: ${name}`);
  for await (const dep of workerDependencies) {
    if (!dep.isProcessed) {
      await dbService.updateDependenciesExecution(name, {
        ...dependencies,
        [dep.workerName]: true
      });
      Logger.debug(`\t >>> worker dependency ${dep.workerName}`);
      await defineWorker({
        workers: workersDefinition,
        job,
        done,
        name: dep.workerName,
        worker: workersDefinition.find(w => w.name === dep.workerName).worker
      });
    }
  }
  if (workerDependencies.length > 0) {
    await dbService.restartDependencies(
      name,
      workerDependencies.reverse().map(w => w.workerName)
    );
  }
  Logger.debug(` >>> worker execution end: ${name}`);
  return worker({ job, done, Logger });
};

const definition = ({ workers: w, name, worker }) => async (job, done) =>
  defineWorker({ workers: w, job, done, name, worker });

const defineWorkers = async (agenda, workersDefinition) => {
  const options = {
    priority: 'normal'
  };
  for await (const { name, priority, dependencies, worker } of workersDefinition) {
    await dbService.restartDependencies(name, dependencies);
    agenda.define(
      name,
      { ...options, priority },
      definition({ workers: workersDefinition, name, worker })
    );
  }
};

const unlockWorkers = () =>
  mongoose.connection.db
    .collection('agendaJobs')
    .updateMany({ lockedAt: { $exists: true } }, { $set: { lockedAt: null } });

const initWorkers = async () => {
  Logger = LoggerFactory(config.get('logger').level);
  const databaseConfig = config.get('databaseConfig');
  const workersConfig = config.get('workersConfig');
  const dbConfig = databaseConfig;
  const agenda = new Agenda({
    db: {
      address: dbConfig.connectionUrl,
      options: {
        useUnifiedTopology: true
      }
    },
    ...workersConfig
  });

  const directedGraph = workers
    .map(({ name, dependencies }) => ({ name, dependencies: dependencies || [] }))
    .reduce(
      (graph, { name, dependencies }) => ({
        ...graph,
        [name]: [...dependencies]
      }),
      {}
    );
  const path = validateCircularWorkers(directedGraph);
  if (path.length) {
    throw new Error(`There is a circular dependencies between workers: ${path}`);
  }

  await defineWorkers(
    agenda,
    workers.map(({ dependencies, ...w }) => ({ ...w, dependencies: dependencies || [] }))
  );

  (async () => {
    await agenda.stop().catch(err => Logger.error(err));
    await unlockWorkers().catch(err => Logger.error(err));
    Logger.debug('Starting all jobs');
    await agenda.start();
    await Promise.all(
      workers.map(({ name, repeatInterval }) =>
        agenda.every(repeatInterval, name, null, { skipImmediate: true })
      )
    );
  })();
};

module.exports = initWorkers;

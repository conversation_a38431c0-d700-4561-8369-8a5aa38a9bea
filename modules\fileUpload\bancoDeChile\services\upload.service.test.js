/* eslint-disable no-console */
const puppeteer = require('puppeteer');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const { uploadFile } = require('./upload.service');

describe('Banco Chile file upload automation', () => {
  beforeAll(beforeAllTests);
  let mocks;
  beforeEach(() => {
    mocks = {
      newPage: jest.fn().mockResolvedValue({
        goto: jest.fn(),
        waitForSelector: jest.fn(),
        click: jest.fn(),
        select: jest.fn(),
        waitFor: jest.fn(),
        type: jest.fn(),
        hover: jest.fn(),
        $eval: jest.fn(),
        $: jest.fn().mockResolvedValue({ uploadFile: jest.fn() }),
        keyboard: { press: jest.fn() }
      }),
      close: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(puppeteer, 'launch').mockImplementationOnce(() => mocks);
  });
  const fileToUpload = 'myFile.txt';

  it('should automate banco Chile file upload', async () => {
    await uploadFile({ fileToUpload, puppeteer });
    expect(puppeteer.launch).toHaveBeenCalled();
    expect(mocks.newPage).toHaveBeenCalled();
    expect(mocks.close).toHaveBeenCalled();
  });

  it('should close browser if any error', async () => {
    mocks.newPage = jest.fn().mockImplementationOnce(() => {
      throw new Error('ERROR UPLOADIND');
    });
    try {
      await uploadFile({ fileToUpload, puppeteer });
      expect(mocks.close).toHaveBeenCalled();
    } catch (error) {
      expect(error).not.toBeUndefined();
    }
  });

  afterAll(afterAllTests);
});

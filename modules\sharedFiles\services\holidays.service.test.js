/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./holidays.service');

const holidays2021 = [
  moment('2021-01-01', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-04-02', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-04-03', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-04-04', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-05-01', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-05-21', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-06-28', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-07-16', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-08-15', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-09-17', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-09-18', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-09-19', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-10-11', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-10-31', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-11-01', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-12-08', 'YYYY-MM-DD').format('YYYY-MM-DD'),
  moment('2021-12-25', 'YYYY-MM-DD').format('YYYY-MM-DD')
];

describe('helper holidays Test', () => {
  beforeAll(beforeAllTests);

  it("get 2021 new year's day", async () => {
    const date = service.getNewYearsDay(2021);
    expect(date).toBe(holidays2021[0]);
  });

  it('get 2021 saint monday day (undefined)', async () => {
    const date = service.getSaintMondayDay(2021);
    expect(date).toBeUndefined();
  });

  it('get 2021 good friday day', async () => {
    const date = service.getGoodFridayDay(2021);
    expect(date).toBe(holidays2021[1]);
  });

  it('get 2021 holy satuday day', async () => {
    const date = service.getHolySaturdayDay(2021);
    expect(date).toBe(holidays2021[2]);
  });

  it('get 2021 easter sunday day', async () => {
    const date = service.getEasterSundayDay(2021);
    expect(date).toBe(holidays2021[3]);
  });

  it('get 2021 labour day', async () => {
    const date = service.getLabourDay(2021);
    expect(date).toBe(holidays2021[4]);
  });

  it('get 2021 navy day', async () => {
    const date = service.getNavyDay(2021);
    expect(date).toBe(holidays2021[5]);
  });

  it('get 2021 saint peter and saint paul day (shift to previous monday)', async () => {
    const date = service.getSaintPeterAndSaintPaulDay(2021);
    expect(date).toBe(holidays2021[6]);
  });

  it('get 2021 our lady of mount carmel day', async () => {
    const date = service.getOurLadyOfMountCarmelDay(2021);
    expect(date).toBe(holidays2021[7]);
  });

  it('get 2021 assumption day', async () => {
    const date = service.getAssumptionDay(2021);
    expect(date).toBe(holidays2021[8]);
  });

  it('get 2021 saint friday day (defined)', async () => {
    const date = service.getSaintFridayDay(2021);
    expect(date).toBe(holidays2021[9]);
  });

  it('get 2021 independence day', async () => {
    const date = service.getIndependenceDay(2021);
    expect(date).toBe(holidays2021[10]);
  });

  it('get 2021 day of the glories of the army', async () => {
    const date = service.getDayOfTheGloriesOfTheArmy(2021);
    expect(date).toBe(holidays2021[11]);
  });

  it('get 2021 colombus day (shift to previous monday)', async () => {
    const date = service.getColumbusDay(2021);
    expect(date).toBe(holidays2021[12]);
  });

  it('get 2021 evangelical and protestant churches day (same day)', async () => {
    const date = service.getEvangelicalAndProtestantChurchesDay(2021);
    expect(date).toBe(holidays2021[13]);
  });

  it('get 2021 all saints day', async () => {
    const date = service.getAllSaintsDay(2021);
    expect(date).toBe(holidays2021[14]);
  });

  it('get 2021 immaculate conception of the virgin day', async () => {
    const date = service.getImmaculateConceptionOfTheVirginDay(2021);
    expect(date).toBe(holidays2021[15]);
  });

  it('get 2021 christmas day', async () => {
    const date = service.getChristmasDay(2021);
    expect(date).toBe(holidays2021[16]);
  });

  it('get 2021 all holidays', async () => {
    const holidays = service.getHolidays(2021);
    expect(holidays).toStrictEqual(holidays2021);
  });

  it('get 2023 saint monday day (defined)', async () => {
    const date = service.getSaintMondayDay(2023);
    expect(date).toBe(moment('2023-01-02', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2022 saint friday day (undefined)', async () => {
    const date = service.getSaintFridayDay(2022);
    expect(date).toBeUndefined();
  });

  it('get 2024 saint peter and saint paul day (same day)', async () => {
    const date = service.getSaintPeterAndSaintPaulDay(2024);
    expect(date).toBe(moment('2024-06-29', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2029 saint peter and saint paul day (shift to next monday)', async () => {
    const date = service.getSaintPeterAndSaintPaulDay(2029);
    expect(date).toBe(moment('2029-07-02', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2024 colombus day (same day)', async () => {
    const date = service.getColumbusDay(2024);
    expect(date).toBe(moment('2024-10-12', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2029 colombus day (shift to next monday)', async () => {
    const date = service.getColumbusDay(2029);
    expect(date).toBe(moment('2029-10-15', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2023 evangelical and protestant churches day (shift to previous friday)', async () => {
    const date = service.getEvangelicalAndProtestantChurchesDay(2023);
    expect(date).toBe(moment('2023-10-27', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  it('get 2021 evangelical and protestant churches day (shift to next friday)', async () => {
    const date = service.getEvangelicalAndProtestantChurchesDay(2029);
    expect(date).toBe(moment('2029-11-02', 'YYYY-MM-DD').format('YYYY-MM-DD'));
  });

  afterAll(afterAllTests);
});

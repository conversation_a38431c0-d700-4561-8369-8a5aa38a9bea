/* eslint-disable no-param-reassign */
const moment = require('moment');
const liquidationHistoric = require('../../../../models/liquidationHistoric');

const isOutOfRange = date => {
  const minDate = moment(new Date('01-01-2020')).startOf('month');
  const maxDate = moment(new Date()).startOf('month');

  const isBeyond = date.diff(maxDate, 'months') > 0;
  const isBelow = date.diff(minDate, 'months') < 0;

  return isBeyond || isBelow;
};

const isBadRequest = (startingDate, endingDate) => {
  // eslint-disable-next-line no-restricted-globals
  if (!startingDate.isValid() || !endingDate.isValid()) return 'Invalid Date';
  if (isOutOfRange(startingDate) || isOutOfRange(endingDate))
    return 'Either Starting or Ending Date are out of range';
  if (startingDate.diff(endingDate, 'months') > 0) return 'Starting date is above Ending date';
  return '';
};

const generateDates = (startMonth, startYear, months) =>
  [...Array(months)].map((v, i) => {
    if ((i + startMonth) % 12 === 0 && i > 1) {
      // eslint-disable-next-line no-plusplus
      startYear++;
    }
    const month =
      ((i + startMonth) % 12) + 1 < 10
        ? `0${((i + startMonth) % 12) + 1}`
        : `${((i + startMonth) % 12) + 1}`;
    return `${month}-${startYear}`;
  });

const generateHashMap = data =>
  data.length
    ? data.reduce((obj, { beneficiaryRut, causantRut }) => {
        obj[`${beneficiaryRut} ${causantRut}`] = { beneficiaryRut, causantRut };
        return obj;
      }, {})
    : {};

const maxObjectWithinKey = (data = []) =>
  data.reduce((prev, current) => {
    return prev.pension.createdAt > current.pension.createdAt ? prev : current;
  });

const filterValuesByMonth = data => {
  const hashMap = generateHashMap(data);
  const valuesByMonth =
    JSON.stringify(hashMap) !== '{}'
      ? Object.keys(hashMap).map(keyValue =>
          data.filter(
            x =>
              x.beneficiaryRut === hashMap[keyValue].beneficiaryRut &&
              x.causantRut === hashMap[keyValue].causantRut
          )
        )
      : [];
  const maxByKey = valuesByMonth.map(x => maxObjectWithinKey(x));
  return maxByKey;
};

const customFlat = data => data.reduce((acc, curr) => [...acc, ...curr], []);

const getLiqByMonth = async date => {
  const startOfMonth = moment(date, 'MM-YYYY')
    .startOf('month')
    .toDate();
  const endOfMonth = moment(date, 'MM-YYYY')
    .endOf('month')
    .toDate();
  const liquidation = await liquidationHistoric
    .aggregate([
      {
        $match: {
          createdAt: {
            $gte: startOfMonth,
            $lt: endOfMonth
          }
        }
      },
      {
        $lookup: {
          from: 'pensions',
          let: {
            beneficiaryP: '$beneficiaryRut',
            causantP: '$causantRut',
            pensionCodeP: '$pensionCodeId'
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$beneficiary.rut', '$$beneficiaryP']
                    },
                    {
                      $eq: ['$causant.rut', '$$causantP']
                    },
                    {
                      $eq: ['$pensionCodeId', '$$pensionCodeP']
                    }
                  ]
                }
              }
            }
          ],
          as: 'pension'
        }
      },
      {
        $unwind: '$pension'
      },
      {
        $redact: {
          $cond: [
            {
              $and: [
                { $eq: ['$causantRut', '$pension.causant.rut'] },
                { $gte: ['$pension.createdAt', startOfMonth] },
                { $lte: ['$pension.createdAt', endOfMonth] }
              ]
            },
            '$$KEEP',
            '$$PRUNE'
          ]
        }
      }
    ])
    .exec();
  const filteredValues = filterValuesByMonth(liquidation);
  return filteredValues;
};

const service = {
  async matchLiquidationPension(startingDate, endingDate) {
    try {
      if (!startingDate || !endingDate)
        throw new Error('Neither StartingDate nor EndingDate are being provided');
      let liquidations = [];

      const start = moment(new Date(startingDate)).startOf('month');
      const end = moment(new Date(endingDate)).startOf('month');

      const badRequestError = isBadRequest(start, end);

      if (!badRequestError) {
        const startingYear = start.year();
        const startingMonth = start.month();

        const numberOfMonths = end.diff(start, 'months') + 1;
        const datesToQuery = generateDates(startingMonth, startingYear, numberOfMonths);
        liquidations = await Promise.all(datesToQuery.map(date => getLiqByMonth(date)));
      }

      return {
        isError: false,
        result: customFlat(liquidations),
        error: '',
        badRequestError
      };
    } catch (error) {
      return {
        isError: true,
        result: [],
        error,
        badRequestError: false
      };
    }
  }
};

module.exports = { ...service };

import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JsonbOperationsService } from '../services/jsonb-operations.service';
import { PensionCalculationJsonbService } from '../services/pension-calculation-jsonb.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  PensionAssets,
  PensionDiscounts,
  JsonbUpdateOperation,
  JsonbQuery
} from '../types/pension-jsonb.types';

@ApiTags('JSONB Pension Operations')
@Controller('pensions/jsonb')
@UseGuards(JwtAuthGuard)
export class JsonbPensionController {

  constructor(
    private readonly jsonbOps: JsonbOperationsService,
    private readonly calculationService: PensionCalculationJsonbService
  ) {}

  @Get('search')
  @ApiOperation({ 
    summary: 'Búsqueda avanzada usando operadores JSONB',
    description: 'Utiliza las capacidades nativas de PostgreSQL JSONB para búsquedas complejas'
  })
  @ApiQuery({ name: 'hasAps', required: false, type: Boolean })
  @ApiQuery({ name: 'minFamilyAssignment', required: false, type: Number })
  @ApiQuery({ name: 'bonusType', required: false, type: String })
  @ApiQuery({ name: 'assetReason', required: false, type: String })
  async searchPensionsByAssets(
    @Query('hasAps') hasAps?: boolean,
    @Query('minFamilyAssignment') minFamilyAssignment?: number,
    @Query('bonusType') bonusType?: string,
    @Query('assetReason') assetReason?: string
  ) {
    const criteria = {
      hasAps,
      minFamilyAssignment,
      bonusType,
      assetReason
    };

    const pensions = await this.jsonbOps.findPensionsByAssetsCriteria(criteria);

    return {
      success: true,
      data: pensions,
      count: pensions.length,
      query: criteria
    };
  }

  @Get('judicial-retentions')
  @ApiOperation({ 
    summary: 'Buscar pensiones con retenciones judiciales activas',
    description: 'Utiliza operadores JSONB para encontrar retenciones judiciales válidas'
  })
  async findPensionsWithJudicialRetentions() {
    const pensions = await this.jsonbOps.findPensionsWithActiveJudicialRetentions();

    return {
      success: true,
      data: pensions.map(p => ({
        id: p.id,
        pensionCodeId: p.pension_code_id,
        beneficiaryRut: p.beneficiary_rut,
        activeRetentions: p.active_retentions,
        totalRetentions: p.active_retentions?.length || 0
      })),
      count: pensions.length
    };
  }

  @Post('complex-search')
  @ApiOperation({ 
    summary: 'Búsqueda compleja con múltiples criterios JSONB',
    description: 'Permite combinar múltiples operadores JSONB en una sola consulta'
  })
  async complexJsonbSearch(@Body() queries: JsonbQuery[]) {
    const pensions = await this.jsonbOps.findPensionsByJsonbCriteria(queries);

    return {
      success: true,
      data: pensions,
      count: pensions.length,
      appliedQueries: queries
    };
  }

  @Put(':id/assets')
  @ApiOperation({ 
    summary: 'Actualizar assets usando operaciones JSONB atómicas',
    description: 'Actualiza campos específicos dentro del JSONB assets sin afectar otros campos'
  })
  @ApiParam({ name: 'id', description: 'ID de la pensión' })
  async updateAssets(
    @Param('id') pensionId: string,
    @Body() assetsUpdate: Partial<PensionAssets>
  ) {
    const operations: JsonbUpdateOperation[] = Object.entries(assetsUpdate).map(([key, value]) => ({
      field: 'assets',
      operation: 'set',
      path: key,
      value
    }));

    await this.jsonbOps.updateJsonbField(pensionId, operations);

    return {
      success: true,
      message: 'Assets actualizados exitosamente',
      updatedFields: Object.keys(assetsUpdate)
    };
  }

  @Put(':id/discounts')
  @ApiOperation({ 
    summary: 'Actualizar descuentos usando merge JSONB',
    description: 'Combina los nuevos descuentos con los existentes usando operador JSONB ||'
  })
  async updateDiscounts(
    @Param('id') pensionId: string,
    @Body() discountsUpdate: Partial<PensionDiscounts>
  ) {
    await this.jsonbOps.updateJsonbField(pensionId, [{
      field: 'discounts',
      operation: 'merge',
      value: discountsUpdate
    }]);

    return {
      success: true,
      message: 'Descuentos actualizados exitosamente'
    };
  }

  @Post(':id/judicial-retention')
  @ApiOperation({ 
    summary: 'Agregar retención judicial usando append JSONB',
    description: 'Agrega una nueva retención judicial al array existente'
  })
  async addJudicialRetention(
    @Param('id') pensionId: string,
    @Body() retention: any
  ) {
    await this.jsonbOps.updateJsonbField(pensionId, [{
      field: 'discounts',
      operation: 'append',
      path: 'judicialRetentions',
      value: {
        ...retention,
        id: `retention_${Date.now()}`,
        createdAt: new Date().toISOString()
      }
    }]);

    return {
      success: true,
      message: 'Retención judicial agregada exitosamente'
    };
  }

  @Post(':id/calculate-optimized')
  @ApiOperation({ 
    summary: 'Cálculo optimizado usando JSONB',
    description: 'Realiza cálculos aprovechando las capacidades de JSONB para mejor performance'
  })
  async calculateOptimized(@Param('id') pensionId: string) {
    const assets = await this.calculationService.calculateAndUpdateAssets(pensionId);
    const discounts = await this.calculationService.calculateAndUpdateDiscounts(pensionId);

    return {
      success: true,
      data: {
        assets,
        discounts,
        calculatedAt: new Date().toISOString()
      },
      message: 'Cálculo optimizado completado'
    };
  }

  @Post('batch-calculate-jsonb')
  @ApiOperation({ 
    summary: 'Cálculo masivo optimizado con JSONB',
    description: 'Procesa múltiples pensiones usando queries JSONB optimizadas'
  })
  async batchCalculateJsonb(
    @Body() filters: {
      pensionType?: string;
      hasAssets?: boolean;
      updatedBefore?: string;
    }
  ) {
    const processedFilters = {
      ...filters,
      updatedBefore: filters.updatedBefore ? new Date(filters.updatedBefore) : undefined
    };

    const result = await this.calculationService.calculateBatchWithJsonbOptimization(processedFilters);

    return {
      success: true,
      data: result,
      message: `Procesadas ${result.processed} pensiones con ${result.errors.length} errores`
    };
  }

  @Get('statistics')
  @ApiOperation({ 
    summary: 'Estadísticas usando agregaciones JSONB',
    description: 'Genera reportes estadísticos aprovechando las funciones de agregación JSONB'
  })
  async getJsonbStatistics() {
    const [assetsStats, reportData] = await Promise.all([
      this.jsonbOps.getAssetsStatistics(),
      this.calculationService.generateJsonbReport()
    ]);

    return {
      success: true,
      data: {
        assetsStatistics: assetsStats,
        detailedReport: reportData
      }
    };
  }

  @Get('advanced-search')
  @ApiOperation({ 
    summary: 'Búsqueda avanzada con criterios complejos',
    description: 'Combina múltiples criterios JSONB para búsquedas sofisticadas'
  })
  @ApiQuery({ name: 'minAps', required: false, type: Number })
  @ApiQuery({ name: 'hasJudicialRetentions', required: false, type: Boolean })
  @ApiQuery({ name: 'bonusConfig', required: false, type: String })
  @ApiQuery({ name: 'calculationVersion', required: false, type: String })
  async advancedSearch(
    @Query('minAps') minAps?: number,
    @Query('hasJudicialRetentions') hasJudicialRetentions?: boolean,
    @Query('bonusConfig') bonusConfig?: string,
    @Query('calculationVersion') calculationVersion?: string
  ) {
    const criteria = {
      minAps,
      hasJudicialRetentions,
      bonusConfig,
      calculationVersion
    };

    const pensions = await this.calculationService.findPensionsWithComplexCriteria(criteria);

    return {
      success: true,
      data: pensions,
      count: pensions.length,
      criteria
    };
  }

  @Post('create-indexes')
  @ApiOperation({ 
    summary: 'Crear índices JSONB optimizados',
    description: 'Crea índices GIN y específicos para mejorar performance de consultas JSONB'
  })
  async createJsonbIndexes() {
    await this.jsonbOps.createJsonbIndexes();

    return {
      success: true,
      message: 'Índices JSONB creados exitosamente'
    };
  }

  @Get(':id/audit-trail')
  @ApiOperation({ 
    summary: 'Obtener pista de auditoría desde JSONB',
    description: 'Recupera el historial de cambios almacenado en el campo audit_trail JSONB'
  })
  async getAuditTrail(@Param('id') pensionId: string) {
    const result = await this.jsonbOps.findPensionsByJsonbCriteria([{
      field: 'auditTrail',
      operator: 'path_exists',
      path: '0'
    }]);

    const pension = result.find(p => p.id === pensionId);

    return {
      success: true,
      data: {
        pensionId,
        auditTrail: pension?.auditTrail || [],
        totalEntries: pension?.auditTrail?.length || 0
      }
    };
  }
}

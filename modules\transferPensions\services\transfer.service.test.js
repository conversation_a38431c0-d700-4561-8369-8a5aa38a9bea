const moment = require('moment');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionerModel = require('../../../models/pension');
const TemporaryPensionerModel = require('../../linkPensions/models/temporaryPensioner');

const pensionerData = require('../../../resources/pensions.json')[0];

const service = require('./transfer.service');

describe('Temporary Pensioner service Model Test', () => {
  beforeAll(beforeAllTests);
  let mocks;

  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn()
    };
    jest.spyOn(PensionerModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should transfer pensions from temporary table', async () => {
    const findMock = {
      lean: jest.fn().mockResolvedValue([
        {
          ...pensionerData,
          disabilityType: '',
          pensionType: 'invalidez'
        }
      ])
    };
    jest.spyOn(TemporaryPensionerModel, 'find').mockImplementationOnce(() => findMock);
    const { isError, error } = await service.transferPensions();

    const pensionsCount = await PensionerModel.countDocuments();

    expect(pensionsCount).toBe(1);
    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  it('should set the endOfValidyDate to the 31st december of the year when the pensioner will complete 24 years', async () => {
    await TemporaryPensionerModel.create({
      ...pensionerData,
      dateOfBirth: moment('19960409').toDate(),
      validityType: 'Vigente orfandad',
      pensionType: 'Pension por orfandad'
    });
    await service.transferPensions();
    const pension = await PensionerModel.find({});
    expect(pension[0].endDateOfValidity).toBeDefined();
    expect(moment(pension[0].endDateOfValidity).format('YYYYMMDD')).toBe('20201231');
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionerModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const axios = require('axios');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { getSAPSinisterData, formatSinisterResponse } = require('./sapSinisterData');
const { sinisterSapServiceData } = require('../../../resources/daysOfTranstientPensions.json');

jest.mock('axios');

describe('Sap Sinister Data - formatSinisterResponse', () => {
  beforeAll(beforeAllTests);

  it('empty data', () => {
    const array = formatSinisterResponse([], '', '');

    expect(array).toStrictEqual([]);
  });

  it('empty data', () => {
    expect(() =>
      formatSinisterResponse([{ fechaInicioReposo: '', fechaAlta: '', tipoAlta: '' }], '', '')
    ).toThrow('Pension has non valid data');
  });

  it('success data formatting', () => {
    const result = formatSinisterResponse(
      [{ fechaInicioReposo: '2020-11-01', fechaAlta: '2020-11-01', tipoAlta: 2 }],
      '1',
      'YYYY-MM-DD'
    );
    expect(result).toStrictEqual([
      { fechaInicioReposo: '2020-11-01', fechaAlta: '2020-11-01', tipoAlta: 2, idSiniestro: '1' }
    ]);
  });

  it('empty data on request', async () => {
    axios.get.mockImplementationOnce(() => Promise.resolve(sinisterSapServiceData));
    await expect(getSAPSinisterData('', axios)).rejects.toThrow(
      "Cannot read property 'then' of undefined"
    );
  });

  it('request with data ', async () => {
    const Axios = jest.fn(() => {
      return Promise.resolve({
        data: {
          reposos: [
            {
              fechaInicioReposo: '2019-07-25',
              fechaAlta: '2019-07-25',
              tipoAlta: 1,
              idSiniestro: '0006456159'
            }
          ]
        },
        idSiniestro: ''
      });
    });

    const result = await getSAPSinisterData('', Axios);

    expect(result).toStrictEqual([
      {
        fechaInicioReposo: '2019-07-25',
        fechaAlta: '2019-07-25',
        tipoAlta: 1,
        idSiniestro: undefined
      }
    ]);
  });

  afterAll(afterAllTests);
});

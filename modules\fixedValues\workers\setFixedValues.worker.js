/* eslint-disable consistent-return */
const cronDescription = 'reset fixed values';
const cronMsgText = 'setting fix value base pension, article40 y article41.';
const cronMark = 'SET_FIXED_BASE_PENSION_VALUE';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = 'Process completed successfully.';
const cronDenpendency1 = 'REAJUST_BY_IPC';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDependencyMessage = dep => `Dependency "${dep}" not yet executed`;
const workerFn = async ({ Logger, done, service, logService, setFixedValues, job }) => {
  try {
    Logger.info(`Start ${cronMsgText}`);
    Logger.info(`${cronMark} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDenpendency1} checking whether this process was previously executed`);
    if (!(await logService.existsLog(cronDenpendency1))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(cronDenpendency1)}`);
      return { message: getMissingDependencyMessage(cronDenpendency1) };
    }

    const { error } = await service.basePensionAndArticlesFixedValues(setFixedValues);

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`Finish ${cronMsgText}`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, cronDenpendency1, workerFn };

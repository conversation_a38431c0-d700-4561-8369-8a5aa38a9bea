const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const StoredFilesSchema = new Schema(
  {
    virtualPath: { type: String, required: true },
    uuid: { type: String, required: true }
  },
  { timestamps: true }
);

StoredFilesSchema.plugin(paginate);
StoredFilesSchema.index({ virtualPath: 1 }, { unique: true });

module.exports = mongoose.model('StoredFiles', StoredFilesSchema);

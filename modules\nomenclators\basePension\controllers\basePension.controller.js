module.exports = ({
  HttpStatus,
  minimunService,
  validationResult,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  const service = minimunService;

  return {
    getAll: async (req, res) => {
      const result = await service.getAll();

      res.status(HttpStatus.OK).json(result);
    },
    update: async (req, res) => {
      const {
        body: { data }
      } = req;
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }

      const successMessage = 'Los valores se guardaron correctamente';

      const { completed, error } = await service.update(data);

      Logger.info('Maintainer Update Service: ', { completed, error });

      if (error) {
        Logger.error(`Maintainer Update Service: error: ${JSON.stringify(error)}`, req.details);

        res.status(HttpStatus.OK).json({});
      } else {
        res.status(HttpStatus.OK).json({ completed, message: successMessage, error });
      }
    }
  };
};

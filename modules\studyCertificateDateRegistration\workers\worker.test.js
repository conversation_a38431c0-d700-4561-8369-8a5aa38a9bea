/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker study Certificate Date Registration Test', () => {
  beforeAll(beforeAllTests);
  let familyAssignmentService;
  let service;
  let Logger;
  let done;

  let logService;
  beforeEach(() => {
    service = {
      registerStartAndEndDateOfStudyCertificate: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };
    familyAssignmentService = {
      fileAlreadyImported: jest.fn(() => Promise.resolve(true))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
  });

  it('success worker retroactive', async () => {
    await workerModule.workerFn({ Logger, logService, familyAssignmentService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.registerStartAndEndDateOfStudyCertificate).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked retroactive in current month ', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, familyAssignmentService, logService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.registerStartAndEndDateOfStudyCertificate).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker retroactive', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, familyAssignmentService, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.registerStartAndEndDateOfStudyCertificate).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

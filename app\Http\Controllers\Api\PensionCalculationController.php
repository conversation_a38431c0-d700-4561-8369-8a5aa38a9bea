<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Domain\Pension\Models\Pension;
use App\Domain\Pension\Services\PensionCalculationService;
use App\Jobs\CalculatePensionBenefitsAndDiscountsJob;
use App\Http\Requests\CalculatePensionRequest;
use App\Http\Requests\BatchCalculationRequest;
use App\Http\Resources\PensionCalculationResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class PensionCalculationController extends Controller
{
    public function __construct(
        private PensionCalculationService $calculationService
    ) {}

    /**
     * Calcula beneficios y descuentos de una pensión específica
     */
    public function calculate(CalculatePensionRequest $request): JsonResponse
    {
        $pension = Pension::with(['discountsAndAssets', 'currentLiquidation'])
            ->findOrFail($request->pension_id);

        try {
            $result = $this->calculationService->calculatePension($pension);

            return response()->json([
                'success' => true,
                'data' => new PensionCalculationResource($result),
                'message' => 'Cálculo realizado exitosamente'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al calcular la pensión: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Inicia cálculo masivo de pensiones
     */
    public function batchCalculate(BatchCalculationRequest $request): JsonResponse
    {
        $filters = $request->validated();
        $batchId = Str::uuid()->toString();

        // Obtener IDs de pensiones que cumplen los filtros
        $query = Pension::enabled();

        if (isset($filters['pension_type'])) {
            $query->where('pension_type', $filters['pension_type']);
        }

        if (isset($filters['validity_type'])) {
            $query->where('validity_type', $filters['validity_type']);
        }

        if (isset($filters['updated_before'])) {
            $query->where('updated_at', '<', $filters['updated_before']);
        }

        $pensionIds = $query->pluck('id');

        if ($pensionIds->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No se encontraron pensiones que cumplan los criterios'
            ], 400);
        }

        // Dividir en chunks para procesamiento paralelo
        $chunks = $pensionIds->chunk(500);
        $totalJobs = $chunks->count();

        foreach ($chunks as $index => $chunk) {
            CalculatePensionBenefitsAndDiscountsJob::dispatch(
                $chunk,
                "{$batchId}_{$index}",
                $filters
            )->delay(now()->addSeconds($index * 5)); // Escalonar jobs
        }

        // Inicializar progreso
        Cache::put("batch_progress_{$batchId}", [
            'status' => 'started',
            'total_pensions' => $pensionIds->count(),
            'total_jobs' => $totalJobs,
            'processed' => 0,
            'started_at' => now()->toISOString()
        ], 7200);

        return response()->json([
            'success' => true,
            'data' => [
                'batch_id' => $batchId,
                'total_pensions' => $pensionIds->count(),
                'total_jobs' => $totalJobs
            ],
            'message' => 'Cálculo masivo iniciado exitosamente'
        ]);
    }

    /**
     * Consulta el progreso de un cálculo masivo
     */
    public function batchProgress(string $batchId): JsonResponse
    {
        $progress = Cache::get("batch_progress_{$batchId}");

        if (!$progress) {
            return response()->json([
                'success' => false,
                'message' => 'Batch no encontrado o expirado'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $progress
        ]);
    }

    /**
     * Recalcula pensiones desactualizadas
     */
    public function recalculateOutdated(): JsonResponse
    {
        try {
            $processed = $this->calculationService->recalculateOutdatedPensions();

            return response()->json([
                'success' => true,
                'data' => ['processed' => $processed],
                'message' => "Se procesaron {$processed} pensiones desactualizadas"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error al recalcular pensiones: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene estadísticas de cálculos
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_pensions' => Pension::enabled()->count(),
            'calculated_today' => Pension::enabled()
                ->whereDate('calculated_at', today())
                ->count(),
            'outdated_pensions' => Pension::enabled()
                ->where('updated_at', '<', now()->subDays(30))
                ->count(),
            'pending_calculations' => Cache::get('pending_calculations', 0),
            'average_calculation_time' => Cache::get('avg_calculation_time', 0),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Simula cálculo sin guardar cambios
     */
    public function simulate(CalculatePensionRequest $request): JsonResponse
    {
        $pension = Pension::with(['discountsAndAssets', 'currentLiquidation'])
            ->findOrFail($request->pension_id);

        try {
            // Crear copia temporal para simulación
            $tempPension = $pension->replicate();
            
            // Aplicar cambios temporales si se proporcionan
            if ($request->has('temporary_changes')) {
                $tempPension->fill($request->temporary_changes);
            }

            $result = $this->calculationService->calculatePension($tempPension);

            return response()->json([
                'success' => true,
                'data' => [
                    'simulation' => new PensionCalculationResource($result),
                    'current' => [
                        'net_pension' => $pension->currentLiquidation?->net_pension ?? 0,
                        'total_assets' => $pension->total_assets,
                        'total_discounts' => $pension->total_discounts,
                    ],
                    'difference' => [
                        'net_pension' => $result->netPension - ($pension->currentLiquidation?->net_pension ?? 0),
                    ]
                ],
                'message' => 'Simulación realizada exitosamente'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error en la simulación: ' . $e->getMessage()
            ], 500);
        }
    }
}

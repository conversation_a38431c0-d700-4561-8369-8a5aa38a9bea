const PensionModel = require('../../../../models/pension');

const VALIDITY_TYPE = /^No\s+vigente$/i;

const getPensions = async rutList => {
  return PensionModel.find({
    validityType: { $not: VALIDITY_TYPE },
    enabled: true,
    'beneficiary.rut': { $in: rutList }
  }).lean();
};

const findItemInArray = (item, list) => {
  return list.find(itemList => itemList.toLowerCase() === item.toLowerCase());
};

const getMappedAIIIPensions = (pensionsList, linesObj) => {
  const listOfRutsInFile = Object.keys(linesObj);

  return pensionsList.map(({ _id, __v, ...data }) => {
    const { rut } = data.beneficiary;

    const actualRut = findItemInArray(rut, listOfRutsInFile);

    const { amount, resolutionNumber, resolutionDate, paymentUniqueId, transferCode } = linesObj[
      actualRut
    ];
    return {
      ...data,
      assets: { ...data.assets, aps: amount, healthExemption: 'Si' },
      apsInfo: {
        ...data.apsInfo,
        apsOrigin: '01',
        apsResolutionNumber: resolutionNumber,
        apsResolutionDate: resolutionDate,
        apsPaymentUniqueId: paymentUniqueId,
        apsTransferCode: transferCode
      }
    };
  });
};

const getMappedBrsaludPensions = pensionsList => {
  return pensionsList.map(({ _id, __v, ...data }) => {
    return { ...data, assets: { ...data.assets, aps: 0, healthDiscount: 'Si' } };
  });
};

const getMappedPapsoePensions = pensionsList => {
  return pensionsList.map(({ _id, __v, ...data }) => {
    return {
      ...data,
      assets: { ...data.assets, aps: 0, healthExemption: 'Si' },
      apsInfo: { ...data.apsInfo, apsOrigin: '00' }
    };
  });
};

const mapLinesDataToObject = lines => {
  const rutRegexList = [];
  const linesMappedToObj = {};

  try {
    lines.forEach(line => {
      const [
        rut,
        apsAssetValue,
        resolutionNumber,
        resolutionDate,
        paymentUniqueId,
        transferCode
      ] = line;
      if (rut && apsAssetValue) {
        const rutRegex = new RegExp(rut, 'i');
        if (!rutRegexList.includes(rutRegex)) rutRegexList.push(rutRegex);
        linesMappedToObj[rut] = {
          amount: (linesMappedToObj[rut] ? linesMappedToObj[rut].amount : 0) + apsAssetValue
        };
        linesMappedToObj[rut].resolutionNumber = resolutionNumber;
        linesMappedToObj[rut].resolutionDate = resolutionDate;
        linesMappedToObj[rut].paymentUniqueId = paymentUniqueId;
        linesMappedToObj[rut].transferCode = transferCode;
      }
    });
    return [rutRegexList, linesMappedToObj];
  } catch (error) {
    return { isError: true, error };
  }
};

module.exports = {
  async updatePensions(pensionsService, parsedLines) {
    const { parsedAIIItapsLines, parsedPapsoeLines, parsedBrsaludLines } = parsedLines;
    const [aIIItapsRutList, aIIItapsLinesObj] = mapLinesDataToObject(parsedAIIItapsLines);
    const papsoeRutList = parsedPapsoeLines.map(rut => new RegExp(rut, 'i'));
    const brsaludRutList = parsedBrsaludLines.map(rut => new RegExp(rut, 'i'));

    try {
      const aIIItapPensions = await getPensions(aIIItapsRutList);
      const papsoePensions = await getPensions(papsoeRutList);
      const brsaludPension = await getPensions(brsaludRutList);
      const mappedaIIItapPensions = getMappedAIIIPensions(aIIItapPensions, aIIItapsLinesObj);
      const mappedpapsoePensions = getMappedPapsoePensions(papsoePensions);
      const mappedbrsaludPensions = getMappedBrsaludPensions(brsaludPension);

      const pensionList = [
        ...mappedaIIItapPensions,
        ...mappedpapsoePensions,
        ...mappedbrsaludPensions
      ];
      const { completed, error } = await pensionsService.updatePensions(pensionList);

      return { isError: !completed, error };
    } catch (error) {
      return { isError: true, error };
    }
  }
};

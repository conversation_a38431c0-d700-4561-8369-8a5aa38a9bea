/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let Logger;
  let logService;
  let service;
  let pensionService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      calculateReservedAmountsBySurvival: jest.fn(() => Promise.resolve({ err: null }))
    };
    logService = {
      existsLog: jest.fn(() => true).mockImplementationOnce(() => false),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {};
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountsBySurvival).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountsBySurvival).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('does not meet dependencyMarks', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountsBySurvival).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('hitorical inactivation pensions fails', async () => {
    service.calculateReservedAmountsBySurvival = jest.fn(() =>
      Promise.resolve({ error: 'truthy' })
    );
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountsBySurvival).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateReservedAmountsBySurvival).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

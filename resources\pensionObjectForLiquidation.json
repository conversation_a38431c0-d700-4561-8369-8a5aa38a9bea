{"causant": {"rut": "18172200-2", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "19685004-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "5537843-6", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": "", "phone": "*********"}, "reservedAmounts": {"forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forSurvival": 0}, "assets": {"aps": 10, "healthDiscount": 0, "healthExemption": 0, "forFamilyAssignment": 0, "nonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0}, "numberOfCharges": 20, "institutionalPatient": false, "numberOfTaxableNonFormulableAssets": 2, "numberOfNetNonFormulableAssets": 5, "numberOfNonFormulableDiscounts": 5, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 20, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 40, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 30, "nonFormulable": 0, "health": 0, "afp": 0, "onePercentAdjusted": 450}, "basePension": 250621.05, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 11234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por enfermedad profesional", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 2960515, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T00:00:00.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 50, "createdAt": "2020-04-19T11:22:35.751Z", "updatedAt": "2020-05-19T11:22:35.751Z", "validatedStudyPeriod": "No", "article41": 50, "endDateOfTheoricalValidity": "2042-08-10T04:00:00.000Z", "endDateOfValidity": "2042-08-10T04:00:00.000Z", "linkedDate": "2020-04-19T11:22:40.844Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3"}
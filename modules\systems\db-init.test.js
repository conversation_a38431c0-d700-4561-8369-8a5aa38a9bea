/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../testsHelper');
const initDB = require('./db-init');
const viewModel = require('./rolesAndPermissions/models/views.model');
const motiveModel = require('../nomenclators/motive/models/motive');

describe('Database initialization test', () => {
  beforeAll(beforeAllTests);

  it('should populate database when there are no views', async () => {
    await initDB();
    const motivesCount = await motiveModel.find();
    const viewsCount = await viewModel.find();

    expect(motivesCount).toBeDefined();
    expect(motivesCount.length).toBeTruthy();
    expect(viewsCount).toBeDefined();
    expect(viewsCount.length).toBeTruthy();
  });

  it('should not populate database when there are already views inserted', async () => {
    const view = { view: 'Home', module: 'Home', viewNumber: 1 };
    await viewModel.create(view);

    await initDB();
    const viewsCount = await viewModel.find();

    expect(viewsCount).toBeDefined();
    expect(viewsCount.length).toBe(1);
  });

  it('should not populate database when there are already motives inserted', async () => {
    const motive = { motive: 'Anticipo', option: 'Descuento' };
    await motiveModel.create(motive);

    await initDB();
    const motivesCount = await motiveModel.find();

    expect(motivesCount).toBeDefined();
    expect(motivesCount.length).toBe(1);
  });

  afterEach(async () => {
    await viewModel.deleteMany().catch(e => console.error(e));
    await motiveModel.deleteMany().catch(e => console.error(e));
  });

  afterAll(afterAllTests);
});

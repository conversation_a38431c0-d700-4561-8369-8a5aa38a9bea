[{"_doc": {"_id": "5ef5240213540c3c90742888", "paymentInfo": {"paymentGateway": "SERVIPAG", "accountNumber": "213213-23", "bank": "BANCO SANTANDER", "branchOffice": "SERVIPAG 1"}, "causant": {"rut": "********-1", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": "las orquideas", "commune": "rengo", "city": "rancagua"}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "familyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": [], "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": []}, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 0, "institutionalPatient": false, "discounts": {"healthUF": 12323, "onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": *********.96, "afp": 0, "totalNonFormulable": 0, "adjustedOnePercent": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 70000, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON> v<PERSON>", "pensionType": "Pensión de viudez sin hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 3333333, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 1.1, "createdAt": "2020-06-25T22:24:02.674Z", "updatedAt": "2020-06-25T22:24:02.674Z", "validatedStudyPeriod": "No", "taxablePension": 0, "netPension": 0, "endDateOfTheoricalValidity": null, "endDateOfValidity": null, "discountsAndAssets": "5ef5217e13540c3c90742651", "linkedDate": "2020-06-25T22:13:18.291Z"}, "_id": "5ef5240213540c3c90742888", "paymentInfo": {"paymentGateway": "SERVIPAG", "accountNumber": "213213-23", "bank": "BANCO SANTANDER", "branchOffice": "SERVIPAG 1"}, "causant": {"rut": "********-1", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES"}, "collector": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "address": "las orquideas", "commune": "rengo", "city": "rancagua"}, "beneficiary": {"rut": "********-7", "name": "ALEX", "lastName": "RAMOS", "mothersLastName": "TORRES", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "familyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": [], "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": []}, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 0, "institutionalPatient": false, "discounts": {"healthUF": 12323, "onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": *********.96, "afp": 0, "totalNonFormulable": 0, "adjustedOnePercent": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 70000, "country": "CHI", "transient": "Si", "cun": "", "initialBasePension": 11.234, "dateOfBirth": "1977-08-11T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PROVIDA S.A.", "healthAffiliation": "FONASA", "validityType": "<PERSON><PERSON><PERSON> v<PERSON>", "pensionType": "Pensión de viudez sin hijos", "disabilityDegree": 75, "disabilityType": "Invalidez total", "resolutionNumber": 91154119, "accidentNumber": 3333333, "resolutionDate": "2019-10-30T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1999-01-11T03:00:00.000Z", "pensionCodeId": "13136", "pensionStartDate": "2010-01-02T03:00:00.000Z", "article40": 1.1, "createdAt": "2020-06-25T22:24:02.674Z", "updatedAt": "2020-06-25T22:24:02.674Z", "validatedStudyPeriod": "No", "taxablePension": 0, "netPension": 0, "endDateOfTheoricalValidity": null, "endDateOfValidity": null, "discountsAndAssets": "5ef5217e13540c3c90742651", "linkedDate": "2020-06-25T22:13:18.291Z"}, {"_doc": {"_id": "5ef5240213540c3c9074288b", "paymentInfo": {"paymentGateway": "Vale vista entregado directamente a la empresa", "accountNumber": "********", "bank": "BANCO BCI", "branchOffice": "BANCO 1"}, "causant": {"rut": "********-6", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-6", "name": "NIÑO CANTANDO", "lastName": "JESUS", "mothersLastName": "AVENDAÑO", "address": "Las dedaleras", "commune": "puente alto", "city": "santiago\r"}, "beneficiary": {"rut": "********-6", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "familyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": [], "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": []}, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 0, "institutionalPatient": false, "discounts": {"healthUF": 123, "onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 3532131.96, "afp": 0, "totalNonFormulable": 0, "adjustedOnePercent": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 70000, "country": "CHI", "transient": "SI", "cun": "", "initialBasePension": 34.827, "dateOfBirth": "1968-05-31T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "ISAPRE", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 50, "disabilityType": "Invalidez parcial", "resolutionNumber": 906, "accidentNumber": 2222222, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "2010-01-01T03:00:00.000Z", "article40": 1.1, "createdAt": "2020-06-25T22:24:02.692Z", "updatedAt": "2020-06-25T22:24:02.692Z", "validatedStudyPeriod": "No", "taxablePension": 0, "netPension": 0, "article41": 0, "endDateOfTheoricalValidity": "2033-05-30T04:00:00.000Z", "endDateOfValidity": "2033-05-30T04:00:00.000Z", "discountsAndAssets": "5ef5217e13540c3c9074264d", "linkedDate": "2020-06-25T22:13:18.285Z", "evaluationDate": "2020-07-01T04:00:00.000Z"}, "_id": "5ef5240213540c3c9074288b", "paymentInfo": {"paymentGateway": "Vale vista entregado directamente a la empresa", "accountNumber": "********", "bank": "BANCO BCI", "branchOffice": "BANCO 1"}, "causant": {"rut": "********-6", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-6", "name": "NIÑO CANTANDO", "lastName": "JESUS", "mothersLastName": "AVENDAÑO", "address": "Las dedaleras", "commune": "puente alto", "city": "santiago\r"}, "beneficiary": {"rut": "********-6", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "email": ""}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "familyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forTaxableTotalNonFormulableAssetsByReason": [], "forTotalNonFormulableDiscountsByReason": [], "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0, "forNetTotalNonFormulableAssetsByReason": []}, "numberOfCharges": 0, "numberOfNonFormulableDiscounts": 0, "numberOfNetNonFormulableAssets": 0, "numberOfTaxableNonFormulableAssets": 0, "institutionalPatient": false, "discounts": {"healthUF": 123, "onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 3532131.96, "afp": 0, "totalNonFormulable": 0, "adjustedOnePercent": 0}, "totalEstimatedDaysToPay": 0, "totalTransitoryDaysToPay": 0, "enabled": true, "basePension": 70000, "country": "CHI", "transient": "SI", "cun": "", "initialBasePension": 34.827, "dateOfBirth": "1968-05-31T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "ISAPRE", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 50, "disabilityType": "Invalidez parcial", "resolutionNumber": 906, "accidentNumber": 2222222, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "2010-01-01T03:00:00.000Z", "article40": 1.1, "createdAt": "2020-06-25T22:24:02.692Z", "updatedAt": "2020-06-25T22:24:02.692Z", "validatedStudyPeriod": "No", "taxablePension": 0, "netPension": 0, "article41": 0, "endDateOfTheoricalValidity": "2033-05-30T04:00:00.000Z", "endDateOfValidity": "2033-05-30T04:00:00.000Z", "discountsAndAssets": "5ef5217e13540c3c9074264d", "linkedDate": "2020-06-25T22:13:18.285Z", "evaluationDate": "2020-07-01T04:00:00.000Z"}]
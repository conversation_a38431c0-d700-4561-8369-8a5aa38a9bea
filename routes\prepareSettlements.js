const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const cajaLosAndesDiscountsFactoryController = require('../modules/prepareSettlements/cajaLosAndesDiscounts/controllers/index.controller');
const cajaLosAndesDiscountsService = require('../modules/prepareSettlements/cajaLosAndesDiscounts/services/index.service');
const cajaLosAndesValidators = require('../modules/prepareSettlements/cajaLosAndesDiscounts/validators');

const FactoryController = require('../modules/prepareSettlements/fonasaDiscounts/controllers/discounts.controller');
const discountsFonasaService = require('../modules/prepareSettlements/fonasaDiscounts/services/discounts.service');
const pensionsService = require('../modules/pensions/services/pension.service');

const DISCOUNTS_LOS_ANDES = '/preparar-liquidacion/cargar-descuentos/caja-los-andes';
const DISCOUNTS_FONASA = '/preparar-liquidacion/cargar-descuentos/fonasa';

module.exports = router => {
  const cajaLosAndesDiscountsController = cajaLosAndesDiscountsFactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    cajaLosAndesDiscountsService,
    validationResult
  });

  const fonasaDiscountController = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    pensionsService,
    discountsFonasaService
  });

  router.get(
    '/caja-los-andes-discounts/current-month-year',
    cajaLosAndesDiscountsController.getCurrentMonthYear
  );
  router.post(
    '/caja-los-andes-discounts/bulk',
    validateAccess(),
    startContextMiddleware,
    getUser(DISCOUNTS_LOS_ANDES),
    cajaLosAndesValidators,
    cajaLosAndesDiscountsController.processCajaLosAndesDiscounts
  );

  router.post(
    '/fonasa-discounts/',
    validateAccess(),
    startContextMiddleware,
    getUser(DISCOUNTS_FONASA),
    fonasaDiscountController.processFonasaDiscounts
  );

  router.get('/fonasa-discounts/time', validateAccess(), fonasaDiscountController.getMonthYear);

  router.get(
    '/indaylimitrange',
    validateAccess(),
    cajaLosAndesDiscountsController.isActionAllowedOnCurrentDate
  );
};

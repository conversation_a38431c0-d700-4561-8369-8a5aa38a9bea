const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'totalAssetsAndDiscounts',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_TOTAL_ASSETS_AND_DISCOUNTS_FREQUENCY,
  description: 'Calculo de haberes y descuentos no formulables por motivo y totales',
  endPoint: 'totalassetsanddiscounts',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

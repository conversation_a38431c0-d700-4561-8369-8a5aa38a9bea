const familyAssignementService = require('../../familyAssignment/services/assignment.service');
const pensionsService = require('../../pensions/services/pension.service');

module.exports = {
  async process() {
    try {
      await familyAssignementService.processFamilyCharges(pensionsService);
      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

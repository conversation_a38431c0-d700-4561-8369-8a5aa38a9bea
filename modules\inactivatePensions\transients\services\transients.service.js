/* eslint-disable no-console */
const moment = require('moment');
const InactivatePensionModel = require('../models/inactivateTransient');
const InactivateTransientModel = require('../models/inactivateTransient');
const PensionModel = require('../../../../models/pension');
const PensionHistoricModel = require('../../../../models/pensionHistoric');

const buildCurrentMonthAndYearQuery = (dateField, additionalCriteria) => {
  const date = moment();
  return {
    ...additionalCriteria,
    $expr: {
      $and: [
        { $eq: [{ $month: `$${dateField}` }, date.month() + 1] },
        { $eq: [{ $year: `$${dateField}` }, date.year()] }
      ]
    }
  };
};

const processPensions = async (item, { transientHelper, axios, Logger }) => {
  try {
    const {
      _doc: { _id, ...pension }
    } = item;
    const accidentCode = pension.accidentNumber.toString();
    const response = await transientHelper.getDataToInactivate({
      axios,
      Logger,
      accidentCode
    });
    const { data } = response;
    if (typeof data !== 'object') return { pensionsToEvaluate: null, pensionsToInactivate: null };

    const { reposos, incapacidad } = data;
    const { result: isToInactivate, fechaAlta } = transientHelper.isAvailableToInactivate(reposos);

    if (isToInactivate) {
      const inactivationPension = transientHelper.inactivatePension({
        fechaAlta,
        incapacidad
      });
      if (inactivationPension) {
        const markPension = transientHelper.inactivateService({
          ...inactivationPension,
          causantRut: pension.causant.rut,
          beneficiaryRut: pension.beneficiary.rut
        });

        const pensionToEvaluate = {
          ...pension,
          evaluationDate: new Date(),
          inactivationReason: inactivationPension.reason,
          endDateOfValidity: new Date(inactivationPension.endDateOfValidity)
        };

        const pensionToInactivate = markPension;
        return { pensionToEvaluate, pensionToInactivate };
      }
    }
    return { pensionToEvaluate: null, pensionToInactivate: null };
  } catch (error) {
    return { pensionToEvaluate: null, pensionToInactivate: null };
  }
};

const service = {
  async getAllAndFilter(query) {
    return InactivatePensionModel.find(query)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },
  async getAllPensionsToInactivate(result, transientHelper, axios, Logger) {
    const pensionsToInactivate = [];
    const pensionsToEvaluate = [];

    // eslint-disable-next-line no-restricted-syntax
    for await (const item of result) {
      const { pensionToEvaluate, pensionToInactivate } = await processPensions(item, {
        transientHelper,
        axios,
        Logger
      });
      if (pensionToEvaluate) pensionsToEvaluate.push(pensionToEvaluate);
      if (pensionToInactivate) pensionsToInactivate.push(pensionToInactivate);
    }
    // eslint-disable-next-line consistent-return
    return { pensionsToInactivate, pensionsToEvaluate };
  },

  async createUpdateTransientPension() {
    const query = buildCurrentMonthAndYearQuery('dateToInactivate');

    try {
      const markedPensions = await InactivateTransientModel.find(query);
      if (!markedPensions.length) return { completed: true };
      const asyncFunctions = markedPensions.map(document => async () => {
        const {
          beneficiaryRut,
          causantRut,
          _id: markID,
          endDateOfValidity,
          inactivationReason
        } = document;

        const currentPension = await PensionModel.findOne({
          'beneficiary.rut': beneficiaryRut,
          'causant.rut': causantRut,
          inactivateManually: { $ne: true },
          manuallyReactivated: { $ne: true },
          enabled: true
        }).lean();

        if (currentPension) {
          await PensionModel.updateOne(
            {
              'beneficiary.rut': beneficiaryRut,
              'causant.rut': causantRut,
              inactivateManually: { $ne: true },
              manuallyReactivated: { $ne: true },
              enabled: true
            },
            {
              $set: {
                endDateOfValidity,
                inactivationDate: new Date(),
                validityType: 'No vigente',
                inactivationReason
              }
            }
          );

          const { _id: id, ...data } = currentPension;
          await PensionHistoricModel.create({
            ...data,
            enabled: false
          }).catch(console.log);
        }

        await InactivateTransientModel.deleteOne({ _id: markID }).catch(console.log);
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of asyncFunctions) {
        await fn();
      }

      return { completed: true };
    } catch (error) {
      return { completed: false, inactivationError: error };
    }
  },
  async createTransientMarkPension(pensions) {
    const session = await InactivateTransientModel.startSession();
    session.startTransaction();
    try {
      const bulk = InactivateTransientModel.collection.initializeOrderedBulkOp();
      const promiseFunctions = pensions.map(pension => async () => {
        bulk.insert(pension);
      });

      // eslint-disable-next-line no-restricted-syntax
      for await (const fn of promiseFunctions) {
        await fn();
      }

      if (bulk.length) {
        await bulk.execute();
      }
      await session.commitTransaction();
      return { completed: true };
    } catch (e) {
      await session.abortTransaction();
      return { inactivationError: e };
    }
  }
};

module.exports = { ...service };

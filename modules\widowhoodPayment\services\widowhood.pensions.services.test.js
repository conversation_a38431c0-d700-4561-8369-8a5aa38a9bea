/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./widowhood.pensions.services');
const pensionService = require('../../pensions/services/pension.service');

describe('Widowhood Pensions Service', () => {
  beforeAll(beforeAllTests);
  it('should modify one pensioner and set marriage bonus', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const correctConditionForPensioner = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio',
      endDateOfValidity: new Date()
    };

    const correctCaseUnderFortyFive = {
      ...pensionData,
      beneficiary: { rut: '8888888-K' },
      causant: { rut: '8888888-K' },
      dateOfBirth: new Date(currentYear - 40, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio',
      endDateOfValidity: new Date()
    };

    const incorrectCaseValidityType = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'Vigente vitalicia',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio'
    };

    const incorrectCasePensionType = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión por accidente de trabajo',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio'
    };

    const incorrectCaseInactivationDate = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth - 1, 20),
      inactivationReason: 'Matrimonio'
    };

    const incorrectCaseInactivationReason = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Fallecimiento'
    };

    await PensionModel.insertMany([
      correctConditionForPensioner,
      correctCaseUnderFortyFive,
      incorrectCaseValidityType,
      incorrectCasePensionType,
      incorrectCaseInactivationDate,
      incorrectCaseInactivationReason
    ]).catch(e => console.error(e));

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));
    expect(error).toBe(null);
    expect(completed).toBe(true);

    const pensioner1 = await PensionModel.findOne({
      'beneficiary.rut': '8888888-K',
      'causant.rut': '8888888-K',
      enabled: true
    }).catch(e => console.error(e));
    const pensioner2 = await PensionModel.findOne({
      'beneficiary.rut': '5537843-6',
      'causant.rut': '18172200-2',
      enabled: true
    }).catch(e => console.error(e));

    expect(pensioner1.assets.marriageBonus).toBe(0);
    expect(pensioner2.assets.marriageBonus).toBe(6014905.2);
  }, 120000);

  it('should modify one pensioner and set marriage bonus for bigger endDateOfValidity case', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const correctConditionForPensionerEndDateOfValidityBigger = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio',
      endDateOfValidity: new Date(currentYear, currentMonth + 3, 25)
    };

    await PensionModel.insertMany([correctConditionForPensionerEndDateOfValidityBigger]).catch(e =>
      console.error(e)
    );

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));
    expect(error).toBe(null);
    expect(completed).toBe(true);

    const pensioner2 = await PensionModel.findOne({
      'beneficiary.rut': '5537843-6',
      'causant.rut': '18172200-2',
      enabled: true
    }).catch(e => console.error(e));

    expect(pensioner2.assets.marriageBonus).toBe(6014905.2);
  }, 120000);

  it('should modify one pensioner and set marriage bonus for smaller endDateOfValidity case', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const correctConditionForPensionerEndDateOfValiditySmaller = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 50, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio',
      endDateOfValidity: new Date(currentYear, currentMonth - 3, 25)
    };

    await PensionModel.insertMany([correctConditionForPensionerEndDateOfValiditySmaller]).catch(e =>
      console.error(e)
    );

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));
    expect(error).toBe(null);
    expect(completed).toBe(true);

    const pensioner2 = await PensionModel.findOne({
      'beneficiary.rut': '5537843-6',
      'causant.rut': '18172200-2',
      enabled: true
    }).catch(e => console.error(e));

    expect(pensioner2.assets.marriageBonus).toBe(5263042.05);
  }, 120000);

  it('a method within the service will fail', async () => {
    jest.spyOn(PensionModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));

    expect(error).toBeDefined();
    expect(completed).toBe(false);
  });

  it('should modify one pensioner and set marriage bonus for smaller thanAgeWidowhood case', async () => {
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth();

    const correctConditionForAgeWhenPensionExpiresSmallerThanAgeWidowhood = {
      ...pensionData,
      dateOfBirth: new Date(currentYear - 20, 1, 1),
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'No vigente',
      enabled: true,
      inactivationDate: new Date(currentYear, currentMonth, 20),
      inactivationReason: 'Matrimonio',
      endDateOfValidity: new Date(currentYear - 30, currentMonth - 3, 25)
    };

    await PensionModel.insertMany([
      correctConditionForAgeWhenPensionExpiresSmallerThanAgeWidowhood
    ]).catch(e => console.error(e));

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));
    expect(error).toBe(null);
    expect(completed).toBe(true);

    const pensioner2 = await PensionModel.findOne({
      'beneficiary.rut': '5537843-6',
      'causant.rut': '18172200-2',
      enabled: true
    }).catch(e => console.error(e));

    expect(pensioner2.assets.marriageBonus).toBe(0);
  }, 120000);

  it('a method within the service will fail', async () => {
    jest.spyOn(PensionModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });

    const { completed, error } = await service
      .setWidowhoodBonus(pensionService)
      .catch(e => console.error(e));

    expect(error).toBeDefined();
    expect(completed).toBe(false);
  });

  afterEach(async () => {
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

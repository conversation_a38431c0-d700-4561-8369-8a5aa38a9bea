/* eslint-disable consistent-return */
const cronDescription = 'expenses acounting report:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'EXPENSE_ACCOUNTING_REPORT';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependencyMark = 'GENERATE_AND_UPLOAD_BANK_FILE';
const missingDependencyMessage = `No se ha ejecutado la dependencia ${dependencyMark} `;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, logService, service, job }) => {
  try {
    Logger.info(`${cronMark} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(missingDependencyMessage);
      return { message: missingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark}: Generating report...`);
    const { error } = await service.generateReports();

    if (error) throw error;

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};
module.exports = { cronMark, dependencyMark, workerFn };

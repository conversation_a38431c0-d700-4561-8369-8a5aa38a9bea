const mongoose = require('mongoose');
const paginate = require('../../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const FonasaDiscount = new Schema(
  {
    rut: {
      type: String,
      required: true,
      match: /^[1-9][0-9]?\d{3}\d{3}-[0-9kK]$/
    },
    discount: {
      type: Number,
      required: true,
      min: 0,
      max: 99999.99
    }
  },
  { timestamps: true }
);
FonasaDiscount.plugin(paginate);
FonasaDiscount.index({ idCode: 1 });

module.exports = mongoose.model('FonasaDiscount', FonasaDiscount);

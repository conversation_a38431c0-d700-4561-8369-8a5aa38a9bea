/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../service/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'postLiquidationCheckpointReport',
  worker: async ({ Logger, done }) => workerModule.workerFn({ Logger, done, service, logService }),
  repeatInterval: process.env.CRON_POST_LIQUIDATION_CHECKPOINT_REPORT_FREQUENCY,
  description:
    'Punto de control para rebajar descuentos de cajas sociales si su sumatoria supera el 25% de la pensión líquida',
  endPoint: 'postliquidationcheckpointreport',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

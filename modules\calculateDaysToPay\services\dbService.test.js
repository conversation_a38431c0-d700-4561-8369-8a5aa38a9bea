/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const service = require('./dbService');
const pensionsData = require('../../../resources/calculateDaysToPay.json');

describe('calculate daysToPay service', () => {
  beforeAll(beforeAllTests);

  it('should calculate days based only on death logic', async done => {
    const dayOfMonth = 17;
    const month = 6;
    const year = 2020;
    const daysToAssert = 30;

    const data = pensionsData[0];
    data.transient = 'No';
    data.deathDate = new Date(year, month, dayOfMonth);
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'different from No vigente';
    data.inactivationReason = 'Fallecimiento';
    data.endDateOfValidity = new Date(year, month, daysToAssert);
    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(daysToAssert);
    done();
  });

  it('should calculate days based only on transient pension', async done => {
    const data = pensionsData[0];

    data.transient = 'Si';
    data.totalTransitoryDaysToPay = 20;

    await PensionModel.insertMany([data]);

    const { completed, error } = await service
      .calculateDaysToPay(1, 1)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(20);
    done();
  });

  it('should calculate days based only on retirement logic', async done => {
    const dayOfMonth = 17;
    const month = 6;
    const year = 2020;
    const daysToAssert = 18;

    const data = pensionsData[0];

    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.evaluationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'different from No vigente';
    data.inactivationReason = 'Jubilación';
    data.endDateOfValidity = new Date(year, month, daysToAssert);

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(daysToAssert);
    done();
  });

  it('should calculate days based only on marriage logic', async done => {
    const fechaActual = new Date();
    const dayOfMonth = 17;
    const month = fechaActual.getMonth();
    const year = fechaActual.getFullYear();
    const daysToAssert = 19;

    const data = pensionsData[0];

    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.evaluationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'different from No vigente';
    data.inactivationReason = 'Matrimonio';
    data.endDateOfValidity = new Date(year, month, daysToAssert);

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(daysToAssert - 1);
    done();
  });

  it('should calculate days based only on end of widowhood logic', async done => {
    const fechaActual = new Date();

    const dayOfMonth = 17;
    const month = fechaActual.getMonth();
    const year = fechaActual.getFullYear();
    const daysToAssert = 19;

    const data = pensionsData[0];

    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'No vigente';
    data.inactivationReason = 'Expiración de año de pago';
    data.endDateOfTheoricalValidity = new Date(year, month, daysToAssert);

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(daysToAssert - 1);
    done();
  });

  it('should calculate days based only on manually inactivation logic', async done => {
    const dayOfMonth = 17;
    const month = 6;
    const year = 2020;
    const daysToAssert = 19;

    const data = pensionsData[0];

    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'No vigente';
    data.inactivateManually = true;
    data.endDateOfValidity = new Date(year, month, daysToAssert);

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [firstRecord] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(firstRecord.daysToPay).toBe(daysToAssert - 1);
    done();
  });

  it('should calculate the lowest among transient, marriage and manually inactivation', async done => {
    const dayOfMonth = 19;
    const month = 6;
    const year = 2020;
    const daysToAssert = 19;

    const data = pensionsData[0];

    data.transient = 'Si';
    data.totalTransitoryDaysToPay = daysToAssert + 1;
    data.inactivateManually = true;
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.evaluationDate = new Date(year, month, dayOfMonth);
    data.inactivationReason = 'Matrimonio';
    data.validityType = 'different from No vigente';
    data.endDateOfValidity = new Date(year, month, daysToAssert - 1);

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [aRecordX] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(aRecordX.daysToPay).toBe(19 - 2);
    done();
  });

  it('should calculate the total days in the month', async done => {
    const dayOfMonth = 19;
    const month = 6;
    const year = 2020;
    const daysToAssert = 19;

    const data = pensionsData[0];

    data.inactivateManually = true;
    data.endDateOfValidity = new Date(year, month + 1, daysToAssert + 5);
    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'No vigente';

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [aRecordX] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(aRecordX.daysToPay).toBe(moment().daysInMonth());
    done();
  });

  it('should calculate 0 as the end of validiy was previous month', async done => {
    const fechaActual = new Date();
    const dayOfMonth = 19;
    const month = fechaActual.getMonth();
    const year = fechaActual.getFullYear();
    const daysToAssert = 1;
    const daysMonth = new Date(year, month + 1, 0).getDate();

    const data = pensionsData[0];

    data.inactivateManually = true;
    data.endDateOfValidity = new Date(year, month - 1, daysToAssert);
    data.transient = 'No';
    data.totalTransitoryDaysToPay = 20;
    data.inactivationDate = new Date(year, month, dayOfMonth);
    data.validityType = 'No vigente';

    await PensionModel.insertMany([data]).catch(err => console.error(err));

    const { completed, error } = await service
      .calculateDaysToPay(month, year)
      .catch(err => console.error(err));

    const [aRecordX] = await PensionModel.find({ enabled: true })
      .lean()
      .catch(err => console.error(err));

    expect(error).toBe(null);
    expect(completed).toBe(true);
    expect(aRecordX.daysToPay).toBe(daysMonth);
    done();
  });

  afterEach(async () => {
    await PensionModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

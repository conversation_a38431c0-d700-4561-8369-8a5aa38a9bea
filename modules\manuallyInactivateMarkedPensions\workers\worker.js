const markDependency = 'INACTIVATE_OR_REACTIVATE_AF_CRONS_GROUP';
const cronMark = 'MANUALLY_INACTIVATE_MARKED_PENSIONS';
const cronDescription = 'Automatically inactivate pensions marked for manual inactivation';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';

const missingDepMsg = `No se ha ejecutado la dependencia ${markDependency}`;
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, logService, service, pensionService, job }) => {
  try {
    Logger.info(`${cronDescription} checking if cron was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: cron dependency verification started...`);
    if (!(await logService.existsLog(markDependency))) {
      Logger.info(missingDepMsg);
      return { message: missingDepMsg, status: 'UNAUTHORIZED' };
    }
    Logger.info(`${cronDescription} process started`);
    const {
      updatedIncativatedPensioners,
      error: inactivatedError
    } = await service.inactivatePensions();
    const {
      updatedNotReactivatedPensioners,
      error: notReactiveError
    } = await service.notReactivatePensions();
    const {
      updatedMixedInactivateReactivated,
      error: mixedInactivateNotReactiveError
    } = await service.inactivateAndNotReactivatePensions();

    if (inactivatedError || notReactiveError || mixedInactivateNotReactiveError)
      throw new Error(inactivatedError || notReactiveError || mixedInactivateNotReactiveError);

    const { completed, error } = await service.updateMarkedPensions({
      pensionService,
      updatedIncativatedPensioners,
      updatedNotReactivatedPensioners,
      updatedMixedInactivateReactivated
    });

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: completed, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, markDependency, workerFn };

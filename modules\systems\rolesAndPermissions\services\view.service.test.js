/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const ViewModel = require('../models/views.model');
const viewService = require('./view.service');

describe('test for CRUD operations on views', () => {
  beforeAll(beforeAllTests);

  let module;
  let view;
  let viewNumber;

  beforeEach(async () => {
    module = 'module1';
    view = 'view1';
    viewNumber = 1;
    await ViewModel.create({ view, module, viewNumber });
  });

  it('should create a view', async done => {
    const viewToCreate = { view: 'view2', module: 'module1', viewNumber: '1' };

    const { completed, error } = await viewService.createView(viewToCreate);

    expect(error).toBeUndefined();
    expect(completed).toBe(true);

    done();
  });

  it('should fail creating a view due to duplicated view', async done => {
    const view1 = { view: 'view1', module: 'module1', viewNumber: 1 };

    const { completed, error } = await viewService.createView({ view: view1 });

    expect(error).toBeDefined();
    expect(completed).toBeUndefined();

    done();
  });

  it('should read all views', async done => {
    const { views, error } = await viewService.readViews();

    expect(error).toBeUndefined();
    expect(views.length).toBe(1);

    done();
  });
  it('should fail reading all views', async done => {
    jest.spyOn(ViewModel, 'find').mockImplementationOnce(() => {
      throw new Error();
    });

    const { views, error } = await viewService.readViews();

    expect(error).toStrictEqual(new Error());
    expect(views).toBeUndefined();

    done();
  });

  it('should update a view', async done => {
    const viewToModify = 'view1';
    const newViewName = 'view2';
    const newModuleName = 'module2';
    const { completed, error } = await viewService.updateView({
      view: viewToModify,
      newViewName,
      newModuleName
    });

    expect(error).toBeUndefined();
    expect(completed).toBe(true);

    done();
  });

  it('should fail updating a view', async done => {
    jest.spyOn(ViewModel, 'findOneAndUpdate').mockImplementationOnce(() => {
      throw new Error();
    });
    const viewToModify = 'view0';
    const newViewName = 'view1';
    const newModuleName = 'module2';
    const { completed, error } = await viewService.updateView({
      view: viewToModify,
      newViewName,
      newModuleName
    });

    expect(error).toStrictEqual(new Error());
    expect(completed).toBeUndefined();

    done();
  });

  it('should fail deleting a view', async done => {
    const viewToDelete = 'view0';
    const { completed, error } = await viewService.deleteView({
      view: viewToDelete
    });

    expect(error).toBeUndefined();
    expect(completed).toBe(true);

    done();
  });

  it('should fail deleting a view', async done => {
    jest.spyOn(ViewModel, 'updateOne').mockImplementationOnce(() => {
      throw new Error();
    });
    const viewToDelete = 'view0';
    const { completed, error } = await viewService.deleteView({
      view: viewToDelete
    });

    expect(error).toStrictEqual(new Error());
    expect(completed).toBeUndefined();

    done();
  });

  afterEach(async () => {
    await ViewModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

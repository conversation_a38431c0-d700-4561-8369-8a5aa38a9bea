module.exports = ({ HttpStatus, Logger, roleService, viewService }) => {
  function manageError(res, error) {
    const { message = 'Internal server error' } = error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
  return {
    createRole: async (req, res) => {
      const { roleName, views } = req.body;
      Logger.info(`Start creating a role: ${roleName}`);

      const { completed, error } = await roleService.createRole({ roleName, views });
      if (error) {
        return manageError(res, error);
      }

      return res.status(HttpStatus.CREATED).json({ completed });
    },

    readRole: async (req, res) => {
      const { roleName } = req.params;
      Logger.info(`Start reading a role: ${roleName}`);

      const { role, error } = await roleService.readRole({ roleName });

      if (error) {
        Logger.error(`Error Getting role: ${error}`);
        return manageError(res, error);
      }
      return res.status(HttpStatus.OK).json(role);
    },

    readAllRoles: async (req, res) => {
      Logger.info(`Start reading all roles`);

      const { roles, error } = await roleService.readAllRoles();

      if (error) {
        Logger.error(`Error reading role: ${error}`);
        return manageError(res, error);
      }
      return res.status(HttpStatus.OK).json({ roles });
    },

    updateRole: async (req, res) => {
      const { roleName, view, newPermission } = req.body;
      Logger.info(`Updating role: ${roleName}`);

      const { completed, error } = await roleService.updateRole({
        roleName,
        view,
        newPermission
      });
      if (error) {
        Logger.error(`Error updating role: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },
    bulkUnorderedRoles: async (req, res) => {
      try {
        const result = await roleService.bulkUpdateOrCreateUnordered(req.body);
        Logger.info(`Bulk creation`, req.details);
        return res.status(HttpStatus.CREATED).json(result);
      } catch (error) {
        Logger.error(`Error bulking views: ${error}`);
        return manageError(res, error);
      }
    },

    createView: async (req, res) => {
      const { view } = req.body;
      Logger.info(`Start creating views: ${view}`);

      const { completed, error } = await viewService.createView(req.body);
      if (error) {
        Logger.error(`Error creating views: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.CREATED).json({ completed });
    },

    readViews: async (req, res) => {
      Logger.info(`Start reading views`);

      const { views, error } = await viewService.readViews();
      if (error) {
        Logger.error(`Error reading views: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ views });
    },

    updateView: async (req, res) => {
      const { view } = req.body;
      Logger.info(`Updating view: ${view}`);

      const { completed, error } = await viewService.updateView(req.body);
      if (error) {
        Logger.error(`Error updating view: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },

    deleteView: async (req, res) => {
      const { view } = req.body;
      Logger.info(`delete view: ${view}`);

      const { completed, error } = await viewService.deleteView({ view });
      if (error) {
        Logger.error(`Error deleting view: ${error}`);
        return manageError(res, error);
      }

      return res.status(HttpStatus.OK).json({ completed });
    },
    bulkUnorderedViews: async (req, res) => {
      try {
        const result = await viewService.bulkUpdateOrCreateUnordered(req.body);
        Logger.info(`Bulk creation`, req.details);
        return res.status(HttpStatus.CREATED).json(result);
      } catch (error) {
        Logger.error(`Error bulking views: ${error}`);
        return manageError(res, error);
      }
    }
  };
};

const cronMark = 'INACTIVATE_BY_AGE_LIMIT';
const dependencyMark = '';
const cronDescription = 'inactivate by age limit :';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = `Process ${cronMark} completed successfully.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, done, service, logService, job }) => {
  try {
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info('Inicio ejecucion cron inactivacion por cumplimiento de edad límite');
    const { hasError, inactivationError } = await service.inactivatePensionsByAgeLimitCompliance();
    if (hasError) throw new Error(inactivationError);
    Logger.info('Inactivacion por cumplimiento de edad límite finalizado exitosamente');
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable no-underscore-dangle */
/* eslint-disable consistent-return */
const jwt = require('jsonwebtoken');
const logger = require('../logger');
const status = require('../constants/http-status');

const TokenModel = require('../../models/token');
const { getToken } = require('./token');

const { JWTsecret, JWTexpires } = process.env;
const options = { expiresIn: JWTexpires };

module.exports = function validateAccess() {
  return function jwtauth(req, res, next) {
    const token = getToken(req);

    if (!token) {
      logger.error('No token provided');
      return res
        .status(status.UNAUTHORIZED)
        .json({
          message: 'No token provided.'
        })
        .end();
    }

    jwt.verify(token, JWTsecret, options, async function dec(err) {
      if (err) {
        logger.error('Token not authorized');
        return res
          .status(status.UNAUTHORIZED)
          .json({
            message: `Token invalid`
          })
          .end();
      }

      const savedToken = await TokenModel.findOne({ token });

      if (!savedToken) {
        logger.error('Token not authorized for user');
        return res
          .status(status.UNAUTHORIZED)
          .json({
            message: `Token invalid`
          })
          .end();
      }

      return next();
    });
  };
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest
        .fn(() => Promise.resolve(true))
        .mockImplementationOnce(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    service = {
      taxablePension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({}))
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, logService, pensionService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.taxablePension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.taxablePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('worker does not have all marks', async () => {
    logService.allMarksExists = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.taxablePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, pensionService, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.taxablePension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

trigger:
  branches:
    include:
      - refs/heads/develop
      - refs/heads/develop-achs
      - refs/heads/master

pool:
  vmImage: ubuntu-latest

stages:
  - stage: Intro
    jobs:
      - job: Intro
        steps:
          - checkout: none
          - task: CmdLine@2
            displayName: Inicio Proceso Análisis
            inputs:
              script: |
                echo ------------------------------------------------------------
                echo Realizando análisis en $(Build.SourceBranch)

  - stage: Testing
    displayName: 'Evaluación de código'
    dependsOn: Intro
    jobs:
      - job: Analisis
        displayName: 'Ejecución de Análisis'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '14.x'
            displayName: 'Install Node.js'

          - script: |
              npm ci
              npm run test:cov --if-present
            displayName: 'npm ci, build and test'

          # azure unit tests results
          - task: PublishTestResults@2
            condition: succeededOrFailed()
            inputs:
              testResultsFiles: 'junit.xml'
            displayName: Publish Unit Tests Results

          # azure unit coverage results
          - task: PublishCodeCoverageResults@1
            inputs:
              codeCoverageTool: Cobertura
              summaryFileLocation: 'coverage/cobertura-coverage.xml'
            displayName: Publish Unit Tests Coverage Results

          - task: SonarQubePrepare@5
            inputs:
              SonarQube: 'SonarQube_PEC'
              scannerMode: 'CLI'
              configMode: 'manual'
              cliProjectKey: $(Build.Repository.Name)
              cliProjectName: $(Build.Repository.Name)
              cliProjectVersion:
              cliSources: '.'
              extraProperties: |
                sonar.qualitygate.wait=true
                sonar.testExecutionReportPaths=test-report.xml
                sonar.javascript.lcov.reportPaths=coverage/lcov.info
                sonar.exclusions=dist/**, lib/**, public/**, node_modules/**, coverage/**, **.test.js, **/models/**, routes/**, **/controllers/**, modules/historicalData/**
                sonar.coverage.exclusions=dist/**, lib/**, public/**, node_modules/**, coverage/**, **/models/**, routes/**, **/controllers/**, modules/historicalData/**, data-seed/**, resources/**
                sonar.sourceEncoding=UTF-8
                sonar.javascript.file.suffixes=.js

          - task: SonarQubeAnalyze@5

          - task: SonarQubePublish@5
            inputs:
              pollingTimeoutSec: '300'

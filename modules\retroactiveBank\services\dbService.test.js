/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const ProcessedJobModel = require('../../sharedFiles/models/processedJob');
const PensionModel = require('../../../models/pension');

const service = require('./dbService');
const enabledPensions = require('../../../resources/retroactiveEnabledPensionBank.json');
const retroactiveHistoricPension = require('../../../resources/retroactiveHistoricPensionBank.json');

describe('retroactive bank Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: [] })),
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('complete retroactive bank file', async () => {
    jest.spyOn(PensionModel, 'aggregate').mockImplementationOnce(() => retroactiveHistoricPension);
    pensionService.getAllAndFilter = jest.fn(() => Promise.resolve({ result: [enabledPensions] }));

    const a = await service.retroactiveBankFile(pensionService);
    expect(a.completed).toBe(true);
  });

  it('complete retroactive bank file with pension type', async () => {
    jest.spyOn(PensionModel, 'aggregate').mockImplementationOnce(() => retroactiveHistoricPension);
    enabledPensions._doc.pensionType = 'Pension por enfermedad profesional';
    pensionService.getAllAndFilter = jest.fn(() => Promise.resolve({ result: [enabledPensions] }));

    const a = await service.retroactiveBankFile(pensionService);
    expect(a.completed).toBe(true);
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
    await PensionModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

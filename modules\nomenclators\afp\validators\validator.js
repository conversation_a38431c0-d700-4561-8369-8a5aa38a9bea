const { isValidRut } = require('../../isapre/validators/rutValidator');

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const regRule = new RegExp(regex, 'i');
const MIN_NUMBER_ALLOWED = 10.0;
const MAX_NUMBER_ALLOWED = 100;
const formatterPercent = value => {
  const number = value && value.toString().replace(/^0+|[^0-9.]/gi, '');
  return number
    ? `${number.toLocaleString(['es', 'en'], { style: 'percent', minimumFractionDigits: 2 })}`
    : '';
};
const percentRegex = /^\d{2}\.?\d{0,2}$/;
const isPercent = value => percentRegex.test(value);
const validFormat = percent =>
  percent
    ? percent
        .toString()
        .replace(/^0+|[^0-9]+/gi, '')
        .replace(/(\d{2})(\d{1,2})/gi, '$1.$2')
    : '';

const rutFormatter = rut =>
  `${rut}`
    .replace(/^0+|[^\dkK]/g, '')
    .replace(/k(?!\b)/gi, '')
    .replace(/([0-9kK])$/, '-$1')
    .toUpperCase();

const rutMatchRule = rut => isValidRut(rutFormatter(rut));

const percentageMatchRule = value =>
  isPercent(value) && value < MAX_NUMBER_ALLOWED && value > MIN_NUMBER_ALLOWED
    ? validFormat(value)
    : '';

const codeRegex = /(\d{2})/;
const codeRule = new RegExp(codeRegex, 'i');

const codeFormatter = code =>
  code
    ? code
        .toString()
        .replace(/[^0-9]+/gi, '')
        .replace(/(\d{2})/gi, '$1')
    : '';

module.exports = {
  regRule,
  percentageMatchRule,
  isPercent,
  validFormat,
  formatterPercent,
  rutMatchRule,
  isValidRut,
  rutFormatter,
  codeRule,
  codeFormatter
};

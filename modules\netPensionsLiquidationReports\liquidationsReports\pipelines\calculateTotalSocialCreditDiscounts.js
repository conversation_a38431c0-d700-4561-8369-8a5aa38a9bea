const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');

const paths = [
  'discounts.socialCredits18',
  'discounts.socialCreditsLosAndes',
  'discounts.socialCreditsLosHeroes',
  'discounts.socialCreditsLaAraucana'
];

const calculate = pension => ({
  ...pension,
  liquidation: {
    ...pension.liquidation,
    totalSocialCreditDiscounts: roundValue(recursiveSum(pension, paths))
  }
});

module.exports = calculate;

/* eslint no-return-await: "error" */
const moment = require('moment');
const PensionModel = require('../../../../models/pension');

const AGE_LIMIT = 45;
const VALIDITY_TYPE = /^Vigente\s+viudez$/i;
const PENSION_TYPE = [
  /Pensi[óo]n\s+de\s+viudez\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+viudez\s+sin\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+con\s+hijos/i,
  /Pensi[óo]n\s+de\s+madre\s+de\s+hijo\s+de\s+filiaci[óo]n\s+no\s+matrimonial\s+sin\s+hijos/i
];

const getMappedData = (array, overrideData) =>
  array.map(({ _id, __v, ...data }) => ({
    ...data,
    ...overrideData,
    previousEndDateOfValidity: data.endDateOfValidity
  }));

const getMappedDataPlus110Year = (array, overrideData) => {
  return array.map(({ _id, __v, ...data }) => {
    const endDateOfValidityNew = new Date(data.dateOfBirth);
    const date110 = new Date(
      endDateOfValidityNew.setUTCFullYear(endDateOfValidityNew.getFullYear() + 110)
    );
    return {
      ...data,
      ...overrideData,
      previousEndDateOfValidity: data.endDateOfValidity,
      endDateOfValidity: date110
    };
  });
};

const getPensions = async () => {
  const pensions = await PensionModel.find({
    validityType: { $regex: VALIDITY_TYPE },
    enabled: true,
    pensionType: { $in: PENSION_TYPE },
    $expr: {
      $and: [
        { $eq: [{ $month: `$endDateOfValidity` }, new Date().getMonth() + 1] },
        { $eq: [{ $year: `$endDateOfValidity` }, new Date().getFullYear()] }
      ]
    },
    manuallyReactivated: { $ne: true },
    inactivateManually: { $ne: true }
  }).lean();
  return pensions;
};

const isUnderFourtyfiveAndHasNoCharge = widow => {
  const { dateOfBirth, numberOfCharges = 0, numberOfChargesExternal = 0 } = widow;
  const countOfCharges = numberOfCharges + numberOfChargesExternal;
  const age = moment().diff(new Date(dateOfBirth), 'years');
  return age < AGE_LIMIT && countOfCharges === 0;
};

const isFourtyfiveOrGreaterAndHasCharge = widow => {
  const { dateOfBirth, numberOfCharges = 0, numberOfChargesExternal = 0 } = widow;
  const countOfCharges = numberOfCharges + numberOfChargesExternal;
  const age = moment().diff(new Date(dateOfBirth), 'years');
  return age >= AGE_LIMIT && countOfCharges > 0;
};

module.exports = {
  async updatePensions(pensionsService) {
    try {
      const pensions = await getPensions();
      const widowsUnderFourtyFive = pensions.filter(isUnderFourtyfiveAndHasNoCharge);
      const widowsFourtyFiveOrGreater = pensions.filter(isFourtyfiveOrGreaterAndHasCharge);
      const mappedUnderFourtyFives = getMappedData(widowsUnderFourtyFive, {
        inactivationReason: 'Expiración año de pago',
        validityType: 'No vigente',
        inactivationDate: new Date(),
        reactivationDate: new Date(),
        evaluationDate: new Date()
      });

      const mappedFourtyFiveOrGreater = getMappedDataPlus110Year(widowsFourtyFiveOrGreater, {
        validityType: 'Vigente vitalicia'
      });

      const pensionList = [...mappedUnderFourtyFives, ...mappedFourtyFiveOrGreater];

      const { completed, error } = await pensionsService.createUpdatePension(pensionList);
      if (!completed) throw new Error(error);
    } catch (error) {
      throw new Error(error);
    }
  }
};

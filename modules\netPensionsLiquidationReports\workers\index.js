const workerModule = require('./worker');

const pensionService = require('../../pensions/services/pension.service');
const liquidationService = require('../../liquidation/services/liquidation.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const liquidationReportService = require('../liquidationsReports/services/dbService');
const netPensionsService = require('../netPensions/services/dbService');
const afpService = require('../../nomenclators/afp/services/index.service');
const ufService = require('../../UFvalue/services/ufValue.service');
const cajaService = require('../../nomenclators/cajaCompensacion/services/index.service');

module.exports = {
  name: 'netPensionsLiquidationReports',
  worker: deps =>
    workerModule.workerFn({
      liquidationReportService,
      netPensionsService,
      logService,
      pensionService,
      liquidationService,
      afpService,
      ufService,
      cajaService,
      ...deps
    }),
  description: 'Calculo de la pensión líquida para pensiones vigentes',
  endPoint: 'netpensionsliquidationreports',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.markDependencies
};

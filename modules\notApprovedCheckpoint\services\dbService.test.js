/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const service = require('./dbService');
const PensionModel = require('../../../models/pension');
const pensions = require('../../../resources/notApprovedCheckpointPensions.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('dbservice Test', () => {
  beforeAll(beforeAllTests);
  let mailService;
  beforeEach(() => {
    mailService = {
      sendEmail: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    jest.spyOn(PensionModel, 'aggregate').mockImplementationOnce(() => [pensions]);
  });

  it('success notApprovedCheckpoint method', async () => {
    const { error, completed } = await service.notApprovedCheckpoint(mailService);

    expect(error).toBeFalsy();
    expect(completed).toBe(true);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

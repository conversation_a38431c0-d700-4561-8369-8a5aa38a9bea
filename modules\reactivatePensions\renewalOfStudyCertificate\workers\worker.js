/* eslint-disable consistent-return */

const markOfCron = 'REACTIVATE_FOR_RENEWAL_OF_STUDY_CERTIFICATE';
const cronDescription = 'reactivacion por renovación de certificado de estudio.';
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = `Process ${markOfCron} completed successfully.`;
const dependencyMark = '';

const workerFn = async ({ Logger, done, logService, service }) => {
  try {
    Logger.info(`Inicio proceso: ${cronDescription}`);
    const { existsLog } = await logService.existsLogAndRetry(markOfCron);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        status: 'UNAUTHORIZED',
        message: alreadyExecutedMessage,
        alreadyExecuted: true
      };
    }
    const { error } = await service.reactivatePensions();
    if (error) throw new Error(error);
    await logService.saveLog(markOfCron);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(markOfCron);
    return { message: `${cronDescription}  ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { markOfCron, dependencyMark, workerFn };

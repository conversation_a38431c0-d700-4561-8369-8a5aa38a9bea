const { recursiveCount } = require('../../../sharedFiles/helpers');

const paths = [
  'discounts.afp',
  'discounts.health',
  'discounts.healthLoan',
  'discounts.othersLosAndes',
  'discounts.othersLosHeroes',
  'discounts.totalNonFormulable',
  'discounts.socialCredits18',
  'discounts.onePercentAdjusted',
  'discounts.socialCreditsLosAndes',
  'discounts.socialCreditsLosHeroes',
  'discounts.socialCreditsLaAraucana'
];

const count = pension => {
  const { numberOfNonFormulableDiscounts = 0 } = pension;
  return {
    ...pension,
    liquidation: {
      ...pension.liquidation,
      numberOfDiscounts: recursiveCount(pension, paths) + numberOfNonFormulableDiscounts
    }
  };
};

module.exports = count;

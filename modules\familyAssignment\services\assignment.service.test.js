/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionModel = require('../../../models/pension');
const TemporaryfamilyAssignmentModel = require('../models/temporaryFamilyAssignment');
const pensionsService = require('../../pensions/services/pension.service');
const service = require('./assignment.service');
const pensionsData = require('../../../resources/pensions.json');
const familyAssignmentData = require('../../../resources/familyAssignment.json');
const logService = require('../../sharedFiles/services/jobLog.service');
const LogModel = require('../../sharedFiles/models/processedJob');

describe('Temporary Family Allowance service Model Test', () => {
  beforeAll(beforeAllTests);

  const inactivationReason = 'Vencimiento de certificado de estudios';
  const validityType = 'No vigente';
  const getLastDayOfPreviousMonth = () => {
    const previousMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastDay = 0;
    return new Date(currentYear, previousMonth, lastDay);
  };
  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should create and save temporary family assigment successfully', async done => {
    const savedfamilyAssignment = await TemporaryfamilyAssignmentModel.create(
      familyAssignmentData[0]
    );

    expect(savedfamilyAssignment._id).toBeDefined();
    expect(savedfamilyAssignment.causantId).toBe(familyAssignmentData[0].causantId);
    expect(savedfamilyAssignment.collectorId).toBe(familyAssignmentData[0].collectorId);
    const { result } = await service.getAll({});
    expect(result.length).toBe(1);
    done();
  });

  it('should sort the result array correctly if a sort parameter is provided', async () => {
    await service.bulkAndDelete(familyAssignmentData);
    const result = await service.getAll({}, { typeOfAssocietedPension: 1 });
    expect(result[0].typeOfAssocietedPension).toBe('Invalidez');
  });

  it('should delete all previous documents on new bulk operation', async () => {
    const familyAssignment = new TemporaryfamilyAssignmentModel(familyAssignmentData[0]);
    await familyAssignment.save();

    const { result } = await service.bulkAndDelete([...familyAssignmentData]);

    expect(result.length).toBe(familyAssignmentData.length);
  });

  it('should return {isError: true} if there is any error', async () => {
    jest.spyOn(TemporaryfamilyAssignmentModel, 'insertMany').mockImplementationOnce(() => {
      throw new Error();
    });
    const { isError } = await service.bulkAndDelete([...familyAssignmentData]);
    expect(isError).toBe(true);
  });

  it('should fail if a require field is not provided', async () => {
    const allowanceWithoutRequiredField = new TemporaryfamilyAssignmentModel({
      ...familyAssignmentData,
      causantId: null
    });
    let err;
    try {
      await allowanceWithoutRequiredField.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
    expect(err.errors.causantId).toBeDefined();
  });

  it('should return true on wasInactivated service on empty model', async () => {
    const { wasInactivated, isError, error } = await service.wasInactivated(
      inactivationReason,
      validityType
    );
    expect(wasInactivated).toBe(true);
    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  it('should return true on wasInactivated service', async () => {
    await PensionModel.create({ ...pensionsData[0] });

    const { wasInactivated, isError, error } = await service.wasInactivated(
      inactivationReason,
      validityType
    );
    expect(wasInactivated).toBe(true);
    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  it('should return true on wasInactivated service', async () => {
    const fieldToUpdate = {
      inactivationReason,
      endDateOfValidity: getLastDayOfPreviousMonth(),
      inactivationDate: new Date(),
      validityType
    };
    await PensionModel.create({ ...pensionsData[0], ...fieldToUpdate });

    const { wasInactivated, isError, error } = await service.wasInactivated(
      inactivationReason,
      validityType
    );
    expect(wasInactivated).toBe(true);
    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  it('should return {error: null} on wasInactivated service', async () => {
    const fieldToUpdate = {
      inactivationReason,
      endDateOfValidity: getLastDayOfPreviousMonth(),
      inactivationDate: new Date(),
      validityType
    };
    await PensionModel.create({ ...pensionsData[0], ...fieldToUpdate });
    await TemporaryfamilyAssignmentModel.create({ ...familyAssignmentData[0] });
    const { isError, error } = await service.wasInactivated(inactivationReason, validityType);

    expect(isError).toBe(false);
    expect(error).toBe(null);
  });

  it('should return error on wasInactivated service', async () => {
    jest.spyOn(PensionModel, 'findOne').mockImplementationOnce(() => {
      throw new Error();
    });
    const { isError } = await service.wasInactivated(inactivationReason, validityType);

    expect(isError).toBe(true);
  });

  it('should process charges', async () => {
    const familyAssignmentActive = {
      ...familyAssignmentData[0],
      chargeValidityType: 'Carga interna activa'
    };
    const familyAssignmentInactive = {
      ...familyAssignmentData[1],
      chargeValidityType: 'Carga interna inactiva'
    };
    const familyAssignmentExternaActive = {
      ...familyAssignmentData[4],
      chargeValidityType: 'Carga externa activa'
    };
    const pensionActive = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pension de viudez con hijos',
      validityType: 'Vigente hasta la jubilación'
    };
    pensionActive.beneficiary.rut = familyAssignmentActive.collectorId;
    pensionActive.causant.rut = familyAssignmentActive.causantId;
    const pensionInactive = {
      ...pensionsData[1],
      enabled: true,
      pensionType: 'Pension de viudez con hijos',
      validityType: 'Vigente hasta la jubilación'
    };
    pensionInactive.beneficiary.rut = familyAssignmentInactive.collectorId;
    pensionInactive.causant.rut = familyAssignmentInactive.causantId;

    await TemporaryfamilyAssignmentModel.create([
      familyAssignmentActive,
      familyAssignmentInactive,
      familyAssignmentExternaActive
    ]);
    await PensionModel.create(pensionActive);
    await PensionModel.create(pensionInactive);

    await service.processFamilyCharges(pensionsService);

    const result = await PensionModel.find({});

    expect(result.length).toBe(2);
    expect(result[0].enabled).toBe(true);
    expect(result[1].enabled).toBe(true);
    expect(result[0].numberOfCharges).toBe(1);
    expect(result[0].numberOfChargesExternal).toBe(1);
    expect(result[0].numberOfChargesArticle41).toBe(0);
  });

  it('should process charges accidente', async () => {
    const familyAssignmentActive = {
      ...familyAssignmentData[0],
      chargeValidityType: 'Carga interna activa'
    };
    const familyAssignmentInactive = {
      ...familyAssignmentData[1],
      chargeValidityType: 'Carga interna inactiva'
    };
    const familyAssignmentExternaActive = {
      ...familyAssignmentData[4],
      chargeValidityType: 'Carga externa activa'
    };
    const pensionActive = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión por accidente de trayecto',
      validityType: 'Vigente hasta la jubilación'
    };
    pensionActive.beneficiary.rut = familyAssignmentActive.collectorId;
    pensionActive.causant.rut = familyAssignmentActive.causantId;
    const pensionInactive = {
      ...pensionsData[1],
      enabled: true,
      pensionType: 'Pensión por accidente de trabajo',
      validityType: 'Vigente hasta la jubilación'
    };
    pensionInactive.beneficiary.rut = familyAssignmentInactive.collectorId;
    pensionInactive.causant.rut = familyAssignmentInactive.causantId;

    await TemporaryfamilyAssignmentModel.create([
      familyAssignmentActive,
      familyAssignmentInactive,
      familyAssignmentExternaActive
    ]);
    await PensionModel.create(pensionActive);
    await PensionModel.create(pensionInactive);

    await service.processFamilyCharges(pensionsService);

    const result = await PensionModel.find({});

    expect(result.length).toBe(2);
    expect(result[0].enabled).toBe(true);
    expect(result[1].enabled).toBe(true);
    expect(result[0].numberOfCharges).toBe(1);
    expect(result[0].numberOfChargesExternal).toBe(1);
    expect(result[0].numberOfChargesArticle41).toBe(2);
  });

  it('should return if log exists', async () => {
    await logService.saveLog('PROCESS_FAMILY_CHARGES');
    const result = await service.processFamilyCharges(pensionsService);
    expect(result).toBeUndefined();
  });

  it('should verify existence of temporal assignment', async () => {
    await TemporaryfamilyAssignmentModel.create(familyAssignmentData[0]);
    const { result } = await service.existTemporaryAssignment();
    expect(result > 0).toBe(true);
  });

  it('should throw error on verify existence of temporal assignment', async () => {
    jest
      .spyOn(TemporaryfamilyAssignmentModel, 'countDocuments')
      .mockImplementation(() => Promise.reject(new Error('error')));
    await TemporaryfamilyAssignmentModel.create(familyAssignmentData[0]);
    const { isError } = await service.existTemporaryAssignment();
    expect(isError).toBe(true);
  });

  it('Should return check if file is already imported', async () => {
    const result1 = await service.fileAlreadyImported();
    await TemporaryfamilyAssignmentModel.create(familyAssignmentData[0]);
    expect(result1).toBe(null);
  });

  it('should process charges and changes values', async () => {
    const familyAssignmentActiveUnder45 = {
      ...familyAssignmentData[2]
    };
    const familyAssignmentActiveOver45 = {
      ...familyAssignmentData[1],
      chargeValidityType: 'Carga interna activa'
    };
    const pensionInactiveUnder45 = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pension de madre de hijo de filiación no matrimonial con hijos',
      validityType: 'No vigente'
    };
    pensionInactiveUnder45.beneficiary.rut = familyAssignmentActiveUnder45.collectorId;
    pensionInactiveUnder45.causant.rut = familyAssignmentActiveUnder45.causantId;

    const pensionInactiveOver45 = {
      ...pensionsData[1],
      enabled: true,
      pensionType: 'Pension de viudez con hijos',
      validityType: 'No vigente',
      dateOfBirth: '1972-10-08'
    };
    pensionInactiveOver45.beneficiary.rut = familyAssignmentActiveOver45.collectorId;
    pensionInactiveOver45.causant.rut = familyAssignmentActiveOver45.causantId;

    await TemporaryfamilyAssignmentModel.insertMany([
      familyAssignmentActiveUnder45,
      familyAssignmentActiveOver45,
      familyAssignmentData[3]
    ]);
    await PensionModel.create(pensionInactiveUnder45);
    await PensionModel.create(pensionInactiveOver45);

    await service.processFamilyCharges(pensionsService);

    const result = await PensionModel.find({});

    expect(result.length).toBe(2);
    expect(result[0].validityType).toBe('Vigente viudez');
    expect(result[0].endDateOfTheoricalValidity).toStrictEqual(
      new Date(familyAssignmentData[2].endDateOfCertificationValidity)
    );
    expect(result[1].validityType).toBe('Vigente vitalicia');

    expect(result[1].endDateOfTheoricalValidity).toStrictEqual(
      new Date('2082-10-08T00:00:00.000Z')
    );
    expect(new Date(moment(result[1].reactivationDate).startOf('date'))).toStrictEqual(
      new Date(moment().startOf('date'))
    );
    expect(result[1].endDateOfValidity).toStrictEqual(result[1].endDateOfTheoricalValidity);
  });

  it('should return if log exists', async () => {
    const familyAssignmentActive = {
      ...familyAssignmentData[0],
      chargeValidityType: 'Carga interna activa'
    };

    const familyAssignmentInactive = {
      ...familyAssignmentData[1],
      chargeValidityType: 'Carga interna inactiva'
    };

    await TemporaryfamilyAssignmentModel.create([familyAssignmentActive, familyAssignmentInactive]);

    const result = await service.getSurvivanceTemporaryFamilyAssignment(['Carga interna activa']);
    expect(result.length).toBe(1);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await TemporaryfamilyAssignmentModel.deleteMany({}).catch(e => console.error(e));
    await PensionModel.deleteMany({}).catch(e => console.error(e));
    await LogModel.deleteMany({}).catch(e => console.error(e));
  });

  afterAll(afterAllTests);
});

import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../database/prisma.service';
import { RulesEngineService } from '../rules-engine/rules-engine.service';
import { CacheService } from '../cache/cache.service';
import { Pension, Prisma } from '@prisma/client';

export interface PensionCalculationResult {
  basePension: Record<string, number>;
  assets: Record<string, number>;
  discounts: Record<string, number>;
  retroactive: Record<string, number>;
  netPension: number;
  calculatedAt: Date;
}

@Injectable()
export class CalculationService {
  private readonly logger = new Logger(CalculationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly rulesEngine: RulesEngineService,
    private readonly cache: CacheService,
  ) {}

  /**
   * Calcula todos los componentes de una pensión con type safety completo
   */
  async calculatePension(pensionId: string): Promise<PensionCalculationResult> {
    const cacheKey = `pension_calculation_${pensionId}`;
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.prisma.$transaction(async (tx) => {
        this.logger.log(`Calculating pension ${pensionId}`);
        
        // Cargar pensión con relaciones usando Prisma
        const pension = await tx.pension.findUniqueOrThrow({
          where: { id: pensionId },
          include: {
            discountsAndAssets: true,
            liquidations: {
              where: { enabled: true },
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        });

        // 1. Calcular pensión base con type safety
        const basePensionResult = await this.calculateBasePension(pension);
        
        // 2. Calcular beneficios
        const assetsResult = await this.calculateAssets(pension);
        
        // 3. Calcular descuentos
        const discountsResult = await this.calculateDiscounts(pension);
        
        // 4. Calcular retroactivos
        const retroactiveResult = await this.calculateRetroactive(pension);
        
        // 5. Calcular pensión neta
        const netPension = this.calculateNetPension(
          basePensionResult,
          assetsResult,
          discountsResult,
          retroactiveResult,
        );

        return {
          basePension: basePensionResult,
          assets: assetsResult,
          discounts: discountsResult,
          retroactive: retroactiveResult,
          netPension,
          calculatedAt: new Date(),
        };
      });
    }, 3600);
  }

  /**
   * Procesa lote de pensiones con optimización Prisma
   */
  async calculatePensionBatch(pensionIds: string[]): Promise<Map<string, PensionCalculationResult | null>> {
    const results = new Map<string, PensionCalculationResult | null>();
    
    // Cargar todas las pensiones de una vez con Prisma
    const pensions = await this.prisma.pension.findMany({
      where: {
        id: { in: pensionIds },
        enabled: true,
      },
      include: {
        discountsAndAssets: true,
        liquidations: {
          where: { enabled: true },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });

    // Procesar en paralelo con Promise.allSettled para mejor performance
    const calculations = await Promise.allSettled(
      pensions.map(async (pension) => {
        try {
          const result = await this.calculatePensionInternal(pension);
          return { pensionId: pension.id, result };
        } catch (error) {
          this.logger.error(`Error calculating pension ${pension.id}`, error);
          return { pensionId: pension.id, result: null };
        }
      }),
    );

    // Mapear resultados
    calculations.forEach((calculation) => {
      if (calculation.status === 'fulfilled') {
        results.set(calculation.value.pensionId, calculation.value.result);
      }
    });

    return results;
  }

  /**
   * Cálculo interno sin transacción (para uso en batch)
   */
  private async calculatePensionInternal(
    pension: Pension & {
      discountsAndAssets?: any;
      liquidations?: any[];
    },
  ): Promise<PensionCalculationResult> {
    const basePensionResult = await this.calculateBasePension(pension);
    const assetsResult = await this.calculateAssets(pension);
    const discountsResult = await this.calculateDiscounts(pension);
    const retroactiveResult = await this.calculateRetroactive(pension);
    
    const netPension = this.calculateNetPension(
      basePensionResult,
      assetsResult,
      discountsResult,
      retroactiveResult,
    );

    return {
      basePension: basePensionResult,
      assets: assetsResult,
      discounts: discountsResult,
      retroactive: retroactiveResult,
      netPension,
      calculatedAt: new Date(),
    };
  }

  private async calculateBasePension(pension: any): Promise<Record<string, number>> {
    const rules = await this.rulesEngine.getBasePensionRules();
    
    const result = {
      basePension: Number(pension.basePension),
      article40: Number(pension.article40),
      article41: Number(pension.article41),
      law19403: Number(pension.law19403),
      law19539: Number(pension.law19539),
      law19953: Number(pension.law19953),
    };

    // Aplicar reglas con type safety
    for (const rule of rules) {
      if (await this.rulesEngine.ruleAppliesTo(rule, pension)) {
        Object.assign(result, await this.rulesEngine.applyRule(rule, result, pension));
      }
    }

    // Aplicar pensión mínima si corresponde
    if (this.shouldApplyMinimumPension(pension.pensionType)) {
      const minimumPension = await this.rulesEngine.getMinimumPension(pension.pensionType);
      const totalCalculated = Object.values(result).reduce((sum, val) => sum + val, 0);
      
      if (totalCalculated < minimumPension) {
        result['minimumPensionAdjustment'] = minimumPension - totalCalculated;
      }
    }

    return result;
  }

  private async calculateAssets(pension: any): Promise<Record<string, number>> {
    // Implementación similar pero con types de TypeScript
    return {
      aps: await this.calculateAps(pension),
      familyAssignment: await this.calculateFamilyAssignment(pension),
      christmasBonus: await this.calculateChristmasBonus(pension),
      // ... otros assets
    };
  }

  private async calculateDiscounts(pension: any): Promise<Record<string, number>> {
    // Implementación con validación de tipos
    return {
      afp: await this.calculateAfpDiscount(pension),
      health: await this.calculateHealthDiscount(pension),
      // ... otros descuentos
    };
  }

  private async calculateRetroactive(pension: any): Promise<Record<string, number>> {
    // Cálculos retroactivos con type safety
    return {};
  }

  private calculateNetPension(
    basePension: Record<string, number>,
    assets: Record<string, number>,
    discounts: Record<string, number>,
    retroactive: Record<string, number>,
  ): number {
    const totalBasePension = Object.values(basePension).reduce((sum, val) => sum + val, 0);
    const totalAssets = Object.values(assets).reduce((sum, val) => sum + val, 0);
    const totalDiscounts = Object.values(discounts).reduce((sum, val) => sum + val, 0);
    const totalRetroactive = Object.values(retroactive).reduce((sum, val) => sum + val, 0);

    return Math.max(0, Math.round((totalBasePension + totalAssets - totalDiscounts + totalRetroactive) * 100) / 100);
  }

  // Métodos auxiliares con type safety
  private shouldApplyMinimumPension(pensionType: string): boolean {
    return ['INVALIDEZ_TOTAL', 'INVALIDEZ_PARCIAL', 'SUPERVIVENCIA', 'ORFANDAD'].includes(pensionType);
  }

  private async calculateAps(pension: any): Promise<number> {
    // Implementación específica
    return 0;
  }

  private async calculateFamilyAssignment(pension: any): Promise<number> {
    // Implementación específica
    return 0;
  }

  private async calculateChristmasBonus(pension: any): Promise<number> {
    // Implementación específica
    return 0;
  }

  private async calculateAfpDiscount(pension: any): Promise<number> {
    // Implementación específica
    return 0;
  }

  private async calculateHealthDiscount(pension: any): Promise<number> {
    // Implementación específica
    return 0;
  }
}

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { Logger } = require('../../testsHelper');
const {
  pensionConditions,
  applyConditions,
  calculateTotalDaysToPayForPension
} = require('./daysUtils');
const {
  estimatedDaysForPreviousMonth,
  estimatedDaysOnCurrentMonth
} = require('../../../resources/daysOfTranstientPensions.json');

const daysForCalculus = jest.fn(() => {
  return {
    lastDayOfPreviousMonth: moment()
      .date(0)
      .format('YYYY-MM-DD'),
    firstMonthDay: moment()
      .startOf('month')
      .format('YYYY-MM-DD'),
    today: moment()
      .startOf('month')
      .format('YYYY-MM-DD'),
    tomorrow: moment()
      .startOf('month')
      .add(1, 'day')
      .format('YYYY-MM-DD'),
    lastMonthDay: moment()
      .endOf('month')
      .format('YYYY-MM-DD')
  };
});

describe('Days Util - calculateTotalDaysToPayForPension', () => {
  it('calculate days for this month', () => {
    const result = calculateTotalDaysToPayForPension(
      estimatedDaysForPreviousMonth.map(({ idSiniestro }) => ({
        accidentNumber: idSiniestro
      })),
      estimatedDaysForPreviousMonth,
      estimatedDaysOnCurrentMonth
    );
    expect(result).toStrictEqual([
      {
        beneficiary: undefined,
        causant: undefined,
        pensionCodeId: undefined,
        accidentNumber: 6456159,
        totalEstimatedDaysToPay: 2,
        totalTransitoryDaysToPay: 2
      },
      {
        beneficiary: undefined,
        causant: undefined,
        pensionCodeId: undefined,
        accidentNumber: 5261396,
        totalEstimatedDaysToPay: 2,
        totalTransitoryDaysToPay: 2
      },
      {
        beneficiary: undefined,
        causant: undefined,
        pensionCodeId: undefined,
        accidentNumber: 2960515,
        totalEstimatedDaysToPay: 2,
        totalTransitoryDaysToPay: 2
      },
      {
        beneficiary: undefined,
        causant: undefined,
        pensionCodeId: undefined,
        accidentNumber: 1288639,
        totalEstimatedDaysToPay: 16,
        totalTransitoryDaysToPay: 16
      }
    ]);
  });

  it('calculate empty arrays', () => {
    const result = calculateTotalDaysToPayForPension([], undefined, undefined);
    expect(result).toStrictEqual([]);
  });

  it('calculate empty arrays and empty object', () => {
    const result = calculateTotalDaysToPayForPension([{}], undefined, undefined);
    expect(result).toStrictEqual([
      {
        beneficiary: undefined,
        causant: undefined,
        accidentNumber: undefined,
        pensionCodeId: undefined,
        totalTransitoryDaysToPay: 0,
        totalEstimatedDaysToPay: 0
      }
    ]);
  });
});
describe('Days Util - pensionConditions', () => {
  it('conditions on empty object', () => {
    const { firstCondition, secondCondition, thirdCondition, fourthCondition } = pensionConditions(
      {}
    );
    expect(firstCondition([], [])).toStrictEqual(false);
    expect(secondCondition('', [])).toStrictEqual(false);
    expect(thirdCondition([], '')).toStrictEqual(false);
    expect(fourthCondition('', '')).toStrictEqual(false);
  });

  it('first condition success', () => {
    const { lastDayOfPreviousMonth, lastMonthDay, today, tomorrow } = daysForCalculus();
    const between = [lastDayOfPreviousMonth, lastMonthDay];
    const { firstCondition } = pensionConditions({ fechaInicioReposo: today, fechaAlta: tomorrow });
    expect(firstCondition(between, between)).toStrictEqual(true);
  });

  it('second condition success', () => {
    const { lastDayOfPreviousMonth, lastMonthDay, today, tomorrow } = daysForCalculus();
    const between = [lastDayOfPreviousMonth, lastMonthDay];
    const { secondCondition } = pensionConditions({
      fechaInicioReposo: today,
      fechaAlta: tomorrow
    });
    expect(secondCondition(tomorrow, between)).toStrictEqual(true);
  });

  it('third condition success', () => {
    const { lastDayOfPreviousMonth, lastMonthDay, today, tomorrow } = daysForCalculus();
    const between = [lastDayOfPreviousMonth, lastMonthDay];
    const { thirdCondition } = pensionConditions({
      fechaInicioReposo: tomorrow,
      fechaAlta: tomorrow
    });
    expect(thirdCondition(between, today)).toStrictEqual(true);
  });

  it('third condition success on non valid date', () => {
    const { lastDayOfPreviousMonth, lastMonthDay, today, tomorrow } = daysForCalculus();
    const between = [lastDayOfPreviousMonth, lastMonthDay];
    const { thirdCondition } = pensionConditions({
      fechaInicioReposo: tomorrow,
      fechaAlta: '0001-01-01'
    });
    expect(thirdCondition(between, today)).toStrictEqual(true);
  });

  it('fourth condition success', () => {
    const { today, tomorrow } = daysForCalculus();
    const { fourthCondition } = pensionConditions({
      fechaInicioReposo: today,
      fechaAlta: tomorrow
    });
    expect(fourthCondition(tomorrow, today)).toStrictEqual(true);
  });

  it('fourth condition success on non valid date', () => {
    const { today, tomorrow } = daysForCalculus();

    const { fourthCondition } = pensionConditions({
      fechaInicioReposo: today,
      fechaAlta: '0001-01-01'
    });
    expect(fourthCondition(tomorrow, today)).toStrictEqual(true);
  });
});

describe('Days Util - applyConditions', () => {
  it('apply conditions on empty data', () => {
    const conditionsForMedicalRest = applyConditions(daysForCalculus(), true, Logger);
    const appliedConditions = [].reduce((arrayOfMedicalRest, curr) => {
      return arrayOfMedicalRest.concat(conditionsForMedicalRest(curr));
    }, []);

    expect(appliedConditions).toStrictEqual([]);
  });

  it('apply every condition on success data', () => {
    const { lastDayOfPreviousMonth, today, tomorrow } = daysForCalculus();
    const sinisterData = [
      [
        {
          fechaInicioReposo: today,
          fechaAlta: today,
          tipoAlta: 1,
          idSiniestro: '**********'
        }
      ],
      [
        {
          fechaInicioReposo: lastDayOfPreviousMonth,
          fechaAlta: today,
          tipoAlta: 2,
          idSiniestro: '**********'
        }
      ],
      [
        {
          fechaInicioReposo: today,
          fechaAlta: tomorrow,
          tipoAlta: 1,
          idSiniestro: '**********'
        }
      ],
      [
        {
          fechaInicioReposo: lastDayOfPreviousMonth,
          fechaAlta: tomorrow,
          tipoAlta: 1,
          idSiniestro: '**********'
        }
      ],
      [
        {
          fechaInicioReposo: lastDayOfPreviousMonth,
          fechaAlta: today,
          tipoAlta: 3,
          idSiniestro: '**********'
        }
      ]
    ];
    const conditionsForMedicalRest = applyConditions(daysForCalculus(), true, Logger);

    const appliedConditions = sinisterData.reduce((arrayOfMedicalRest, curr) => {
      return arrayOfMedicalRest.concat(conditionsForMedicalRest(curr));
    }, []);

    expect(appliedConditions).toStrictEqual([
      { idSiniestro: 6456159, daysToPay: 0 },
      { idSiniestro: 5261396, daysToPay: 1 },
      { idSiniestro: 2960515, daysToPay: moment().daysInMonth() },
      { idSiniestro: 1288639, daysToPay: moment().daysInMonth() },
      { idSiniestro: 5261397, daysToPay: 1 }
    ]);
  });
});

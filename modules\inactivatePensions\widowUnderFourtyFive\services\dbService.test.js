/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionModel = require('../../../../models/pension');
const pensionsService = require('../../../pensions/services/pension.service');
const service = require('./dbService');
const pensionsData = require('../../../../resources/pensions.json');

describe('Inactivate widows pensioner under 45 years Test', () => {
  beforeAll(beforeAllTests);
  const currentYear = new Date().getFullYear();

  it('should inactivate widow pensioner under 45 years old', async () => {
    const thirtyYearsAgo = new Date().getFullYear() - 30;
    const currenMonth = new Date().getMonth() + 1;

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'Vigente viudez',
      dateOfBirth: `${thirtyYearsAgo}-03-03`,
      numberOfCharges: 0,
      numberOfChargesExternal: 0,
      endDateOfValidity: `${currentYear}-${currenMonth}-03`
    };
    // Create a widow pensioner under 45 years old
    await PensionModel.create(pension);
    await service.updatePensions(pensionsService);
    const dbDocs = await PensionModel.find({});
    // now Pensions table should have 1 docs
    expect(dbDocs.length).toBe(1);
    // new fields should be added to the result docs
    expect(dbDocs[0].enabled).toBe(true);
    expect(dbDocs[0].inactivationReason).toBeDefined();
    expect(dbDocs[0].validityType).toBe('No vigente');
    expect(dbDocs[0].inactivationReason).toBe('Expiración año de pago');
  });

  it('should change validityType to "Vitalicia" for widow greater than 45 years', async () => {
    const currenMonth = new Date().getMonth() + 1;
    const plus110Year = new Date('2086-03-03');

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'Vigente viudez',
      dateOfBirth: '1976-03-03',
      numberOfCharges: 1,
      numberOfChargesExternal: 0,
      endDateOfValidity: `${currentYear}-${currenMonth}-03`
    };
    // Create a widow pensioner under 45 years old
    await PensionModel.create(pension);
    await service.updatePensions(pensionsService);
    const dbDocs = await PensionModel.find({});
    // now Pensions table should have 1 docs
    expect(dbDocs.length).toBe(1);
    // validityType should be changed to Vigente Vitalicia
    expect(dbDocs[0].validityType).toBe('Vigente vitalicia');
    expect(dbDocs[0].endDateOfValidity.toString()).toBe(plus110Year.toString());
  });

  it('should change validityType to "Vitalicia" for widow greater than 45 years external', async () => {
    const currenMonth = new Date().getMonth() + 1;
    const plus110Year = new Date('2086-03-03');

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'Vigente viudez',
      dateOfBirth: '1976-03-03',
      numberOfCharges: 0,
      numberOfChargesExternal: 1,
      endDateOfValidity: `${currentYear}-${currenMonth}-03`
    };
    // Create a widow pensioner under 45 years old
    await PensionModel.create(pension);
    await service.updatePensions(pensionsService);
    const dbDocs = await PensionModel.find({});
    // now Pensions table should have 1 docs
    expect(dbDocs.length).toBe(1);
    // validityType should be changed to Vigente Vitalicia
    expect(dbDocs[0].validityType).toBe('Vigente vitalicia');
    expect(dbDocs[0].endDateOfValidity.toString()).toBe(plus110Year.toString());
  });

  it('should return if mark exists', async () => {
    const fourtySevenYearsAgo = new Date().getFullYear() - 47;
    const currenMonth = new Date().getMonth() + 1;

    const pension = {
      ...pensionsData[0],
      enabled: true,
      pensionType: 'Pensión de viudez con hijos',
      validityType: 'Vigente viudez',
      dateOfBirth: `${fourtySevenYearsAgo}-03-03`,
      numberOfCharges: 1,
      numberOfChargesExternal: 1,
      endDateOfValidity: `${currentYear}-${currenMonth}-03`
    };
    // Create a widow pensioner under 45 years old
    await PensionModel.create(pension);
    const result = await service.updatePensions(pensionsService);

    expect(result).toBeUndefined();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const PensionModel = require('../../../../models/pension');
const FonasaDiscountModel = require('../models/discounts');
const pensionService = require('../../../pensions/services/pension.service');
const discountService = require('./discounts.service');
const discountsData = require('../../../../resources/fonasaDiscounts.json');
const pensionsData = require('../../../../resources/pensions.json');

describe('test service', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(FonasaDiscountModel, 'startSession').mockImplementationOnce(() => mocks);
  });
  it('should create and save a single object into fonasaDiscount', async done => {
    const { isError, error } = await discountService
      .bulkInsert(discountsData[0])
      .catch(err => console.log(err));

    expect(isError).toBe(false);
    expect(error).toBe(null);
    done();
  });
  it('should reject a discount that is not a number fonasaDiscount', async done => {
    const { isError, error } = await discountService
      .bulkInsert(discountsData[1])
      .catch(err => console.log(err));
    expect(isError).toBe(true);
    expect(error).toBeDefined();
    done();
  });
  it('should return a true result if given an empty array ', async done => {
    const { isError, error } = await discountService
      .bulkInsert(discountsData[2])
      .catch(err => console.log(err));
    expect(isError).toBe(false);
    expect(error).toBeDefined();

    done();
  });
  it('getCurrentTime should return the current month and year  ', async done => {
    const serverDate = await discountService.getCurrentTime().catch(err => console.log(err));
    const currentDate = moment().format('MMYY');

    expect(serverDate).toBe(currentDate);

    done();
  });

  it('Should process fonasa discounts', async done => {
    const pension = pensionsData[0];
    pension.beneficiary.rut = '5237365-4';
    await FonasaDiscountModel.insertMany(discountsData[0]).catch(err => console.error(err));
    await PensionModel.insertMany([pension]).catch(err => console.error(err));
    await discountService.processFonasaDiscounts(pensionService);
    const allPensionDocs = await PensionModel.countDocuments().catch(err => console.error(err));
    expect(allPensionDocs).toBe(1);
    const enabledPensionDocs = await PensionModel.find({ enabled: true });
    expect(enabledPensionDocs.length).toBe(1);
    expect(enabledPensionDocs[0].discounts.healthLoan).toEqual(0);
    done();
  });
  afterEach(async () => {
    jest.restoreAllMocks();
    await FonasaDiscountModel.deleteMany({}).catch(err => console.error(err));
    await PensionModel.deleteMany({}).catch(err => console.error(err));
  });

  afterAll(afterAllTests);
});

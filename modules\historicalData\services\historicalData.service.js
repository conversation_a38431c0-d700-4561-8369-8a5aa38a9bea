/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const PensionModel = require('../../../models/pension');
const DiscountsAndAssetModel = require('../../../models/discountsAndAssets');
const LiquidationModel = require('../../../models/liquidation');

const { objDecryption } = require('./crypto');
const { generateDummyObj } = require('./generateDummyObj');
const {
  discountsAndAssetsDefaultFields,
  liquidationDefaultFields
} = require('./generateDefaultFields');

const { processSpecialCases } = require('./processSpecialCases');

const defaultDateToCreate = '01-04-2021';
const YES = /S/i;

const service = {
  async insertAll(pensions = [], dateToCreate) {
    const session = await PensionModel.startSession();
    const liquidationSession = await LiquidationModel.startSession();
    const discountsAndAssetsIds = [];

    session.startTransaction();
    liquidationSession.startTransaction();

    const { SECRET_KEY_DECRYPT_HISTORICAL_PENSION_DATA } = process.env;
    const defaultDate =
      moment(dateToCreate, 'DD-MM-YYYY').toISOString() ||
      moment(defaultDateToCreate, 'DD-MM-YYYY').toISOString();

    const decryptValues = !!SECRET_KEY_DECRYPT_HISTORICAL_PENSION_DATA;

    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();
      const liquidationBulk = LiquidationModel.collection.initializeOrderedBulkOp();

      // eslint-disable-next-line no-restricted-syntax
      for await (const [index, doc] of pensions.entries()) {
        const { beneficiary, causant, collector, retirement, pensionCodeId, ...otherFields } = doc;

        let decryptedBeneficiary;
        let decryptedCausant;
        let decryptedCollector;

        if (decryptValues) {
          decryptedBeneficiary = objDecryption(beneficiary, index);
          decryptedCausant = objDecryption(causant, index);
          decryptedCollector = objDecryption(collector, index);
        } else {
          decryptedBeneficiary = generateDummyObj(index);
          decryptedCausant = generateDummyObj(index);
          decryptedCollector = generateDummyObj(index);
        }

        const { rut: beneficiaryRut } = decryptedBeneficiary;
        const { rut: causantRut } = decryptedCausant;

        const processedPensionFields = processSpecialCases(otherFields);

        const discountsAndAssets = await DiscountsAndAssetModel.findOneAndUpdate(
          { beneficiaryRut, causantRut, pensionCodeId },
          {
            $set: { beneficiaryRut, causantRut, pensionCodeId, ...discountsAndAssetsDefaultFields }
          },
          { upsert: true, new: true }
        );

        discountsAndAssetsIds.push(discountsAndAssets._id);
        liquidationBulk.insert({
          ...liquidationDefaultFields,
          beneficiaryRut,
          causantRut,
          pensionCodeId,
          createdAt: new Date(defaultDate),
          updatedAt: new Date(defaultDate)
        });

        bulk.insert({
          ...processedPensionFields,
          discountsAndAssets: discountsAndAssets._id,
          beneficiary: decryptedBeneficiary,
          causant: decryptedCausant,
          collector: decryptedCollector,
          pensionCodeId,
          heavyDuty: '',
          parentRUT: '',
          oldAgePensionInProcess: 0,
          createdAt: new Date(defaultDate),
          updatedAt: new Date(defaultDate),
          linkedDate: new Date(defaultDate),
          retirement: !!YES.test(retirement)
        });
      }

      if (bulk.length) {
        await bulk.execute();
        await liquidationBulk.execute();
      }

      await session.commitTransaction();
      await liquidationSession.commitTransaction();

      return { error: null };
    } catch (error) {
      await session.abortTransaction();
      await liquidationSession.abortTransaction();
      await DiscountsAndAssetModel.deleteMany({ _id: { $in: discountsAndAssetsIds } });
      return { error };
    }
  },
  getPensionersFromFile: async ({ path, fsClient, split }) => {
    try {
      return new Promise(resolve => {
        const fileReadStream = fsClient.createReadStream(path, {
          flags: 'r',
          encoding: 'utf-8'
        });

        const jsonArr = [];

        const lineStream = fileReadStream.pipe(split());

        lineStream.on('data', chunk => {
          if (chunk) {
            const json = JSON.parse(chunk);
            jsonArr.push(json);
          }
        });

        lineStream.on('end', () => {
          return resolve({ pensioners: jsonArr });
        });
      });
    } catch (error) {
      return { error };
    }
  }
};

module.exports = service;

const word = '0-9a-záéíóúàèìòùãẽĩõũỹg̃ñäöüëïâêîôûçğş';
const regex = `^([${word}\\.\\-',])+(\\s[${word}\\.\\-',]+)*$`;
// eslint-disable-next-line no-misleading-character-class
const regRule = new RegExp(regex, 'i');

const codeRegex = /(\d{3})/;
const codeRule = new RegExp(codeRegex, 'i');

const codeFormatter = code =>
  code
    ? code
        .toString()
        .replace(/[^0-9]+/gi, '')
        .replace(/(\d{3})/gi, '$1')
    : '';

module.exports = { regRule, codeRule, codeFormatter };

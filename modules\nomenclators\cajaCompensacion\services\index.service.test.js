const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const cajaModel = require('../../../../models/compensationboxs');
const service = require('./index.service');

describe('Cajas nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  it('should find one and update', async () => {
    const Caja = {
      name: 'caja X',
      percentage: 12.02,
      isMaxAmount: true,
      maxAmount: 120000
    };

    await cajaModel.create(Caja);
    const { error } = await service.updateCaja({
      name: 'caja X',
      percentage: 10,
      maxAmount: 1000000
    });
    const { result, isError } = await service.getCajas();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].percentage).toBe(10);
  });

  it('should dont find', async () => {
    const nameDontExist = 'ImaginaryBox';
    const { result, isError } = await service.getCajaByName(nameDontExist);
    expect(isError).not.toBeDefined();
    expect(result).toBe(null);
  });

  it('should find one', async () => {
    const Caja2 = {
      name: 'caja zzz',
      percentage: 1,
      isMaxAmount: true,
      maxAmount: 120000
    };

    await cajaModel.create(Caja2);
    const nameCajaZ = 'caja zzz';
    const { result, isError } = await service.getCajaByName(nameCajaZ);
    expect(isError).not.toBeDefined();
    expect(result.name).toBe(nameCajaZ);
  });

  afterAll(afterAllTests);
});

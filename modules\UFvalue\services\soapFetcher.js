const getUFObjectFromXML = (obj, keys) => {
  const [key] = keys;
  if (!obj[key]) return null;
  if (key === 'obs') return obj[key][0];
  return getUFObjectFromXML(obj[key], keys.splice(1));
};

const fetchSoapData = async args => {
  const { util, client, requestArgs, url, keys } = args;
  const request = await client(url, {});
  const method = util.promisify(request.GetSeries);
  const result = await method(requestArgs);
  return { data: getUFObjectFromXML(result, [...keys]) };
};

module.exports = fetchSoapData;

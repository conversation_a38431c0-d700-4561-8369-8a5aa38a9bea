/* eslint-disable consistent-return */
const service = require('../services/dbService');
const logService = require('../../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'inactivatePensionsByAgeLimit',
  worker: deps => workerModule.workerFn({ service, logService, ...deps }),
  repeatInterval: process.env.CRON_INACTIVATE_BY_AGE_LIMIT,
  description: 'Inactivar pensiones de orfandad por limite de edad (24 años)',
  endPoint: 'inactivatebyagelimit',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

const getMappeddDiscountPensions = (pensionsList, linesObj, field) => {
  const criteriaMapper = { '1': 'Si', '3': 'No' };
  const mappedList = pensionsList.map(({ _id, __v, ...data }) => {
    const { rut } = data.beneficiary;
    const movementTypeValue = linesObj[rut.toLowerCase()];
    return {
      ...data,
      discounts: {
        ...data.discounts,
        [field]: criteriaMapper[movementTypeValue]
      }
    };
  });
  return mappedList;
};

const getMappedSocialCreditPensions = (pensionsList, linesObj, field) => {
  const mappedList = pensionsList.map(({ _id, __v, ...data }) => {
    const { rut } = data.beneficiary;
    const discountAmountValue = linesObj[rut.toLowerCase()];
    return {
      ...data,
      discounts: {
        ...data.discounts,
        [field]: discountAmountValue
      }
    };
  });
  return mappedList;
};

const getMappedAnotherDiscountPensions = (pensionsList, linesObj, field) => {
  const mappedList = pensionsList.map(({ _id, __v, ...data }) => {
    const { rut } = data.beneficiary;
    const anotherDiscountValue = linesObj[rut.toLowerCase()];
    return {
      ...data,
      discounts: {
        ...data.discounts,
        [field]: anotherDiscountValue
      }
    };
  });
  return mappedList;
};

const mapLinesData = lines => {
  const rutRegexList = [];
  const linesMappedToObj = {};
  lines.forEach(line => {
    const [rut, value] = line;
    if (rut && value) {
      rutRegexList.push(new RegExp(rut, 'i'));
      linesMappedToObj[rut] = value;
    }
  });
  return [rutRegexList, linesMappedToObj];
};

module.exports = fieldsObj => ({
  async updatePensions(pensionsService, parsedLines) {
    const {
      discountFileParsedLines,
      socialCreditFileParsedLines,
      anotherDiscountFileParsedLines
    } = parsedLines;
    const [discountRutList, discountLinesObj] = mapLinesData(discountFileParsedLines);
    const [socialCreditRutList, socialCreditLinesObj] = mapLinesData(socialCreditFileParsedLines);
    const [anotherDiscountRutList, anotherDiscountLinesObj] = mapLinesData(
      anotherDiscountFileParsedLines
    );

    try {
      const getPensions = pensionsService.findValidPensionsByRut;
      const discountPensions = await getPensions(discountRutList);

      const mappedDiscountPensions = getMappeddDiscountPensions(
        discountPensions,
        discountLinesObj,
        fieldsObj.discountFieldName
      );
      const { completed: completedUpdate, errorUpdate } = await pensionsService.updatePensions([
        ...mappedDiscountPensions
      ]);

      const socialCreditPensions = await getPensions(socialCreditRutList);
      const mappedSocialCreditPensions = getMappedSocialCreditPensions(
        socialCreditPensions,
        socialCreditLinesObj,
        fieldsObj.socialCreditFieldName
      );

      const { completed, error } = await pensionsService.updatePensions([
        ...mappedSocialCreditPensions
      ]);

      const anotherDiscountPensions = await getPensions(anotherDiscountRutList);
      const mappedAnotherDiscountPensions = getMappedAnotherDiscountPensions(
        anotherDiscountPensions,
        anotherDiscountLinesObj,
        fieldsObj.othersFieldName
      );

      const { completed: completedAnother, errorAnother } = await pensionsService.updatePensions([
        ...mappedAnotherDiscountPensions
      ]);

      return {
        isError: !completed || !completedUpdate || !completedAnother,
        error: error || errorUpdate || errorAnother
      };
    } catch (error) {
      return { isError: true, error };
    }
  }
});

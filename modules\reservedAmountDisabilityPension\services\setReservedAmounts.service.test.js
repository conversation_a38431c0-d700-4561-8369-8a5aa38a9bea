/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const LiquidationModel = require('../../../models/liquidation');
const pensionData = require('../../../resources/pensionObjectForLiquidation.json');
const service = require('./setReservedAmount.service');
const pensionService = require('../../pensions/services/pension.service');

describe('Liquidation reports service', () => {
  beforeAll(beforeAllTests);
  it('should get pensions and create liquidation reports', async () => {
    const pension = {
      ...pensionData,
      transient: 'no',
      pensionType: 'Pension por accidente de trabajo',
      validityType: 'No vigente'
    };
    const liquidation = {
      beneficiaryRut: pension.beneficiary.rut,
      causantRut: pension.causant.rut,
      pensionCodeId: pension.pensionCodeId,
      taxablePension: 12345.056
    };
    const insertedPension = await PensionModel.create(pension).catch(e => console.error(e));
    await LiquidationModel.create(liquidation).catch(e => console.error(e));
    const { completed, error } = await service.reservedDisabilityPension(pensionService);
    const updatedPension = await PensionModel.findById(insertedPension._id);
    expect(completed).toBe(true);
    expect(error).toBe(null);
    expect(updatedPension.reservedAmounts.forDisability).toBe(12345.06);
  });

  afterAll(afterAllTests);
});

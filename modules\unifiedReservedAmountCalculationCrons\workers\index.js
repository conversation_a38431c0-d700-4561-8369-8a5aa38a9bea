/* eslint-disable no-unused-expressions */
const {
  worker: calculateReservedAmountDisabilityPension
} = require('../../reservedAmountDisabilityPension/workers');
const {
  worker: calculateReservedAssetsAndDiscounts
} = require('../../calculateAssetsBonusDiscounts/workers');
const {
  worker: setReservedAmountForInstitutionalPatient
} = require('../../reservedAmountInstitutionalPatient/workers');
const {
  worker: setReservedAmountForSurvival
} = require('../../calculateReservedAmountBySurvival/workers');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'reservedAssetsAndDiscountsAmountCalculation',
  worker: deps =>
    workerModule.workerFn({
      logService,
      setReservedAmountForInstitutionalPatient,
      calculateReservedAssetsAndDiscounts,
      calculateReservedAmountDisabilityPension,
      setReservedAmountForSurvival,
      ...deps
    }),
  repeatInterval: process.env.CRON_RESERVED_ASSETS_AND_DISCOUNTS_AMOUNT_CALCULATION_FREQUENCY,
  description: 'Cron unificado para el calculo de montos reservados',
  endPoint: 'reservedassetsanddiscountsamountcalculation',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

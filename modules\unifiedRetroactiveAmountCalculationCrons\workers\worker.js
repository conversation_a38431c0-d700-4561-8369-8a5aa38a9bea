const cronDescription = 'activos totales y descuentos unificados:';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = 'Proceso completado con éxito.';
const dependencyMark = 'UNIFIED_BULKLOAD_AND_IPS';
const cronMark = 'UNIFIED_RETROACTIVE_AMOUNT_CALCULATION';

const getMissingDependencyMessage = dep => `La dependencia ${dep} aún no se ha ejecutado`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  done,
  logService,
  calculateRetroactiveDisabilityPension,
  calculateRetroactiveBankFile,
  calculateRetroactiveAmountForInstitutianalPatient,
  calculateRetroactiveAmountForSurvival,
  job
}) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark) };
    }

    Logger.info(`${cronDescription} process started`);
    const {
      executionCompleted: amountDisabilityPensionCompleted,
      message: amountDisabilityPensionMessage,
      alreadyExecuted: amountDisabilityPensionAlreadyExecuted
    } = await calculateRetroactiveDisabilityPension({ done, Logger });

    const {
      executionCompleted: retroactiveBankFileCompleted,
      message: retroactiveBankFileMessage,
      alreadyExecuted: retroactiveBankFileAlreadyExecuted
    } = await calculateRetroactiveBankFile({ done, Logger });

    const {
      executionCompleted: retroactiveAmountForInstitutionalPatientCompleted,
      message: retroactiveAmountForInstitutionalPatientMessage,
      alreadyExecuted: retroactiveAmountForInstitutionalPatientExecuted
    } = await calculateRetroactiveAmountForInstitutianalPatient({ done, Logger });

    const {
      executionCompleted: retroactiveAmountForSurvivalCompleted,
      message: retroactiveAmountForSurvivalMessage,
      alreadyExecuted: retroactiveAmountForSurvivalExecuted
    } = await calculateRetroactiveAmountForSurvival({ done, Logger });

    const messageOutput = {
      amountDisabilityPensionMessage,
      retroactiveBankFileMessage,
      retroactiveAmountForInstitutionalPatientMessage,
      retroactiveAmountForSurvivalMessage
    };

    const allCompleted =
      (amountDisabilityPensionCompleted || amountDisabilityPensionAlreadyExecuted) &&
      (retroactiveBankFileCompleted || retroactiveBankFileAlreadyExecuted) &&
      (retroactiveAmountForInstitutionalPatientCompleted ||
        retroactiveAmountForInstitutionalPatientExecuted) &&
      (retroactiveAmountForSurvivalCompleted || retroactiveAmountForSurvivalExecuted);

    if (!allCompleted) throw new Error(JSON.stringify(messageOutput));

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

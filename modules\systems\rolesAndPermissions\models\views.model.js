const mongoose = require('mongoose');

const { Schema } = mongoose;

const viewSchema = new Schema(
  {
    module: {
      type: String,
      required: true
    },
    viewNumber: {
      type: Number,
      required: true
    },
    view: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      primary: true
    },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

module.exports = mongoose.model('View', viewSchema);

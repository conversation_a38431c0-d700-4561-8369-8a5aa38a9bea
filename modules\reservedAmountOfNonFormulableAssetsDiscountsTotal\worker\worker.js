const cronDescription = 'calcular las cantidades reservadas para activos y descuentos';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const dependencyMark = 'GENERATE_AND_UPLOAD_BANK_FILE';
const cronMark = 'SET_RESERVED_AMOUNT_NONFORMULABLE_ASSETS_DISCOUNTS';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const getMissingDependencyMessage = dep => `Dependencia "${dep}" aún no ejecutada.`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, logService, pensionService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`${cronDescription}: start dependency verification`);

    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));

      return { message: getMissingDependencyMessage(dependencyMark) };
    }

    Logger.info(`${cronDescription} process started`);
    const { error } = await service.calculateReservedAmount(pensionService);

    if (error) throw new Error(error);
    await logService.saveLog(cronMark);

    Logger.info(`${cronDescription} process finished`);

    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

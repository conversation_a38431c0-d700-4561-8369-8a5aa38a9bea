{"_id": "5f3c0337a953bf40ae3a021a", "paymentInfo": {"branchOffice": "", "paymentGateway": "Vale vista Banco de Chile", "accountNumber": "011-222-444-001", "bank": "Centro"}, "causant": {"rut": "********-0", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-6", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "address": "Av. mata", "commune": "Santiago", "city": "Santiago"}, "beneficiary": {"phone": "", "rut": "********-8", "name": "MIGUEL IGNACIO", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO", "email": "<EMAIL>"}, "reservedAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0}, "assets": {"aps": 2000, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "taxableTotalNonFormulable": 0, "netTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "retroactiveAmounts": {"forSurvival": 0, "forDisability": 0, "forInstitutionalPatient": 0, "forRejection": 0, "forBonuses": 0, "forTaxableTotalNonFormulableAssets": 0, "forNetTotalNonFormulableAssets": 0, "forTotalNonFormulableDiscounts": 0, "forBasePension": 0, "forArticle40": 0, "forArticle41": 0, "forFamilyAssignment": 0}, "numberOfCharges": 0, "institutionalPatient": false, "discounts": {"healthUF": 0, "onePercentLaAraucana": "No", "nonFormulableByReason": [], "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0}, "liquidation": [{"_id": "5f08a0ac510ce24572d1fac2", "taxablePension": 10000, "totalAssets": 80.21, "totalOnePercentDiscounts": 6770, "totalSocialCreditDiscounts": 760, "totalDiscounts": 220.211, "numberOfAssets": 10, "numberOfDiscounts": 30, "netPension": 3899.72, "basePension": 22212.1, "fixedBasePension": 30, "daysToPay": 10, "enabled": true, "beneficiaryRut": "********-8", "causantRut": "********-0", "taxablePensionDate": "2020-08-09T19:55:00.584Z", "updatedAt": "2020-08-09T20:00:03.254Z", "createdAt": "2020-08-09T20:00:03.254Z"}, {"_id": "5f08a0ac510ce24572d1fac2", "taxablePension": 30000, "totalAssets": 80.21, "totalOnePercentDiscounts": 6770, "totalSocialCreditDiscounts": 760, "totalDiscounts": 220.211, "numberOfAssets": 10, "numberOfDiscounts": 30, "netPension": 3899.72, "basePension": 22212.1, "fixedBasePension": 30, "daysToPay": 10, "enabled": false, "beneficiaryRut": "********-8", "causantRut": "********-0", "taxablePensionDate": "2020-08-09T19:55:00.584Z", "updatedAt": "2020-08-09T20:00:03.254Z", "createdAt": "2020-08-09T20:00:03.254Z"}], "enabled": true, "basePension": 170000, "country": "CHI", "transient": "SI", "cun": "122", "initialBasePension": 134.5, "dateOfBirth": "1968-05-31T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "ISAPRE", "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por enfermedad profesional", "disabilityDegree": 50, "disabilityType": "Invalidez parcial", "resolutionNumber": 906, "accidentNumber": 1111122, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "2010-02-22T03:00:00.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "2010-01-01T03:00:00.000Z", "article40": 90000, "createdAt": "2020-08-18T16:34:30.049Z", "updatedAt": "2020-08-18T16:34:30.049Z", "validatedStudyPeriod": "No", "fixedBasePension": 0, "fixedArticle40": 0, "fixedArticle41": 0, "daysToPay": 0, "inactivateManually": false, "article41": 0, "endDateOfTheoricalValidity": null, "endDateOfValidity": null, "discountsAndAssets": "5f3c0337a953bf40ae3a0217", "linkedDate": "2020-08-18T16:35:03.717Z"}
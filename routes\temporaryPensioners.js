const { validationResult } = require('express-validator');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const { validators } = require('../modules/linkPensions/validators/checkFieldsExistence');
const FactoryController = require('../modules/linkPensions/controllers/temporaryPensions.controller');
const validateAccess = require('../lib/auth/validate');
const { getUser, startContextMiddleware } = require('../lib/middleware/continuation-local-storage');

const LINK_TEMPORARY_PENSIONS = '/nuevas-pensiones/importar';

module.exports = router => {
  const temporaryPensionsController = FactoryController({
    HttpStatus,
    ErrorBuilder,
    Logger,
    validationResult
  });

  router.get('/', validateAccess(), async (req, res) =>
    temporaryPensionsController.getAll(req, res)
  );
  router.post(
    '/bulk',
    [validateAccess(), validators, startContextMiddleware, getUser(LINK_TEMPORARY_PENSIONS)],
    temporaryPensionsController.insertAll
  );
};

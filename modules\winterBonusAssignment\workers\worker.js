const cronDescription = 'set winter bonus';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el año actual.';
const cronMark = 'SET_WINTER_BONUS';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = 'CRON_BASE_MINIMUN_PENSION_WORKER';

const getMissingDependencyMessage = dep => `La dependencia ${dep} aún no se ha ejecutado`;

const workerFn = async ({ Logger, logService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { executionCompleted: false, message: getMissingDependencyMessage(dependencyMark) };
    }

    const { error } = await service.setWinterBonus();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

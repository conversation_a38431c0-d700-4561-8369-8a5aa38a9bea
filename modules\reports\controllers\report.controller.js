const moment = require('moment');

const PARTIAL_BANK_FILE_PATH = '/Banco/nomina_bancaria';
const FILENAME = `Reporteria inactivar - reactivar.xlsx`;

module.exports = ({
  HttpStatus,
  service,
  excelService,
  storageService,
  ErrorBuilder = { build: () => [501, 'not implemented'] },
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    inactivationAndReactivationReport: async (req, res) => {
      try {
        Logger.info('Get inactivation and reactivation report: ', req.details);
        const { result, isError, error } = await service.getInactivationReactivationReport();

        const workbook = excelService.createSheets(result);

        res.header(
          'content-type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );
        res.header('content-disposition', `attachment; filename=${FILENAME}`);
        res.status(HttpStatus.OK);
        workbook.write(FILENAME, res);

        if (isError) {
          manageError(res, error);
        }
      } catch (err) {
        manageError(res, err);
      }
    },
    getDate: async (req, res) => {
      Logger.info('getting Date for Reports');
      res.status(HttpStatus.OK).json(moment().format('DDMMYYYY'));
    },
    checkLatestBankFile: async (req, res) => {
      try {
        Logger.info('checking the latest bank file');
        const { result: bankFilesList } = await storageService.findFilesByPartialPath(
          PARTIAL_BANK_FILE_PATH
        );

        if (!bankFilesList.length)
          return res.status(HttpStatus.NOTFOUND).json('No se han encontrado archivos');

        return res.status(HttpStatus.OK).json({ result: bankFilesList[0] });
      } catch (err) {
        return manageError(res, err);
      }
    },
    downloadBankFile: async (req, res) => {
      try {
        Logger.info('download bank file: ', req.details);
        const { uuid } = req.params;

        const { data, status, headers } = await storageService.downloadFile(uuid);
        if (status !== 200) throw new Error(data);

        res.header('content-type', headers['content-type']);
        res.header('content-disposition', headers['content-disposition']);

        Logger.info('sending bank file to client...');
        return res.send(data);
      } catch (err) {
        return manageError(res, err);
      }
    },
    checkLatestPreviredFile: async (req, res) => {
      try {
        Logger.info('checking the latest previred file');
        const { result: previredFilesList } = await storageService.findFilesByPartialPath(
          '/Previred/prev'
        );

        if (!previredFilesList.length)
          return res.status(HttpStatus.NOTFOUND).json('No se han encontrado archivos');

        return res.status(HttpStatus.OK).json({ result: previredFilesList[0] });
      } catch (err) {
        return manageError(res, err);
      }
    },
    downloadPreviredFile: async (req, res) => {
      try {
        Logger.info('download previred file: ', req.details);
        const { uuid } = req.params;

        const { data, status, headers } = await storageService.downloadFile(uuid);
        if (status !== 200) throw new Error(data);

        res.header('content-type', headers['content-type']);
        res.header('content-disposition', headers['content-disposition']);

        Logger.info('sending previred file to client...');
        return res.send(data);
      } catch (err) {
        return manageError(res, err);
      }
    }
  };
};

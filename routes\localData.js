const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');

const testModel = require('../models/tests');
const validateAccess = require('../lib/auth/validate');

module.exports = router => {
  router.get('/array/*', validateAccess(), async (req, res) => {
    const result = await testModel.find({}).lean();
    Logger.info(`GET [array] local data: ${JSON.stringify(result)}`);
    return res.status(HttpStatus.OK).json(result);
  });
  router.get('/object/*', validateAccess(), async (req, res) => {
    const [result] = await testModel.find({}).lean();
    Logger.info(`GET [object] local data: ${JSON.stringify(result)}`);
    return res.status(HttpStatus.OK).json(result);
  });
  router.post('/array/*', validateAccess(), async (req, res) => {
    const result = await testModel.find({}).lean();
    Logger.info(`POST [array] local data: ${JSON.stringify(result)}`);
    return res.status(HttpStatus.OK).json(result);
  });
  router.post('/object/*', validateAccess(), async (req, res) => {
    const [result] = await testModel.find({}).lean();
    Logger.info(`POST [object] local data: ${JSON.stringify(result)}`);
    return res.status(HttpStatus.OK).json(result);
  });
};

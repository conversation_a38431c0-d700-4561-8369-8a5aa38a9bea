const uploadToSFTP = async ({
  fileName,
  fs,
  sftpClient,
  sftpCredentials,
  connectToSFTPServer,
  result,
  mapperData,
  table,
  Logger
}) => {
  try {
    const processData = result || [];
    const data = processData.map(mapperData);
    const tableData = table(data);

    await fs.writeFile(`${__dirname}/${fileName}`, tableData).catch(err => Logger.error(err));

    const { connected, error } = await connectToSFTPServer(sftpClient, sftpCredentials);
    if (!connected) throw error;
    await sftpClient.uploadFrom(
      `${__dirname}/${fileName}`,
      `${process.env.CIVIL_REGISTRATION_SFTP_INPUT_FOLDER}${fileName}`
    );
    return { completed: true };
  } catch (error) {
    return { error };
  } finally {
    await fs.unlink(`${__dirname}/${fileName}`).catch(err => Logger.error(err));
    sftpClient.close();
  }
};

module.exports = uploadToSFTP;

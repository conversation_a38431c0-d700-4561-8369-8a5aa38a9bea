module.exports = ({ HttpStatus, ErrorBuilder, cajaService, validationResult, Logger }) => {
  return {
    updateCaja: async (req, res) => {
      Logger.info('update nomenclator Caja: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { name, percentage, isMaxAmount, maxAmount } = req.body.caja;
      const { result, isError, error } = await cajaService.updateCaja({
        name,
        percentage,
        isMaxAmount,
        maxAmount
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator Caja has been successfully completed');
        res.status(HttpStatus.OK).json({ result });
      }
    },

    getCajas: async (req, res) => {
      try {
        Logger.info('Get all Cajas');
        const { result } = await cajaService.getCajas();
        res.status(HttpStatus.OK).json({ result });
      } catch (error) {
        Logger.error(error);
      }
    }
  };
};

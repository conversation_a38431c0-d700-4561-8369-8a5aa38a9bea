const Excel = require('exceljs');
const fs = require('fs');

const listToCSV = data => {
  const { createdAt } = data[0];
  const myDate = new Date(createdAt);
  const spMonth = myDate.toLocaleString('es-419', { month: 'long' }).toUpperCase();
  const nmbYear = myDate.getFullYear();
  const MANUAL_ASIG_FAMILIAR = '1106010004';

  const csvData = [];
  csvData.push(`${spMonth} ${nmbYear}`);
  csvData.push('P. ECONÓMICAS, , TOTAL');

  const bodyData = data.map(element => {
    const { label, code, value } = element;

    if (code === MANUAL_ASIG_FAMILIAR) {
      return `${label},${code}, Ingresar este valor manualmente`;
    }

    return `${label},${code},${value}`;
  });

  csvData.push(...bodyData);

  return csvData.join('\n');
};

const getWorkBook = async (data, fileName) => {
  try {
    const workbook = new Excel.Workbook();
    const options = {
      sheetName: 'Contabilizacion'
    };
    const filePath = `${__dirname}/${fileName}.csv`;
    const cvsData = listToCSV(data);
    const TITLE_ROW = 2;
    const FIRST_DATA_ROW = 3;
    const MAX_ROW_ALTERED = FIRST_DATA_ROW + data.length;

    fs.writeFileSync(filePath, cvsData);

    const worksheet = await workbook.csv.readFile(filePath, options);
    const colA = worksheet.getColumn('A');
    const colB = worksheet.getColumn('B');
    const row2 = worksheet.getRow(2);

    worksheet.mergeCells('A1:B1');
    worksheet.mergeCells('A2:B2');
    colA.width = 40;
    colB.width = 15;
    colB.alignment = { vertical: 'middle', horizontal: 'center' };
    row2.height = 30;
    row2.alignment = { vertical: 'middle', horizontal: 'center' };

    for (let i = TITLE_ROW; i < MAX_ROW_ALTERED; i += 1) {
      worksheet.getCell(`A${i}`).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '39873C' }
      };

      worksheet.getCell(`A${i}`).border = {
        top: { style: 'double' },
        left: { style: 'double' },
        bottom: { style: 'double' },
        right: { style: 'double' }
      };
    }

    worksheet.getCell('C2').fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '39873C' }
    };

    fs.unlinkSync(filePath);

    return { workbook };
  } catch (error) {
    return { error };
  }
};

module.exports = {
  getWorkBook
};

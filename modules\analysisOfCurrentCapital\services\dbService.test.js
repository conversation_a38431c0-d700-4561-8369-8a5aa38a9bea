/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const PensionModel = require('../../../models/pension');
const service = require('./dbService');
const pensionsData = require('../../../resources/pensions.json');
const CurrentCapitalReport = require('../models/CurrentCapitalReport');

describe('Verify checkpoint after calculate discounts', () => {
  beforeAll(beforeAllTests);

  let pensionOne;
  let pensionTwo;
  let pensionThree;
  let pensionFour;
  let pensionFive;

  beforeEach(() => {
    pensionOne = {
      ...pensionsData[0],
      validityType: 'supervivencia inválida',
      enabled: true,
      createdAt: moment().toDate(),
      updatedAt: moment().toDate(),
      currentCapitalCalculation: {
        totalCapital: 4000,
        capitalLaw19578: 1050,
        capitalLaw19578Ipc: 2050,
        basePensionCapital: 10000,
        capitalPBIpc: 25000
      }
    };

    pensionTwo = {
      ...pensionOne,
      enabled: false,
      createdAt: moment()
        .subtract(1, 'month')
        .toDate(),
      currentCapitalCalculation: {
        totalCapital: 4000
      }
    };

    pensionThree = {
      ...pensionsData[0],
      validityType: 'supervivencia inválida',
      enabled: true,
      createdAt: moment().toDate(),
      updatedAt: moment().toDate(),
      currentCapitalCalculation: {
        totalCapital: 5000,
        capitalLaw19578: 1050,
        capitalLaw19578Ipc: 2050
      }
    };

    pensionFour = {
      ...pensionOne,
      pensionType: 'Pensión por accidente de trayecto',
      enabled: false,
      createdAt: moment()
        .subtract(1, 'month')
        .toDate(),
      currentCapitalCalculation: {
        totalCapital: 5000
      }
    };

    pensionFive = {
      ...pensionsData[0],
      validityType: 'supervivencia inválida',
      enabled: true,
      createdAt: moment().toDate(),
      updatedAt: moment().toDate(),
      currentCapitalCalculation: {
        totalCapital: 5000,
        capitalLaw19578: 10050,
        capitalLaw19578Ipc: 2050
      }
    };
  });

  it('should generate report 1 correctly', async () => {
    await PensionModel.insertMany([pensionOne, pensionTwo]);
    await service.generateValidPensionsInPrevAndCurrMonthReport({
      firstCategory: 'Mantiene',
      secondCategory: 'Nuevo',
      reportType: 'Reporte 1'
    });
    const result = await CurrentCapitalReport.find({});
    const [reporteNueva] = result.filter(r => r.category === 'Nuevo');
    const [reporteMantiene] = result.filter(r => r.category === 'Mantiene');
    const [reporteGeneral] = result.filter(r => r.category === 'Total general');

    expect(result.length).toBe(3);
    expect(reporteMantiene.pensionporaccidentedetrabajo).toBe(4000);
    expect(reporteNueva.pensionporaccidentedetrabajo).toBe(0);
    expect(reporteGeneral.pensionporaccidentedetrabajo).toBe(4000);
  });

  it('should generate report 2 correctly', async () => {
    await PensionModel.insertMany([pensionThree, pensionFour]);
    await service.generateValidPensionsInPrevAndCurrMonthReport({
      firstCategory: 'Salida',
      secondCategory: 'Mantiene',
      reportType: 'Reporte 2'
    });
    const result = await CurrentCapitalReport.find({});
    const [reporteSalida] = result.filter(r => r.category === 'Salida');
    const [reporteMantiene] = result.filter(r => r.category === 'Mantiene');
    const [reporteGeneral] = result.filter(r => r.category === 'Total general');

    expect(result.length).toBe(3);
    expect(reporteMantiene.pensionporaccidentedetrayecto).toBe(5000);
    expect(reporteSalida.pensionporaccidentedetrayecto).toBe(0);
    expect(reporteGeneral.pensionporaccidentedetrayecto).toBe(5000);
  });

  it('should generate report 3 correctly', async () => {
    await PensionModel.insertMany([
      {
        ...pensionOne,
        pensionType: 'pension por orfandad',
        linkedDate: moment().toDate()
      },
      {
        ...pensionThree,
        reactivationDate: moment().toDate(),
        pensionType: 'pension por orfandad',
        inactivationReason: 'Vencimiento de certificado de estudios'
      }
    ]);
    await service.generatePensionsLinkedAndReactivatedInCurrentMonthReport();
    const result = await CurrentCapitalReport.find({ reportType: 'Reporte 3' });
    const [newlyLinkedReport] = result.filter(r => r.category === 'Nuevos enlazados');
    const [reactivatedReport] = result.filter(r => r.category === 'Transitoria reactivada');
    const [reportStudyCertificate] = result.filter(
      r => r.category === 'Reactivada por Certificado de estudios'
    );

    expect(result.length).toBe(4);
    expect(newlyLinkedReport.pensionpororfandad).toBe(4000);
    expect(reactivatedReport.pensionporaccidentedetrayecto).toBe(0);
    expect(reportStudyCertificate.pensionpororfandad).toBe(5000);
  });

  it('should generate report 4 correctly', async () => {
    await PensionModel.insertMany([
      {
        ...pensionOne,
        enabled: true,
        pensionType: 'Pension de viudez con hijos',
        validityType: 'No Vigente',
        capitalStatus: 'Salida',
        inactivationDate: moment().toDate(),
        inactivationReason: 'Vencimiento de certificado de estudios'
      },
      {
        ...pensionTwo,
        currentCapitalCalculation: {
          totalCapital: 10000
        }
      }
    ]);
    await service.generatePensionsInactInCurrMonthReport();
    const result = await CurrentCapitalReport.find({ reportType: 'Reporte 4' });
    const [outgoingReport] = result.filter(r => r.category === 'Salida');
    const [reportStudyCertificate] = result.filter(
      r => r.category === 'Vencimiento de certificado de estudios'
    );

    expect(result.length).toBe(9);
    expect(reportStudyCertificate.pensiondeviudezconhijos).toBe(10000);
    expect(outgoingReport.pensiondeviudezconhijos).toBe(10000);
  });

  it('Should generate csv report data and correct filename', async () => {
    await service.generatePensionsInactInCurrMonthReport();
    const { csvReportData, filename, error } = await service.getCsvReportData('report4');
    expect(csvReportData).toMatch(/Motivo de inactivaci[oó]n/);
    expect(error).toBe(false);
    expect(filename).toMatch(/Reporte4_/);
  });

  it('Should generate report 5', async () => {
    await PensionModel.insertMany([pensionOne, pensionThree, pensionFive]);
    await service.generateValidPensionInCurrentMonthReport();
  });

  afterEach(async () => {
    await PensionModel.deleteMany().catch(error => console.error(error));
    await CurrentCapitalReport.deleteMany().catch(error => console.error(error));
  });

  afterAll(afterAllTests);
});

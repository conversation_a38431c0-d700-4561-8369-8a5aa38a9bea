/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker inactivate or reactivate process Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let TemporaryHorphanhoodService;
  beforeEach(() => {
    done = jest.fn();
    service = {
      inactivationReactivationProcess: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };
    TemporaryHorphanhoodService = {
      horphanhoodFileAlreadyImported: jest.fn(() => Promise.resolve(true))
    };

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      TemporaryHorphanhoodService
    });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.inactivationReactivationProcess).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      TemporaryHorphanhoodService
    });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.inactivationReactivationProcess).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      TemporaryHorphanhoodService
    });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.inactivationReactivationProcess).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    service.inactivationReactivationProcess = jest.fn(() =>
      Promise.reject(new Error('Error service'))
    );
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      TemporaryHorphanhoodService
    });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(service.inactivationReactivationProcess).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

## Prestaciones economicas

Aplicación para administrar el proceso de generación y ejecución de pagos de pensiones. Este proyecto se encarga del Bakend.

## Comenzando 🚀

El repositorio del proyecto se ordena bajo el modelo de branching [GitFlow](<https://achsdev.visualstudio.com/Template-Estandar_Proyecto_Devops/_wiki/wikis/Template-Estandar_Proyecto_Devops.wiki/4591/Repositorio-de-c%C3%B3digo-Source-Code-Management-(SCM)?anchor=4>)-branching-model)
Información complementaria de GitFlow: [GitFlow Atlassian](https://www.atlassian.com/es/git/tutorials/comparing-workflows/gitflow-workflow)

Mira **Deployment** para conocer como desplegar el proyecto.

### Pre-requisitos 📋

Node 14.17.4
MongoDB 5.0.6

### Instalación 🔧

Instalar Node 14.17.4
Instalar librerías del proyecto ejecutando desde la consola el comando "npm ci"

Si tienes mas de una versión de node Instalar NVM en este link encontraras el aplicativo y ejemplos de como utilizarlo https://github.com/coreybutler/nvm-windows

Instalar MongoDB 5.0.6  
Si quieres instalar MongoDB en ambiente local en este link encuentras el instalador https://www.mongodb.com/

Asegurarse que exista el archivo de variables de entorno ubicado en la raiz .env si no existe debes crearlo y copiar el contenido del archivo .env.tpl en el archivo .env recien creado.

Modificar variable de entorno MONGODB_DB_URL existe en archivo .env el valor indica la cadena de conexión a la base de datos mongoDB
ejemplo:
MONGODB_DB_URL=mongodb://localhost:27017/achs-pensiones-dev

Modificar variable de entorno para poder inciar sesion:
TENANT_ID
CLIENT_ID

## Ejecutando las pruebas ⚙️

Ejecutar comando npm start. Al ejecutar comando de no existir la DB en el servidor se creara la DB y todas las collections que la componen

si no existe datos de pensionados en la DB seguir los pasos indicados en el documento link:
https://achsdev.visualstudio.com/AG139%20-%20Prestaciones%20Economicas%202.0/_wiki/wikis/AG139---Prestaciones-Economicas-2.0.wiki/5438/-Base-de-datos

video explicativo carga de datos link:
https://achs-my.sharepoint.com/:v:/g/personal/oyanezq_achs_cl/ET13ynB3X6FBjISGCRJjKwMBXwN3Qy2giwJh_AEmqBnKFQ

### Analice las pruebas end-to-end 🔩

para revisar pruebas unitarias ejecutar comando "npm run test:cov"
Al ejecuar comando anterior se creara la carpeta coverage en la raiz del proyecto en la cual encontras el siguiente archivo coverage\lcov-report\index.html.
En el cual visualizaras detalle de covertura de pruebas de las componentes que componen la API

### Y las pruebas de estilo de codificación ⌨️

_Explica que verifican estas pruebas y por qué_

```
Da un ejemplo
```

## Despliegue 📦

instrucciones para realizar modificaciones a la api
posicionarse sobre branche develop-achs "git checkout develop-achs"
actualizar branche "git pull origin develop-achs"
crear branche para nuevo desarrollo "git checkout -b feature/xxxxx-nuevo-desarrollo"
realizar commit de nuevo desarrollo "npm run commit" (proyecto cuenta con libreria eslint la cual revisa la calidad del codigo implementado)
realizar push de nuevo desarrollo "git push origin feature/111622-correccion-calculo-dias-transitoria"

crear PR feature/xxxxx-nuevo-desarrollo en develop-achs
desplegar PR en ambientes de QA
cerrar PR feature/xxxxx-nuevo-desarrollo

## Construido con 🛠️

- [NodeJs](https://nodejs.org/es/) - El framework bakend usado
- [MongoDB](https://www.mongodb.com/es) - Motor de base de datos

## Contribuyendo 🖇️

Por favor lee el [CONTRIBUTING.md](https://gist.github.com/villanuevand/xxxxxx) para detalles de nuestro código de conducta, y el proceso para enviarnos pull requests.

## Wiki 📖

Puedes encontrar mucho más de cómo utilizar este proyecto en nuestra [Wiki](https://achsdev.visualstudio.com/AG139%20-%20Prestaciones%20Economicas%202.0/_wiki/wikis/AG139---Prestaciones-Economicas-2.0.wiki/352/Informaci%C3%B3n-del-Proyecto-Prestaciones-Econ%C3%B3micas-2.0)

## Versionado 📌

Usamos [SemVer](http://semver.org/) para el versionado. Para todas las versiones disponibles, mira los [tags en este repositorio](https://github.com/tu/proyecto/tags).

## Autores ✒️

_Menciona a todos aquellos que ayudaron a levantar el proyecto desde sus inicios_

- **Andrés Villanueva** - _Trabajo Inicial_ - [villanuevand](https://github.com/villanuevand)

* **Equipo ACHS**  
  También puedes mirar la lista de todos los [Equipo Proyecto](https://achsdev.visualstudio.com/AG139%20-%20Prestaciones%20Economicas%202.0/_wiki/wikis/AG139---Prestaciones-Economicas-2.0.wiki/5446/1.-Resumen-Ejecutivo) quíenes han participado en este proyecto.

## Licencia 📄

Este proyecto está bajo la Licencia (Tu Licencia) - mira el archivo [LICENSE.md](LICENSE.md) para detalles

## Expresiones de Gratitud 🎁

- Comenta a otros sobre este proyecto 📢
- Invita una cerveza 🍺 o un café ☕ a alguien del equipo.
- Da las gracias públicamente 🤓.
- etc.

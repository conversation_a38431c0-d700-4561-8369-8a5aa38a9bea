/* eslint-disable consistent-return */
const { worker: workerFn } = require('../../sharedFiles/worker');
const service = require('../services');

const FOLDER_PATH = process.env.BULKLOAD_CAJA18_FILES_FTP_FOLDER_PATH;

const { CAJA18_FTP_HOST, CAJA18_FTP_USER, CAJA18_FTP_PASS, CAJA18_FTP_PORT } = process.env;
const ftpCredentials = {
  host: CAJA18_FTP_HOST,
  user: CAJA18_FTP_USER,
  password: CAJA18_FTP_PASS,
  port: CAJA18_FTP_PORT
};

const workerName = 'CAJA_18_BULK_LOAD';
const description = 'Carga masiva Caja 18';
const endPoint = 'caja18bulkload';
const dependencyMark = '';
const name = 'SIN_NOMBRE';

const worker = async ({ Logger, done }) =>
  workerFn({ Logger, done, service, FOLDER_PATH, ftpCredentials, workerName });

module.exports = { worker, workerName, name, description, endPoint, dependencyMark };

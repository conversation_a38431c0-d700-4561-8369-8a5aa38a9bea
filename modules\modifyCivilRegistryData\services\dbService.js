const PensionModel = require('../../../models/pension');
const { calculateEndDateOfValidity } = require('../helpers/modifiers');

const VALIDITY_TYPE = /No\s+vigente/i;
const FIELDS_TO_RECALCULATE = ['gender', 'dateOfBirth'];

const getRutList = lines => {
  const rutRegexList = [];
  lines.forEach(line => {
    const { rut } = line;
    if (rut) {
      rutRegexList.push(new RegExp(rut, 'i'));
    }
  });
  return rutRegexList;
};

const searchDifferences = (filePensions, dbPensions) => {
  return dbPensions
    .map(dbPension => {
      const filePension = filePensions.find(item => item.rut === dbPension.beneficiary.rut);
      const keys = Object.keys(filePension);
      const modifiedPension = { ...dbPension };
      const modifiedFields = [];
      keys.forEach(key => {
        if (dbPension[key] && dbPension[key].toString() !== filePension[key].toString()) {
          modifiedPension[key] = filePension[key];
          modifiedFields.push(key);
        }
        if (dbPension.beneficiary[key] && dbPension.beneficiary[key] !== filePension[key]) {
          modifiedPension.beneficiary[key] = filePension[key];
          modifiedFields.push(key);
        }
      });
      return modifiedFields.length ? { ...modifiedPension, modifiedFields } : null;
    })
    .filter(pension => pension);
};

const hasChangedBirthDateOrGender = modifiedFields =>
  modifiedFields.some(field => FIELDS_TO_RECALCULATE.includes(field));

const findElementByIndex = (pensionList, pension) => {
  const index = pensionList.findIndex(p => p.beneficiary.rut === pension.beneficiary.rut);
  const retrievedPension = pensionList.splice(index, 1);
  return retrievedPension;
};

const recalculateEndDateOfValidity = async pensionList => {
  const pensionsToRecalculate = [];
  pensionList
    .filter(pension => hasChangedBirthDateOrGender(pension.modifiedFields))
    .forEach(pension => pensionsToRecalculate.push(...findElementByIndex(pensionList, pension)));
  const recalculatedPensions = await Promise.all(
    pensionsToRecalculate.map(async pension => calculateEndDateOfValidity(pension))
  );
  return [...pensionList, ...recalculatedPensions];
};

const normalizePensionsToUpdateList = pensionsToUpdate =>
  pensionsToUpdate.map(item => {
    const { modifiedFields, ...normalizedPension } = item;
    return normalizedPension;
  });

const service = {
  async modifyCivilRegistryData(fileLines) {
    const rutRegexList = getRutList(fileLines);
    const pensions = await PensionModel.find({
      'beneficiary.rut': { $in: rutRegexList },
      validityType: { $not: VALIDITY_TYPE },
      enabled: true
    })
      .lean()
      .exec();

    const pensionsWithDifferences = searchDifferences(fileLines, pensions);

    const recalculatedPensions = await recalculateEndDateOfValidity(pensionsWithDifferences);

    return normalizePensionsToUpdateList(recalculatedPensions);
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionModel = require('../../../../models/pension');
const pensionsService = require('../../../pensions/services/pension.service');
const service = require('./dbService');
const pensionsData = require('../../../../resources/pensions.json');

const parsedLines = {
  parsedAIIItapsLines: [
    ['6564757-5', 89544],
    ['6564757-5', 57859],
    ['6177418-1', 87021]
  ],
  parsedPapsoeLines: ['13778818-7', '10240876-4'],
  parsedBrsaludLines: ['3282566-4']
};
describe('Bulk load IPS tests', () => {
  beforeAll(beforeAllTests);

  it('should update pension to add APS asset amount', async () => {
    const pension1 = {
      ...pensionsData[0],
      beneficiary: { rut: '6564757-5' },
      enabled: true,
      validityType: 'Vigente viudez'
    };
    const pension2 = {
      ...pension1,
      validityType: 'NO Vigente',
      beneficiary: { rut: '13778818-7' }
    };

    await PensionModel.insertMany([pension1, pension2]);
    try {
      const { isError } = await service.updatePensions(pensionsService, parsedLines);
      const dbDocs = await PensionModel.find({});
      // now Pensions table should have 2 docs
      expect(dbDocs.length).toBe(2);
      const enabledDocs = await PensionModel.find({ enabled: true }).lean();
      // new fields should be added to the result docs
      expect(enabledDocs[0].assets).toBeDefined();
      expect(enabledDocs[0].assets.healthExemption).toBe('Si');
      expect(enabledDocs[0].assets.aps).toBe(147403);
      // No error
      expect(isError).toBe(false);
    } catch (error) {
      console.error(error);
    }
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

const workerModule = require('./worker');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');

module.exports = {
  name: 'set-values-to-zero-pension',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SET_VALUE_TO_ZERO_PENSIONS,
  description: 'Setear valor 0 en campos de los pensionados',
  endPoint: 'setzerovaluespensionworker',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.cronDependency
};

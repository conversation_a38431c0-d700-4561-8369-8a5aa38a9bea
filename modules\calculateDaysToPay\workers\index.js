const workerModule = require('./worker');
const service = require('../services/dbService');
const sharedHelper = require('../../sharedFiles/services/helpers.service');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'calculateDaysToPayWorker',
  worker: deps =>
    workerModule.workerFn({
      service,
      sharedHelper,
      logService,
      ...deps
    }),
  description: 'Calculo de días a pagar de todas las pensiones',
  endPoint: 'calculatedaystopayworker',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

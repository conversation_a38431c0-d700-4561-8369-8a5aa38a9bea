const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  worker: deps =>
    workerModule.workerFn({
      service,
      pensionService,
      logService,
      ...deps
    }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'calculateTotalAssetsDiscountsAndRetroactiveAmounts',
  description: 'Calculo de retroactivos para pensiones de invalidez',
  endPoint: 'calculateretroactivedisabilitypension',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

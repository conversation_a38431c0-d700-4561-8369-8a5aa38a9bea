/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const InactivateMarriageModel = require('../models/inactivateMarriageModel');
const PensionModel = require('../../../../models/pension');
const pensionData = require('../../../../resources/pensions.json');
const serviceDB = require('./dbService');
const pensionService = require('../../../pensions/services/pension.service');

describe('db service Test', () => {
  let mocks;
  beforeAll(beforeAllTests);
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(InactivateMarriageModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('find One and delete', async () => {
    const toSaveData = {
      causantRut: '5e502569b20a4257fc0ece70',
      beneficiaryRut: '6e502569b20a4257fc0ece70',
      inactivationReason: 'Matrimonio',
      endDateOfValidity: '2020-02-10T03:00:00.000Z',
      dateToInactivate: '2020-03-01T03:00:00.000Z'
    };
    const validInactivateMarriage = new InactivateMarriageModel(toSaveData);
    const savedInactivateMarriage = await validInactivateMarriage.save();

    expect(savedInactivateMarriage._id).toBeDefined();
    expect(savedInactivateMarriage.beneficiaryRut.toString()).toBe(toSaveData.beneficiaryRut);
    expect(savedInactivateMarriage.causantRut.toString()).toBe(toSaveData.causantRut);

    const { isError, result } = await serviceDB.findOneAndDelete(savedInactivateMarriage._id);

    expect(isError).toBe(false);
    expect(result.beneficiaryRut).toBe(toSaveData.beneficiaryRut);
    expect(result.causantRut).toBe(toSaveData.causantRut);
  });

  it('getAll Pension', async () => {
    const { isError, result } = await serviceDB.getAllMarriagePensionToInactivate();
    expect(result).toStrictEqual([]);
    expect(!!isError).toBe(false);
  });

  it('is not processed in current months', async () => {
    const toSaveData = {
      causantRut: '5e502569b20a4257fc0ece70',
      beneficiaryRut: '6e502569b20a4257fc0ece70',
      inactivationReason: 'Matrimonio',
      endDateOfValidity: '2020-02-10T03:00:00.000Z',
      dateToInactivate: '2020-03-01T03:00:00.000Z'
    };
    const validInactivateMarriage = new InactivateMarriageModel(toSaveData);
    const savedInactivateMarriage = await validInactivateMarriage.save();

    const { completed } = await serviceDB.createUpdateMarriagePension(
      [savedInactivateMarriage],
      pensionService
    );
    expect(completed).toBe(true);
  });

  it('should not create in current months', async () => {
    const totalBefore = await PensionModel.countDocuments().exec();
    const { completed } = await serviceDB.createUpdateMarriagePension([], pensionService);
    const totalAfter = await PensionModel.countDocuments().exec();
    expect(completed).toBe(true);
    expect(totalAfter).toBe(totalBefore);
  });
  it('is not processed in current months', async () => {
    const {
      pensionsToEvaluate,
      inactivationError
    } = await serviceDB.saveMarriagePensionsToInactivate({
      lines: []
    });
    expect(inactivationError).toBeUndefined();
    expect(pensionsToEvaluate).toStrictEqual([]);
  });

  it('find dont found id', async () => {
    const { result } = await serviceDB.findOneAndDelete('5e502569b20a4257fc0ece70');
    expect(result).toBe(null);
  });

  it('find fake objectId id', async () => {
    const { isError } = await serviceDB.findOneAndDelete('sadsa');
    expect(isError).toBe(true);
  });

  it('find by id and delete', async () => {
    const toSaveData = {
      causantRut: '5e502569b20a4257fc0ece70',
      beneficiaryRut: '6e502569b20a4257fc0ece70',
      inactivationReason: 'Matrimonio',
      endDateOfValidity: '2020-02-10T03:00:00.000Z',
      dateToInactivate: '2020-03-01T03:00:00.000Z'
    };
    const validInactivateMarriage = new InactivateMarriageModel(toSaveData);
    const savedInactivateMarriage = await validInactivateMarriage.save();

    const { result } = await serviceDB.findOneAndDelete(savedInactivateMarriage.id);
    expect(result.beneficiaryRut.toString()).toBe('6e502569b20a4257fc0ece70');
  });

  it('worker and bulk', async () => {
    const validPension = new PensionModel(pensionData[0]);
    await validPension.save();
    const toSaveData = {
      causantRut: validPension.causant.rut,
      beneficiaryRut: validPension.beneficiary.rut,
      inactivationReason: 'Matrimonio',
      endDateOfValidity: '2020-02-10T03:00:00.000Z',
      dateToInactivate: new Date()
    };
    const validInactivateMarriage = new InactivateMarriageModel(toSaveData);

    await validInactivateMarriage.save();
    const { completed } = await serviceDB.createUpdateMarriagePension();

    expect(completed).toBe(true);
  });

  it('fail process saveMarriage to Inactivate', async () => {
    jest
      .spyOn(InactivateMarriageModel, 'find')
      .mockImplementationOnce(() => Promise.reject(new Error('error')));

    const { completed } = await serviceDB.createUpdateMarriagePension();
    expect(completed).toBe(false);
  });

  it('should execute proccess to inactivate', async () => {
    const beneficiaryRut = pensionData[0].beneficiary.rut;
    const validPension = new PensionModel({
      ...pensionData[0],
      enabled: true,
      pensionType: 'Pension de viudez con hijos',
      pensionStartDate: '2020-01-01T03:00:00.000Z'
    });
    await validPension.save();
    const lines = [[beneficiaryRut, '20200303']]; // must deactivate pension

    const { pensionsToEvaluate } = await serviceDB.saveMarriagePensionsToInactivate({ lines });
    const totalToInactivate = await InactivateMarriageModel.countDocuments();
    const [pensionToInactivate] = await InactivateMarriageModel.find({});

    expect(pensionsToEvaluate).toBeDefined();
    expect(pensionToInactivate).toBeDefined();
    expect(totalToInactivate).toBe(1);
    expect(moment(pensionToInactivate._doc.dateToInactivate).format('YYYY-MM-DD')).toBe(
      moment()
        .add(1, 'month')
        .startOf('month')
        .format('YYYY-MM-DD')
    ); // first day of next month
    expect(moment(pensionToInactivate._doc.endDateOfValidity).format('YYYY-MM-DD')).toBe(
      '2020-03-02'
    ); // a day before 2020-03-03
    expect(pensionToInactivate._doc.inactivationReason).toBe('Matrimonio');
  });

  afterEach(async () => {
    await PensionModel.deleteMany();
    await InactivateMarriageModel.deleteMany();
    jest.restoreAllMocks();
  });
  afterAll(afterAllTests);
});

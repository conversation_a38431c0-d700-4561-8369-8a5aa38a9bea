const moment = require('moment');

function law19668(date) {
  if (date.day() === 2 || date.day() === 3 || date.day() === 4) {
    date.subtract(date.day() - 1, 'days');
  } else if (date.day() === 5) {
    date.add(3, 'days');
  }
  return date;
}

function getNewYearsDay(year) {
  return moment(`${year}-01-01`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getSaintMondayDay(year) {
  const date = moment(`${year}-01-02`, 'YYYY-MM-DD');
  if (date.day() === 1) {
    return date.format('YYYY-MM-DD');
  }
  return undefined;
}

function getEasterSundayDay(year) {
  const Y = year;
  const a = Y % 19;
  const b = Math.floor(Y / 100);
  const c = Y % 100;
  const d = Math.floor(b / 4);
  const e = b % 4;
  const f = Math.floor((b + 8) / 25);
  const g = Math.floor((b - f + 1) / 3);
  const h = (19 * a + b - d - g + 15) % 30;
  const i = Math.floor(c / 4);
  const k = c % 4;
  const L = (32 + 2 * e + 2 * i - h - k) % 7;
  const m = Math.floor((a + 11 * h + 22 * L) / 451);
  const month = Math.floor((h + L - 7 * m + 114) / 31);
  const day = ((h + L - 7 * m + 114) % 31) + 1;
  return moment(`${year}-${month}-${day}`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getGoodFridayDay(year) {
  return moment(getEasterSundayDay(year), 'YYYY-MM-DD')
    .subtract(2, 'days')
    .format('YYYY-MM-DD');
}

function getHolySaturdayDay(year) {
  return moment(getEasterSundayDay(year), 'YYYY-MM-DD')
    .subtract(1, 'days')
    .format('YYYY-MM-DD');
}

function getLabourDay(year) {
  return moment(`${year}-05-01`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getNavyDay(year) {
  return moment(`${year}-05-21`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getSaintPeterAndSaintPaulDay(year) {
  return law19668(moment(`${year}-06-29`, 'YYYY-MM-DD')).format('YYYY-MM-DD');
}

function getOurLadyOfMountCarmelDay(year) {
  return moment(`${year}-07-16`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getAssumptionDay(year) {
  return moment(`${year}-08-15`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getSaintFridayDay(year) {
  const date = moment(`${year}-09-17`, 'YYYY-MM-DD');
  if (date.day() === 5) {
    return date.format('YYYY-MM-DD');
  }
  return undefined;
}

function getIndependenceDay(year) {
  return moment(`${year}-09-18`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getDayOfTheGloriesOfTheArmy(year) {
  return moment(`${year}-09-19`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getColumbusDay(year) {
  return law19668(moment(`${year}-10-12`, 'YYYY-MM-DD')).format('YYYY-MM-DD');
}

function getEvangelicalAndProtestantChurchesDay(year) {
  const date = moment(`${year}-10-31`, 'YYYY-MM-DD');
  if (date.day() === 2) {
    date.subtract(4, 'days');
  } else if (date.day() === 3) {
    date.add(2, 'days');
  }
  return date.format('YYYY-MM-DD');
}

function getAllSaintsDay(year) {
  return moment(`${year}-11-01`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getImmaculateConceptionOfTheVirginDay(year) {
  return moment(`${year}-12-08`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getChristmasDay(year) {
  return moment(`${year}-12-25`, 'YYYY-MM-DD').format('YYYY-MM-DD');
}

function getHolidays(year) {
  const holidays = [];
  holidays.push(getNewYearsDay(year));
  holidays.push(getSaintMondayDay(year));
  holidays.push(getGoodFridayDay(year));
  holidays.push(getHolySaturdayDay(year));
  holidays.push(getEasterSundayDay(year));
  holidays.push(getLabourDay(year));
  holidays.push(getNavyDay(year));
  holidays.push(getSaintPeterAndSaintPaulDay(year));
  holidays.push(getOurLadyOfMountCarmelDay(year));
  holidays.push(getAssumptionDay(year));
  holidays.push(getSaintFridayDay(year));
  holidays.push(getIndependenceDay(year));
  holidays.push(getDayOfTheGloriesOfTheArmy(year));
  holidays.push(getColumbusDay(year));
  holidays.push(getEvangelicalAndProtestantChurchesDay(year));
  holidays.push(getAllSaintsDay(year));
  holidays.push(getImmaculateConceptionOfTheVirginDay(year));
  holidays.push(getChristmasDay(year));
  return holidays.filter(x => x);
}

module.exports = {
  getNewYearsDay,
  getSaintMondayDay,
  getEasterSundayDay,
  getHolySaturdayDay,
  getGoodFridayDay,
  getLabourDay,
  getNavyDay,
  getSaintPeterAndSaintPaulDay,
  getOurLadyOfMountCarmelDay,
  getAssumptionDay,
  getSaintFridayDay,
  getIndependenceDay,
  getDayOfTheGloriesOfTheArmy,
  getColumbusDay,
  getEvangelicalAndProtestantChurchesDay,
  getAllSaintsDay,
  getImmaculateConceptionOfTheVirginDay,
  getChristmasDay,
  getHolidays
};

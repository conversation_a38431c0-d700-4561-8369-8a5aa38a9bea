/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const CajaLosAndesDiscountModel = require('../models/cajaLosAndesDiscount');
const PensionModel = require('../../../../models/pension');

const service = require('./index.service');
const cajaLosAndesDiscountsData = require('../../../../resources/cajaLosAndesDiscounts.json');
const pensionsData = require('../../../../resources/pensions.json');

const invalidData = {
  rut: '12110043572-1',
  movementType: 'Si',
  creditDiscountAmount: 1111.11,
  anotherDiscountAmount: 1674.59
};

describe('test service', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(CajaLosAndesDiscountModel, 'startSession').mockImplementationOnce(() => mocks);
  });
  it('should create and save a single object into caja los andes discounts', async done => {
    const {
      result: { completed },
      isError,
      error
    } = await service.insertMany([cajaLosAndesDiscountsData[0]]);
    const docsCajaLosAndes = await CajaLosAndesDiscountModel.find({}).lean();
    expect(completed).toBe(true);
    expect(isError).toBe(undefined);
    expect(error).toBe(undefined);
    expect(docsCajaLosAndes.length).toBe(1);
    expect(docsCajaLosAndes[0].rut).toBe('11348393-8');
    expect(docsCajaLosAndes[0].movementType).toBe(1);
    expect(docsCajaLosAndes[0].creditDiscountAmount).toBe(16357.78);
    expect(docsCajaLosAndes[0].anotherDiscountAmount).toBe(20765.12);
    done();
  });
  it('should not create nor save a single object into caja los andes discounts if a single value is wrong', async done => {
    const {
      result: { completed },
      isError,
      error
    } = await service.insertMany([...cajaLosAndesDiscountsData, invalidData]);
    const docsCajaLosAndes = await CajaLosAndesDiscountModel.find({}).lean();
    expect(completed).toBe(false);
    expect(isError).toBe(true);
    expect(error).toBeDefined();
    expect(docsCajaLosAndes.length).toBe(0);
    done();
  });
  it('getCurrentTime should return the current month and year  ', async done => {
    const {
      result: { currentMonthYear }
    } = await service.getCurrentMonthYear();
    const currentDate = moment().format('MMYY');
    expect(currentMonthYear).toBe(currentDate);
    done();
  });

  it('Should process caja los andes discounts', async done => {
    const pensions = pensionsData;

    pensions[0].beneficiary.rut = '11348393-8';
    pensions[1].beneficiary.rut = '10043572-1';

    await CajaLosAndesDiscountModel.insertMany(cajaLosAndesDiscountsData).catch(err =>
      console.error(err)
    );

    await PensionModel.insertMany(pensions).catch(err => console.error(err));
    await service.processCajaLosAndesDiscounts();

    const allPensionDocs = await PensionModel.countDocuments().catch(err => console.error(err));
    expect(allPensionDocs).toBe(3);
    const enabledPensionDocs = await PensionModel.find({
      enabled: true,
      validityType: { $not: /no vigente/i }
    });
    expect(enabledPensionDocs.length).toBe(2);
    expect(enabledPensionDocs[0].discounts.onePercentLosAndes).toEqual('No');
    expect(enabledPensionDocs[0].discounts.socialCreditsLosAndes).toEqual(0);
    expect(enabledPensionDocs[0].discounts.othersLosAndes).toEqual(0);
    expect(enabledPensionDocs[1].discounts.onePercentLosAndes).toEqual('No');
    expect(enabledPensionDocs[1].discounts.socialCreditsLosAndes).toEqual(0);
    expect(enabledPensionDocs[1].discounts.othersLosAndes).toEqual(0);

    done();
  });
  afterEach(async () => {
    jest.restoreAllMocks();
    await CajaLosAndesDiscountModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

const moment = require('moment');

module.exports = ({
  HttpStatus,
  summarizeSettlements,
  excelService,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  const service = summarizeSettlements;
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res
      .status(statusError)
      .json({ ...contentError, errors: [...contentError.errors, error.message] });
  }
  const customError = (res, error) =>
    res.status(HttpStatus.BAD_REQUEST).json({ message: 'Bad Request', errors: [error] });
  return {
    getDate: async (req, res) => {
      Logger.info('getting Date for datePicker');
      res.status(HttpStatus.OK).json(new Date());
    },
    getExcelData: async (req, res) => {
      const { startingDate, endingDate } = req.params;
      const { isError, error, result, badRequestError } = await service.matchLiquidationPension(
        startingDate,
        endingDate
      );

      if (isError || badRequestError) {
        Logger.error(`unable to fetch excel file ${error}`);
        // eslint-disable-next-line no-unused-expressions
        isError ? manageError(res, error) : customError(res, badRequestError);
      } else {
        try {
          const date = moment(new Date()).format('DDMMYYYY');
          const fileName = `reporte_liquidacion_${date}.xlsx`;

          const workbook = excelService.excelService(result);
          res.header(
            'content-type',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          );
          res.header('content-disposition', `attachment; filename=${fileName}`);
          res.status(HttpStatus.OK);
          workbook.write(fileName, res);
          Logger.info('Sending data for excel file');
        } catch (err) {
          manageError(res, err);
          Logger.error(`Error sending file ${err}`);
        }
      }
    }
  };
};

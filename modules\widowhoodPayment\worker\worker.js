/* eslint-disable no-unused-expressions */

/* eslint-disable consistent-return */
const cronMark = 'CRON_WIDOWHOOD_PAYMENT';
const cronDescription = 'widowhood payment';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const dependency = 'CRON_BASE_MINIMUN_PENSION_WORKER';
const getMissingDependencyMessage = `Dependencia ${dependency} aún no ejecutada`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, service, pensionService, logService, done, job }) => {
  try {
    Logger.info(`Inicio de ejecucion worker de widowhoodPayment`);
    Logger.info('widowhoodPayment: verificando si se ejecuto el cálculo de pension mínima');

    if (!(await logService.existsLog(dependency))) {
      Logger.info(getMissingDependencyMessage);
      return { message: getMissingDependencyMessage };
    }

    Logger.info('Verificando si se ejecutó previamente el worker de widowhoodPayment');

    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info('Iniciando proceso de actualización de bonos de viudez');

    const { error } = await service.setWidowhoodBonus(pensionService);

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependency, workerFn };

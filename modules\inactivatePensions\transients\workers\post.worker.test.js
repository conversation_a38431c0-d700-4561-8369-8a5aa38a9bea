/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./post.worker');
const ProcessedJobModel = require('../../../sharedFiles/models/processedJob');

describe('Worker registro civil Test', () => {
  beforeAll(beforeAllTests);

  let service;
  let Logger;
  let done;
  let logService;
  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
    service = {
      getAllAndFilter: jest.fn(),
      createUpdateMarriagePension: jest.fn()
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });
  it('worker with data', async () => {
    service.createUpdateTransientPension = jest.fn(() =>
      Promise.resolve({ hasError: false, inactivationError: '' })
    );
    await workerModule.workerFn({
      Logger,
      transientService: service,
      done,
      logService
    });

    expect(service.createUpdateTransientPension).toHaveBeenCalled();
  });

  it('catch worker error', async () => {
    service.createUpdateTransientPension = jest.fn(() =>
      Promise.resolve({ hasError: true, inactivationError: 'fake error' })
    );
    await workerModule.workerFn({
      Logger,
      logService,
      transientService: service,
      done
    });

    expect(service.createUpdateTransientPension).toHaveBeenCalled();
  });

  afterEach(async () => {
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

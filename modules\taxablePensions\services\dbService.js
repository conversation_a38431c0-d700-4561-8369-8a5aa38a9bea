/* eslint-disable no-console */
/* eslint-disable no-restricted-syntax */

const calculateTaxablePension = ({ _id, ...pension }) => {
  const {
    basePension = 0,
    article40 = 0,
    article41 = 0,
    law19403 = 0,
    law19539 = 0,
    law19953 = 0,
    assets,
    retroactiveAmounts,
    beneficiary,
    causant,
    pensionCodeId
  } = pension;
  const { aps = 0, taxableTotalNonFormulable = 0, marriageBonus = 0 } = assets;
  const { rut: beneficiaryRut } = beneficiary;
  const { rut: causantRut } = causant;
  const { forSurvival = 0, forDisability = 0 } = retroactiveAmounts;
  const sumValues =
    basePension +
    article40 +
    article41 +
    law19403 +
    law19539 +
    law19953 +
    marriageBonus +
    aps +
    forSurvival +
    taxableTotalNonFormulable +
    forDisability;
  const roundValues = Math.round(sumValues * 100) / 100;

  return {
    taxablePension: roundValues,
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    taxablePensionDate: new Date()
  };
};

const service = {
  async taxablePension(pensions, liquidationService) {
    const calculatedLiquidations = pensions.map(({ _doc }) => calculateTaxablePension(_doc));

    const { completed, error } = await liquidationService.createUpdateLiquidation(
      calculatedLiquidations
    );
    return { completed, error };
  }
};

module.exports = { calculateTaxablePension, ...service };

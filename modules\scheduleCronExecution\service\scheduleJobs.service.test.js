/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./scheduleJobs.service');
const { jobFields } = require('../scheduleCronList');

describe('Schedule Cronjobs ', () => {
  beforeAll(beforeAllTests);

  let getMonthHolidays;
  let getFirstNbusinessDays;
  let logService;
  beforeEach(() => {
    getFirstNbusinessDays = jest.fn(() => Promise.resolve(['2020-07-01']));
    getMonthHolidays = jest.fn(() => Promise.resolve([]));
    logService = {
      existsLog: jest.fn(() => Promise.resolve(false))
    };
  });

  const scheduleCronJobList = [
    {
      name: 'testName',
      businessDaysToExecute: 13,
      fileMarks: ['someDependency']
    }
  ];

  it('should generate an object of AgendaJob', async () => {
    const { jobListWithExecutionDate } = await service.getJobListWithExecutionDate({
      scheduleCronJobList,
      jobFields,
      getFirstNbusinessDays,
      getMonthHolidays
    });

    expect(jobListWithExecutionDate.length).toBe(1);
  });

  it('should insert or update two jobs in the AgendaJob collection', async () => {
    const { jobListWithExecutionDate } = await service.getJobListWithExecutionDate({
      scheduleCronJobList,
      jobFields,
      getFirstNbusinessDays,
      getMonthHolidays
    });

    const { completed, errorSchedulingJobs } = await service.scheduleJobs(jobListWithExecutionDate);

    const [doc1] = await mongoose.connection.db
      .collection('agendaJobs')
      .find({})
      .toArray();
    const date = moment('2020-07-01').format('YYYY-MM-DD');
    const isAfterDate = moment(date).diff(new Date()) > 0;

    expect(errorSchedulingJobs).toBe(null);
    expect(completed).toBe(true);
    expect(doc1.name).toBe('testName');
    expect(moment(doc1.nextRunAt).format('YYYY-MM-DD')).toStrictEqual(
      isAfterDate
        ? moment('2020-07-01').format('YYYY-MM-DD')
        : moment(new Date()).format('YYYY-MM-DD')
    );
  });

  it('should return job unfinished false as it will not find a mark', async () => {
    const { areAllJobsFinished, errorGettingMarks } = await service.areAllJobsFinished({
      logService,
      scheduleCronJobList
    });

    expect(areAllJobsFinished).toBe(false);
    expect(errorGettingMarks).toBe(null);
  });

  it('should return job unfinished false as it will throw an error', async () => {
    logService.existsLog = jest.fn(() => Promise.reject(new Error()));
    const { areAllJobsFinished, errorGettingMarks } = await service.areAllJobsFinished({
      logService,
      scheduleCronJobList
    });

    expect(areAllJobsFinished).toBe(false);
    expect(errorGettingMarks).toStrictEqual(new Error());
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

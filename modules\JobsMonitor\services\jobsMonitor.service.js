const mongoose = require('mongoose');
const moment = require('moment');
const logService = require('../../sharedFiles/services/jobLog.service');
const markCron = require('../models/cronKeyMark');

const EXECUTED = 1;
const NOTEXECUTED = 2;
const PENDINGEXECUTED = 3;

const service = {
  async getAgendaJob(nameJob) {
    try {
      return await mongoose.connection.db
        .collection('agendaJobs')
        .aggregate([
          {
            $match: {
              name: nameJob
            }
          },
          {
            $sort: {
              _id: -1
            }
          },
          {
            $limit: 1
          }
        ])
        .toArray();
    } catch (e) {
      return [];
    }
  },

  lastRegisteredJob(agenda) {
    if (!agenda.length) return NOTEXECUTED;

    const lastItem = agenda.pop();
    const { lastRunAt, nextRunAt } = lastItem;
    const validLastRunAt = moment(lastRunAt);
    const validLnextRunAt = moment(nextRunAt);
    if (!validLastRunAt.isValid() && !validLnextRunAt.isValid()) return NOTEXECUTED;
    if (validLnextRunAt.isValid() && validLnextRunAt > moment().toDate()) return PENDINGEXECUTED;
    return NOTEXECUTED;
  },

  async verifyExecutionMarca(mark, nameAgenda) {
    let status = 0;
    const executed = await logService.existsLog(mark);
    if (executed) status = EXECUTED;
    else {
      const agenda = await this.getAgendaJob(nameAgenda);
      status = this.lastRegisteredJob(agenda);
    }
    return status;
  },

  /* eslint no-await-in-loop: "error" */
  async verifyExecution() {
    const monitorJobs = markCron.getConfigCron();
    return Promise.all(
      monitorJobs.map(async item => {
        const { description, mark, endPoint, nameAgenda, dependencyMark } = item;
        const status = await this.verifyExecutionMarca(mark, nameAgenda);
        return {
          brandExecution: mark,
          description,
          endPoint: `/executeprocess/${endPoint}`,
          cronDependency: dependencyMark,
          status
        };
      })
    );
  },

  async getAllMonitorJobs() {
    try {
      const monitorJobs = await this.verifyExecution();
      return {
        result: { result: monitorJobs.map(monitor => ({ ...monitor })) },
        error: null
      };
    } catch (error) {
      return { result: [], error };
    }
  },

  async insertMarcaJob(cronMark) {
    try {
      await logService.saveLog(cronMark);
      return { result: true, error: null };
    } catch (error) {
      return { result: false, error };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker unified Reserved Amount Calculation Crons Test', () => {
  beforeAll(beforeAllTests);

  let Logger;
  let done;
  let setReservedAmountForInstitutionalPatient;
  let calculateReservedAmountDisabilityPension;
  let calculateReservedAssetsAndDiscounts;
  let logService;
  let setReservedAmountForSurvival;
  beforeEach(() => {
    calculateReservedAmountDisabilityPension = jest.fn(() =>
      Promise.resolve({
        executionCompleted: true,
        message: 'success',
        alreadyExecuted: true
      })
    );
    calculateReservedAssetsAndDiscounts = jest.fn(() =>
      Promise.resolve({
        executionCompleted: true,
        message: 'success',
        alreadyExecuted: true
      })
    );
    setReservedAmountForInstitutionalPatient = jest.fn(() =>
      Promise.resolve({
        executionCompleted: true,
        message: 'success',
        alreadyExecuted: true
      })
    );
    setReservedAmountForSurvival = jest.fn(() =>
      Promise.resolve({
        executionCompleted: true,
        message: 'success',
        alreadyExecuted: true
      })
    );

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
  });

  it('success worker retroactive', async () => {
    await workerModule.workerFn({
      Logger,
      logService,
      setReservedAmountForInstitutionalPatient,
      calculateReservedAssetsAndDiscounts,
      calculateReservedAmountDisabilityPension,
      setReservedAmountForSurvival,
      done
    });
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(setReservedAmountForInstitutionalPatient).toBeCalled();
    expect(calculateReservedAssetsAndDiscounts).toBeCalled();
    expect(calculateReservedAmountDisabilityPension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked retroactive in current month ', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      setReservedAmountForInstitutionalPatient,
      calculateReservedAssetsAndDiscounts,
      calculateReservedAmountDisabilityPension,
      setReservedAmountForSurvival,
      logService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(setReservedAmountForInstitutionalPatient).not.toBeCalled();
    expect(calculateReservedAssetsAndDiscounts).not.toBeCalled();
    expect(calculateReservedAmountDisabilityPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker retroactive', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      setReservedAmountForInstitutionalPatient,
      calculateReservedAssetsAndDiscounts,
      calculateReservedAmountDisabilityPension,
      setReservedAmountForSurvival,
      logService,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(setReservedAmountForInstitutionalPatient).not.toBeCalled();
    expect(calculateReservedAssetsAndDiscounts).not.toBeCalled();
    expect(calculateReservedAmountDisabilityPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

describe('worker ips Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let ftpHelper;
  let getParsedLinesFromIpsFiles;
  let sftp;
  beforeEach(() => {
    done = jest.fn();
    sftp = {
      Client: jest.fn(),
      connectToSFTPServer: jest.fn(() => Promise.resolve({ connected: true }))
    };
    ftpHelper = {
      extractDownloadedIpsZipFiles: jest.fn(() => Promise.resolve({ files: [{}] })),
      downloadIpsZipFiles: jest.fn(() =>
        Promise.resolve({
          patprDirPath: '',
          papsoDirPath: '',
          brsaludDirPath: '',
          downloadError: ''
        })
      )
    };
    service = {
      updatePensions: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    getParsedLinesFromIpsFiles = jest.fn(() => Promise.resolve());

    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    service.updatePensions = jest.fn(() => Promise.reject(new Error('Error service')));
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error download service', async () => {
    ftpHelper.downloadIpsZipFiles = jest.fn(() =>
      Promise.resolve({
        patprDirPath: '',
        papsoDirPath: '',
        brsaludDirPath: '',
        downloadError: 'error'
      })
    );
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(2);
  });

  it('fail worker error empty file', async () => {
    ftpHelper.extractDownloadedIpsZipFiles = jest.fn(() => Promise.resolve({ files: [] }));
    await workerModule.workerFn({
      Logger,
      service,
      logService,
      done,
      ftpHelper,
      getParsedLinesFromIpsFiles,
      sftp
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.updatePensions).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(2);
  });

  afterAll(afterAllTests);
});

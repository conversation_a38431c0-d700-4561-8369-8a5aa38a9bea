<?php

namespace App\Domain\Pension\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Domain\Pension\Models\DiscountsAndAssets;
use App\Domain\Pension\Models\Liquidation;
use App\Domain\Pension\Models\PensionHistoric;

class Pension extends Model
{
    use HasFactory;

    protected $fillable = [
        'pension_code_id',
        'beneficiary_rut',
        'causant_rut',
        'base_pension',
        'initial_base_pension',
        'pension_type',
        'enabled',
        'validity_type',
        'article_40',
        'article_41',
        'law_19403',
        'law_19539',
        'law_19953',
        'assets',
        'discounts',
        'retroactive_amounts',
        'reserved_amounts',
        'current_capital',
        'days_to_pay',
        'pension_start_date',
        'end_date_of_validity',
        'disability_degree',
        'number_of_charges',
        'family_group',
        'institutional_patient',
        'afp_affiliation',
        'health_affiliation',
        'payment_info',
    ];

    protected $casts = [
        'assets' => 'array',
        'discounts' => 'array',
        'retroactive_amounts' => 'array',
        'reserved_amounts' => 'array',
        'payment_info' => 'array',
        'pension_start_date' => 'date',
        'end_date_of_validity' => 'date',
        'enabled' => 'boolean',
        'institutional_patient' => 'boolean',
        'base_pension' => 'decimal:2',
        'initial_base_pension' => 'decimal:2',
        'article_40' => 'decimal:2',
        'article_41' => 'decimal:2',
        'law_19403' => 'decimal:2',
        'law_19539' => 'decimal:2',
        'law_19953' => 'decimal:2',
        'current_capital' => 'decimal:2',
        'days_to_pay' => 'integer',
        'disability_degree' => 'integer',
        'number_of_charges' => 'integer',
        'family_group' => 'integer',
    ];

    // Relationships
    public function discountsAndAssets(): HasOne
    {
        return $this->hasOne(DiscountsAndAssets::class, 'pension_code_id', 'pension_code_id');
    }

    public function liquidations(): HasMany
    {
        return $this->hasMany(Liquidation::class, 'pension_code_id', 'pension_code_id');
    }

    public function currentLiquidation(): HasOne
    {
        return $this->hasOne(Liquidation::class, 'pension_code_id', 'pension_code_id')
                    ->where('enabled', true)
                    ->latest();
    }

    public function historics(): HasMany
    {
        return $this->hasMany(PensionHistoric::class, 'pension_code_id', 'pension_code_id');
    }

    // Scopes
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    public function scopeByPensionType($query, string $type)
    {
        return $query->where('pension_type', $type);
    }

    public function scopeByValidityType($query, string $type)
    {
        return $query->where('validity_type', $type);
    }

    // Accessors & Mutators
    public function getTotalBasePensionAttribute(): float
    {
        return $this->base_pension + $this->article_40 + $this->article_41;
    }

    public function getTotalLawsAttribute(): float
    {
        return $this->law_19403 + $this->law_19539 + $this->law_19953;
    }

    public function getTotalAssetsAttribute(): float
    {
        $assets = $this->assets ?? [];
        return collect($assets)->sum();
    }

    public function getTotalDiscountsAttribute(): float
    {
        $discounts = $this->discounts ?? [];
        return collect($discounts)->sum();
    }

    // Business Logic Methods
    public function isActive(): bool
    {
        return $this->enabled && 
               (!$this->end_date_of_validity || $this->end_date_of_validity->isFuture());
    }

    public function requiresRecalculation(): bool
    {
        return $this->updated_at->diffInDays(now()) > 30;
    }

    public function hasRetroactiveAmounts(): bool
    {
        $retroactive = $this->retroactive_amounts ?? [];
        return collect($retroactive)->sum() > 0;
    }
}

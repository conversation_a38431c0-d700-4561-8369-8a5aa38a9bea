const { getNamespace } = require('cls-hooked');
const { decode } = require('jsonwebtoken');

const getToken = require('./getToken');

const ns = getNamespace('session');
const startContextMiddleware = (req, res, next) => {
  ns.run(() => {
    next();
  });
};

const getUser = endpoint => {
  return function setToken(req, res, next) {
    const token = getToken(req);
    const { name, email } = decode(token);

    ns.set('user', { name, email, endpoint });
    return next();
  };
};

module.exports = { startContextMiddleware, getUser, getNamespace };

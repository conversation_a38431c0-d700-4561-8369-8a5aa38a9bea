const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TemporaryImportedBonusPensionersSchema = new Schema(
  {
    pensionerRut: { type: String, required: true },
    name: { type: String },
    dateBirth: { type: String },
    institutionCode: { type: String, maxlength: 2, required: true },
    institutionRut: { type: String },
    informationPARGP: { type: String },
    informationPARNB: { type: String },
    startDate: { type: String },
    amountOtherPension: { type: Number, required: true },
    numberLoads: { type: String },
    pensionType: { type: String },
    otherAssets: { type: String },
    payBonus: { type: String, maxlength: 2, required: true }
  },

  { timestamps: true }
);
TemporaryImportedBonusPensionersSchema.plugin(paginate);
TemporaryImportedBonusPensionersSchema.index({ pensionerRut: 1 });

module.exports = mongoose.model(
  'TemporaryImportedBonusPensioners',
  TemporaryImportedBonusPensionersSchema
);

const mongoose = require('mongoose');
const paginate = require('../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TrackingUserActivitySchema = new Schema(
  {
    name: { type: String, maxlength: 70, required: true },
    email: { type: String, maxlength: 70, required: true },
    changes: { type: Object },
    action: { type: String },
    endpoint: { type: String },
    modelName: { type: String },
    documentUniqueIdentifier: { type: Object }
  },
  { timestamps: true }
);

TrackingUserActivitySchema.plugin(paginate);

module.exports = mongoose.model('TrackingUserActivity', TrackingUserActivitySchema);

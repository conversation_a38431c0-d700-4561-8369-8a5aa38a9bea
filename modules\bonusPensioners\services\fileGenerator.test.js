/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const bonusPensionersModel = require('../../generateBonusAssignmentFile/models/temporaryBonusPensioners');
const dataPensionerBonus = require('../../../resources/temporaryPensionerBonus.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const {
  generateBonusFileContent,
  downloadFileAsBase64,
  decodeBase64File,
  readFileContent,
  validateFileLines
} = require('./fileGenerator');

describe('bonusPensioners fileGenerator Test', () => {
  beforeAll(beforeAllTests);

  const CORRECT_IPS_TXT_FILE_STRING =
    'MTYwMzY1MTk7MTtKVUFOIFBFUkVaIFBFUkVaICAgICAgICAgICAgICAgICAgICAgICAgOzE5NzYwMjE1OzA0OzAwMDAwNzAzNjAxMDA7NjsgOyAgOzE5OTkwMjI1OzAzNTAwMDA7MDE7MztIQUJFUkVTO3PDrQ0KMDUxMjE1MDU7SztNQVJJQSBHT05aQUxFWiBHT05aQUxFWiAgICAgICAgICAgICAgICAgOzE5ODAwMjE1OzA2OzAwMDAwNzAzNjAxMDA7NjsgOyAgOzIwMjAwMzMwOzAzMDAwMDE7MDI7MjtIQUJFUkVTO05v';

  let importedCorrectPensioner;
  let importedIncorrectPensionerOne;
  let importedIncorrectPensionerTwo;

  beforeEach(() => {
    importedCorrectPensioner = {
      pensionerRut: '11111111',
      pensionerCheckDigit: '1',
      payBonus: 'sí',
      institutionCode: '09',
      amountOtherPension: '0584784'
    };
    importedIncorrectPensionerOne = {
      pensionerRut: '',
      pensionerCheckDigit: '1',
      payBonus: '',
      institutionCode: '',
      amountOtherPension: ''
    };
    importedIncorrectPensionerTwo = {
      pensionerRut: '22222222',
      pensionerCheckDigit: '1',
      payBonus: 'de',
      institutionCode: '09',
      amountOtherPension: '0584784'
    };
  });

  it('generate correct content for BonusPensioners txt file', async () => {
    await bonusPensionersModel.create(dataPensionerBonus[0]);
    await bonusPensionersModel.create(dataPensionerBonus[1]);

    const pensionerBonusList = await bonusPensionersModel.find({});
    const result = generateBonusFileContent(pensionerBonusList);
    const resultTextLines = result.split('\n');

    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThanOrEqual(1);
    expect(resultTextLines.length).toBe(pensionerBonusList.length);
  });

  it('generate empty string as content for BonusPensioners txt file when there are no data', async () => {
    const result = generateBonusFileContent([]);

    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThanOrEqual(0);
  });

  it('generate correct file string of bonusPensioners txt file', async () => {
    const fileContent = '130365191JUAN PEREZ PEREZ\n05121505KMARIA GONZALEZ GONZALEZ';
    const { result, error } = downloadFileAsBase64(fileContent);
    const expectFileString =
      'MTMwMzY1MTkxSlVBTiBQRVJFWiBQRVJFWgowNTEyMTUwNUtNQVJJQSBHT05aQUxFWiBHT05aQUxFWg==';

    expect(error).toBe(null);
    expect(result).toBeDefined();
    expect(result).toBe(expectFileString);
  });

  it('return error code 404 when there are no data', async () => {
    const fileContent = '';
    const { error } = downloadFileAsBase64(fileContent);

    expect(error).toBeDefined();
    expect(error.code).toBe(404);
  });

  it('decode and read successfully the IPS txt file', async () => {
    const { result, error } = decodeBase64File(CORRECT_IPS_TXT_FILE_STRING);
    const fileLines = readFileContent(result);

    expect(error).toBe(null);
    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThanOrEqual(1);
    expect(fileLines).toBeDefined();
    expect(fileLines.length).toBe(2);
    expect(fileLines[0].pensionerRut).toBe('16036519');
    expect(fileLines[0].pensionerCheckDigit).toBe('1');
    expect(fileLines[0].payBonus).toBe('sí');
  });

  it('successfully validate a correct IPS txt file data', async () => {
    const fileLines = [importedCorrectPensioner];
    const errorLines = validateFileLines(fileLines);

    expect(errorLines).toBeDefined();
    expect(errorLines.length).toBe(0);
  });

  it('successfully identify empty IPS txt file required fields', async () => {
    const fileLines = [importedCorrectPensioner, importedIncorrectPensionerOne];
    const errorLines = validateFileLines(fileLines);

    expect(errorLines).toBeDefined();
    expect(errorLines.length).toBe(1);
    expect(errorLines[0].lineNumber).toBe(2);
    expect(errorLines[0].errorFields.length).toBe(4);
    expect(errorLines[0].errorFields[0].field).toBe('pensionerRut');
    expect(errorLines[0].errorFields[1].field).toBe('payBonus');
    expect(errorLines[0].errorFields[2].field).toBe('institutionCode');
    expect(errorLines[0].errorFields[3].field).toBe('amountOtherPension');
  });

  it('successfully identify wrong pay bonus value on IPS txt file', async () => {
    const fileLines = [importedCorrectPensioner, importedIncorrectPensionerTwo];
    const errorLines = validateFileLines(fileLines);

    expect(errorLines).toBeDefined();
    expect(errorLines.length).toBe(1);
    expect(errorLines[0].lineNumber).toBe(2);
    expect(errorLines[0].errorFields.length).toBe(1);
    expect(errorLines[0].errorFields[0].field).toBe('payBonus');
  });

  afterEach(async () => {
    await bonusPensionersModel.deleteMany().catch(error => console.log(error));
  });
  afterAll(afterAllTests);
});

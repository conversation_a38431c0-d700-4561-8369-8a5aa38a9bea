/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./dbService');

const resource = require('../../../resources/historicalPensions.json');

describe('DBservice historical pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let pensionService;
  let temporaryService;
  beforeEach(() => {
    pensionService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: [] }))
    };
    temporaryService = {
      createTemporaryPensions: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
  });

  it('success create sharedFields', async () => {
    const {
      pensionCodeId,
      pensionType,
      validityType,
      basePension,
      beneficiaryName,
      endDateOfValidity,
      endDateOfTheoricalValidity,
      transient
    } = await service.sharedFields(resource[0]);
    expect(pensionCodeId).toBeDefined();
    expect(pensionType).toBeDefined();
    expect(basePension).toBeDefined();
    expect(validityType).toBeDefined();
    expect(beneficiaryName).toBeDefined();
    expect(endDateOfTheoricalValidity).toBeDefined();
    expect(endDateOfValidity).toBeDefined();
    expect(transient).toBeDefined();
  });

  it('success inactivation Mapper Pension', async () => {
    const result = await service.inactivationMapper(resource);

    const {
      pensionCodeId,
      pensionType,
      validityType,
      basePension,
      beneficiaryName,
      endDateOfValidity,
      endDateOfTheoricalValidity,
      transient,
      inactivationReason,
      paymentEndDate
    } = result[0];
    expect(pensionCodeId).toBeDefined();
    expect(pensionType).toBeDefined();
    expect(basePension).toBeDefined();
    expect(validityType).toBeDefined();
    expect(beneficiaryName).toBeDefined();
    expect(endDateOfTheoricalValidity).toBeDefined();
    expect(endDateOfValidity).toBeDefined();
    expect(transient).toBeDefined();
    expect(inactivationReason).toBeDefined();
    expect(paymentEndDate).toBeDefined();
  });

  it('success reactivation Mapper Pension', async () => {
    const result = await service.reactivationMapper([resource[1]]);

    const {
      pensionCodeId,
      pensionType,
      validityType,
      basePension,
      beneficiaryName,
      endDateOfValidity,
      endDateOfTheoricalValidity,
      transient,
      reactivationReason,
      paymentEndDate
    } = result[0];
    expect(pensionCodeId).toBeDefined();
    expect(pensionType).toBeDefined();
    expect(basePension).toBeDefined();
    expect(validityType).toBeDefined();
    expect(beneficiaryName).toBeDefined();
    expect(endDateOfTheoricalValidity).toBeDefined();
    expect(endDateOfValidity).toBeDefined();
    expect(transient).toBeDefined();
    expect(reactivationReason).toBeDefined();
    expect(paymentEndDate).toBeDefined();
  });

  it('success historical Inactivation Pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.resolve({ result: [resource[0]] }));

    const { completed, error } = await service.historicalInactivationPensions(
      pensionService,
      temporaryService
    );
    expect(completed).toBe(true);
    expect(error).toBe(null);
  });

  it('success historical Reactivation Pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.resolve({ result: [resource[1]] }));

    const { completed, error } = await service.historicalReactivationPensions(
      pensionService,
      temporaryService
    );
    expect(completed).toBe(true);
    expect(error).toBe(null);
  });

  it('fail historical Inactivation Pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.reject(new Error('error')));

    const { completed, error } = await service.historicalInactivationPensions(
      pensionService,
      temporaryService
    );
    expect(completed).toBe(false);
    expect(error).toBeDefined();
  });

  it('fail historical Reactivation Pensions', async () => {
    pensionService.getAllAndFilter = jest.fn(() => Promise.reject(new Error('error')));

    const { completed, error } = await service.historicalReactivationPensions(
      pensionService,
      temporaryService
    );
    expect(completed).toBe(false);
    expect(error).toBeDefined();
  });

  afterAll(afterAllTests);
});

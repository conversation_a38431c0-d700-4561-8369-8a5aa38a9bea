[{"institutionalPatient": false, "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "dateOfBirth": "1921-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "Depósito cuenta ahorro otros bancos", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": "", "phone": "*********"}, "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "createdAt": "2020-05-28T21:39:24.551Z", "updatedAt": "2020-05-28T21:39:24.551Z", "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionStartDate": "2016-09-21T03:00:00.000Z", "pensionCodeId": "17153", "article40": 10000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "law19403": 0, "law19539": 0, "law19953": 0, "payBonus": "SI", "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3", "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": "33"}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "apsInfo": {"apsResolutionNumber": 0, "apsResolutionDate": 0, "apsPaymentUniqueId": 0, "apsTransferCode": "0", "apsOrigin": ""}, "temporaryimportedbonuspensioners": {"pensionerRut": "********-K", "payBonus": "NO", "updatedAt": "2021-04-05T01:23:34.253+00:00", "createdAt": "2021-04-05T01:23:34.253+00:00"}}, {"institutionalPatient": false, "enabled": true, "basePension": 1042452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "dateOfBirth": "1921-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-2", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "Vigente hasta la jubilación", "pensionType": "Pensión por accidente de trabajo", "disabilityDegree": 55, "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "createdAt": "2020-05-28T21:39:24.551Z", "updatedAt": "2020-05-28T21:39:24.551Z", "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "pensionStartDate": "2016-09-21T03:00:00.000Z", "pensionCodeId": "17153", "article40": 20000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "law19403": 0, "law19539": 0, "law19953": 0, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "33333333-3", "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaAraucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "othersLosHeroes": 0, "healthLoan": 0, "health": 0, "afp": 0, "totalNonFormulable": 0, "onePercentAdjusted": 0, "healthUF": "33.9"}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "marriageBonus": 0, "christmasBonus": 0, "nationalHolidaysBonus": 0, "winterBonus": 0, "nonFormulable": 0, "rebsal": 0, "adjustedHealthExemption": 0, "netTotalNonFormulable": 0, "taxableTotalNonFormulable": 0, "netNonFormulableByReason": [], "taxableNonFormulableByReason": []}, "temporaryimportedbonuspensioners": {"pensionerRut": "********-2", "payBonus": "NO", "updatedAt": "2021-04-05T01:23:34.253+00:00", "createdAt": "2021-04-05T01:23:34.253+00:00"}}]
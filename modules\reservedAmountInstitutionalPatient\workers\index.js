const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  // Se coloca el mismo nombre que el del unificado para poder ver ejecucion en la agendajob
  name: 'reservedAssetsAndDiscountsAmountCalculation',
  description: 'Calculo de montos reservados para pacientes institucionales',
  endPoint: 'setreservedamountforinstitutionalpatient',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

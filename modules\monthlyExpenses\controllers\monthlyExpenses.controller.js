module.exports = ({
  HttpStatus,
  service,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }

  return {
    getStats: async (req, res) => {
      const { isError, error, result } = await service.getStats();
      Logger.info('Get monthly expenses: ', req.details);
      if (isError) {
        Logger.error(`Get monthly expenses error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    }
  };
};

const cronDescription = 'calculating taxable pensions.';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'TAXABLE_PENSION';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const dependenciesArray = ['REAJUST_BASEPENSION_AND_ARTICLES', 'TOTAL_ASSETS_AND_DISCOUNTS'];
const VALIDITY_TYPE = /No\s+vigente/i;

const getMissingDependencyMessage = `No se ha ejecutado una o más de las dependencias ${dependenciesArray.join(
  ', '
)} `;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const workerFn = async ({
  Logger,
  logService,
  pensionService,
  liquidationService,
  service,
  job,
  done
}) => {
  try {
    Logger.info(`Inicio procesamiento cron pensión imponible.`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    if (!(await logService.allMarksExists(dependenciesArray))) {
      Logger.info(getMissingDependencyMessage);
      return {
        message: getMissingDependencyMessage,
        status: 'UNAUTHORIZED'
      };
    }
    const { result: pensions } = await pensionService.getAllAndFilter({
      enabled: true,
      validityType: { $not: VALIDITY_TYPE }
    });
    const { err } = await service.taxablePension(pensions, liquidationService);
    if (err) throw new Error(err);
    await logService.saveLog(cronMark);
    Logger.info(`Fin procesamiento cron pensión imponible.`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependenciesArray, workerFn };

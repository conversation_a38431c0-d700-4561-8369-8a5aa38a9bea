/* eslint-disable no-restricted-syntax */
const tmp = require('tmp');

const { PensionModel } = require('../../../linkPensions/services/link.service');
const { buildAggregation } = require('./aggregationBuilder');

const { env } = process;
const { COLLECTION_DISCOUNT_HEALTH_SFTP_OUTPUT_FOLDER } = env;
const sftpCredentials = {
  host: env.COLLECTION_DISCOUNT_HEALTH_SFTP_HOST,
  port: env.COLLECTION_DISCOUNT_HEALTH_SFTP_PORT,
  username: env.COLLECTION_DISCOUNT_HEALTH_SFTP_USER,
  password: env.COLLECTION_DISCOUNT_HEALTH_SFTP_PASS
};

const service = {
  async generateFile(fileGenerationUtils, Model = PensionModel) {
    const { getLine, getFileName, appendFile, util } = fileGenerationUtils;
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;
    const aggregation = buildAggregation(year, month, 'assets.healthDiscount');
    const cursor = Model.aggregate(aggregation).cursor();
    const fileName = getFileName();
    const append = util.promisify(appendFile);

    let fileCreated = false;
    for await (const doc of cursor) {
      const lines = [];
      const { matchedPensions } = doc;
      const { result } = matchedPensions;

      lines.push(getLine({ pension: doc, isRejectedPension: false }));
      result.forEach(item => {
        lines.push(getLine({ pension: item, isRejectedPension: true }));
      });

      await append(fileName, lines.join('')).catch(error => {
        throw error;
      });
      fileCreated = true;
    }

    return fileCreated ? fileName : null;
  },

  async uploadFileToSftpServer({ fileName, fileGenerationUtils, Sftp }) {
    const sftpClient = new Sftp.Client();
    try {
      const { compressFile, getZipFileName } = fileGenerationUtils;
      const zipFileName = getZipFileName();
      const fileToUpload = compressFile(fileName, `${tmp.dirSync().name}/${zipFileName}`);
      const { connected, error } = await Sftp.connectToSFTPServer(sftpClient, sftpCredentials);
      if (!connected) throw error;
      await sftpClient.uploadFrom(
        fileToUpload,
        `${COLLECTION_DISCOUNT_HEALTH_SFTP_OUTPUT_FOLDER}/${zipFileName}`
      );
      return fileToUpload;
    } catch (error) {
      throw new Error(error);
    } finally {
      sftpClient.close();
    }
  }
};

module.exports = service;

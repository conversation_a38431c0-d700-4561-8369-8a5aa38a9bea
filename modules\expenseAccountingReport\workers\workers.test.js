/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./worker');
const PensionModel = require('../../../models/pension');

describe('Expense accounting Worker', () => {
  beforeAll(beforeAllTests);

  let service;
  let Logger;
  let logService;
  let done;

  beforeEach(() => {
    done = jest.fn();

    logService = {
      existsLog: jest.fn().mockImplementationOnce(() => Promise.resolve(false)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };

    service = {
      generateReports: jest.fn().mockResolvedValue({})
    };

    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('should return if cron is already executed', async () => {
    logService.existsLogAndRetry = jest.fn().mockImplementationOnce(() => Promise.resolve(true));

    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(done).toHaveBeenCalled();
  });

  it('should return if dependency is not already executed', async () => {
    logService.existsLog = jest.fn().mockImplementationOnce(() => Promise.resolve(false));

    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(done).toHaveBeenCalled();
  });

  it('should call the service', async () => {
    logService.existsLog = jest.fn().mockImplementationOnce(() => Promise.resolve(true));

    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(3);
    expect(service.generateReports).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(1);
    expect(done).toHaveBeenCalled();
  });

  it('should throw if the service returns an error', async () => {
    logService.existsLog = jest.fn().mockImplementationOnce(() => Promise.resolve(true));

    service.generateReports = jest.fn().mockResolvedValue({ error: 'Error' });
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLog).toHaveBeenCalledTimes(1);
    expect(Logger.info).toHaveBeenCalledTimes(2);
    expect(service.generateReports).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).not.toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

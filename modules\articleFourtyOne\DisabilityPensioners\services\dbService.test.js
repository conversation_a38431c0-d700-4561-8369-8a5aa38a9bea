/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const PensionModel = require('../../../../models/pension');
const pensionsService = require('../../../pensions/services/pension.service');
const service = require('./dbService');
const pensionsData = require('../../../../resources/pensions.json');

const pensionTypes = [
  'Pensión por accidente de trabajo',
  'Pensión por accidente de trayecto',
  'Pensión por enfermedad profesional'
];

describe('Calculate article 41', () => {
  beforeAll(beforeAllTests);

  const basePension = 300000;
  const numberOfCharges = 30;
  const pensionType = pensionTypes[Math.floor(Math.random() * pensionTypes.length)];
  const modifiedPension = {
    ...pensionsData[0],
    enabled: true,
    basePension,
    numberOfCharges,
    pensionType
  };

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should result to a 50% of the basePension when disabilityType = Invalidez parcial', async () => {
    const disabilityType = 'Invalidez parcial';
    const charges = 3;
    const expectedArticle41Value = +(0.05 * (charges - 2) * basePension).toFixed(2);

    const pension = { ...modifiedPension, disabilityType };
    try {
      await PensionModel.create(pension);
      await service.updatePensions(pensionsService);
    } catch (error) {
      console.error(error);
    }
    const dbDocs = await PensionModel.find({});
    // now Pensions table should have 2 docs
    expect(dbDocs.length).toBe(1);
    // only one doc should be enabled
    const enabledDoc = await PensionModel.find({ enabled: true });
    expect(enabledDoc.length).toBe(1);
    // article41 field should be set to 50% of the basePension
    expect(enabledDoc[0].article41).toEqual(expectedArticle41Value);
  });

  it('should result to a 100% of the basePension when disabilityType = Invalidez total', async () => {
    const disabilityType = 'Invalidez total';
    const pension = { ...modifiedPension, disabilityType };
    try {
      await PensionModel.create(pension);
      await service.updatePensions(pensionsService);
      const dbDocs = await PensionModel.find({});
      // now Pensions table should have 2 docs
      expect(dbDocs.length).toBe(1);
      // only one doc should be enabled
      const enabledDoc = await PensionModel.find({ enabled: true });
      expect(enabledDoc.length).toBe(1);
      // article41 field should be set to 100% of the basePension
      expect(enabledDoc[0].article41).toEqual(enabledDoc[0].basePension);
    } catch (error) {
      console.error(error);
    }
  });

  it('should result to a 140% of the basePension when disabilityType = Gran Invalido', async () => {
    const disabilityType = 'Gran Invalido';
    const pension = { ...modifiedPension, disabilityType };
    try {
      await PensionModel.create(pension);
      await service.updatePensions(pensionsService);
      const dbDocs = await PensionModel.countDocuments({});
      // now Pensions table should have 2 docs
      expect(dbDocs).toBe(1);
      // only one doc should be enabled
      const enabledDoc = await PensionModel.find({ enabled: true });
      expect(enabledDoc.length).toBe(1);
      // article41 field should be set to 140% of the basePension
      const expectedArticle41Value = +(enabledDoc[0].basePension * 1.4).toFixed(3);
      expect(enabledDoc[0].article41).toEqual(expectedArticle41Value);
    } catch (error) {
      console.error(error);
    }
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    try {
      await PensionModel.deleteMany({});
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const TemporaryPensionerModel = require('../models/temporaryPensioner');

const pensionerData = require('../../../resources/pensions.json')[0];

const service = require('./temporary.service');

describe('Temporary Pensioner service Model Test', () => {
  beforeAll(beforeAllTests);

  it('create & save temporary pensioner successfully', async () => {
    const validPensioner = new TemporaryPensionerModel(pensionerData);
    const savedPensioner = await validPensioner.save();

    // Object Id should be defined when successfully saved to MongoDB.
    expect(savedPensioner._id).toBeDefined();
    expect(savedPensioner.causant.name).toBe(pensionerData.causant.name);
    expect(savedPensioner.collector.rut).toBe(pensionerData.collector.rut);
    expect(savedPensioner.discounts.healthUF).toBe(pensionerData.discounts.healthUF);
    expect(savedPensioner.heavyDuty).toBe(pensionerData.heavyDuty);
  });

  // Test Validation is working!!!
  // It should us told us the errors in on gender field.
  it('create temporary pensioner without required field should failed', async () => {
    const pensionerWithoutRequiredField = new TemporaryPensionerModel({
      ...pensionerData,
      gender: null
    });
    let err;
    try {
      await pensionerWithoutRequiredField.save();
    } catch (error) {
      err = error;
    }
    expect(err).toBeInstanceOf(mongoose.Error.ValidationError);
    expect(err.errors.gender).toBeDefined();
  });

  it('should return {isError: true} if there is any error', async () => {
    jest.spyOn(TemporaryPensionerModel, 'insertMany').mockImplementationOnce(() => {
      throw new Error();
    });
    const { isError } = await service.bulkAndDelete([pensionerData]);
    expect(isError).toBe(true);
  });

  it('validation cases', async () => {
    // create a document and save it in the collection
    const pension = {
      ...pensionerData,
      pensionStartDate: '',
      article40: 100.0,
      discounts: { ...pensionerData.discounts, healthUf: 'fake' }
    };

    // calling the service's bulkAndDelete method to delete the previous doc and create a new one
    const { isError } = await service.bulkAndDelete([pension]);

    expect(isError).toBe(true);
  });

  it('should create and save temporary family allowance successfully', async done => {
    // create and save one  document
    const pensioner = await TemporaryPensionerModel.create(pensionerData);
    // Object Id should be defined when successfully saved to MongoDB.
    expect(pensioner._id).toBeDefined();
    // the collection should now have one saved document
    const { result } = await service.getAll({});
    expect(result.length).toBe(1);
    done();
  });

  it('should sort the result array correctly if a sort parameter is provided', async () => {
    await service.bulkAndDelete([
      { ...pensionerData, country: 'VEN' },
      { ...pensionerData },
      { ...pensionerData, country: 'HTI' }
    ]);
    const result = await service.getAll({}, { country: 1 });
    expect(result[0].country).toBe('CHI');
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await TemporaryPensionerModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

const moment = require('moment');

const { getFirstNbusinessDays, getMonthHolidays } = require('../../../sharedFiles/helpers');
const logService = require('../../../sharedFiles/services/jobLog.service');
const { worker: bulkLoadCaja18Worker } = require('../../caja18/workers');
const { worker: bulkLoadCajaLosHeroesWorker } = require('../../cajaLosHeroes/workers');
const { worker: bulkLoadCajaLaAraucanaWorker } = require('../../laAraucana/workers');
const { worker: bulkLoadIpsWorker } = require('../../ips/workers');

const dependencyMark = 'CRON_BASE_MINIMUN_PENSION_WORKER';
const cronMark = 'UNIFIED_BULKLOAD_AND_IPS';
const DAYS_LIMIT = process.env.BUSINESS_DAYS_LIMIT_FOR_CAJAS_AND_IPS_FILES_BULK_LOAD;

const cronDescription = 'unified bulk load and ips:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const isNotWithinPeriodOfTime =
  'El proceso no puede ser ejecutado porque no está dentro del rango temporal';

const getMissingDependencyMessage = dependency => `No se ha ejecutado la dependencia ${dependency}`;

const isInRange = (start, end) =>
  moment().isBetween(moment(start, 'YYYY-MM-DD'), moment(end, 'YYYY-MM-DD'), 'days', '[]');
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const worker = async ({ Logger, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }
    const first12BusinessDays = await getFirstNbusinessDays(
      new Date(),
      DAYS_LIMIT,
      getMonthHolidays
    );
    const firstBusinessDay = [...first12BusinessDays].shift();
    const twelthBusinessDays = [...first12BusinessDays].pop();
    const todayIsInDayLimitRange = isInRange(firstBusinessDay, twelthBusinessDays);
    if (!todayIsInDayLimitRange) throw new Error(isNotWithinPeriodOfTime);

    const {
      executionCompleted: bulkLoadCaja18WorkerCompleted,
      message: bulkLoadCaja18WorkerMessage,
      alreadyExecuted: bulkLoadCaja18WorkerExecuted
    } = await bulkLoadCaja18Worker({ Logger, done });

    const {
      executionCompleted: bulkLoadCajaLosHeroesWorkerCompleted,
      message: bulkLoadCajaLosHeroesWorkerMessage,
      alreadyExecuted: bulkLoadCajaLosHeroesWorkerExecuted
    } = await bulkLoadCajaLosHeroesWorker({ Logger, done });

    const {
      executionCompleted: bulkLoadCajaLaAraucanaWorkerCompleted,
      message: bulkLoadCajaLaAraucanaWorkerMessage,
      alreadyExecuted: bulkLoadCajaLaAraucanaWorkerExecuted
    } = await bulkLoadCajaLaAraucanaWorker({ Logger, done });

    const {
      executionCompleted: bulkLoadIpsWorkerCompleted,
      message: bulkLoadIpsWorkerMessage,
      alreadyExecuted: bulkLoadIpsWorkerExecuted
    } = await bulkLoadIpsWorker({ Logger, done });

    const allCompleted =
      (bulkLoadCaja18WorkerCompleted || bulkLoadCaja18WorkerExecuted) &&
      (bulkLoadCajaLosHeroesWorkerCompleted || bulkLoadCajaLosHeroesWorkerExecuted) &&
      (bulkLoadCajaLaAraucanaWorkerCompleted || bulkLoadCajaLaAraucanaWorkerExecuted) &&
      (bulkLoadIpsWorkerCompleted || bulkLoadIpsWorkerExecuted);

    const messageOutput = {
      bulkLoadCaja18WorkerMessage,
      bulkLoadCajaLosHeroesWorkerMessage,
      bulkLoadCajaLaAraucanaWorkerMessage,
      bulkLoadIpsWorkerMessage
    };

    if (!allCompleted) throw new Error(JSON.stringify(messageOutput));

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = {
  name: 'unifiedBulkLoadAndIpsCrons',
  cronMark,
  worker,
  repeatInterval: process.env.CRON_UNIFIED_BULK_LOAD_AND_IPS_FREQUENCY,
  description: 'Cron unificado para carga masiva de CC e IPS',
  endPoint: 'unifiedbulkloadandips',
  dependencyMark
};

const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'set amount retention',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_TOTAL_ASSETS_AND_DISCOUNTS_FREQUENCY,
  description: 'Calculo de retenciones juridicas',
  endPoint: 'discountJudicialRetention',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

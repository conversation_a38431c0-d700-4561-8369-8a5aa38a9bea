/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const RoleModel = require('../models/roles.model');
const ViewModel = require('../models/views.model');
const roleService = require('./role.service');

describe('test for CRUD operations on role', () => {
  beforeAll(beforeAllTests);

  it('should create a role', async done => {
    const roleName = 'a brand new role';
    const views = [
      {
        view: '5ffdafe31c83fe865d017f8b',
        permission: 'Read'
      }
    ];

    const { completed, error } = await roleService.createRole({ roleName, views });

    expect(error).toBeUndefined();
    expect(completed).toBe(true);

    done();
  });

  it('should fail creating a role', async done => {
    const roleName = 'a brand new role';
    const views = [{ undefinedField: '' }];

    const { completed, error } = await roleService.createRole({ roleName, views });

    expect(error).toBeDefined();
    expect(completed).toBeUndefined();

    done();
  });

  it('should read a role', async done => {
    const roleName = 'role1';
    const views = [
      {
        view: '5ffdafe31c83fe865d017f8b',
        permission: 'Read'
      }
    ];

    await RoleModel.create({ roleName, views });
    const { role: readRole, error } = await roleService.readRole({ roleName });

    expect(error).toBeUndefined();
    expect(readRole.roleName).toBe(roleName);
    expect(readRole.views[0].module).toBeUndefined();

    done();
  });
  it('should fail reading a role', async done => {
    jest.spyOn(RoleModel, 'findOne').mockImplementationOnce(() => {
      throw new Error();
    });

    const roleName = 'role1';

    const { role: readRole, error } = await roleService.readRole({ roleName });

    expect(error).toStrictEqual(new Error());
    expect(readRole).toBeUndefined();

    done();
  });

  it('should read all roles', async done => {
    const roleName = 'role1';
    const views = [
      {
        view: '5ffdafe31c83fe865d017f8b',
        permission: 'Read'
      }
    ];
    await RoleModel.create({ roleName, views });
    const { roles, error } = await roleService.readAllRoles();

    expect(error).toBeUndefined();
    expect(roles.length).toBe(1);

    done();
  });

  it('should update a role', async done => {
    const roleName = 'role1';
    const views = [
      {
        view: '5ffdafe31c83fe865d017f8b',
        permission: 'Read'
      }
    ];

    await ViewModel.create({ _id: views[0].view, module: 'asdf', view: 'hola', viewNumber: 1 });
    await RoleModel.create({ roleName, views });

    const viewToUpdate = '5ffdafe31c83fe865d017f8b';
    const newPermission = 'Write';
    const { completed, error } = await roleService.updateRole({
      roleName,
      view: viewToUpdate,
      newPermission
    });

    expect(error).toBeUndefined();
    expect(completed).toBe(true);

    done();
  });

  it('should fail updating a role', async done => {
    const roleName = 'role1';
    const viewToUpdate = '5ffdafe31c83fe865d017f8b';
    const newPermission = 'Write';
    const { completed, error } = await roleService.updateRole({
      roleName,
      view: viewToUpdate,
      newPermission
    });

    expect(error).toStrictEqual(new Error('role does not exist'));
    expect(completed).toBeUndefined();

    done();
  });

  afterEach(async () => {
    await RoleModel.deleteMany({}).catch(err => console.log(err));
    await ViewModel.deleteMany({}).catch(err => console.log(err));
  });
  afterAll(afterAllTests);
});

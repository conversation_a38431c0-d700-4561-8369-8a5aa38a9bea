[{"key": "beneficiaryRut", "path": "beneficiary.rut"}, {"key": "beneficiary<PERSON><PERSON>", "path": "beneficiary.name"}, {"key": "beneficiaryLastName", "path": "beneficiary.lastName"}, {"key": "beneficiaryMothersLastName", "path": "beneficiary.mothersLastName"}, {"key": "beneficiaryEmail", "path": "beneficiary.email"}, {"key": "beneficiary<PERSON><PERSON>", "path": "beneficiary.phone"}, {"key": "beneficiaryBirthDate", "path": "dateOfBirth"}, {"key": "causantRut", "path": "causant.rut"}, {"key": "causantName", "path": "causant.name"}, {"key": "causantLastName", "path": "causant.lastName"}, {"key": "causantMothersLastName", "path": "causant.mothersLastName"}, {"key": "collectorRut", "path": "collector.rut"}, {"key": "collectorName", "path": "collector.name"}, {"key": "collectorLastName", "path": "collector.last<PERSON><PERSON>"}, {"key": "collectorMothersLastName", "path": "collector.mothersLastName"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "path": "collector.address"}, {"key": "collectorCommune", "path": "collector.commune"}, {"key": "collectorCity", "path": "collector.city"}, {"key": "country", "path": "country"}, {"key": "endDateOfTheoricalValidity", "path": "endDateOfTheoricalValidity"}, {"key": "endDateOfValidity", "path": "endDateOfValidity"}, {"key": "inactivationReason", "path": "inactivationReason"}, {"key": "pensionStartDate", "path": "pensionStartDate"}, {"key": "pensionType", "path": "pensionType"}, {"key": "pensionCodeId", "path": "pensionCodeId"}, {"key": "ChangeOfPensionTypeDueToCharges", "path": "ChangeOfPensionTypeDueToCharges"}, {"key": "validityType", "path": "validityType"}, {"key": "afpAffiliation", "path": "afpAffiliation"}, {"key": "healthAffiliation", "path": "healthAffiliation"}, {"key": "currentCapital", "path": "currentCapital"}, {"key": "basePension", "path": "basePension"}, {"key": "disabilityDegree", "path": "disabilityDegree"}, {"key": "disabilityType", "path": "disabilityType"}, {"key": "resolutionNumber", "path": "resolutionNumber"}, {"key": "resolutionDate", "path": "resolutionDate"}, {"key": "institutionalPatient", "path": "institutionalPatient"}, {"key": "cun", "path": "cun"}, {"key": "accidentDate", "path": "accidentDate"}, {"key": "disabilityStartDate", "path": "disabilityStartDate"}, {"key": "accidentNumber", "path": "accidentNumber"}, {"key": "numberOfCharges", "path": "numberOfCharges"}, {"key": "transient", "path": "transient"}, {"key": "paymentGateway", "path": "paymentInfo.paymentGateway"}, {"key": "branchOffice", "path": "paymentInfo.branchOffice"}, {"key": "accountNumber", "path": "paymentInfo.accountNumber"}, {"key": "bank", "path": "paymentInfo.bank"}, {"key": "bankRejected", "path": "paymentInfo.bankRejected"}, {"key": "paycheckRefunded", "path": "paymentInfo.paycheckRefunded"}, {"key": "manuallyReactivated", "path": "paymentInfo.manuallyReactivated"}, {"key": "onePercentLaAraucana", "path": "discounts.onePercentLaAraucana"}, {"key": "onePercent18", "path": "discounts.onePercent18"}, {"key": "onePercentLosAndes", "path": "discounts.onePercentLosAndes"}, {"key": "onePercentLosHeroes", "path": "discounts.onePercentLosHeroes"}, {"key": "onePercentAdjusted", "path": "discounts.onePercentAdjusted"}, {"key": "othersLosHeroes", "path": "discounts.othersLosHeroes"}, {"key": "othersLosAndes", "path": "discounts.othersLosAndes"}, {"key": "healthLoan", "path": "discounts.healthLoan"}, {"key": "health", "path": "discounts.health"}, {"key": "afp", "path": "discounts.afp"}, {"key": "totalNonFormulable", "path": "discounts.totalNonFormulable"}, {"key": "aps", "path": "assets.aps"}, {"key": "nationalHolidaysBonus", "path": "assets.nationalHolidaysBonus"}, {"key": "christ<PERSON><PERSON><PERSON><PERSON>", "path": "assets.christmasBonus"}, {"key": "winterBonus", "path": "assets.winterBonus"}, {"key": "<PERSON><PERSON><PERSON><PERSON>", "path": "assets.marriageBonus"}, {"key": "rebsal", "path": "assets.rebsal"}, {"key": "adjustedHealthExemption", "path": "assets.adjustedHealthExemption"}, {"key": "article40", "path": "article40"}, {"key": "article41", "path": "article41"}, {"key": "forFamilyAssignment", "path": "assets.forFamilyAssignment"}, {"key": "taxableTotalNonFormulable", "path": "assets.taxableTotalNonFormulable"}, {"key": "netTotalNonFormulable", "path": "assets.netTotalNonFormulable"}, {"key": "forBasePension", "path": "retroactiveAmounts.forBasePension"}, {"key": "forArticle40", "path": "retroactiveAmounts.forArticle40"}, {"key": "forArticle41", "path": "retroactiveAmounts.forArticle41"}, {"key": "forFamilyAssignmentRetroActiveAmount", "path": "retroactiveAmounts.forFamilyAssignment"}, {"key": "forTaxableTotalNonFormulableAssets", "path": "retroactiveAmounts.forTaxableTotalNonFormulableAssets"}, {"key": "forInstitutionalPatient", "path": "retroactiveAmounts.forInstitutionalPatient"}, {"key": "forRejection", "path": "retroactiveAmounts.forRejection"}, {"key": "forBonuses", "path": "retroactiveAmounts.forBonuses"}, {"key": "forNetTotalNonFormulableAssets", "path": "retroactiveAmounts.forNetTotalNonFormulableAssets"}, {"key": "forTotalNonFormulableDiscounts", "path": "retroactiveAmounts.forTotalNonFormulableDiscounts"}, {"key": "bankRejected", "path": "bankRejected"}, {"key": "paycheckRefunded", "path": "paycheckRefunded"}, {"key": "manuallyReactivated", "path": "manuallyReactivated"}, {"key": "reservedAmountForPaycheck", "path": "reservedAmounts.forPayCheck"}, {"key": "retroactiveAmountForPaycheck", "path": "retroactiveAmounts.forPayCheck"}, {"key": "law19403", "path": "law19403"}, {"key": "law19539", "path": "law19539"}, {"key": "law19953", "path": "law19953"}, {"key": "totalPensionAccrued", "path": "retroactiveConstitution.totalPensionAccrued"}, {"key": "strennaRetroConstitution", "path": "retroactiveConstitution.strennaRetroConstitution"}, {"key": "otherLink", "path": "retroactiveConstitution.otherLink"}, {"key": "indemnityDiscount", "path": "retroactiveConstitution.indemnityDiscount"}, {"key": "healthDiscountAccrued", "path": "retroactiveConstitution.healthDiscountAccrued"}, {"key": "afpDiscountAccrued", "path": "retroactiveConstitution.afpDiscountAccrued"}, {"key": "settlement", "path": "retroactiveConstitution.settlement"}]
const logService = require('../../sharedFiles/services/jobLog.service');
const IpcService = require('../services/ipc.service');
const PensionService = require('../../pensions/services/pension.service');
const IPC = require('./ipcHelper');
const workerModule = require('./worker');

module.exports = {
  workerModule,
  name: 'ipcService',
  worker: deps =>
    workerModule.workerFn({
      ipcService: IpcService,
      ipcHelper: IPC,
      pensionService: PensionService,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_IPC_FRECUENCY,
  description: 'Obtener variación del IPC y si aplica reajustar por IPC',
  endPoint: 'ipcservice',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

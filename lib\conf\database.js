/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { Seeder } = require('mongo-seeding');

require('../../models/logs');
const logger = require('../logger');

const defaultConnection = 'mongodb://localhost:27017/db';
const seed = async ({ connectionUrl, dataSeedPath, modelValidatorPath, dropCollection }) => {
  const SeederConfig = {
    database: connectionUrl || defaultConnection,
    dropCollections: false,
    mongoClientOptions: { UnifiedTopologyOptions: { useUnifiedTopology: true } }
  };
  const { db } = mongoose.connection;
  const seeder = new Seeder(SeederConfig);
  const dbCollections = await db.collections();
  const collections = seeder.readCollectionsFromPath(path.resolve(dataSeedPath));
  try {
    for (const collection of collections) {
      if (
        dropCollection &&
        dbCollections.find(({ collectionName }) => collectionName === collection.name)
      ) {
        await db.collection(collection.name).drop();
      }
      const modelPath = path.resolve(modelValidatorPath, collection.name);
      const cols = await db.collections();
      if (fs.existsSync(`${modelPath}.js`)) {
        const collectionOptions = require(modelPath);
        if (!cols.find(({ collectionName }) => collectionName === collection.name)) {
          await db.createCollection(collection.name, collectionOptions);
        }
        if ((await db.collection(collection.name).countDocuments()) === 0) {
          await db.collection(collection.name).insertMany(
            collections
              .filter(({ name }) => name === collection.name)
              .map(({ documents }) => documents)
              .pop()
          );
          logger.debug(`Success in collections ${collection.name} data seed!\n`);
        }
      } else {
        logger.error(`Error: model(${collection.name}) not found in path "${modelPath}.js"\n`);
      }
    }
    logger.info(`Success in collections data seed!\n`);
  } catch (e) {
    logger.error('Error: ', e);
  }
};

const dbInstance = {
  config: ({ connectionUrl, dataSeedPath, modelValidatorPath, dropCollection }) => {
    const ConnectOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true
    };

    mongoose.connect(connectionUrl || defaultConnection, ConnectOptions, err => {
      if (err) throw err;
      logger.info('MongoDb', !connectionUrl ? defaultConnection : connectionUrl);
      seed({ connectionUrl, dataSeedPath, modelValidatorPath, dropCollection });
    });
  }
};

module.exports = dbInstance;

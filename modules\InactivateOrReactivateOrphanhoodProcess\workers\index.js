const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services');
const TemporaryHorphanhoodService = require('../../orphanhood/services/orphanhood.service');
const workerModule = require('./worker');

module.exports = {
  name: 'inactivateOrReactivateOrphanhoodProcess',
  worker: deps =>
    workerModule.workerFn({ TemporaryHorphanhoodService, logService, service, ...deps }),
  description: 'Cron inactivar-reactivar orfandad por certificado',
  endPoint: 'inactivateorreactivateorphanhoodprocess',
  cronMark: workerModule.cronMarkOrphanhood,
  dependencyMark: workerModule.cronDependencyOrphanhood
};

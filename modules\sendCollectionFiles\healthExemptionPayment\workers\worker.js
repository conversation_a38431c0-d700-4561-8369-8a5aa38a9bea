const alreadyExecutedMessage = 'Este proceso ya fue ejecutado para el mes actual.';
const cronMark = 'HEALTH_EXEMPTION_PAYMENT';
const successMessage = `EL proceso ${cronMark} se completo exitosamente.`;
const cronDescription = 'Generado y enviado el archivo esalud al servidor SFTP';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = '';
const workerFn = async ({ Logger, done, service, logService, Sftp, fileGenerationUtils, job }) => {
  try {
    Logger.info(`Cron execution start: ${cronMark}. Checking if cron was previously executed...`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark} starting process. Generating circular file...`);
    const fileName = await service.generateFile(fileGenerationUtils);
    Logger.info(`${cronMark} file generated succesfully to location: ${fileName}`);

    Logger.info(`${cronMark} Zipping and uploading health exemption file to SFTP server...`);
    const fileUploaded = await service.uploadFileToSftpServer({
      fileName,
      fileGenerationUtils,
      Sftp
    });
    Logger.info(
      `${cronMark} file zipped and uploaded successfully. Zip file location: ${fileUploaded}`
    );

    await logService.saveLog(cronMark);
    Logger.info(`${cronMark}: execution completed.`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

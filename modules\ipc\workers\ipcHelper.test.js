const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const { calculateField, createIpcObject, parseXmlToJSON } = require('./ipcHelper');
const { successXML, errorXML } = require('../../../resources/ipc.json');
const { roundValue } = require('../../sharedFiles/helpers');

describe('Helper IPC', () => {
  beforeAll(beforeAllTests);
  let ipcHelper;

  beforeEach(async () => {
    ipcHelper = {
      getPreviousMonthIPC: jest.fn().mockResolvedValue({
        result: {
          indexDateString: '01-11-2020',
          serieskey: {
            keyfamilyid: 'F074',
            seriesid: 'F074.IPC.IND.Z.EP18.C.M',
            datastage: 'INTERNAL',
            exists: 'true'
          },
          status: 'OK',
          value: '105.06'
        },
        error: false
      }),
      calculateField,
      createIpcObject
    };
  });
  it('success get last month ipc', async () => {
    const { result, error } = await ipcHelper.getPreviousMonthIPC();

    expect(ipcHelper.getPreviousMonthIPC).toHaveBeenCalled();
    expect(error).toBe(false);
    expect(result.value).toBe('105.06');
  });

  it('should success on field calculation', () => {
    const result = ipcHelper.calculateField('142452', '1.4582', 'basePension');
    expect(result).toBe(roundValue(144529.235064));
  });

  it('succes on ipc  object creation', () => {
    const { value, percentage, isLastPercentageChange } = ipcHelper.createIpcObject(
      '01-09-2020',
      '105.06',
      {
        isLastPercentageChange: true,
        date: '2020-05-01T03:00:00.000Z',
        value: '103.55',
        percentage: ''
      }
    );

    expect(value).toBe('105.06');
    expect(percentage).toBe('1.4582');
    expect(isLastPercentageChange).toBe(false);
  });

  it('succes on ipc  object creation december con variacion de noviembre', () => {
    const { value, percentage, isLastPercentageChange } = ipcHelper.createIpcObject(
      '01-11-2020',
      '105.06',
      {
        isLastPercentageChange: true,
        date: '2020-08-01T03:00:00.000Z',
        value: '103.55',
        percentage: ''
      }
    );

    expect(value).toBe('105.06');
    expect(percentage).toBe('1.4582');
    expect(isLastPercentageChange).toBe(true);
  });

  it('success in creating objects ipc variation greater than 10 percent', () => {
    const { value, percentage, isLastPercentageChange } = ipcHelper.createIpcObject(
      '01-05-2020',
      '114.06',
      {
        isLastPercentageChange: true,
        date: '2019-11-01T03:00:00.000Z',
        value: '103.55',
        percentage: ''
      }
    );

    expect(value).toBe('114.06');
    expect(percentage).toBe('10.1496');
    expect(isLastPercentageChange).toBe(true);
  });

  it('error on ipc object creation - empty values', () => {
    expect(() => ipcHelper.createIpcObject({})).toThrow('Empty values for ipc object creation');
  });

  it('error on ipc object creation - no variation', () => {
    expect(() =>
      ipcHelper.createIpcObject('01-11-2020', '105.06', {
        isLastPercentageChange: true,
        date: '2019-11-01T03:00:00.000Z',
        value: '',
        percentage: ''
      })
    ).toThrow('Error trying to get the variation');
  });

  it('success on parse xml', async () => {
    const resultado = await parseXmlToJSON(successXML);
    expect(resultado).toStrictEqual({
      indexDateString: '01-03-2020',
      serieskey: {
        keyfamilyid: 'F074',
        seriesid: 'F074.IPC.IND.Z.EP18.C.M',
        datastage: 'INTERNAL',
        exists: 'true'
      },
      status: 'OK',
      value: '105.06'
    });
  });

  it('error on parse xml', async () => {
    await expect(() => parseXmlToJSON(errorXML)).toThrow('Nonexistent key: 0');
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

const buildAggregation = (year, month, queryField) => [
  {
    $match: {
      enabled: true,
      validityType: {
        $not: new RegExp('No vigente', 'i')
      },
      [queryField]: new RegExp('s[ií]', 'i'),
      $expr: {
        $and: [
          {
            $eq: [
              {
                $month: '$createdAt'
              },
              month
            ]
          },
          {
            $eq: [
              {
                $year: '$createdAt'
              },
              year
            ]
          }
        ]
      }
    }
  },
  {
    $project: {
      beneficiary: 1,
      causant: 1,
      pensionType: 1,
      rejectionIPS: 1,
      createdAt: 1,
      rejectionHealthReductionAmount: 1,
      assets: 1,
      basePension: 1,
      article40: 1,
      law19403: 1,
      law19539: 1,
      law19953: 1,
      pensionCodeId: 1
    }
  },
  {
    $lookup: {
      from: 'pensionhistorics',
      let: {
        beneficiary: '$beneficiary.rut',
        causant: '$causant.rut',
        pensionCode: '$pensionCodeId'
      },
      pipeline: [
        {
          $match: {
            $expr: {
              $and: [
                {
                  $eq: ['$beneficiary.rut', '$$beneficiary']
                },
                {
                  $eq: ['$causant.rut', '$$causant']
                },
                {
                  $eq: ['$pensionCodeId', '$$pensionCode']
                },
                {
                  $eq: ['$enabled', false]
                }
              ]
            }
          }
        },
        {
          $project: {
            beneficiary: 1,
            causant: 1,
            pensionType: 1,
            rejectionIPS: 1,
            createdAt: 1,
            rejectionHealthReductionAmount: 1,
            assets: 1,
            basePension: 1,
            article40: 1,
            law19403: 1,
            law19539: 1,
            law19953: 1,
            pensionCodeId: 1
          }
        },
        {
          $sort: {
            _id: -1
          }
        }
      ],
      as: 'matchedPensions'
    }
  },
  {
    $addFields: {
      matchedPensions: {
        $reduce: {
          input: '$matchedPensions',
          initialValue: {
            result: [],
            status: true
          },
          in: {
            $cond: [
              {
                $and: [
                  {
                    $eq: ['$$this.rejectionIPS', true]
                  },
                  {
                    $eq: ['$$value.status', true]
                  }
                ]
              },
              {
                result: {
                  $concatArrays: ['$$value.result', ['$$this']]
                },
                status: '$$value.status'
              },
              {
                result: '$$value.result',
                status: false
              }
            ]
          }
        }
      }
    }
  }
];

module.exports = { buildAggregation };

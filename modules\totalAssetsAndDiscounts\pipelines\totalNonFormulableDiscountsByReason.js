const calculate = pension => {
  const totalAmountByReason = {};
  const { discountsAndAssets = {} } = pension;
  const { discountsNonFormulable = [] } = discountsAndAssets;
  const REASON = /retenci[oó]n judicial/i;
  discountsNonFormulable.forEach(asset => {
    const { reason, amount } = asset;
    if (reason && !REASON.test(reason)) {
      totalAmountByReason[reason] = (totalAmountByReason[reason] || 0) + amount;
    } else if (reason && REASON.test(reason)) {
      totalAmountByReason[reason] = 0;
    }
  });
  const nonFormulableByReason = Object.entries(totalAmountByReason).map(([reason, amount]) => ({
    reason,
    amount
  }));

  return { ...pension, discounts: { ...pension.discounts, nonFormulableByReason } };
};

module.exports = calculate;

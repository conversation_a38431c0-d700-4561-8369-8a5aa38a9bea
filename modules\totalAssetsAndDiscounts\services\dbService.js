const pipe = require('../pipelines/pipeline');
const pensionModel = require('../../../models/pension');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  async calculateTotalAssetsAndDiscounts() {
    const pensions = await pensionModel
      .find({ enabled: true })
      .populate('discountsAndAssets')
      .lean()
      .exec();
    const pensionsList = pensions.map(({ __v, _id, ...pension }) => pipe(pension));

    const { error } = await pensionService.updatePensions(pensionsList);
    if (error) throw new Error(error);
  },
  async calculateTotalAssetAndDiscount(beneficiaryRut, causantRut) {
    const pension = await pensionModel
      .findOne({ enabled: true, 'beneficiary.rut': beneficiaryRut, 'causant.rut': causantRut })
      .populate('discountsAndAssets')
      .lean()
      .exec();
    const processedPension = pipe(pension);

    const { error } = await pensionService.updatePensions([processedPension]);
    if (error) throw new Error(error);
  }
};

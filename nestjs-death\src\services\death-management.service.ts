import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { NotificationService } from '../common/notifications/notification.service';
import { PaymentService } from './payment.service';
import { PensionCalculationService } from './pension-calculation.service';

export interface DeathNotificationData {
  pensionCodeId: string;
  beneficiaryRut: string;
  deathDate: Date;
  deathCertificateNumber: string;
  civilRegistry: string;
  notifiedBy: string;
  notificationMethod: 'FAMILY' | 'CIVIL_REGISTRY' | 'AUTOMATIC';
  documents: any[];
}

export interface BeneficiaryInfo {
  rut: string;
  name: string;
  relationshipType: 'SPOUSE' | 'CHILD' | 'PARENT';
  birthDate: Date;
  isDependent: boolean;
  documentNumber: string;
}

/**
 * Servicio para gestionar fallecimientos y transiciones de pensión
 */
@Injectable()
export class DeathManagementService {
  private readonly logger = new Logger(DeathManagementService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly notificationService: NotificationService,
    private readonly paymentService: PaymentService,
    private readonly calculationService: PensionCalculationService
  ) {}

  /**
   * Procesa notificación de fallecimiento
   */
  async processDeathNotification(data: DeathNotificationData): Promise<string> {
    this.logger.log(`Processing death notification for pension ${data.pensionCodeId}`);

    return await this.prisma.$transaction(async (tx) => {
      // 1. Crear notificación de fallecimiento
      const deathNotification = await tx.deathNotification.create({
        data: {
          ...data,
          notificationDate: new Date(),
          status: 'PENDING'
        }
      });

      // 2. Suspender pagos inmediatamente
      await this.suspendPayments(tx, data.pensionCodeId, data.deathDate);

      // 3. Iniciar proceso de verificación
      await this.initiateVerificationProcess(tx, deathNotification.id);

      // 4. Notificar a áreas involucradas
      await this.notifyDeathToRelevantAreas(data);

      this.logger.log(`Death notification ${deathNotification.id} created and payments suspended`);
      return deathNotification.id;
    });
  }

  /**
   * Verifica y procesa el fallecimiento
   */
  async verifyAndProcessDeath(notificationId: string, verifiedBy: string): Promise<void> {
    const notification = await this.prisma.deathNotification.findUniqueOrThrow({
      where: { id: notificationId },
      include: { pension: true }
    });

    await this.prisma.$transaction(async (tx) => {
      // 1. Marcar como verificado
      await tx.deathNotification.update({
        where: { id: notificationId },
        data: {
          status: 'VERIFIED',
          verifiedAt: new Date(),
          verifiedBy
        }
      });

      // 2. Determinar tipo de proceso según tipo de pensión
      const processType = this.determineProcessType(notification.pension);

      // 3. Crear proceso de fallecimiento
      const deathProcess = await tx.deathProcess.create({
        data: {
          deathNotificationId: notificationId,
          pensionCodeId: notification.pensionCodeId,
          processType,
          processStatus: 'INITIATED',
          lastPaymentDate: await this.calculateLastPaymentDate(notification.deathDate),
          finalPaymentAmount: await this.calculateFinalPayment(notification.pension, notification.deathDate),
          proportionalAmount: await this.calculateProportionalPayment(notification.pension, notification.deathDate),
          pendingPayments: await this.getPendingPayments(notification.pensionCodeId),
          identifiedBeneficiaries: []
        }
      });

      // 4. Ejecutar proceso específico
      await this.executeDeathProcess(tx, deathProcess.id, processType);
    });
  }

  /**
   * Determina el tipo de proceso según el tipo de pensión
   */
  private determineProcessType(pension: any): string {
    switch (pension.pensionType) {
      case 'INVALIDEZ_TOTAL':
      case 'INVALIDEZ_PARCIAL':
        return 'CONVERT_TO_SURVIVAL';
      
      case 'SUPERVIVENCIA':
        return 'REDISTRIBUTE_OR_TERMINATE';
      
      case 'ORFANDAD':
        return 'TERMINATE';
      
      default:
        return 'INVESTIGATE';
    }
  }

  /**
   * Ejecuta el proceso específico de fallecimiento
   */
  private async executeDeathProcess(tx: any, processId: string, processType: string): Promise<void> {
    switch (processType) {
      case 'CONVERT_TO_SURVIVAL':
        await this.convertToSurvivalPension(tx, processId);
        break;
      
      case 'REDISTRIBUTE_OR_TERMINATE':
        await this.redistributeOrTerminate(tx, processId);
        break;
      
      case 'TERMINATE':
        await this.terminatePension(tx, processId);
        break;
      
      default:
        await this.initiateInvestigation(tx, processId);
    }
  }

  /**
   * Convierte pensión de invalidez a supervivencia
   */
  private async convertToSurvivalPension(tx: any, processId: string): Promise<void> {
    const process = await tx.deathProcess.findUniqueOrThrow({
      where: { id: processId },
      include: { 
        deathNotification: { 
          include: { pension: true } 
        } 
      }
    });

    // 1. Identificar beneficiarios elegibles
    const beneficiaries = await this.identifyEligibleBeneficiaries(
      process.deathNotification.pension
    );

    if (beneficiaries.length === 0) {
      // No hay beneficiarios - terminar pensión
      await this.terminatePension(tx, processId);
      return;
    }

    // 2. Crear pensiones de supervivencia
    const newPensions = [];
    
    for (const beneficiary of beneficiaries) {
      const survivalPension = await this.createSurvivalPension(
        tx,
        process.deathNotification.pension,
        beneficiary
      );
      newPensions.push(survivalPension);
    }

    // 3. Actualizar proceso
    await tx.deathProcess.update({
      where: { id: processId },
      data: {
        processStatus: 'SURVIVAL_PENSIONS_CREATED',
        newPensions: newPensions,
        identifiedBeneficiaries: beneficiaries
      }
    });

    // 4. Terminar pensión original
    await this.terminateOriginalPension(tx, process.pensionCodeId);
  }

  /**
   * Crea pensión de supervivencia para un beneficiario
   */
  private async createSurvivalPension(tx: any, originalPension: any, beneficiary: BeneficiaryInfo): Promise<any> {
    const survivalPercentage = this.getSurvivalPercentage(beneficiary.relationshipType);
    const newPensionAmount = Number(originalPension.basePension) * (survivalPercentage / 100);

    const survivalPension = await tx.survivalPension.create({
      data: {
        originalPensionId: originalPension.id,
        newPensionCodeId: await this.generateNewPensionCode(),
        newBeneficiaryRut: beneficiary.rut,
        newBeneficiaryName: beneficiary.name,
        relationshipType: beneficiary.relationshipType,
        originalPensionAmount: originalPension.basePension,
        survivalPercentage,
        newPensionAmount,
        effectiveDate: new Date(),
        requiredDocuments: this.getRequiredDocuments(beneficiary.relationshipType),
        submittedDocuments: [],
        status: 'PENDING_DOCUMENTS'
      }
    });

    // Crear nueva pensión en el sistema principal
    await this.createNewPensionRecord(tx, survivalPension, originalPension);

    return survivalPension;
  }

  /**
   * Obtiene porcentaje de supervivencia según relación
   */
  private getSurvivalPercentage(relationshipType: string): number {
    const percentages = {
      'SPOUSE': 100,      // 100% para cónyuge
      'CHILD': 50,        // 50% para cada hijo (hasta 100% total)
      'PARENT': 50        // 50% para padres si no hay cónyuge ni hijos
    };

    return percentages[relationshipType] || 0;
  }

  /**
   * Suspende pagos inmediatamente
   */
  private async suspendPayments(tx: any, pensionCodeId: string, deathDate: Date): Promise<void> {
    // 1. Obtener pagos pendientes
    const pendingPayments = await this.paymentService.getPendingPayments(pensionCodeId);

    // 2. Crear suspensión
    await tx.paymentSuspension.create({
      data: {
        pensionCodeId,
        suspensionReason: 'DEATH',
        suspensionDate: new Date(),
        affectedPayments: pendingPayments,
        suspendedAmount: pendingPayments.reduce((sum: number, p: any) => sum + p.amount, 0),
        authorizedBy: 'SYSTEM_AUTO'
      }
    });

    // 3. Cancelar pagos programados
    await this.paymentService.cancelScheduledPayments(pensionCodeId, deathDate);
  }

  /**
   * Calcula último pago válido
   */
  private async calculateLastPaymentDate(deathDate: Date): Promise<Date> {
    // El último pago válido es hasta el día anterior al fallecimiento
    const lastValidDate = new Date(deathDate);
    lastValidDate.setDate(lastValidDate.getDate() - 1);
    return lastValidDate;
  }

  /**
   * Calcula pago final proporcional
   */
  private async calculateFinalPayment(pension: any, deathDate: Date): Promise<number> {
    const monthlyAmount = Number(pension.basePension);
    const deathDay = deathDate.getDate();
    const daysInMonth = new Date(deathDate.getFullYear(), deathDate.getMonth() + 1, 0).getDate();
    
    // Pago proporcional hasta el día del fallecimiento
    return (monthlyAmount / daysInMonth) * (deathDay - 1);
  }

  /**
   * Calcula pago proporcional del mes
   */
  private async calculateProportionalPayment(pension: any, deathDate: Date): Promise<number> {
    return this.calculateFinalPayment(pension, deathDate);
  }

  /**
   * Identifica beneficiarios elegibles
   */
  private async identifyEligibleBeneficiaries(pension: any): Promise<BeneficiaryInfo[]> {
    // En un sistema real, esto consultaría registros civiles, bases de datos familiares, etc.
    // Por ahora, simulamos la lógica
    
    const beneficiaries: BeneficiaryInfo[] = [];
    
    // Buscar en datos de cargas familiares
    if (pension.numberOfCharges > 0) {
      // Simular hijos dependientes
      for (let i = 0; i < pension.numberOfCharges; i++) {
        beneficiaries.push({
          rut: `${Math.floor(Math.random() * 99999999)}-${Math.floor(Math.random() * 9)}`,
          name: `Hijo/a ${i + 1}`,
          relationshipType: 'CHILD',
          birthDate: new Date('2010-01-01'),
          isDependent: true,
          documentNumber: `DOC${i + 1}`
        });
      }
    }

    return beneficiaries;
  }

  /**
   * Termina pensión original
   */
  private async terminateOriginalPension(tx: any, pensionCodeId: string): Promise<void> {
    await tx.pension.update({
      where: { pensionCodeId },
      data: {
        enabled: false,
        endDateOfValidity: new Date(),
        updatedAt: new Date()
      }
    });
  }

  /**
   * Genera nuevo código de pensión
   */
  private async generateNewPensionCode(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `SUP-${timestamp}-${random}`;
  }

  /**
   * Obtiene documentos requeridos según tipo de beneficiario
   */
  private getRequiredDocuments(relationshipType: string): any[] {
    const baseDocuments = [
      { type: 'DEATH_CERTIFICATE', required: true },
      { type: 'IDENTITY_CARD', required: true }
    ];

    switch (relationshipType) {
      case 'SPOUSE':
        return [
          ...baseDocuments,
          { type: 'MARRIAGE_CERTIFICATE', required: true },
          { type: 'BANK_ACCOUNT_INFO', required: true }
        ];
      
      case 'CHILD':
        return [
          ...baseDocuments,
          { type: 'BIRTH_CERTIFICATE', required: true },
          { type: 'STUDY_CERTIFICATE', required: false },
          { type: 'DEPENDENCY_DECLARATION', required: true }
        ];
      
      case 'PARENT':
        return [
          ...baseDocuments,
          { type: 'BIRTH_CERTIFICATE_DECEASED', required: true },
          { type: 'DEPENDENCY_PROOF', required: true }
        ];
      
      default:
        return baseDocuments;
    }
  }

  /**
   * Crea registro de nueva pensión
   */
  private async createNewPensionRecord(tx: any, survivalPension: any, originalPension: any): Promise<void> {
    await tx.pension.create({
      data: {
        pensionCodeId: survivalPension.newPensionCodeId,
        beneficiaryRut: survivalPension.newBeneficiaryRut,
        causantRut: originalPension.beneficiaryRut, // El fallecido se convierte en causante
        basePension: survivalPension.newPensionAmount,
        initialBasePension: survivalPension.newPensionAmount,
        pensionType: 'SUPERVIVENCIA',
        validityType: 'PERMANENTE',
        enabled: false, // Se habilitará cuando se aprueben documentos
        
        // Copiar algunos datos del original
        dateOfBirth: new Date('1980-01-01'), // Se obtendría de registros civiles
        gender: 'F',
        pensionStartDate: survivalPension.effectiveDate,
        
        // Configuración inicial
        assets: {},
        discounts: {},
        paymentInfo: {}
      }
    });
  }

  /**
   * Obtiene pagos pendientes
   */
  private async getPendingPayments(pensionCodeId: string): Promise<any[]> {
    // Consultar pagos programados pero no ejecutados
    return [];
  }

  /**
   * Inicia proceso de verificación
   */
  private async initiateVerificationProcess(tx: any, notificationId: string): Promise<void> {
    // Crear tareas de verificación
    // Notificar a equipo de verificación
    // Programar seguimientos
  }

  /**
   * Notifica fallecimiento a áreas relevantes
   */
  private async notifyDeathToRelevantAreas(data: DeathNotificationData): Promise<void> {
    // Notificar a:
    // - Área de pagos
    // - Área legal
    // - Servicio al cliente
    // - Auditoría
  }

  /**
   * Redistribuye o termina pensión de supervivencia
   */
  private async redistributeOrTerminate(tx: any, processId: string): Promise<void> {
    // Lógica para redistribuir entre beneficiarios restantes
    // o terminar si no quedan beneficiarios elegibles
  }

  /**
   * Termina pensión definitivamente
   */
  private async terminatePension(tx: any, processId: string): Promise<void> {
    const process = await tx.deathProcess.findUniqueOrThrow({
      where: { id: processId },
      include: { deathNotification: true }
    });

    // 1. Marcar pensión como terminada
    await tx.pension.update({
      where: { pensionCodeId: process.pensionCodeId },
      data: {
        enabled: false,
        endDateOfValidity: new Date(),
        updatedAt: new Date()
      }
    });

    // 2. Procesar pagos finales y reembolsos
    await this.processFinalPaymentsAndRefunds(tx, process);

    // 3. Actualizar proceso
    await tx.deathProcess.update({
      where: { id: processId },
      data: {
        processStatus: 'TERMINATED',
        terminationDate: new Date(),
        processedAt: new Date(),
        processedBy: 'SYSTEM'
      }
    });
  }

  /**
   * Procesa pagos finales y reembolsos
   */
  private async processFinalPaymentsAndRefunds(tx: any, process: any): Promise<void> {
    // 1. Calcular si hay sobrepagos que recuperar
    const overpayments = await this.calculateOverpayments(process);
    
    if (overpayments.length > 0) {
      // Crear proceso de reembolso
      await tx.refundProcess.create({
        data: {
          pensionCodeId: process.pensionCodeId,
          refundReason: 'OVERPAYMENT_DEATH',
          refundAmount: overpayments.reduce((sum: number, op: any) => sum + op.amount, 0),
          periodFrom: process.deathNotification.deathDate,
          periodTo: new Date(),
          affectedPayments: overpayments,
          recoveryMethod: 'BANK_REVERSAL',
          recoveryStatus: 'PENDING',
          contactInfo: {}
        }
      });
    }

    // 2. Procesar pago final proporcional si corresponde
    if (process.finalPaymentAmount > 0) {
      await this.paymentService.processProportionalPayment(
        process.pensionCodeId,
        process.finalPaymentAmount
      );
    }
  }

  /**
   * Calcula sobrepagos a recuperar
   */
  private async calculateOverpayments(process: any): Promise<any[]> {
    // Identificar pagos realizados después de la fecha de fallecimiento
    return [];
  }

  /**
   * Inicia investigación para casos complejos
   */
  private async initiateInvestigation(tx: any, processId: string): Promise<void> {
    await tx.deathProcess.update({
      where: { id: processId },
      data: {
        processStatus: 'UNDER_INVESTIGATION'
      }
    });
  }
}

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const tmp = require('tmp');
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const fileHelpers = require('./fileHelpers');

describe('File helpers Test', () => {
  beforeAll(beforeAllTests);

  let sftpClient;
  let connectToSFTPServer;

  beforeEach(() => {
    sftpClient = {
      exists: jest.fn(() => Promise.resolve('-')),
      downloadTo: jest.fn(),
      close: jest.fn()
    };
    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
  });

  it('should return full path of file', () => {
    const FOLDER_PATH = '/folder/path';
    const expectedFileName = `/folder/path/Achs_${moment().format('YYYYMM')}_out.txt`;
    const receivedFileName = fileHelpers.getCivilRegistryFileName(FOLDER_PATH);

    expect(receivedFileName).toBe(expectedFileName);
  });

  it('should return true when remote file exists', async () => {
    const exists = await fileHelpers.checkRemoteFileExistence(sftpClient);

    expect(exists).toBe(true);
  });

  it('should download file if exists', async () => {
    fileHelpers.getCivilRegistryFileName = jest.fn(() => Promise.resolve('/remote/filePath'));
    fileHelpers.checkRemoteFileExistence = jest.fn(() => Promise.resolve(true));

    const { file } = await fileHelpers.downloadCivilRegistryFileFromSFTP({
      sftpClient,
      connectToSFTPServer,
      tmp,
      fileHelpers
    });

    expect(file).toBeDefined();
    expect(sftpClient.close).toHaveBeenCalled();
  });

  afterAll(afterAllTests);
});

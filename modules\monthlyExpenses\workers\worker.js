const cronDescription = 'Monthly expenses:';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const dependencyMark = 'GENERATE_AND_UPLOAD_BANK_FILE';
const cronMark = 'MONTHLY_EXPENSES';
const successMessage = `El proceso ${cronMark} se completó correctamente`;

const getMissingDependencyMessage = dep => `La dependencia "${dep}" aún no se ha ejecutado`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, logService, service, job, done }) => {
  try {
    Logger.info(`${cronDescription} checking if calculation of ${dependencyMark} was executed`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark) };
    }
    Logger.info(`${cronDescription} checking whether this process was previously executed`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }
    Logger.info(`Starting processing of  ${cronDescription} worker`);
    const { error: errorMonthlyExpenses } = await service.calculateMonthlyExpense();
    if (errorMonthlyExpenses) throw new Error(errorMonthlyExpenses);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process completed`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const service = require('./dbService');
const afpService = require('../../nomenclators/afp/services/index.service');
const resourcePensions = require('../../../resources/pensionsRetroactive.json');
const afps = require('../../../resources/netPensions/afps.json');
const pensionModel = require('../../../models/pension');

describe('Set calculation of earned fields', () => {
  beforeAll(beforeAllTests);

  it('get ruler  for calculation', async () => {
    service.getPensionsAllEnabled = jest.fn(() => Promise.resolve(resourcePensions));
    afpService.getAfpsWithFilters = jest.fn(() => Promise.resolve({ result: afps }));
    const { completed, pensioners } = await service.setAccruedFields();
    expect(pensioners.length).toBe(4);
    expect(completed).toBe(true);

    expect(pensioners[0].retroactiveConstitution.settlement).toBe(0);
    expect(pensioners[0].retroactiveConstitution.healthDiscountAccrued).toBe(0);
    expect(pensioners[0].retroactiveConstitution.afpDiscountAccrued).toBe(0);

    expect(pensioners[1].retroactiveConstitution.settlement).toBe(1656600);
    expect(pensioners[1].retroactiveConstitution.healthDiscountAccrued).toBe(140000);
    expect(pensioners[1].retroactiveConstitution.afpDiscountAccrued).toBe(203400);

    expect(pensioners[2].retroactiveConstitution.settlement).toBe(2484900);
    expect(pensioners[2].retroactiveConstitution.healthDiscountAccrued).toBe(210000);
    expect(pensioners[2].retroactiveConstitution.afpDiscountAccrued).toBe(305100);

    expect(pensioners[3].retroactiveConstitution.settlement).toBe(1656600);
    expect(pensioners[3].retroactiveConstitution.healthDiscountAccrued).toBe(140000);
    expect(pensioners[3].retroactiveConstitution.afpDiscountAccrued).toBe(203400);
  });

  it('get enabled pensions', async () => {
    await pensionModel.insertMany(resourcePensions);
    const result = await service.getPensionsAllEnabled();
    expect(result.length).toBeGreaterThanOrEqual(1);
  });

  afterAll(afterAllTests);
});

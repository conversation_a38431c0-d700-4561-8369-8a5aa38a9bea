const {
  getInactiveTemporaryHorphanhood
} = require('../../../orphanhood/services/orphanhood.service');
const { createUpdatePension } = require('../../../pensions/services/pension.service');

const logService = require('../../../sharedFiles/services/jobLog.service');

const VALIDITY_TYPE = /Vigente Orfandad/i;
const TYPE_PENSION = [/Pensi[oó]n por orfandad/i, /Pensi[oó]n de orfandad de padre y madre/i];
const VALIDITY_FALSE = false;

module.exports = {
  async inactivate(Logger) {
    try {
      const cronMark = 'INACTIVATE_ORPHANHOOD_BY_STUDY_CERTIFICATE';
      if (await logService.existsLog(cronMark)) return;
      Logger.info(`Inicio proceso: inactivar por orfandad`);
      const result = await getInactiveTemporaryHorphanhood(
        VALIDITY_FALSE,
        VALIDITY_TYPE,
        TYPE_PENSION
      );

      if (result.length) {
        const pensionList = result.map(({ _id, endDate, ...pensionerData }) => {
          const { pension } = pensionerData;
          const { beneficiary, causant, pensionCodeId } = pension;
          return {
            beneficiary,
            causant,
            pensionCodeId,
            inactivationReason: 'Vencimiento de certificado de estudios',
            endDateOfValidity: endDate,
            inactivationDate: new Date(),
            validityType: 'No vigente'
          };
        });

        if (pensionList.length) {
          const { completed, error } = await createUpdatePension(pensionList);
          if (!completed) throw new Error(error);
        }
      }
      await logService.saveLog(cronMark);
    } catch (error) {
      Logger.error(`Error al inactivar orfandad: ${error}`);
      throw new Error(error);
    }
  }
};

const { formatter } = require('./formatFields');

const pensionFields = [
  { liquidationMonth: { name: '<PERSON><PERSON>', format: formatter.month, path: 'createdAt' } },
  { liquidationYear: { name: 'Año', format: formatter.year, path: 'createdAt' } },
  {
    pensionCodeId: {
      name: 'Número de pensión',
      format: formatter.number,
      path: 'pension.pensionCodeId'
    }
  },
  {
    beneficiaryRut: { name: 'Rut beneficiario', format: formatter.normal, path: 'beneficiaryRut' }
  },
  {
    pensionType: { name: 'Tipo de pensión', format: formatter.normal, path: 'pension.pensionType' }
  },
  {
    validityType: {
      name: 'Tipo de vigencia',
      format: formatter.normal,
      path: 'pension.validityType'
    }
  },
  {
    basePension: { name: 'Pensión base', format: formatter.currency, path: 'pension.basePension' }
  },
  {
    fixedBasePension: {
      name: 'Pensión base fija',
      format: formatter.currency,
      path: 'pension.fixedBasePension'
    }
  },
  {
    daysToPay: {
      name: '<PERSON><PERSON> a pagar',
      format: formatter.number,
      path: 'pension.daysToPay'
    }
  },
  {
    fullname: { name: 'Nombre beneficiario', format: formatter.normal, path: 'fullname' }
  },
  {
    taxablePension: {
      name: 'Pensión imponible',
      format: formatter.currency,
      path: 'taxablePension'
    }
  },
  {
    netPension: { name: 'Pensión líquida', format: formatter.currency, path: 'netPension' }
  },
  {
    article40: { name: 'Artículo 40', format: formatter.currency, path: 'pension.article40' }
  },
  {
    article41: { name: 'Artículo 41', format: formatter.currency, path: 'pension.article41' }
  },
  {
    assetsFamilyAssignment: {
      name: 'Haber asignación familiar',
      format: formatter.currency,
      path: 'pension.assets.forFamilyAssignment'
    }
  },
  {
    assetsAPS: { name: 'Haber APS', format: formatter.currency, path: 'pension.assets.aps' }
  },
  {
    assetsREBSAL: {
      name: 'REBSAL (rebaja de salud ajustado)',
      format: formatter.currency,
      path: 'pension.assets.rebsal'
    }
  },
  {
    retroactiveAmountsForBasePension: {
      name: 'Monto retroactivo por pensión base',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forBasePension'
    }
  },
  {
    retroactiveAmountsForArticle40: {
      name: 'Monto retroactivo por artículo 40',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forArticle40'
    }
  },
  {
    retroactiveAmountsForArticle41: {
      name: 'Monto retroactivo por artículo 41',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forArticle41'
    }
  },
  {
    retroactiveAmountsForBonuses: {
      name: 'Monto retroactivo por aguinaldos',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forBonuses'
    }
  },
  {
    retroactiveAmountsForFamilyAssignment: {
      name: 'Monto retroactivo por asignación familiar',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forFamilyAssignment'
    }
  },
  {
    assetsMarriageBonus: {
      name: 'Bono por matrimonio',
      format: formatter.currency,
      path: 'pension.assets.marriageBonus'
    }
  },
  {
    assetsAdjustedHealthExemption: {
      name: 'Haber exención de salud ajustado',
      format: formatter.currency,
      path: 'pension.assets.adjustedHealthExemption'
    }
  },
  {
    retroactiveAmountsForDisability: {
      name: 'Monto retroactivo de invalidez',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forDisability'
    }
  },
  {
    retroactiveAmountsForInstitutionalPatient: {
      name: 'Monto retroactivo por paciente institucional',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forInstitutionalPatient'
    }
  },
  {
    retroactiveAmountsForRejection: {
      name: 'Monto retroactivo por rechazo',
      format: formatter.currency,
      path: 'pension.retroactiveAmounts.forRejection'
    }
  },
  {
    assetsChristmasBonus: {
      name: 'Haber aguinaldo de navidad',
      format: formatter.currency,
      path: 'pension.assets.christmasBonus'
    }
  },
  {
    assetsNationalHolidaysBonus: {
      name: 'Haber aguinaldo fiestas patrias',
      format: formatter.currency,
      path: 'pension.assets.nationalHolidaysBonus'
    }
  },
  {
    assetsWinterBonus: {
      name: 'Haber bono invierno',
      format: formatter.currency,
      path: 'pension.assets.winterBonus'
    }
  },
  {
    totalAssets: { name: 'Total haberes', format: formatter.currency, path: 'totalAssets' }
  },
  {
    totalOnePercentDiscounts: {
      name: 'Total Descuento 1%',
      format: formatter.currency,
      path: 'totalOnePercentDiscounts'
    }
  },
  {
    totalSocialCreditDiscounts: {
      name: 'Total Descuentos créditos sociales',
      format: formatter.currency,
      path: 'totalSocialCreditDiscounts'
    }
  },
  {
    discountsOthersLosHeroes: {
      name: 'Otros descuentos caja Los Heroes',
      format: formatter.currency,
      path: 'pension.discounts.othersLosHeroes'
    }
  },
  {
    discountsOthersLosAndes: {
      name: 'Otros descuentos caja Los andes',
      format: formatter.currency,
      path: 'pension.discounts.othersLosAndes'
    }
  },
  {
    discountsHealthLoan: {
      name: 'Descuento préstamo de salud',
      format: formatter.currency,
      path: 'pension.discounts.healthLoan'
    }
  },
  {
    discountsHealth: {
      name: 'Descuento salud',
      format: formatter.currency,
      path: 'pension.discounts.health'
    }
  },
  {
    discountsAfp: {
      name: 'Descuento AFP',
      format: formatter.currency,
      path: 'pension.discounts.afp'
    }
  },
  {
    totalDiscounts: { name: 'Total descuentos', format: formatter.currency, path: 'totalDiscounts' }
  },
  {
    paymentInfoPaymentGateway: {
      name: 'Vía de pago',
      format: formatter.normal,
      path: 'pension.paymentInfo.paymentGateway'
    }
  },
  {
    paymentInfoBranchOffice: {
      name: 'Sucursal',
      format: formatter.normal,
      path: 'pension.paymentInfo.branchOffice'
    }
  },
  {
    paymentInfoAccountNumber: {
      name: 'Número de cuenta',
      format: formatter.normal,
      path: 'pension.paymentInfo.accountNumber'
    }
  },
  {
    paymentInfoBank: { name: 'Banco', format: formatter.normal, path: 'pension.paymentInfo.bank' }
  }
];

const simpleFields = pensionFields.map(x => Object.keys(x)[0]);

module.exports = { pensionFields, simpleFields };

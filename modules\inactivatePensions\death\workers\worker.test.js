/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

describe('worker inactivate by death Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  let filesHelper;
  let sftpClient;
  let connectToSFTPServer;
  let pensionService;

  beforeEach(() => {
    done = jest.fn();
    sftpClient = {
      Client: jest.fn()
    };
    filesHelper = {
      readSFTPFile: jest.fn(() => Promise.resolve({ lines: [{}], error: null }))
    };
    service = {
      createUpdateDeathPension: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    pensionService = {
      updateDisableByDeath: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest
        .fn(() => Promise.resolve(false))
        .mockImplementationOnce(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };

    connectToSFTPServer = jest.fn(() => Promise.resolve({ connected: true }));
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      pensionService,
      filesHelper,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.createUpdateDeathPension).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      filesHelper,
      done
    });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.createUpdateDeathPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should return when the dependency mark is missing', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));

    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      filesHelper,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.createUpdateDeathPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('should save cron mark when it gets an empty file', async () => {
    filesHelper.readSFTPFile = jest.fn(() => Promise.resolve({ lines: [] }));

    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      filesHelper,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.createUpdateDeathPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      pensionService,
      filesHelper,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.createUpdateDeathPension).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    service.createUpdateDeathPension = jest.fn(() => Promise.reject(new Error('Error service')));
    await workerModule.workerFn({
      Logger,
      service,
      sftpClient,
      connectToSFTPServer,
      logService,
      pensionService,
      filesHelper,
      done
    });

    expect(logService.existsLog).toBeCalled();
    expect(service.createUpdateDeathPension).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

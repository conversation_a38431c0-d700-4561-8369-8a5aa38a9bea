/* eslint-disable consistent-return */

const cronDependency = 'REACTIVATE_TRANSIENT_PRE_WORKER';
const cronMark = 'INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNEMENT_PROCESS';

const cronDescription = 'inactivate or reactivate family assingment:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';

const successMessage = `El proceso ${cronMark} se completó correctamente`;
const notImportedMessage =
  'No se ha realizado el proceso de importación del archivo de asignación familiar';
const getMissingDependencyMessage = `No se ha ejecutado la dependencia ${cronDependency}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  done,
  logService,
  TemporaryFamilyAssignmentService,
  service,
  job
}) => {
  try {
    Logger.info(
      'Inicio proceso inactivacion/reactivacion: revisando importacion archivo asignacion familiar'
    );

    const { existsLog, message } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message, status: 'UNAUTHORIZED' };
    }
    if (!(await TemporaryFamilyAssignmentService.fileAlreadyImported())) {
      Logger.info(notImportedMessage);
      return { message: notImportedMessage, status: 'NOTFOUND' };
    }
    Logger.info('inactivacion/reactivacion: revisando ejecucion dependencia cron transitorias');
    if (!(await logService.existsLog(cronDependency))) {
      Logger.info('Fin proceso: Dependencia cron transitorias aun no ejecutado.');
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    Logger.info(`Inicio proceso:  inactivación/Reactivación cargas familiares`);
    const { error } = await service.process();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, cronDependency, workerFn };

const calculate = pension => {
  const REASON = /retenci[oó]n judicial/i;
  const { discountsAndAssets = {} } = pension;
  const { discountsNonFormulable = [] } = discountsAndAssets;
  const filterDiscountsNonFormulable = discountsNonFormulable.filter(
    retention => !REASON.test(retention.reason)
  );
  const totalNonFormulable = filterDiscountsNonFormulable.reduce(
    (acc, current) => acc + current.amount,
    0
  );

  return { ...pension, discounts: { ...pension.discounts, totalNonFormulable } };
};

module.exports = calculate;

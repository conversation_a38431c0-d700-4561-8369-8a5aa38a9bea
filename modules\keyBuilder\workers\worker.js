const cronDescription = 'Generando valores llave';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const dependencyMark = 'CRON_BASE_MINIMUN_PENSION_WORKER';
const markOfCron = 'CRON_KEY_BUILDER';
const successMessage = `El proceso ${markOfCron} se completó correctamente.`;

const getMissingDependencyMessage = dep => `La dependencia "${dep}" aún no se ha ejecutado`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ Logger, logService, pensionService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { message: getMissingDependencyMessage(dependencyMark) };
    }
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);

    const { existsLog } = await logService.existsLogAndRetry(markOfCron);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription} process started`);
    const { err, result } = await service.generateKeys(pensionService);
    if (err) throw new Error(err);

    const { error } = await pensionService.createUpdatePension(result);

    if (error) throw new Error(error);
    await logService.saveLog(markOfCron);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(markOfCron);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { markOfCron, dependencyMark, workerFn };

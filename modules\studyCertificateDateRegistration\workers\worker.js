const cronDescription = 'cron para registrar fecha de inicio/fin de los certificados de estudios';
const cronMark = 'REGISTER_START_AND_END_DATE_STUDY_CERTIFICATE';
const importErrorMessage = `Interrupcion ejecución ${cronDescription}. Archivo de cargas familiares aún no importado`;
const successMessage = `Process ${cronMark} completed successfully.`;
const alreadyExecutedMessage = 'This process was already executed for the current month.';
const dependencyMark = '';

const workerFn = async ({ Logger, done, logService, familyAssignmentService, service }) => {
  try {
    Logger.info(`Inicio ejecucion: ${cronDescription}`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    if (!(await familyAssignmentService.fileAlreadyImported())) {
      Logger.info(importErrorMessage);
      throw new Error(importErrorMessage);
    }

    const { error } = await service.registerStartAndEndDateOfStudyCertificate();
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`${cronMark}: process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

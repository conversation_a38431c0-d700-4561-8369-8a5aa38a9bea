<?php

namespace App\Domain\Pension\Services;

use App\Domain\Pension\Models\Pension;
use App\Domain\Pension\Models\DiscountsAndAssets;
use App\Domain\Pension\Services\AssetsCalculationService;
use App\Domain\Pension\Services\DiscountsCalculationService;
use App\Domain\Pension\Services\RetroactiveCalculationService;
use App\Domain\Pension\DTOs\PensionCalculationResult;
use App\Domain\Shared\Services\CacheService;
use App\Domain\Shared\Services\RulesEngineService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PensionCalculationService
{
    public function __construct(
        private AssetsCalculationService $assetsService,
        private DiscountsCalculationService $discountsService,
        private RetroactiveCalculationService $retroactiveService,
        private CacheService $cacheService,
        private RulesEngineService $rulesEngine
    ) {}

    /**
     * Calcula todos los componentes de una pensión
     */
    public function calculatePension(Pension $pension): PensionCalculationResult
    {
        $cacheKey = "pension_calculation_{$pension->id}_{$pension->updated_at->timestamp}";
        
        return $this->cacheService->remember($cacheKey, 3600, function () use ($pension) {
            return DB::transaction(function () use ($pension) {
                Log::info("Calculating pension", ['pension_id' => $pension->id]);
                
                // 1. Calcular pensión base y artículos
                $basePensionResult = $this->calculateBasePension($pension);
                
                // 2. Calcular beneficios (assets)
                $assetsResult = $this->assetsService->calculateAssets($pension);
                
                // 3. Calcular descuentos
                $discountsResult = $this->discountsService->calculateDiscounts($pension);
                
                // 4. Calcular montos retroactivos
                $retroactiveResult = $this->retroactiveService->calculateRetroactive($pension);
                
                // 5. Calcular pensión neta
                $netPension = $this->calculateNetPension(
                    $basePensionResult,
                    $assetsResult,
                    $discountsResult,
                    $retroactiveResult
                );
                
                return new PensionCalculationResult(
                    basePension: $basePensionResult,
                    assets: $assetsResult,
                    discounts: $discountsResult,
                    retroactive: $retroactiveResult,
                    netPension: $netPension,
                    calculatedAt: now()
                );
            });
        });
    }

    /**
     * Calcula la pensión base aplicando reglas IPC y mínimos
     */
    private function calculateBasePension(Pension $pension): array
    {
        $rules = $this->rulesEngine->getBasePensionRules();
        
        $result = [
            'base_pension' => $pension->base_pension,
            'article_40' => $pension->article_40,
            'article_41' => $pension->article_41,
            'law_19403' => $pension->law_19403,
            'law_19539' => $pension->law_19539,
            'law_19953' => $pension->law_19953,
        ];

        // Aplicar reglas de reajuste IPC
        foreach ($rules as $rule) {
            if ($rule->appliesTo($pension)) {
                $result = $rule->apply($result, $pension);
            }
        }

        // Aplicar pensión mínima si corresponde
        if ($this->shouldApplyMinimumPension($pension)) {
            $minimumPension = $this->rulesEngine->getMinimumPension($pension->pension_type);
            $totalCalculated = array_sum($result);
            
            if ($totalCalculated < $minimumPension) {
                $result['minimum_pension_adjustment'] = $minimumPension - $totalCalculated;
            }
        }

        return $result;
    }

    /**
     * Calcula la pensión neta final
     */
    private function calculateNetPension(
        array $basePension,
        array $assets,
        array $discounts,
        array $retroactive
    ): float {
        $totalBasePension = array_sum($basePension);
        $totalAssets = array_sum($assets);
        $totalDiscounts = array_sum($discounts);
        $totalRetroactive = array_sum($retroactive);

        $netPension = $totalBasePension + $totalAssets - $totalDiscounts + $totalRetroactive;

        return max(0, round($netPension, 2));
    }

    /**
     * Procesa un lote de pensiones de forma optimizada
     */
    public function calculatePensionBatch(array $pensionIds): array
    {
        $pensions = Pension::with(['discountsAndAssets'])
            ->whereIn('id', $pensionIds)
            ->get();

        $results = [];
        
        foreach ($pensions as $pension) {
            try {
                $results[$pension->id] = $this->calculatePension($pension);
            } catch (\Exception $e) {
                Log::error("Error calculating pension", [
                    'pension_id' => $pension->id,
                    'error' => $e->getMessage()
                ]);
                
                $results[$pension->id] = null;
            }
        }

        return $results;
    }

    /**
     * Verifica si debe aplicar pensión mínima
     */
    private function shouldApplyMinimumPension(Pension $pension): bool
    {
        return in_array($pension->pension_type, [
            'INVALIDEZ_TOTAL',
            'INVALIDEZ_PARCIAL',
            'SUPERVIVENCIA',
            'ORFANDAD'
        ]);
    }

    /**
     * Recalcula todas las pensiones que requieren actualización
     */
    public function recalculateOutdatedPensions(): int
    {
        $outdatedPensions = Pension::enabled()
            ->where('updated_at', '<', now()->subDays(30))
            ->pluck('id')
            ->chunk(500);

        $processed = 0;

        foreach ($outdatedPensions as $chunk) {
            $this->calculatePensionBatch($chunk->toArray());
            $processed += $chunk->count();
        }

        return $processed;
    }
}

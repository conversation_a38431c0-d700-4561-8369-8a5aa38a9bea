const PensionModel = require('../../../models/pension');
const MonthlyExpensesModel = require('../models/monthlyExpenses');

const months = {
  0: 'Enero',
  1: 'Febrero',
  2: '<PERSON><PERSON>',
  3: 'Abril',
  4: '<PERSON>',
  5: '<PERSON><PERSON>',
  6: '<PERSON>',
  7: 'Agosto',
  8: 'Septiembre',
  9: 'Octubre',
  10: 'Noviem<PERSON>',
  11: 'Diciembre'
};
const MONTHS_AGO = 6;

const NOT_VALID = /no vigente/i;

const professionalDiseasesList = [/Pensi[óo]n por enfermedad profesional/i];

const accidentList = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i
];

const survivalList = [
  /Pensi[óo]n de viudez con hijos/i,
  /Pensi[óo]n de viudez sin hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i,
  /Pensi[óo]n por orfandad/i,
  /Pensi[óo]n de orfandad de padre y madre/i
];

const searchPensionWithLiquidation = () => {
  return PensionModel.aggregate([
    {
      $match: {
        enabled: true,
        validityType: { $not: NOT_VALID },
        pensionType: {
          $in: [...professionalDiseasesList, ...accidentList, ...survivalList]
        }
      }
    },
    {
      $project: {
        beneficiary: 1,
        causant: 1,
        pensionType: 1,
        assets: 1
      }
    },
    {
      $lookup: {
        from: 'liquidations',
        let: {
          beneficiaryRut: '$beneficiary.rut',
          causantRut: '$causant.rut'
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  {
                    $eq: ['$beneficiaryRut', '$$beneficiaryRut']
                  },
                  {
                    $eq: ['$causantRut', '$$causantRut']
                  },
                  {
                    $eq: ['$enabled', true]
                  }
                ]
              }
            }
          }
        ],
        as: 'liquidations'
      }
    }
  ]);
};

const sumExpenses = array => {
  return array.reduce((prev, cur) => {
    const { liquidations = [] } = cur;
    const taxablePension = liquidations.length ? liquidations[0].taxablePension : 0;
    const { aps = 0 } = cur.assets;
    const totalExpense = Math.round(taxablePension - aps);
    return prev + totalExpense;
  }, 0);
};

const fillMissingData = ({ relativeToPresent, lastMonthStats, currentYear }) => {
  const relativeDate = new Date(currentYear, relativeToPresent, 1);
  const relativeMonth = relativeDate.getMonth();
  const record = lastMonthStats.find(({ month }) => month === months[relativeMonth]);
  if (record) return record;
  return {
    spentProfessionalDisease: 0,
    spentAccident: 0,
    spentSurvival: 0,
    totalSpent: 0,
    month: months[relativeMonth],
    date: relativeDate,
    validPensioners: 0,
    year: relativeDate.getFullYear()
  };
};

const service = {
  async calculateMonthlyExpense() {
    try {
      const pensionsWithLiquidation = (await searchPensionWithLiquidation()) || [];
      const validPensioners = pensionsWithLiquidation.length;
      const spentProfessionalDisease = sumExpenses(
        pensionsWithLiquidation.filter(({ pensionType }) =>
          professionalDiseasesList.some(rx => rx.test(pensionType))
        )
      );
      const spentAccident = sumExpenses(
        pensionsWithLiquidation.filter(({ pensionType }) =>
          accidentList.some(rx => rx.test(pensionType))
        )
      );
      const spentSurvival = sumExpenses(
        pensionsWithLiquidation.filter(({ pensionType }) =>
          survivalList.some(rx => rx.test(pensionType))
        )
      );
      const totalSpent = Math.round(spentProfessionalDisease + spentAccident + spentSurvival);

      const date = new Date();

      await MonthlyExpensesModel.findOneAndUpdate(
        {
          month: months[date.getMonth()],
          year: date.getFullYear()
        },
        {
          spentProfessionalDisease,
          spentAccident,
          spentSurvival,
          totalSpent,
          month: months[date.getMonth()],
          date,
          validPensioners,
          year: date.getFullYear(),
          enabled: true,
          updatedAt: new Date()
        },
        {
          upsert: true
        }
      ).exec();

      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  },
  async getStats() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    try {
      const lastMonthStats = await MonthlyExpensesModel.find({
        date: {
          $gte: new Date(currentYear, currentMonth - (MONTHS_AGO - 1), 1),
          $lt: new Date(currentYear, currentMonth + 1, 1)
        }
      })
        .sort({ date: 1 })
        .exec();

      const monthsToEvaluate = Array.from(
        { length: MONTHS_AGO },
        (v, i) => currentMonth - i
      ).reverse();

      const result = monthsToEvaluate.map(relativeToPresent =>
        fillMissingData({ relativeToPresent, lastMonthStats, currentYear })
      );

      return {
        error: false,
        result
      };
    } catch (error) {
      return { error, result: null };
    }
  }
};

module.exports = { ...service };

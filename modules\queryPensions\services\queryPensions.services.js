/* eslint-disable no-misleading-character-class */
/* eslint-disable no-unreachable */
/* eslint-disable no-param-reassign */
const moment = require('moment');
const PensionModel = require('../../../models/pension');
const TemporaryFamilyAssignment = require('../../familyAssignment/models/temporaryFamilyAssignment');
const PaymentDateModel = require('../../paymentDate/models/paymentDate');
const UpdatePensionerInfo = require('../models/UpdatePensionerInfo');
const UpdatePensionType = require('../models/UpdatePensionType');
const liquidationHistoricModel = require('../../../models/liquidationHistoric');
const flattenPensionerFields = require('../formatters/pensionerFormatter');
const {
  findOneAndUpdateWithTracking
} = require('../../trackingUserActivity/services/trackingUserActivity.service');
const { removeAccents, cleanDots } = require('../../sharedFiles/helpers');
const {
  setEndDateOfValidityByGender
} = require('../../linkPensions/services/modifiers/retirement');
const ModelRol = require('../../systems/rolesAndPermissions/models/roles.model');
const ModelUser = require('../../../models/user');
const serviceMail = require('../../sendMail/service/sendMail.service');
const { getMonth } = require('../../sharedFiles/services/helpers.service');

const NOT_VALID_PENSION = /no vigente/i;
const NOT_VALID_FAMILY_ASSIGNMENT = [/Interna inactiva/i, /Inactiva/i];

const AFP_CERTIFICATE_FILE_NAME = /certificado[ _]de[ _]afiliaci[oóó]n[ _]afp.pdf/i;
const NOUROLOGIC_CERTIFICATE_FILE_NAME = /certificado[ _]neurol[oóó]gico.pdf/i;
const NOTARIAL_FILE_NAME = /poder[ _]notarial[ _]para cambio[ _]de[ _]cobrante.pdf/i;
const COURTS_OPINION_FILE_NAME = /Dictamen[ _]de[ _]tribunales.pdf/i;
const TYPE_ROL = [/Jefe de PEC/i];

const NOREPLY_FROM_EMAIL = '<EMAIL>';
const { TEMPLATE_ID_CRON_NOTIFICATION } = process.env;

const moneyFormatter = new Intl.NumberFormat('es-CL', {
  style: 'currency',
  currency: 'CLP',
  maximumFractionDigits: 2
});

const infoMessage = infoBasePension => {
  const { rutBeneficiary, pensionCodeId, basePension, newBasePension, email } = infoBasePension;
  const modificationDate = moment().format('DD/MM/YYYY');
  const modificationTime = moment
    .utc()
    .subtract(4, 'hours')
    .format('HH:mm:ss');

  return {
    name: `Modificación pensión base N° ${pensionCodeId}`,
    month: getMonth(moment().month()),
    year: moment().year(),
    status: 'OK',
    title: `Se notifica la modificación de la pensión N° ${pensionCodeId}, cuyo beneficiario corresponde al RUT: ${rutBeneficiary}, cuyos montos se modifican en los siguientes términos: Se sustituye el monto de pensión base de ${moneyFormatter.format(
      basePension
    )} al valor de ${moneyFormatter.format(
      newBasePension
    )}. Se deja constancia que la modificación fue realizada por ${email} desde el perfil del pensionado el ${modificationDate} a las ${modificationTime}`,
    subject: `Modificación pensión base N° ${pensionCodeId}`
  };
};

const buildMail = (listEmail, message) => {
  return {
    to: listEmail,
    from: NOREPLY_FROM_EMAIL,
    cc: '',
    templateId: TEMPLATE_ID_CRON_NOTIFICATION,
    fromname: 'ACHS-PEC',
    dynamic_template_data: {
      ...message
    }
  };
};

const mapToDot = {
  beneficiary: obj => ({
    'beneficiary.rut': { $regex: new RegExp(obj.beneficiary.rut, 'i') },
    enabled: true
  }),
  causant: obj => ({ 'causant.rut': { $regex: new RegExp(obj.causant.rut, 'i') }, enabled: true }),
  collector: obj => ({
    'collector.rut': { $regex: new RegExp(obj.collector.rut, 'i') },
    enabled: true
  }),
  pensionCodeId: obj => ({
    pensionCodeId: { $regex: new RegExp(obj.pensionCodeId, 'i') },
    enabled: true
  })
};

const getKeys = obj => Object.keys(obj);

const filterKeys = (keysInQuery, availableKeys) =>
  availableKeys.filter(key => keysInQuery.includes(key));

const generateObject = (field, fieldName) => {
  if (field !== undefined && field !== null) return { [fieldName]: field };
  return {};
};

const definePensionerFileName = (typeFile = '') => {
  const FILES_NAMES = [
    {
      regex: AFP_CERTIFICATE_FILE_NAME,
      value: process.env.PENSIONER_AFP_CERTIFICATE_TARGET_FILE_NAME
    },
    {
      regex: NOUROLOGIC_CERTIFICATE_FILE_NAME,
      value: process.env.PENSIONER_NOUROLOGIC_CERTIFICATE_TARGET_FILE_NAME
    },
    { regex: NOTARIAL_FILE_NAME, value: process.env.PENSIONER_NOTARIAL_TARGET_FILE_NAME },
    {
      regex: COURTS_OPINION_FILE_NAME,
      value: process.env.PENSIONER_COURTS_OPINION_TARGET_FILE_NAME
    }
  ];
  const { value = '' } = FILES_NAMES.find(({ regex }) => regex.test(typeFile)) || {};
  return value || typeFile;
};

const resolveStorageTargetProps = (sourceFileName = '') => {
  const [beneficiaryRut, causantRut, pensionCodeId, ...rest] = sourceFileName.split(' ');
  const [beneficiaryRutNumber] = cleanDots(beneficiaryRut).split('-');
  const [causantRutNumber] = cleanDots(causantRut).split('-');
  const fileName = rest.join(' ');
  const targetName = removeAccents(fileName);

  return { beneficiaryRutNumber, causantRutNumber, pensionCodeId, targetName };
};

const buildBeneficiaryObject = (beneficiary, newPensionerInfo = {}) => {
  return {
    beneficiary: {
      ...beneficiary,
      ...generateObject(newPensionerInfo.beneficiaryEmail, 'email'),
      ...generateObject(newPensionerInfo.beneficiaryPhone, 'phone')
    }
  };
};

const buildCollectorObject = (collector, newPensionerInfo = {}) => {
  return {
    collector: {
      ...collector,
      ...generateObject(newPensionerInfo.collectorRut, 'rut'),
      ...generateObject(newPensionerInfo.collectorName, 'name'),
      ...generateObject(newPensionerInfo.collectorLastName, 'lastName'),
      ...generateObject(newPensionerInfo.collectorMothersLastName, 'mothersLastName'),
      ...generateObject(newPensionerInfo.collectorAddress, 'address'),
      ...generateObject(newPensionerInfo.collectorCommune, 'commune'),
      ...generateObject(newPensionerInfo.collectorCity, 'city')
    }
  };
};

const buildPaymentInfoObject = (paymentInfo, newPensionerInfo = {}) => {
  return {
    paymentInfo: {
      ...paymentInfo,
      ...generateObject(newPensionerInfo.paymentGateway, 'paymentGateway'),
      ...generateObject(newPensionerInfo.bank, 'bank'),
      ...generateObject(newPensionerInfo.branchOffice, 'branchOffice'),
      ...generateObject(newPensionerInfo.accountNumber, 'accountNumber'),
      ...generateObject(newPensionerInfo.bankRejected, 'bankRejected'),
      ...generateObject(newPensionerInfo.paycheckRefunded, 'paycheckRefunded')
    }
  };
};

const getOriginalPensionerRut = async (beneficiaryRut, causantRut, pensionCode) => {
  const beneficiaryRegex = new RegExp(beneficiaryRut, 'i');
  const causantRegex = new RegExp(causantRut, 'i');
  const { beneficiary = {}, causant = {}, pensionCodeId = 0, basePension = 0 } =
    (await PensionModel.findOne({
      'beneficiary.rut': { $regex: beneficiaryRegex },
      'causant.rut': { $regex: causantRegex },
      pensionCodeId: pensionCode,
      enabled: { $eq: true }
    })
      .lean()
      .exec()
      .catch(() => {
        return {
          beneficiary: { rut: beneficiaryRut },
          causant: { rut: causantRut },
          pensionCodeId,
          basePension
        };
      })) || {};
  return {
    originalBeneficairyRut: beneficiary.rut || beneficiaryRut,
    originalCausantRut: causant.rut || causantRut,
    pensionCodeId: pensionCodeId || 0,
    basePension: basePension || 0
  };
};

const service = {
  async getRolesUsers() {
    try {
      return ModelRol.aggregate([
        {
          $addFields: { rolId: { $toString: '$_id' } }
        },
        {
          $match: { roleName: { $in: TYPE_ROL } }
        }
      ]);
    } catch (error) {
      return [];
    }
  },

  async filteUserByRole(ids) {
    try {
      return ModelUser.aggregate([
        {
          $addFields: { userRolId: { $toString: '$role' } }
        },
        {
          $match: {
            userRolId: { $in: ids }
          }
        }
      ]);
    } catch (error) {
      return [];
    }
  },

  async getListDestinations() {
    const listRoles = await this.getRolesUsers();
    const idsRoles = listRoles.map(item => item.rolId);
    const listUser = await this.filteUserByRole(idsRoles);

    return listUser.map(user => user.email);
  },

  async sendNotificationEmail(infoBasePension) {
    try {
      const message = infoMessage(infoBasePension);
      const listSenders = await this.getListDestinations();
      const emailList = listSenders.toString();

      const msg = buildMail(emailList, message);
      const { error } = await serviceMail.sendEmail(msg);
      if (error) throw new Error(error);

      return { completed: true, error: null };
    } catch (error) {
      return { completed: false, error };
    }
  },

  async queryPensions(query) {
    try {
      const keysInQuery = getKeys(query);
      const keysInMap = getKeys(mapToDot);
      const params = filterKeys(keysInQuery, keysInMap);

      if (params.length > 1) throw new Error('More than one parameter supplied');

      const queryDotNotation = mapToDot[params[0]](query);

      const pensions = await PensionModel.find(queryDotNotation)
        .select(
          'beneficiary causant.rut collector.rut pensionCodeId pensionType validityType endDateOfValidity dateOfBirth'
        )
        .exec();

      return { error: null, result: pensions };
    } catch (error) {
      return { error, result: [] };
    }
  },
  async findExtendedPensioner({ rutBeneficiary, rutCausant, pensionCodeId }) {
    try {
      const beneficiaryRegex = new RegExp(rutBeneficiary, 'i');
      const causantRegex = new RegExp(rutCausant, 'i');
      const [pensionLiquidationJoin = { liquidation: [] }] = await PensionModel.aggregate([
        {
          $match: {
            'beneficiary.rut': { $regex: beneficiaryRegex },
            'causant.rut': { $regex: causantRegex },
            enabled: { $eq: true },
            pensionCodeId
          }
        },
        {
          $lookup: {
            from: 'discountsandassets',
            let: {
              beneficiaryD: '$beneficiary.rut',
              causantD: '$causant.rut',
              pensionCodeD: '$pensionCodeId'
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$beneficiaryRut', '$$beneficiaryD']
                      },
                      {
                        $eq: ['$causantRut', '$$causantD']
                      },
                      {
                        $eq: ['$pensionCodeId', '$$pensionCodeD']
                      }
                    ]
                  }
                }
              }
            ],
            as: 'discountsAndAssets'
          }
        },
        {
          $unwind: { path: '$discountsAndAssets' }
        },
        {
          $lookup: {
            from: 'liquidations',
            let: {
              beneficiaryL: '$beneficiary.rut',
              causantL: '$causant.rut',
              pensionCodeL: '$pensionCodeId'
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$beneficiaryRut', '$$beneficiaryL']
                      },
                      {
                        $eq: ['$causantRut', '$$causantL']
                      },
                      {
                        $eq: ['$pensionCodeId', '$$pensionCodeL']
                      }
                    ]
                  }
                }
              }
            ],
            as: 'liquidation'
          }
        }
      ]).exec();

      const [properLiquidation = {}] = pensionLiquidationJoin.liquidation.filter(
        ({ causantRut, enabled }) => causantRut === rutCausant && enabled
      );
      const { discountsAndAssets = {}, gender } = pensionLiquidationJoin;
      const pensionLiquidation = { ...pensionLiquidationJoin, liquidation: properLiquidation };

      return {
        isError: false,
        result: { ...flattenPensionerFields(pensionLiquidation), gender, discountsAndAssets },
        error: ''
      };
    } catch (error) {
      return {
        result: {},
        error
      };
    }
  },
  async findPensionerTemporalTable({ rutBeneficiary, rutCausant, pensionCodeId }) {
    try {
      const beneficiaryRegex = new RegExp(rutBeneficiary, 'i');
      const causantRegex = new RegExp(rutCausant, 'i');
      const temporalInfo =
        (await UpdatePensionerInfo.findOne({
          beneficiaryRut: { $regex: beneficiaryRegex },
          causantRut: { $regex: causantRegex },
          pensionCodeId
        })
          .lean()
          .exec()) || {};

      return {
        result: temporalInfo
      };
    } catch (error) {
      return {
        result: {},
        error
      };
    }
  },
  async getNextPaymentDate(data) {
    try {
      const january = '01';
      const december = '12';
      const currentMonth = `${moment().month() + 1}`;
      const currentYear = moment().year();
      const year = currentMonth === december ? currentYear + 1 : currentYear;
      const month =
        currentMonth !== december
          ? `0${currentMonth + 1}`.replace(/^(\d?)(\d{2})$/, '$2')
          : january;

      const [nextPaymentDate] = await PaymentDateModel.aggregate([
        {
          $match: {
            year: { $eq: year },
            month: { $eq: month }
          }
        }
      ]);

      return {
        errorNextPaymentDate: '',
        data: { ...data, nextPaymentDate: nextPaymentDate.paymentDate }
      };
    } catch (errorNextPaymentDate) {
      return {
        errorNextPaymentDate,
        data
      };
    }
  },
  async uploadToFTP({ fileName, file, client, fs, connectToFTPServer, ftpCredentials }) {
    const clientFtp = client;

    try {
      const { connected } = await connectToFTPServer(clientFtp, { ...ftpCredentials });
      if (!connected) throw new Error('Could not connect to FTP');

      await fs.writeFile(`${__dirname}/${fileName}`, file);

      clientFtp.ftp.verbose = true;

      await clientFtp.uploadFrom(
        `${__dirname}/${fileName}`,
        `${process.env.PENSIONER_DOCUMENTS_FTP_INPUT_FOLDER}/${fileName}`
      );

      await fs.unlink(`${__dirname}/${fileName}`);
      return { ftpError: false };
    } catch (ftpError) {
      return { ftpError };
    } finally {
      clientFtp.close();
    }
  },
  async checkPensionerDocument({
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    nameAndRegex,
    storageService
  }) {
    try {
      const [beneficiaryRutNumber] = cleanDots(beneficiaryRut).split('-');
      const [causantRutNumber] = cleanDots(causantRut).split('-');
      const folderPrefix = `${beneficiaryRutNumber}/${causantRutNumber}/${pensionCodeId}`;

      const { result: listResult } = await storageService.findFilesByPrefix(folderPrefix);

      if (!listResult.length) return { storageError: false, fileExistenceDetails: nameAndRegex };

      const doesFileExist = (regex = null) =>
        !!listResult.find(fileRegistry => {
          const { virtualPath } = fileRegistry;
          return virtualPath.match(new RegExp(regex));
        });

      const fileExistenceDetails = Object.keys(nameAndRegex).reduce(
        (obj, key) => {
          obj[key] = { ...obj[key], existence: doesFileExist(nameAndRegex[key].regex) };
          return obj;
        },
        { ...nameAndRegex }
      );

      return { storageError: false, fileExistenceDetails };
    } catch (storageError) {
      return { storageError, fileExistenceDetails: nameAndRegex };
    }
  },
  async getPensionerDocument({ filenameRegex, client, connectToFTPServer, ftpCredentials }) {
    const clientFtp = client;
    try {
      const { connected } = await connectToFTPServer(clientFtp, { ...ftpCredentials });
      if (!connected) throw new Error('Could not connect to FTP');

      const regexFile = new RegExp(`^${filenameRegex}$`, 'i');

      const listOfFiles = await clientFtp.list(
        `${process.env.PENSIONER_DOCUMENTS_FTP_INPUT_FOLDER}`
      );

      const { name } =
        listOfFiles.filter(file => file.isFile).find(file => regexFile.test(file.name)) || {};

      if (!name) throw new Error('file does not exist in ftp');

      await clientFtp.downloadTo(
        `${__dirname}/${name}`,
        `${process.env.PENSIONER_DOCUMENTS_FTP_INPUT_FOLDER}/${name}`
      );

      return { ftpError: false, filename: name };
    } catch (ftpError) {
      return { ftpError };
    } finally {
      clientFtp.close();
    }
  },
  async updateToTemporalTable({ beneficiaryRut, causantRut, pensionCodeId, pensionerInfo, user }) {
    try {
      const {
        originalBeneficairyRut,
        originalCausantRut,
        pensionCodeId: pensionCode,
        basePension
      } = await getOriginalPensionerRut(beneficiaryRut, causantRut, pensionCodeId);

      const infoBasePension = {
        rutBeneficiary: originalBeneficairyRut,
        pensionCodeId: pensionCode,
        basePension,
        newBasePension: pensionerInfo.basePension,
        email: user.email
      };
      if (pensionerInfo.basePension && pensionerInfo.basePension !== basePension) {
        const { completed: successful } = await this.sendNotificationEmail(infoBasePension);
        if (!successful) {
          return { error: 'Error al informar modificación a Jefe de PEC', completed: false };
        }
      }

      let extraModifiedFields = {};
      const { gender, validityType } = pensionerInfo;
      if (gender && !validityType) {
        const beneficiaryRegex = new RegExp(beneficiaryRut, 'i');
        const causantRegex = new RegExp(causantRut, 'i');
        const { dateOfBirth, endDateOfValidity } =
          (await PensionModel.findOne(
            {
              'beneficiary.rut': { $regex: beneficiaryRegex },
              'causant.rut': { $regex: causantRegex },
              pensionCodeId,
              enabled: { $eq: true }
            },
            { dateOfBirth: 1, endDateOfValidity: 1 }
          )
            .lean()
            .exec()) || {};

        if (dateOfBirth) {
          extraModifiedFields = setEndDateOfValidityByGender(
            { previousEndDateOfValidity: endDateOfValidity },
            dateOfBirth,
            gender
          );
        }
      }

      await findOneAndUpdateWithTracking({
        model: UpdatePensionerInfo,
        user,
        fullQuery: [
          { beneficiaryRut: originalBeneficairyRut, causantRut: originalCausantRut, pensionCodeId },
          { ...pensionerInfo, ...extraModifiedFields },
          { upsert: true }
        ]
      });

      return { error: false, completed: true };
    } catch (error) {
      return { error, completed: false };
    }
  },
  async updatePension({ parameterUpdate }) {
    try {
      const { beneficiaryRut, causantRut, pensionCodeId, pensionerInfo, user } = parameterUpdate;
      let updatedPension;
      const beneficiaryRegex = new RegExp(beneficiaryRut, 'i');
      const causantRegex = new RegExp(causantRut, 'i');
      const { _id, ...OldPensionerInfo } =
        (await PensionModel.findOne({
          'beneficiary.rut': { $regex: beneficiaryRegex },
          'causant.rut': { $regex: causantRegex },
          pensionCodeId,
          enabled: { $eq: true }
        })
          .lean()
          .exec()) || {};

      const infoBasePension = {
        rutBeneficiary: beneficiaryRut,
        pensionCodeId: OldPensionerInfo.pensionCodeId,
        basePension: OldPensionerInfo.basePension,
        newBasePension: pensionerInfo.basePension,
        email: user.email
      };
      if (pensionerInfo.basePension && pensionerInfo.basePension !== OldPensionerInfo.basePension) {
        const { completed: successful } = await this.sendNotificationEmail(infoBasePension);
        if (!successful) {
          return { error: 'Error al informar modificación a Jefe de PEC', completed: false };
        }
      }

      if (_id) {
        const {
          beneficiary,
          collector,
          paymentInfo,
          dateOfBirth: oldDateOfBirth,
          gender: oldGender,
          validityType: oldValidityType,
          endDateOfValidity: oldEndDateOfValidity,
          endDateOfTheoricalValidity: oldEndDateOfTheoricalValidity,
          previousEndDateOfValidity: oldPreviousEndDateOfValidity,
          ...otherOldData
        } = OldPensionerInfo;

        const {
          beneficiaryEmail,
          beneficiaryPhone,
          collectorRut,
          collectorName,
          collectorLastName,
          collectorMothersLastName,
          collectorAddress,
          collectorCommune,
          collectorCity,
          paymentGateway,
          bank,
          branchOffice,
          accountNumber,
          dateOfBirth,
          gender,
          validityType,
          endDateOfValidity,
          endDateOfTheoricalValidity,
          ...modifiedNonNestedFields
        } = pensionerInfo;

        let extraModifiedFields = {
          gender: gender || oldGender,
          validityType: validityType || oldValidityType,
          dateOfBirth: dateOfBirth || oldDateOfBirth,
          endDateOfValidity: endDateOfValidity || oldEndDateOfValidity,
          endDateOfTheoricalValidity: endDateOfTheoricalValidity || oldEndDateOfTheoricalValidity,
          previousEndDateOfValidity: endDateOfValidity
            ? oldEndDateOfValidity
            : oldPreviousEndDateOfValidity
        };
        if (gender && extraModifiedFields.dateOfBirth && !validityType) {
          extraModifiedFields = setEndDateOfValidityByGender(
            { ...extraModifiedFields, previousEndDateOfValidity: oldEndDateOfValidity },
            extraModifiedFields.dateOfBirth,
            gender
          );
        }

        updatedPension = await findOneAndUpdateWithTracking({
          model: PensionModel,
          user,
          fullQuery: [
            { _id },
            {
              $set: {
                ...buildBeneficiaryObject(beneficiary, pensionerInfo),
                ...buildCollectorObject(collector, pensionerInfo),
                ...buildPaymentInfoObject(paymentInfo, pensionerInfo),
                ...otherOldData,
                ...modifiedNonNestedFields,
                ...extraModifiedFields
              }
            }
          ]
        });
      }

      return { error: false, completed: !!updatedPension };
    } catch (error) {
      return { error, completed: false };
    }
  },
  async updateTemporalPensionType({
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    pensionType,
    ChangeOfPensionTypeDueToCharges,
    user
  }) {
    try {
      const { originalBeneficairyRut, originalCausantRut } = await getOriginalPensionerRut(
        beneficiaryRut,
        causantRut,
        pensionCodeId
      );

      await findOneAndUpdateWithTracking({
        model: UpdatePensionType,
        user,
        fullQuery: [
          { beneficiaryRut: originalBeneficairyRut, causantRut: originalCausantRut, pensionCodeId },
          { $set: { pensionType, ChangeOfPensionTypeDueToCharges } },
          { upsert: true }
        ]
      });

      return { error: false, completed: true };
    } catch (error) {
      return { error, completed: false };
    }
  },
  async findTemporalPensionType({ rutBeneficiary, rutCausant, pensionCodeId }) {
    try {
      const beneficiaryRegex = new RegExp(rutBeneficiary, 'i');
      const causantRegex = new RegExp(rutCausant, 'i');
      const temporalPensionType =
        (await UpdatePensionType.findOne({
          beneficiaryRut: { $regex: beneficiaryRegex },
          causantRut: { $regex: causantRegex },
          pensionCodeId
        })
          .lean()
          .exec()) || {};

      return {
        result: temporalPensionType
      };
    } catch (error) {
      return {
        result: {},
        error
      };
    }
  },
  async getHistoricalSettlementsByDate({
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    lowerDate,
    upperDate,
    Model = liquidationHistoricModel
  }) {
    try {
      const beginningLowerDate = moment(lowerDate)
        .startOf('month')
        .toDate();
      const endingUpperDate = moment(upperDate)
        .endOf('month')
        .toDate();

      const historicalSettlements = await Model.aggregate([
        {
          $match: {
            beneficiaryRut,
            causantRut,
            pensionCodeId,
            updatedAt: {
              $gte: beginningLowerDate,
              $lte: endingUpperDate
            }
          }
        },
        {
          $group: {
            _id: { month: { $month: '$updatedAt' }, year: { $year: '$updatedAt' } },
            date: { $last: '$updatedAt' },
            data: { $last: '$$ROOT' }
          }
        },
        { $replaceRoot: { newRoot: '$data' } },
        { $sort: { updatedAt: 1 } }
      ]).exec();
      return { historicalSettlements };
    } catch (error) {
      return { error };
    }
  },
  getInfoForPensionCertificate: async ({
    beneficiaryRut,
    causantRut,
    pensionCodeId,
    pensionModel = PensionModel,
    temporaryFamilyAssignment = TemporaryFamilyAssignment
  }) => {
    try {
      const beneficiaryRutRegex = new RegExp(beneficiaryRut, 'i');
      const causantRutRegex = new RegExp(causantRut, 'i');

      const startOfMonth = moment()
        .startOf('month')
        .toDate();
      const endOfMonth = moment()
        .endOf('month')
        .toDate();

      const [pensionLiquidationJoin = { liquidation: [] }] = await pensionModel
        .aggregate([
          {
            $match: {
              'beneficiary.rut': beneficiaryRutRegex,
              'causant.rut': causantRutRegex,
              pensionCodeId,
              enabled: true
            }
          },
          {
            $lookup: {
              from: 'liquidations',
              let: {
                pensionBeneficiaryRut: '$beneficiary.rut',
                pensionCausantRut: '$causant.rut',
                pensionCodeIdP: '$pensionCodeId'
              },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        {
                          $eq: ['$beneficiaryRut', '$$pensionBeneficiaryRut']
                        },
                        {
                          $eq: ['$causantRut', '$$pensionCausantRut']
                        },
                        {
                          $eq: ['$pensionCodeId', '$$pensionCodeIdP']
                        }
                      ]
                    }
                  }
                }
              ],
              as: 'liquidation'
            }
          }
        ])
        .exec();

      const { liquidation: ArrOfLiquidations } = pensionLiquidationJoin;
      const liquidation = ArrOfLiquidations.find(
        liquidationItem =>
          liquidationItem.enabled && causantRutRegex.test(liquidationItem.causantRut)
      );

      const { numberOfCharges, familyGroup: pensionerFamilyGroup } = pensionLiquidationJoin;
      if (numberOfCharges === 0) {
        return { result: { ...pensionLiquidationJoin, liquidation } };
      }

      let chargesOfPensioner;

      if (beneficiaryRutRegex.test(causantRut)) {
        chargesOfPensioner = await temporaryFamilyAssignment.aggregate([
          {
            $match: {
              causantId: causantRutRegex,
              chargeValidityType: { $nin: NOT_VALID_FAMILY_ASSIGNMENT },
              createdAt: {
                $gte: startOfMonth,
                $lte: endOfMonth
              }
            }
          }
        ]);
        return { result: { ...pensionLiquidationJoin, chargesOfPensioner, liquidation } };
      }

      chargesOfPensioner = await pensionModel.aggregate([
        {
          $match: {
            'beneficiary.rut': { $not: beneficiaryRutRegex },
            'causant.rut': causantRutRegex,
            validityType: { $not: NOT_VALID_PENSION },
            familyGroup: pensionerFamilyGroup,
            enabled: true
          }
        },
        {
          $project: {
            beneficiary: 1,
            causant: 1,
            dateOfBirth: 1,
            familyGroup: 1,
            endDateOfValidity: 1
          }
        }
      ]);

      return {
        result: {
          ...pensionLiquidationJoin,
          chargesOfPensioner,
          liquidation
        }
      };
    } catch (error) {
      return { error };
    }
  }
};
module.exports = {
  ...service,
  generateObject,
  buildBeneficiaryObject,
  buildCollectorObject,
  buildPaymentInfoObject,
  resolveStorageTargetProps,
  definePensionerFileName,
  infoMessage
};

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./pre.worker');

const transients = require('../../../../resources/inactivatePension.json');
const pensionsToInactivate = require('../../../../resources/pensionsToInactivate.json');
const getDataSap = require('../../../../resources/sapRequest.json');
const transientHelper = require('../services/transientsHelper');

describe('Worker inactivate transient Test', () => {
  beforeAll(beforeAllTests);
  let linkService;
  let service;
  let axios;
  let logService;
  let Logger;
  let done;
  let transientService;
  let pensionService;
  beforeEach(() => {
    done = jest.fn();
    Logger = {
      info: jest.fn(),
      error: jest.fn()
    };
    linkService = {
      getAllAndFilter: jest.fn(() => Promise.resolve({ result: pensionsToInactivate }))
    };
    axios = jest.fn(() => Promise.resolve({ data: getDataSap[0] }));
    service = {
      createTransientMarkPension: jest.fn(() =>
        Promise.resolve({ completed: true, hasError: false, inactivationError: '' })
      ),
      getAllPensionsToInactivate: jest.fn(() => Promise.resolve([])),

      getAllAndFilter: jest.fn(() => Promise.resolve({ result: transients }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    transientService = {
      getAllPensionsToInactivate: jest.fn(() =>
        Promise.resolve({ pensionsToInactivate: '', pensionsToEvaluate: '' })
      ),
      createTransientMarkPension: jest.fn(() =>
        Promise.resolve({ completed: true, inactivationError: false })
      )
    };
    pensionService = {
      createUpdatePension: jest.fn(() => Promise.resolve({}))
    };
  });
  it('worker inactivate by "alta medica"', async () => {
    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(linkService.getAllAndFilter).toHaveBeenCalled();
    expect(service.getAllPensionsToInactivate);
  });
  it('worker inactivate by "resolucion definitiva"', async () => {
    axios = jest.fn(() => Promise.resolve({ data: getDataSap[1] }));
    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(linkService.getAllAndFilter).toHaveBeenCalled();
    expect(service.getAllPensionsToInactivate);
  });

  it('worker already worked', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(true));

    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(done).toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject(new Error('Error')));
    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker by not having all the marks in the array', async () => {
    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('completed worker by having all the process neede to complete the process', async () => {
    logService.allMarksExists = jest
      .fn(() => Promise.resolve(true))
      .mockImplementationOnce(() => Promise.resolve(true));

    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('processed fail worker by not having all the process neede to complete the process', async () => {
    logService.allMarksExists = jest
      .fn(() => Promise.resolve(true))
      .mockImplementationOnce(() => Promise.resolve(true));

    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('if not completed worker ', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    transientService.getAllPensionsToInactivate = jest.fn(() => ({
      pensionsToInactivate: '',
      pensionsToEvaluate: ''
    }));
    transientService.createTransientMarkPension = jest.fn(() =>
      Promise.resolve({ completed: false, inactivationError: true })
    );
    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      done
    });
    expect(logService.existsLog).toBeCalled();
  });

  it('success  completed worker ', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    transientService.getAllPensionsToInactivate = jest.fn(() => ({
      pensionsToInactivate: '',
      pensionsToEvaluate: ''
    }));

    await workerModule.workerFn({
      Logger,
      linkService,
      transientService: service,
      axios,
      transientHelper,
      logService,
      pensionService,
      done
    });
    expect(logService.existsLog).toBeCalled();
  });

  afterAll(afterAllTests);
});

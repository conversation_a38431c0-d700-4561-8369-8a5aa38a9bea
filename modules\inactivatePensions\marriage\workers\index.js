const moment = require('moment');
const tmp = require('tmp');
const postWorkerModule = require('./post.worker');
const preWorkerModule = require('./pre.worker');
const marriageService = require('../services/dbService');
const pensionService = require('../../../pensions/services/pension.service');
const filesHelper = require('../../sharedFiles/filesHelper');
const { Client, connectToSFTPServer } = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');

const {
  CIVIL_REGISTRATION_SFTP_HOST,
  CIVIL_REGISTRATION_SFTP_USER,
  CIVIL_REGISTRATION_SFTP_PASS,
  CIVIL_REGISTRATION_SFTP_PORT
} = process.env;
const sftpCredentials = {
  host: CIVIL_REGISTRATION_SFTP_HOST,
  user: CIVIL_REGISTRATION_SFTP_USER,
  password: CIVIL_REGISTRATION_SFTP_PASS,
  port: CIVIL_REGISTRATION_SFTP_PORT
};

module.exports = {
  marriagePostworker: {
    name: 'InactivateByMarriage-postworker',
    worker: async deps =>
      postWorkerModule.workerFn({
        service: marriageService,
        moment,
        pensionService,
        logService,
        ...deps
      }),
    description:
      'Inactivar pensiones marcadas en el mes anterior al actual para inactivar por matrimonio',
    endPoint: 'inactivatebymarriagepostworker',
    cronMark: postWorkerModule.cronMark,
    dependencyMark: postWorkerModule.dependencyMark
  },
  marriagePreworker: {
    name: 'InactivateByMarriage-preworker',
    worker: async deps => {
      const sftpClient = new Client();
      return preWorkerModule.workerFn({
        sftpClient,
        sftpCredentials,
        connectToSFTPServer,
        logService,
        filesHelper,
        service: marriageService,
        pensionService,
        tmp,
        ...deps
      });
    },
    description: 'Marcar pensiones de viudez a inactivar el mes siguiente al actual por matrimonio',
    endPoint: 'inactivatebymarriagepreworker',
    cronMark: preWorkerModule.cronMark,
    dependencyMark: preWorkerModule.dependencyMark
  }
};

/* eslint-disable no-unused-expressions */
const moment = require('moment');
const LiquidationModel = require('../../../models/liquidation');
const liquidationHistoricModel = require('../../../models/liquidationHistoric');

const service = {
  async findOneLiquidationAndUpdate(id) {
    try {
      const data = await LiquidationModel.findOneAndUpdate(
        { _id: id, enabled: true },
        { $set: { enabled: false } },
        { returnNewDocument: true }
      )
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { error };
    }
  },
  async getAllAndFilter(query) {
    return LiquidationModel.find(query)
      .then(data => ({ result: data }))
      .catch(error => ({
        result: [],
        isError: true,
        error
      }));
  },
  async getAllWithFilter(query, selectedFields) {
    try {
      const data = await LiquidationModel.find(query, selectedFields)
        .lean()
        .exec();
      return { result: data };
    } catch (error) {
      return { result: [], error };
    }
  },
  async createUpdateLiquidation(liquidationList) {
    const session = await LiquidationModel.startSession();
    session.startTransaction();
    try {
      const bulk = LiquidationModel.collection.initializeOrderedBulkOp();
      const bulkHistoric = liquidationHistoricModel.collection.initializeOrderedBulkOp();
      liquidationList.forEach(({ _id, ...liquidation }) => {
        const { beneficiaryRut, causantRut, pensionCodeId } = liquidation;
        bulk
          .find({
            beneficiaryRut,
            causantRut,
            pensionCodeId,
            enabled: true
          })
          .upsert()
          .updateOne({
            $set: {
              ...liquidation,
              enabled: true,
              updatedAt: new Date(),
              createdAt: new Date()
            }
          });
      });

      liquidationList.forEach(({ _id, ...liquidation }) => {
        const { beneficiaryRut, causantRut, pensionCodeId } = liquidation;
        let { liquidationMonth, liquidationYear } = liquidation;
        liquidationMonth = !liquidationMonth ? moment().month() + 1 : liquidationMonth;
        liquidationYear = !liquidationYear ? moment().year() : liquidationYear;

        bulkHistoric
          .find({
            beneficiaryRut,
            causantRut,
            pensionCodeId,
            liquidationMonth,
            liquidationYear,
            enabled: true
          })
          .upsert()
          .updateOne({
            $set: {
              ...liquidation,
              liquidationMonth,
              liquidationYear,
              enabled: true,
              updatedAt: new Date(),
              createdAt: new Date()
            }
          });
      });

      if (bulk.length) await bulk.execute();
      if (bulkHistoric.length) await bulkHistoric.execute();
      await session.commitTransaction();
      return { completed: true, error: null };
    } catch (error) {
      await session.abortTransaction();
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

const moment = require('moment');

const PaymentDateModel = require('../../paymentDate/models/paymentDate');
const MotiveModel = require('../../nomenclators/motive/models/motive');
const LiquidationModel = require('../../../models/liquidation');
const BankModel = require('../../nomenclators/banks/models/banks');
const ServipagModel = require('../../nomenclators/servipag/models/servipag');
const CollectorModel = require('../../../models/collectorretentions');

const PREFIX_CODE_ASSET = 125;
const PREFIX_CODE_DISCOUNT = 313;
const PAYMENT_DETAINED = [/Pago retenido institucional/i, /Pago retenido no institucional/i];

const {
  mapperCollectorLine,
  mapperCollectorRetentionLine,
  mapperBeneficiaryLine,
  mapperBeneficiaryRetentionLine,
  mapperCollectorData,
  mapperCollectorRetentionData,
  mapperHeader,
  mapperRegisterMessage,
  mapperType81,
  mapperType80,
  getMapper,
  assetsAndDiscountMappers
} = require('./mappers');

const NOT_VALID = /no vigente/i;

const cleanRut = (rut = '') => rut.replace(/[^0-9kK-]/g, '').toUpperCase();
const dataLiquidation = result => {
  return result.map(({ netPension = 0, ...item }) => ({
    ...item,
    netPension: Math.max(0, Math.round((netPension + Number.EPSILON) * 100) / 100)
  }));
};

const sumLiquidationsTotal = result => {
  const data = dataLiquidation(result);
  return data.reduce(
    (sum, { netPension }) => Math.round((sum + netPension + Number.EPSILON) * 100) / 100,
    0
  );
};

const liquidationCurrentMonthReducer = result => {
  return {
    result: dataLiquidation(result),
    totalSumLiquidations: Math.max(
      0,
      Math.round((sumLiquidationsTotal(result) + Number.EPSILON) * 100) / 100
    ).toString()
  };
};

const liquidationCurrentMonth = async () => {
  const today = moment().startOf('month');

  return new Promise(resolve => {
    LiquidationModel.find({
      enabled: true,
      createdAt: {
        $gte: today.toDate(),
        $lte: moment(today)
          .endOf('month')
          .toDate()
      }
    })
      .lean()
      .then(function(result) {
        resolve(liquidationCurrentMonthReducer(result));
      });
  });
};

const collectorsRetention = resultCollectors => {
  return { resultRetention: resultCollectors };
};
const getCollectorMonth = async () => {
  return new Promise(resolve => {
    CollectorModel.find({})
      .lean()
      .then(function(result) {
        resolve(collectorsRetention(result));
      });
  });
};

const getCollectorRetentionTypes = (
  pensionData,
  extraData,
  liquidation,
  assetMapper,
  collectorRetentions
) => {
  const retentionsPensions = [];
  try {
    collectorRetentions.forEach(collectorRetention => {
      retentionsPensions.push(
        mapperCollectorRetentionLine(pensionData, { ...extraData, collectorRetention }),
        mapperBeneficiaryRetentionLine(pensionData, { ...extraData, collectorRetention }),
        mapperCollectorRetentionData(pensionData, { ...extraData, collectorRetention })
      );
    });
    return retentionsPensions;
  } catch (error) {
    return retentionsPensions;
  }
};

const getBranchOffices = async () => {
  const [banks, servipags] = await Promise.all([
    BankModel.find({ enabled: true }),
    ServipagModel.find({ enabled: true })
  ]);

  return {
    banks,
    servipags
  };
};

const getDateToPayFormat = resultPaymentDate => {
  return resultPaymentDate.paymentDate
    ? moment(resultPaymentDate.paymentDate).format('YYYYMMDD')
    : '';
};
const getDateToPay = async () => {
  const date = new Date();

  const currentMonth = date.getMonth() + 1;
  const years = date.getFullYear();

  return new Promise(resolve => {
    PaymentDateModel.findOne({
      month: currentMonth.toString().padStart(2, '0'),
      year: years
    })
      .lean()
      .then(function(resultPaymentDate) {
        resolve(getDateToPayFormat(resultPaymentDate));
      });
  });
};

const getMotives = async () => {
  const motives = await MotiveModel.find({})
    .lean()
    .catch(() => []);

  const assets = motives.filter(item => item.option === 'Haber');
  const discounts = motives.filter(item => item.option === 'Descuento');

  return [assets, discounts];
};

const bodyBankPayRoll = (pensions, extraData) => {
  const bodyLines = [];
  const { liquidations, assetMapper, discountMapper, collectors } = extraData;
  pensions.forEach(pension => {
    const { beneficiary, causant, pensionCodeId: codePension, _doc: pensionData } = pension;
    const { rut: beneficiaryId } = beneficiary;
    const { rut: causantId } = causant;
    const liquidation = liquidations.find(
      ({ beneficiaryRut, causantRut, pensionCodeId }) =>
        cleanRut(beneficiaryRut) === cleanRut(beneficiaryId) &&
        cleanRut(causantRut) === cleanRut(causantId) &&
        pensionCodeId === codePension
    );

    bodyLines.push(
      mapperCollectorLine(pensionData, { ...extraData, liquidation }),
      mapperBeneficiaryLine(pensionData, { ...extraData, liquidation, assetMapper }),
      mapperCollectorData(pensionData, { ...extraData, liquidation, discountMapper })
    );

    const collectorRetentions = collectors.filter(element => {
      const { beneficiaryRutOrigen, causantRutOrigen } = element;
      return (
        cleanRut(beneficiaryRutOrigen) === cleanRut(beneficiaryId) &&
        cleanRut(causantRutOrigen) === cleanRut(causantId)
      );
    });

    if (collectorRetentions && collectorRetentions.length > 0) {
      bodyLines.push(
        ...getCollectorRetentionTypes(
          pensionData,
          extraData,
          liquidation,
          assetMapper,
          collectorRetentions
        )
      );
    }
  });

  return bodyLines;
};

const service = {
  async generateBankPayRoll({ pensionService, fsClient, ufService }) {
    try {
      const { result } = await pensionService.getAllAndFilter({
        validityType: { $not: NOT_VALID },
        'paymentInfo.paymentGateway': { $nin: PAYMENT_DETAINED },
        enabled: true
      });

      const fileName = `nomina_bancaria${moment().format('MMYYYY')}.txt`;
      const dateToPay = await getDateToPay();
      const [byAssets, byDiscounts] = await getMotives();

      const { value = 0 } = (await ufService.getCurrentUfValue()) || {};
      const { result: liquidations, totalSumLiquidations } = await liquidationCurrentMonth();
      const { servipags, banks } = await getBranchOffices();
      const { resultRetention: collectors } = await getCollectorMonth();

      const assetMapper = getMapper([...byAssets], PREFIX_CODE_ASSET);
      const discountMapper = getMapper([...byDiscounts], PREFIX_CODE_DISCOUNT);

      const bankPayrollRows = [
        mapperHeader(dateToPay, totalSumLiquidations),
        ...bodyBankPayRoll(result, {
          liquidations,
          banks,
          servipags,
          dateToPay,
          ufValue: value,
          discountMapper,
          assetMapper,
          collectors
        }),
        ...mapperRegisterMessage(),
        ...mapperType80([...assetsAndDiscountMappers, ...assetMapper, ...discountMapper]),
        mapperType81()
      ];

      let file = '';
      bankPayrollRows.forEach(line => {
        file = `${file}${line}\n`;
      });

      await fsClient.writeFile(
        `${__dirname}/../../unifiedGenerateAndUploadBankFileCrons/workers/${fileName}`,
        file
      );

      return { error: null, completed: true };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

module.exports = { ...service };

const extractSubstring = (line, position, length) => line.substr(position, length);

const countUniqueResolutionNumber = lines => {
  const resolutionNumbers = lines.map(l => l.substr(15, 8));
  return new Set(resolutionNumbers).size;
};

const sumAmounts = lines => lines.reduce((sum, l) => sum + +l.substr(114, 8), 0);

const composeRctapsLine = (lines, filteredLines) => {
  const emissionPeriod = extractSubstring(lines[0], 0, 6);
  const institutionRut = '703601006';
  const transferDate = extractSubstring(lines[0], 148, 8);
  const transferCode = '0'.padEnd(10, '0');
  const uniqueResolutionNumberCount = String(countUniqueResolutionNumber(lines)).padStart(8, '0');
  const allResolutionsNumberCount = String(lines.length).padStart(8, '0');
  const totalIpsAmounts = String(sumAmounts(lines)).padStart(15, '0');
  const excessPaymentAmount = String(sumAmounts(filteredLines)).padStart(15, '0');
  const underPaymentAmount = '0'.padEnd(15, '0');
  const returnmentDate = '0'.padEnd(8, '0');
  const returnmentCode = ' '.padEnd(10);

  return [
    emissionPeriod,
    institutionRut,
    transferDate,
    transferCode,
    uniqueResolutionNumberCount,
    allResolutionsNumberCount,
    totalIpsAmounts,
    excessPaymentAmount,
    underPaymentAmount,
    returnmentDate,
    returnmentCode
  ].join('');
};

module.exports = composeRctapsLine;

/* eslint-disable no-restricted-syntax */
const moment = require('moment');
const { roundValue } = require('../../sharedFiles/helpers');
const PensionModel = require('../../../models/pension');
const PensionHistoricModel = require('../../../models/pensionHistoric');

const VALIDITY_TYPE = /^No\s+vigente$/i;
const PENSION_TYPES = [
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trabajo/i,
  /Pensi[óo]n\s+por\s+accidente\s+de\s+trayecto/i,
  /Pensi[óo]n\s+por\s+enfermedad\s+profesional/i
];

const getPensions = async () => {
  return PensionModel.find({
    pensionType: { $in: PENSION_TYPES },
    enabled: true,
    validityType: { $not: VALIDITY_TYPE },
    institutionalPatient: false
  }).lean();
};

const findRelatedPensions = async pension => {
  const { beneficiary, causant, pensionCodeId } = pension;
  return PensionHistoricModel.aggregate([
    {
      $match: {
        createdAt: {
          $lt: moment()
            .startOf('month')
            .toDate()
        },
        enabled: false,
        'beneficiary.rut': beneficiary.rut,
        'causant.rut': causant.rut,
        pensionCodeId
      }
    },
    {
      $group: {
        _id: {
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        },
        data: { $first: '$$ROOT' }
      }
    },
    { $replaceRoot: { newRoot: '$data' } },
    { $project: { reservedAmounts: 1, createdAt: 1, institutionalPatient: 1 } },
    { $sort: { createdAt: -1 } }
  ]);
};

const calculateTotalRetroactiveAmount = (pension, relatedPensions) => {
  const { _id, ...data } = pension;
  let retroactiveAmount = 0;
  for (const relatedPension of relatedPensions) {
    const { institutionalPatient, reservedAmounts } = relatedPension;
    if (!institutionalPatient) break;
    retroactiveAmount += reservedAmounts.forInstitutionalPatient || 0;
  }
  return {
    ...data,
    retroactiveAmounts: {
      ...pension.retroactiveAmounts,
      forInstitutionalPatient: roundValue(retroactiveAmount)
    }
  };
};

module.exports = {
  async setTotalRetroactiveAmounts(pensionService) {
    const updatedPensions = [];
    try {
      const pensions = await getPensions();
      for await (const pension of pensions) {
        const relatedPensions = await findRelatedPensions(pension);
        if (relatedPensions.length) {
          const updatedPension = calculateTotalRetroactiveAmount(pension, relatedPensions);
          updatedPensions.push(updatedPension);
        }
      }
      const { completed, error } = await pensionService.createUpdatePension(updatedPensions);
      return { completed, error };
    } catch (error) {
      return { completed: false, error };
    }
  }
};

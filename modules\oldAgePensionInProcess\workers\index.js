const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'old-age-pension-in-process',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SEND_NOTIFICATION_EMAIL,
  description: 'Pension de vejez en tramite',
  endPoint: 'oldagepensioninprocess',
  cronMark: workerModule.markOfCron,
  dependencyMark: workerModule.dependencyMark
};

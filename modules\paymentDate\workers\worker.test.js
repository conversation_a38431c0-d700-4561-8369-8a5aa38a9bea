/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker payment dates Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let done;
  let logService;
  beforeEach(() => {
    done = jest.fn();
    service = {
      generatePaymentDates: jest.fn(() => Promise.resolve({ completed: true, error: null }))
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.generatePaymentDates).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.generatePaymentDates).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.generatePaymentDates).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

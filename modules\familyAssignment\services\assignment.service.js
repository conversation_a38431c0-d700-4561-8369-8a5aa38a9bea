const moment = require('moment');

const PensionModel = require('../../../models/pension');
const TemporaryFamilyAssignmentModel = require('../models/temporaryFamilyAssignment');
const logService = require('../../sharedFiles/services/jobLog.service');

const notValid = /No vigente/i;

const PENSION_TYPE = [
  /Pensi[óo]n de viudez con hijos/i,
  /Pensi[óo]n de viudez sin hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i,
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const PENSION_TRABAJO = /Pensi[óo]n por accidente de trabajo/i;
const PENSION_TRAYECTO = /Pensi[óo]n por accidente de trayecto/i;
const PENSION_PROFESIONAL = /Pensi[óo]n por enfermedad profesional/i;

const AGE_LIMIT = 45;
const PENSION_VIUDEZ_CON_HIJOS = /Pensi[óo]n de viudez con hijos/i;
const PENSION_VIUDEZ_SIN_HIJOS = /Pensi[óo]n de viudez sin hijos/i;
const PENSION_MADRE_CON_HIJOS = /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i;
const PENSION_MADRE_SIN_HIJOS = /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i;

const Article41Count = (pensionType, numberOfChargesArticle41) => {
  if (
    PENSION_TRABAJO.test(pensionType) ||
    PENSION_TRAYECTO.test(pensionType) ||
    PENSION_PROFESIONAL.test(pensionType)
  ) {
    return numberOfChargesArticle41;
  }

  return 0;
};

module.exports = {
  async getFamilyAssignmentPensioners() {
    return TemporaryFamilyAssignmentModel.aggregate([
      {
        $match: {
          chargeValidityType: {
            $in: [/Carga interna activa/i, /Carga externa activa/i]
          }
        }
      },
      {
        $group: {
          _id: {
            causantId: '$causantId',
            collectorId: '$collectorId'
          },
          internalCounter: {
            $sum: {
              $cond: [{ $eq: [{ $toLower: '$chargeValidityType' }, 'carga interna activa'] }, 1, 0]
            }
          },
          externalCounter: {
            $sum: {
              $cond: [{ $eq: [{ $toLower: '$chargeValidityType' }, 'carga externa activa'] }, 1, 0]
            }
          },
          numberOfChargesArticle41: {
            $sum: {
              $cond: [{ $eq: [{ $toLower: '$relationship' }, 'hijo'] }, 1, 0]
            }
          },
          familyAssignmentAssets: {
            $sum: {
              $cond: [
                { $eq: [{ $toLower: '$chargeValidityType' }, 'carga interna activa'] },
                '$familyAssignment',
                0
              ]
            }
          },
          retroactiveFamilyAssignment: {
            $sum: {
              $cond: [
                {
                  $eq: [{ $toLower: '$chargeValidityType' }, 'carga interna activa']
                },
                '$retroactiveFamilyAssignment',
                0
              ]
            }
          },
          maxEndDateOfCertification: { $max: '$endDateOfCertificationValidity' }
        }
      },
      {
        $lookup: {
          from: 'pensions',
          let: {
            beneficiaryRut: {
              $trim: { input: '$_id.collectorId' }
            },
            causantRut: {
              $trim: { input: '$_id.causantId' }
            }
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    {
                      $eq: ['$beneficiary.rut', '$$beneficiaryRut']
                    },
                    {
                      $eq: ['$causant.rut', '$$causantRut']
                    }
                  ]
                }
              }
            }
          ],
          as: 'pension'
        }
      },
      {
        $unwind: {
          path: '$pension',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $match: {
          'pension.pensionType': {
            $in: PENSION_TYPE
          }
        }
      },
      {
        $project: {
          internalCounter: 1,
          externalCounter: 1,
          numberOfChargesArticle41: 1,
          familyAssignmentAssets: 1,
          retroactiveFamilyAssignment: 1,
          maxEndDateOfCertification: 1,
          pension: 1
        }
      }
    ]);
  },

  async getSurvivanceTemporaryFamilyAssignment(chargeValidityTypes) {
    const familyAssignment = await TemporaryFamilyAssignmentModel.aggregate([
      {
        $match: {
          typeOfAssocietedPension: { $regex: /supervivencia/i },
          chargeValidityType: { $in: [...chargeValidityTypes] }
        }
      },
      {
        $group: {
          _id: { causantId: '$causantId', chargeId: '$chargeId' }
        }
      }
    ]);
    return familyAssignment.map(({ _id: { causantId, chargeId } }) => ({
      causantId,
      chargeId
    }));
  },
  async bulkAndDelete(body) {
    try {
      await TemporaryFamilyAssignmentModel.deleteMany({}).exec();
      const result =
        JSON.stringify(body) !== '{}'
          ? await TemporaryFamilyAssignmentModel.insertMany(body)
          : null;
      return { isError: !result, result, error: { name: 'MongoError', code: 11000 } };
    } catch (error) {
      return { isError: true, error };
    }
  },

  async getAll(query = null, sort = null) {
    if (query && sort) {
      return TemporaryFamilyAssignmentModel.find(query).sort(sort);
    }
    return TemporaryFamilyAssignmentModel.find(query)
      .sort(sort)
      .then(data => ({ result: data }))
      .catch(error => ({
        isError: true,
        error
      }));
  },

  async wasInactivated(inactivationReason, validityType) {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    try {
      const [hasPensionDocuments, hasAssignmentDocuments] = await Promise.all([
        PensionModel.findOne({})
          .countDocuments()
          .exec(),
        TemporaryFamilyAssignmentModel.findOne({})
          .countDocuments()
          .exec()
      ]);

      if (hasPensionDocuments && hasAssignmentDocuments) {
        const wasInactivated = await PensionModel.findOne({
          inactivationDate: {
            $gte: new Date(currentYear, currentMonth, 1),
            $lt: new Date(currentYear, currentMonth + 1, 1)
          },
          inactivationReason,
          validityType,
          enabled: true
        })
          .countDocuments()
          .exec();
        return {
          wasInactivated: !!wasInactivated,
          completed: !!wasInactivated,
          isError: false,
          error: null
        };
      }
      return { wasInactivated: true, completed: false, isError: false, error: null };
    } catch (error) {
      return { isError: true, error };
    }
  },

  async processFamilyCharges(pensionsService) {
    try {
      if (await logService.existsLog('PROCESS_FAMILY_CHARGES')) return;
      const familyAssignment = await this.getFamilyAssignmentPensioners();

      const pensionsProccessed = (
        await Promise.all(
          familyAssignment.map(async assignment => {
            const {
              externalCounter,
              internalCounter,
              numberOfChargesArticle41,
              familyAssignmentAssets,
              retroactiveFamilyAssignment,
              maxEndDateOfCertification,
              pension
            } = assignment;

            const {
              assets,
              retroactiveAmounts,
              validityType,
              dateOfBirth,
              pensionType,
              endDateOfTheoricalValidity,
              reactivationDate,
              endDateOfValidity
            } = pension;

            let validityTypeAux = validityType;
            let endDateOfTheoricalValidityAux = endDateOfTheoricalValidity;
            let reactivationDateAux = reactivationDate;
            let endDateOfValidityAux = endDateOfValidity;

            if (
              notValid.test(validityType) &&
              (internalCounter > 0 || externalCounter > 0) &&
              (PENSION_VIUDEZ_CON_HIJOS.test(pensionType) ||
                PENSION_VIUDEZ_SIN_HIJOS.test(pensionType) ||
                PENSION_MADRE_CON_HIJOS.test(pensionType) ||
                PENSION_MADRE_SIN_HIJOS.test(pensionType))
            ) {
              const dateOfBirthAux = new Date(dateOfBirth);
              const age = moment().diff(dateOfBirthAux, 'years');
              if (age >= AGE_LIMIT) {
                validityTypeAux = 'Vigente vitalicia';
                endDateOfTheoricalValidityAux = new Date(
                  dateOfBirthAux.setUTCFullYear(dateOfBirthAux.getFullYear() + 110)
                );
              } else {
                validityTypeAux = 'Vigente viudez';
                endDateOfTheoricalValidityAux = new Date(maxEndDateOfCertification);
              }
              reactivationDateAux = moment().toDate();
              endDateOfValidityAux = endDateOfTheoricalValidityAux;
            }

            return {
              ...pension,
              numberOfCharges: internalCounter,
              numberOfChargesExternal: externalCounter,
              numberOfChargesArticle41: Article41Count(pensionType, numberOfChargesArticle41),
              validityType: validityTypeAux,
              endDateOfTheoricalValidity: endDateOfTheoricalValidityAux,
              reactivationDate: reactivationDateAux,
              endDateOfValidity: endDateOfValidityAux,
              assets: { ...assets, forFamilyAssignment: familyAssignmentAssets },
              retroactiveAmounts: {
                ...retroactiveAmounts,
                forFamilyAssignment: retroactiveFamilyAssignment
              }
            };
          })
        )
      ).filter(({ isError }) => !isError);

      if (pensionsProccessed.length) {
        const { completed, error } = await pensionsService.createUpdatePension(pensionsProccessed);
        if (!completed) throw new Error(error);
      }
      await logService.saveLog('PROCESS_FAMILY_CHARGES');
    } catch (error) {
      throw new Error(error);
    }
  },
  async existTemporaryAssignment() {
    const result = await TemporaryFamilyAssignmentModel.countDocuments().catch(error => ({
      isError: true,
      error
    }));
    if (result.isError) {
      return { result: 0, ...result };
    }
    return { result, isError: false };
  },

  async fileAlreadyImported() {
    return TemporaryFamilyAssignmentModel.findOne({
      $expr: {
        $and: [
          { $eq: [{ $month: `$createdAt` }, moment().month() + 1] },
          { $eq: [{ $year: `$createdAt` }, moment().year()] }
        ]
      }
    }).catch(() => false);
  }
};

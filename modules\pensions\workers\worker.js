const notValid = [/no vigente/i];
const cronDescription = 'informacion trasferencias de pension';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const cronMark = 'DATA_TO_CIVIL_REGISTRATION';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const firstDependencyMark = 'CRON_TRANSFER_PENSIONER_INFO';
const secondDependencyMark = 'SET_VALUES_TO_ZERO';
const thirdDependencyMark = 'TRANSFER_PENSIONS';

const getMissingDependencyMessage = dependencyMark =>
  `No se ha ejecutado la dependencia ${dependencyMark}`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({
  Logger,
  sftpClient,
  connectToSFTPServer,
  sftpCredentials,
  fs,
  service,
  logService,
  table,
  mapperData,
  done,
  moment,
  uploadToSFTP,
  job
}) => {
  try {
    Logger.info(`${cronMark} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronMark}: start dependency verification`);

    if (!(await logService.existsLog(firstDependencyMark))) {
      Logger.info(`${cronMark}:${getMissingDependencyMessage(firstDependencyMark)}`);
      return { message: getMissingDependencyMessage(firstDependencyMark), status: 'UNAUTHORIZED' };
    }

    if (!(await logService.existsLog(secondDependencyMark))) {
      Logger.info(getMissingDependencyMessage(secondDependencyMark));
      return { message: getMissingDependencyMessage(secondDependencyMark), status: 'UNAUTHORIZED' };
    }

    if (!(await logService.existsLog(thirdDependencyMark))) {
      Logger.info(getMissingDependencyMessage(thirdDependencyMark));
      return { message: getMissingDependencyMessage(thirdDependencyMark), status: 'UNAUTHORIZED' };
    }

    Logger.info(`${cronMark} process started`);
    const { result } = await service.getAllAndFilter({
      validityType: { $nin: notValid },
      enabled: true
    });

    const fileName = `Achs_${moment().format('YYYYMM')}.txt`;

    const { error } = await uploadToSFTP({
      fileName,
      fs,
      sftpClient,
      sftpCredentials,
      connectToSFTPServer,
      result,
      mapperData,
      table,
      Logger
    });
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);

    Logger.info(successMessage);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, firstDependencyMark, workerFn };

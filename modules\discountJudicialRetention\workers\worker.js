const cronDescription = 'set amount retention';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el año actual.';
const cronMark = 'SET_AMOUNT_RETENTION';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const markDependencies = ['TAXABLE_PENSION', 'UF_VALUE', 'TOTAL_ASSETS_AND_DISCOUNTS'];

const getMissingDependencyMessage = `No se ha ejecutado una o más de las dependencias ${markDependencies.join(
  ', '
)}`;
const workerFn = async ({ Logger, logService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} comprobar si este proceso se ejecutó previamente o no`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED' };
    }

    if (!(await logService.allMarksExists(markDependencies))) {
      Logger.info(getMissingDependencyMessage);
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    const { error } = await service.setAmountRetention();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} proceso finalizado`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, markDependencies, workerFn };

import { PrismaService } from '../prisma/prisma.service';
import { Injectable } from '@nestjs/common';

/**
 * Eloquent-style Query Builder para Prisma
 * Inspirado en Laravel Eloquent para mayor productividad
 */
@Injectable()
export class EloquentQueryBuilder<T = any> {
  private model: any;
  private query: any = {};
  private includes: any = {};
  private orderBy: any[] = [];
  private limitValue?: number;
  private offsetValue?: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly modelName: string,
  ) {
    this.model = (this.prisma as any)[modelName];
  }

  /**
   * Equivalente a Laravel's where()
   */
  where(field: string, operator?: string | any, value?: any): this {
    if (arguments.length === 2) {
      value = operator;
      operator = 'equals';
    }

    if (!this.query.where) {
      this.query.where = {};
    }

    switch (operator) {
      case '=':
      case 'equals':
        this.query.where[field] = value;
        break;
      case '>':
      case 'gt':
        this.query.where[field] = { gt: value };
        break;
      case '>=':
      case 'gte':
        this.query.where[field] = { gte: value };
        break;
      case '<':
      case 'lt':
        this.query.where[field] = { lt: value };
        break;
      case '<=':
      case 'lte':
        this.query.where[field] = { lte: value };
        break;
      case 'like':
        this.query.where[field] = { contains: value, mode: 'insensitive' };
        break;
      case 'in':
        this.query.where[field] = { in: value };
        break;
      case 'not':
        this.query.where[field] = { not: value };
        break;
    }

    return this;
  }

  /**
   * Equivalente a Laravel's whereIn()
   */
  whereIn(field: string, values: any[]): this {
    return this.where(field, 'in', values);
  }

  /**
   * Equivalente a Laravel's whereBetween()
   */
  whereBetween(field: string, values: [any, any]): this {
    if (!this.query.where) {
      this.query.where = {};
    }
    this.query.where[field] = { gte: values[0], lte: values[1] };
    return this;
  }

  /**
   * Equivalente a Laravel's whereDate()
   */
  whereDate(field: string, date: Date | string): this {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    return this.whereBetween(field, [startOfDay, endOfDay]);
  }

  /**
   * Equivalente a Laravel's with() - Eager Loading
   */
  with(relations: string | string[] | Record<string, any>): this {
    if (typeof relations === 'string') {
      this.includes[relations] = true;
    } else if (Array.isArray(relations)) {
      relations.forEach(relation => {
        this.includes[relation] = true;
      });
    } else {
      Object.assign(this.includes, relations);
    }

    this.query.include = this.includes;
    return this;
  }

  /**
   * Equivalente a Laravel's orderBy()
   */
  orderBy(field: string, direction: 'asc' | 'desc' = 'asc'): this {
    this.orderBy.push({ [field]: direction });
    this.query.orderBy = this.orderBy;
    return this;
  }

  /**
   * Equivalente a Laravel's latest()
   */
  latest(field: string = 'createdAt'): this {
    return this.orderBy(field, 'desc');
  }

  /**
   * Equivalente a Laravel's oldest()
   */
  oldest(field: string = 'createdAt'): this {
    return this.orderBy(field, 'asc');
  }

  /**
   * Equivalente a Laravel's limit()
   */
  limit(count: number): this {
    this.limitValue = count;
    this.query.take = count;
    return this;
  }

  /**
   * Equivalente a Laravel's offset()
   */
  offset(count: number): this {
    this.offsetValue = count;
    this.query.skip = count;
    return this;
  }

  /**
   * Equivalente a Laravel's paginate()
   */
  paginate(page: number = 1, perPage: number = 15): this {
    const skip = (page - 1) * perPage;
    this.query.skip = skip;
    this.query.take = perPage;
    return this;
  }

  /**
   * Equivalente a Laravel's get()
   */
  async get(): Promise<T[]> {
    return await this.model.findMany(this.query);
  }

  /**
   * Equivalente a Laravel's first()
   */
  async first(): Promise<T | null> {
    const result = await this.model.findFirst(this.query);
    return result;
  }

  /**
   * Equivalente a Laravel's firstOrFail()
   */
  async firstOrFail(): Promise<T> {
    const result = await this.model.findFirstOrThrow(this.query);
    return result;
  }

  /**
   * Equivalente a Laravel's find()
   */
  async find(id: string | number): Promise<T | null> {
    return await this.model.findUnique({
      where: { id },
      include: this.includes,
    });
  }

  /**
   * Equivalente a Laravel's findOrFail()
   */
  async findOrFail(id: string | number): Promise<T> {
    return await this.model.findUniqueOrThrow({
      where: { id },
      include: this.includes,
    });
  }

  /**
   * Equivalente a Laravel's count()
   */
  async count(): Promise<number> {
    const { include, orderBy, take, skip, ...countQuery } = this.query;
    return await this.model.count(countQuery);
  }

  /**
   * Equivalente a Laravel's exists()
   */
  async exists(): Promise<boolean> {
    const count = await this.count();
    return count > 0;
  }

  /**
   * Equivalente a Laravel's chunk()
   */
  async chunk(size: number, callback: (items: T[]) => Promise<void> | void): Promise<void> {
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      const items = await this.model.findMany({
        ...this.query,
        take: size,
        skip: offset,
      });

      if (items.length === 0) {
        hasMore = false;
        break;
      }

      await callback(items);
      offset += size;

      if (items.length < size) {
        hasMore = false;
      }
    }
  }

  /**
   * Equivalente a Laravel's pluck()
   */
  async pluck(field: string): Promise<any[]> {
    const results = await this.model.findMany({
      ...this.query,
      select: { [field]: true },
    });

    return results.map((item: any) => item[field]);
  }

  /**
   * Scope personalizado (equivalente a Laravel scopes)
   */
  scope(scopeName: string, ...args: any[]): this {
    const scopeMethod = `scope${scopeName.charAt(0).toUpperCase() + scopeName.slice(1)}`;
    
    if (typeof (this as any)[scopeMethod] === 'function') {
      return (this as any)[scopeMethod](...args);
    }

    return this;
  }

  /**
   * Scope: enabled (equivalente a Laravel's local scopes)
   */
  scopeEnabled(): this {
    return this.where('enabled', true);
  }

  /**
   * Scope: byType
   */
  scopeByType(type: string): this {
    return this.where('type', type);
  }

  /**
   * Raw query builder para casos complejos
   */
  raw(): any {
    return this.query;
  }

  /**
   * Reset query builder
   */
  reset(): this {
    this.query = {};
    this.includes = {};
    this.orderBy = [];
    this.limitValue = undefined;
    this.offsetValue = undefined;
    return this;
  }
}

const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');

module.exports = {
  name: 'monthly-expenses',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_MONTHLY_EXPENSES,
  description: 'Cron que realiza los calculos para el gráfico de la pantalla Home',
  endPoint: 'monthlyexpenses',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

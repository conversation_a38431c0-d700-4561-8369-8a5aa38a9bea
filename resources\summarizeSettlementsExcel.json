[{"beneficiaryRut": "********-8", "causantRut": "********-8", "pensionCodeId": "17153", "taxablePension": 25000, "taxablePensionDate": "2020-05-26T04:00:00.000Z", "totalAssets": 26000, "totalOnePercentDiscounts": 27000, "totalSocialCreditDiscounts": 28000, "totalDiscounts": 29000, "numberOfAssets": 30000, "numberOfDiscounts": 31000, "liquidationMonth": 5, "liquidationYear": 2020, "netPension": 32000, "fixedBasePension": 15151, "enabled": true, "createdAt": "2020-05-26T04:00:00.000Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "********-3"}, {"institutionalPatient": false, "createdAt": "2020-05-26T04:00:00.000Z", "enabled": true, "basePension": 142452, "country": "CHI", "transient": "No", "cun": "3781076", "initialBasePension": 34827, "dateOfBirth": "1997-10-08T04:00:00.000Z", "gender": "M", "afpAffiliation": "AFP MODELO S.A.", "healthAffiliation": "FONASA", "paymentInfo": {"paymentGateway": "", "accountNumber": "", "bank": ""}, "causant": {"rut": "********-8", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES"}, "collector": {"rut": "********-K", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "address": ""}, "beneficiary": {"rut": "********-8", "name": "CHRISTOPHER", "lastName": "YANEZ", "mothersLastName": "BAHAMONDES", "email": ""}, "validityType": "No vigente", "pensionType": "Pensión por orfandad", "disabilityDegree": 55, "pensionStartDate": "2019-01-03T03:00:00.000Z", "disabilityType": "Invalidez parcial", "resolutionNumber": ********, "accidentNumber": 5486522, "resolutionDate": "2019-10-02T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "2016-09-21T03:00:00.000Z", "endDateOfTheoricalValidity": "2020-10-02T03:00:00.000Z", "pensionCodeId": "17153", "article40": 30000, "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "********-3"}, {"beneficiaryRut": "********-8", "causantRut": "********-8", "taxablePension": 25000, "taxablePensionDate": "2020-05-26T04:00:00.000Z", "totalAssets": 26000, "totalOnePercentDiscounts": 27000, "totalSocialCreditDiscounts": 28000, "totalDiscounts": 29000, "numberOfAssets": 30000, "numberOfDiscounts": 31000, "liquidationMonth": 5, "liquidationYear": 2020, "netPension": 32000, "fixedBasePension": 15151, "enabled": true, "createdAt": "2020-05-26T04:00:00.000Z", "pension": {"paymentInfo": {"paymentGateway": "asdf", "accountNumber": "fdsa", "bank": "asdf", "branchOffice": "asdf"}, "causant": {"rut": "********-5", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "AVENDAÑO"}, "collector": {"rut": "********-8", "name": "ANGEL", "lastName": "ROGEL", "mothersLastName": "", "address": ""}, "beneficiary": {"rut": "********-8", "name": "<PERSON>", "lastName": "Poblete", "mothersLastName": "<PERSON><PERSON><PERSON>", "email": ""}, "reservedAmounts": {"forDisability": 0, "forInstitutionalPatient": 0, "forRejection": ********, "forSurvival": ********}, "assets": {"aps": 0, "healthDiscount": "No", "healthExemption": "No", "forFamilyAssignment": 0, "nonFormulable": 0, "christmasBonus": 10, "nationalHolidaysBonus": 10, "winterBonus": 20, "rebsal": 850000, "totalAssets": 1148}, "retroactiveAmounts": {"forSurvival": 9181989, "forDisability": ********, "forInstitutionalPatient": **********, "forRejection": *********, "forNetTotalNonFormulableAssetsByReason": [{"amount": 10000, "reason": "comidaTotalNonFormulable1"}, {"amount": 10990, "reason": "comidaTotalNonFormulable2"}], "forTaxableTotalNonFormulableAssetsByReason": [{"amount": 29000, "reason": "comidaTaxable1"}, {"amount": 29000, "reason": "comidaTaxable2"}], "forTotalNonFormulableDiscountsByReason": [{"amount": 11111, "reason": "comidaDiscount1"}, {"amount": 1525178, "reason": "comidaDiscount2"}]}, "numberOfCharges": 0, "institutionalPatient": false, "taxablePension": 0, "netPension": 0, "discounts": {"onePercentLaAraucana": "No", "socialCreditsLaArucana": 0, "onePercent18": "No", "socialCredits18": 0, "onePercentLosAndes": "No", "socialCreditsLosAndes": 0, "othersLosAndes": 0, "onePercentLosHeroes": "No", "socialCreditsLosHeroes": 0, "healthLoan": 0, "nonFormulable": [{"amount": 20999999, "reason": "solo existe aca"}, {"amount": *********, "reason": "descuento2"}, {"amount": *********, "reason": "descuento1"}], "health": 1, "afp": **********, "onePercents": 0, "socialCredits": 0, "otherDiscounts": 0, "totalDiscounts": 0}, "onePercent": 24500, "socialCredits": 123, "otherDiscounts": "nope", "enabled": true, "basePension": 182943, "country": "CHI", "transient": "No", "cun": "", "initialBasePension": 15000, "dateOfBirth": "1996-06-26T04:00:00.000Z", "gender": "F", "afpAffiliation": "AFP PLANVITAL S.A.", "healthAffiliation": "FONASA", "validityType": "asdkjfan", "pensionType": "Pensión de viudez con hijos", "disabilityDegree": 50, "disabilityType": "Invalidez total", "resolutionNumber": 91154675, "accidentNumber": 6456159, "resolutionDate": "2019-10-15T03:00:00.000Z", "disabilityStartDate": "1900-01-01T04:42:46.000Z", "accidentDate": "1900-01-01T04:42:46.000Z", "pensionCodeId": "23129", "pensionStartDate": "2019-01-03T03:00:00.000Z", "article40": 0, "article41": **********, "createdAt": "2020-02-28T14:02:21.409Z", "updatedAt": "2020-05-26T21:04:03.405Z", "validatedStudyPeriod": "No", "linkedDate": "2020-05-11T17:04:42.436Z", "familyGroup": 1, "increasingInLaw19578": 10.01, "increasingInLaw19953": 10.02, "increasingInLaw20102": 10.03, "basePensionWithoutIncreases": 99.99, "heavyDuty": "Si", "parentRUT": "********-3"}}]
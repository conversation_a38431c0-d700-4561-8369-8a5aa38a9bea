const cronMark = 'RETROACTIVE_DISABILITY_PENSION';
const cronDescription = 'deshabilita pensión por invalidez de retroactividad';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const dependencyMark = '';

const workerFn = async ({ Logger, pensionService, service, logService, done }) => {
  try {
    Logger.info(`Inicio procesamiento cron calculo retroactivo pensión invalidez`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    const { error } = await service.retroactiveDisabilityPension(pensionService);
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-expressions */
const Model = require('../../../inactivatePensions/transients/models/inactivateTransient');

const PENSION_TYPES = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const notValid = /No vigente/i;

const createMarkToReactivate = async results => {
  let isBulked = false;
  const session = await Model.startSession();
  session.startTransaction();

  try {
    const bulk = Model.collection.initializeOrderedBulkOp();

    const promiseFunctions = results.map(item => async () => {
      const {
        inactivationReason,
        endDateOfValidity,
        dateToInactivate,
        beneficiaryRut,
        causantRut
      } = item;
      bulk.insert({
        inactivationReason,
        beneficiaryRut,
        causantRut,
        endDateOfValidity,
        dateToInactivate
      });
      isBulked = true;
    });

    // eslint-disable-next-line no-restricted-syntax
    for await (const fn of promiseFunctions) {
      await fn();
    }
    isBulked && (await bulk.execute());

    await session.commitTransaction();
    return { completed: true, error: null };
  } catch (e) {
    await session.abortTransaction();
    return { completed: false, error: e };
  }
};

const preService = {
  createMarkToReactivate,

  async reactivateTransient({
    linkService,
    sapRequests,
    axios,
    transientHelper,
    pensionService,
    Logger
  }) {
    try {
      const { result } = await linkService.getAllAndFilter({
        validityType: { $in: notValid },
        transient: /si/i,
        pensionType: { $in: PENSION_TYPES },
        inactivationReason: /alta m[eé]dica/i,
        inactivateManually: { $ne: true },
        manuallyReactivated: { $ne: true },
        enabled: true
      });

      const pensions = [];

      // eslint-disable-next-line no-restricted-syntax
      for await (const item of result) {
        const { accidentNumber, endDateOfValidity } = item;
        const accidentCode = accidentNumber.toString();
        const response = await sapRequests.getDataToReactivate({
          axios,
          accidentCode
        });

        const { data } = response;
        if (typeof data !== 'object') return;

        const { reposos, incapacidad } = data;
        const { result: isToInactivate, fechaAlta } = transientHelper.isAvailableToReactivate(
          reposos,
          endDateOfValidity
        );

        if (isToInactivate) {
          const pension = transientHelper.reactivatePension({
            fechaAlta,
            incapacidad,
            pension: item
          });
          if (pension) {
            pensions.push({ ...pension });
          }
        }
      }
      const pensionsToMark = pensions.filter(item => item.dateToInactivate);

      const { error: err } = await createMarkToReactivate(pensionsToMark);
      if (err) throw new Error(err);
      const pensionsToReactivate = pensions.map(
        ({ dateToInactivate, beneficiaryRut, causantRut, ...item }) => ({
          ...item,
          previousEndDateOfValidity: item.endDateOfValidity,
          reactivationDate: new Date()
        })
      );

      const { error } = await pensionService.createUpdatePension(pensionsToReactivate);

      if (error) throw new Error(error);
    } catch (error) {
      Logger.error(error);
      throw new Error(error);
    }
  }
};
module.exports = {
  ...preService
};

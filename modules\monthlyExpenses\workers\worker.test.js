/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

describe('worker Test', () => {
  beforeAll(beforeAllTests);
  let pensionService;
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      calculateMonthlyExpense: jest.fn(() => Promise.resolve({ completed: true, err: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    pensionService = {};
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateMonthlyExpense).toBeCalled();
    expect(logService.saveLog).toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(4);
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.calculateMonthlyExpense).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(3);
  });

  it('fail worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateMonthlyExpense).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('should return when calculate monthly expenses has thrown an error', async () => {
    service.calculateMonthlyExpense = jest

      .fn()
      .mockResolvedValue({ error: true, completed: false });
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });
    expect(service.calculateMonthlyExpense).toHaveBeenCalledTimes(1);
    expect(Logger.error).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
  });

  it('dependency mark already created ', async () => {
    logService.existsLog = jest.fn(() => Promise.resolve(false));
    await workerModule.workerFn({ Logger, logService, pensionService, service, done });

    expect(logService.existsLog).toBeCalled();
    expect(service.calculateMonthlyExpense).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.info).toHaveBeenCalledTimes(2);
  });

  afterAll(afterAllTests);
});

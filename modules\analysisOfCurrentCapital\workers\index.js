/* eslint-disable consistent-return */
const logService = require('../../sharedFiles/services/jobLog.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'analysisOfCurrentCapital',
  worker: async ({ Logger, done }) => workerModule.workerFn({ Logger, done, service, logService }),
  repeatInterval: process.env.CRON_ANALYSYS_OF_CURRENT_CAPITAL_FREQUENCY,
  description: 'Calculos y filtrados para reportes de capitales vigentes',
  endPoint: 'analysisofcurrentcapital',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

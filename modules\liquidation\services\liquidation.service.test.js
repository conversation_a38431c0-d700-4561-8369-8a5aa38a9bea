/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const LiquidationModel = require('../../../models/liquidation');
const service = require('./liquidation.service');

const liquidationData = require('../../../resources/newLiquidations.json');

describe('Liquidation service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(LiquidationModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should find one liquidation and update', async () => {
    // create and save one  document
    const liquidation = await LiquidationModel.create({ ...liquidationData[0], enabled: true });
    expect(liquidation._id).toBeDefined();

    const { error } = await service.findOneLiquidationAndUpdate(liquidation._id);
    const { result, isError } = await service.getAllAndFilter();

    expect(error).not.toBeDefined();
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].enabled).toBe(false);
  });

  it('should create one liquidation if it doesnt exist', async () => {
    await service.createUpdateLiquidation([{ ...liquidationData[0] }]);

    const after = await service.getAllAndFilter();
    expect(after.result.length).toBe(1);
  });

  it('should get liquidations with specific fields', async () => {
    const {
      _doc: { _id, taxablePension }
    } = await LiquidationModel.create({
      ...liquidationData[0],
      enabled: true
    });
    const { result } = await service.getAllWithFilter(
      {
        enabled: true
      },
      { enabled: 1, taxablePension: 1 }
    );

    expect(result.length).toBe(1);
    expect(result[0]._id).toStrictEqual(_id);
    expect(result[0].taxablePeriodPension).not.toBeDefined();
    expect(result[0].enabled).toBe(true);
    expect(result[0].taxablePension).toBe(taxablePension);
  });

  it('should NOT create new and update old liquidation defined by BENEFICIARY RUT and CAUSANT RUT', async () => {
    const {
      _doc: { _id, __v, ...liquidation }
    } = await LiquidationModel.create({
      ...liquidationData[0],
      enabled: true
    });
    const before = await service.getAllAndFilter();
    expect(before.result.length).toBe(1);

    await service.createUpdateLiquidation([{ ...liquidation }]);

    const after = await service.getAllAndFilter();
    expect(after.result.length).toBe(1);
  });

  it('should throw an error', async () => {
    await service.createUpdateLiquidation(null);
    expect(mocks.abortTransaction).toBeCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await LiquidationModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

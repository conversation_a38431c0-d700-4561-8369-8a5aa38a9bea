{"specialization": {}, "envi": "production", "databaseConfig": {"dataSeedPath": "data-seed", "modelValidatorPath": "./models", "dropCollection": false, "connectionUrl": "env:MONGODB_DB_URL"}, "workersConfig": {"defaultLockLifetime": 10000, "processEvery": "10 seconds", "defaultConcurrency": 20, "maxConcurrency": 50}, "logger": {"level": "debug"}, "middleware": {"static": {"module": {"arguments": ["path:./.build"]}}, "router": {"module": {"arguments": [{"directory": "path:./routes"}]}}, "compress": {"enabled": true, "priority": 10, "module": "compression"}, "json": {"enabled": true, "priority": 60, "module": {"name": "body-parser", "method": "json", "arguments": [{"limit": "15mb"}]}}, "appsec": {"enabled": true, "priority": 110, "module": {"name": "lusca", "arguments": [{"csrf": false, "p3p": false, "xframe": "SAME-ORIGIN", "csp": false}]}}, "multipart": {"enabled": true, "priority": 80, "module": {"name": "kraken-js/middleware/multipart", "arguments": [{"maxFieldsSize": 20960000, "multiples": true, "keepExtensions": true}]}}}}
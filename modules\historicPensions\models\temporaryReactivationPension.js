const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TemporaryReactivationPensionSchema = new Schema(
  {
    pensionCodeId: { type: String, required: true },
    pensionType: { type: String, required: true },
    validityType: { type: String, maxlength: 160, required: true },
    basePension: { type: Number, min: 0, max: 99999999, required: true },
    beneficiaryName: { type: String, required: true },
    endDateOfValidity: { type: Date, required: true },
    endDateOfTheoricalValidity: { type: Date, required: true },
    reactivationReason: { type: String, required: true },
    paymentEndDate: { type: Date, required: true },
    transient: { type: String, required: true },
    inactivationDate: { type: Date },
    enabled: {
      type: Boolean,
      default: true
    }
  },
  { timestamps: true }
);

TemporaryReactivationPensionSchema.plugin(paginate);
TemporaryReactivationPensionSchema.index({ beneficiaryName: 1 });

module.exports = mongoose.model('TemporaryReactivationPension', TemporaryReactivationPensionSchema);

import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * Sistema de Notificaciones estilo Laravel para NestJS
 * Inspirado en Laravel Notifications con múltiples canales
 */

export interface NotificationChannel {
  send(notifiable: any, notification: Notification): Promise<void>;
}

export abstract class Notification {
  abstract via(notifiable: any): string[];
  abstract toMail?(notifiable: any): MailMessage;
  abstract toDatabase?(notifiable: any): any;
  abstract toSlack?(notifiable: any): SlackMessage;
  abstract toSms?(notifiable: any): SmsMessage;
}

export class MailMessage {
  public subject: string = '';
  public greeting: string = '';
  public line: string[] = [];
  public action: { text: string; url: string } | null = null;
  public salutation: string = 'Saludos cordiales';

  constructor(public to: string) {}

  setSubject(subject: string): this {
    this.subject = subject;
    return this;
  }

  setGreeting(greeting: string): this {
    this.greeting = greeting;
    return this;
  }

  addLine(line: string): this {
    this.line.push(line);
    return this;
  }

  setAction(text: string, url: string): this {
    this.action = { text, url };
    return this;
  }

  setSalutation(salutation: string): this {
    this.salutation = salutation;
    return this;
  }
}

export class SlackMessage {
  public content: string = '';
  public channel: string = '#general';
  public username: string = 'Sistema Pensiones';
  public emoji: string = ':bell:';
  public attachments: any[] = [];

  setContent(content: string): this {
    this.content = content;
    return this;
  }

  setChannel(channel: string): this {
    this.channel = channel;
    return this;
  }

  addAttachment(attachment: any): this {
    this.attachments.push(attachment);
    return this;
  }
}

export class SmsMessage {
  constructor(public content: string, public to: string) {}
}

/**
 * Canal de Email
 */
@Injectable()
export class MailChannel implements NotificationChannel {
  private readonly logger = new Logger(MailChannel.name);

  async send(notifiable: any, notification: Notification): Promise<void> {
    if (!notification.toMail) {
      throw new Error('Notification does not support mail channel');
    }

    const mailMessage = notification.toMail(notifiable);
    
    // Aquí integrarías con tu servicio de email (SendGrid, SES, etc.)
    this.logger.log(`📧 Sending email to ${mailMessage.to}: ${mailMessage.subject}`);
    
    // Simulación de envío
    await this.sendEmail(mailMessage);
  }

  private async sendEmail(message: MailMessage): Promise<void> {
    // Implementación real del envío de email
    console.log('Email sent:', {
      to: message.to,
      subject: message.subject,
      body: this.buildEmailBody(message),
    });
  }

  private buildEmailBody(message: MailMessage): string {
    let body = `${message.greeting}\n\n`;
    body += message.line.join('\n\n');
    
    if (message.action) {
      body += `\n\n[${message.action.text}](${message.action.url})`;
    }
    
    body += `\n\n${message.salutation}`;
    return body;
  }
}

/**
 * Canal de Base de Datos
 */
@Injectable()
export class DatabaseChannel implements NotificationChannel {
  private readonly logger = new Logger(DatabaseChannel.name);

  constructor(private readonly prisma: any) {} // PrismaService

  async send(notifiable: any, notification: Notification): Promise<void> {
    if (!notification.toDatabase) {
      throw new Error('Notification does not support database channel');
    }

    const data = notification.toDatabase(notifiable);
    
    await this.prisma.notification.create({
      data: {
        type: notification.constructor.name,
        notifiableType: notifiable.constructor.name,
        notifiableId: notifiable.id,
        data: JSON.stringify(data),
        readAt: null,
      },
    });

    this.logger.log(`💾 Database notification saved for ${notifiable.id}`);
  }
}

/**
 * Canal de Slack
 */
@Injectable()
export class SlackChannel implements NotificationChannel {
  private readonly logger = new Logger(SlackChannel.name);

  async send(notifiable: any, notification: Notification): Promise<void> {
    if (!notification.toSlack) {
      throw new Error('Notification does not support Slack channel');
    }

    const slackMessage = notification.toSlack(notifiable);
    
    // Aquí integrarías con Slack API
    this.logger.log(`💬 Sending Slack message to ${slackMessage.channel}`);
    
    await this.sendSlackMessage(slackMessage);
  }

  private async sendSlackMessage(message: SlackMessage): Promise<void> {
    // Implementación real de Slack
    console.log('Slack message sent:', {
      channel: message.channel,
      content: message.content,
      username: message.username,
    });
  }
}

/**
 * Servicio Principal de Notificaciones
 */
@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private channels: Map<string, NotificationChannel> = new Map();

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly mailChannel: MailChannel,
    private readonly databaseChannel: DatabaseChannel,
    private readonly slackChannel: SlackChannel,
  ) {
    this.registerChannels();
  }

  private registerChannels(): void {
    this.channels.set('mail', this.mailChannel);
    this.channels.set('database', this.databaseChannel);
    this.channels.set('slack', this.slackChannel);
  }

  async send(notifiable: any, notification: Notification): Promise<void> {
    const channels = notification.via(notifiable);
    
    const promises = channels.map(async (channelName) => {
      const channel = this.channels.get(channelName);
      
      if (!channel) {
        this.logger.warn(`Channel ${channelName} not found`);
        return;
      }

      try {
        await channel.send(notifiable, notification);
        this.logger.log(`✅ Notification sent via ${channelName}`);
      } catch (error) {
        this.logger.error(`❌ Failed to send notification via ${channelName}:`, error);
        
        // Emitir evento de fallo para retry logic
        this.eventEmitter.emit('notification.failed', {
          notifiable,
          notification,
          channel: channelName,
          error,
        });
      }
    });

    await Promise.allSettled(promises);
  }

  async sendToMany(notifiables: any[], notification: Notification): Promise<void> {
    const promises = notifiables.map(notifiable => this.send(notifiable, notification));
    await Promise.allSettled(promises);
  }
}

/**
 * Notificaciones Específicas del Sistema de Pensiones
 */
export class PensionCalculatedNotification extends Notification {
  constructor(private readonly pension: any, private readonly result: any) {
    super();
  }

  via(notifiable: any): string[] {
    return ['mail', 'database'];
  }

  toMail(notifiable: any): MailMessage {
    return new MailMessage(notifiable.email)
      .setSubject('Cálculo de Pensión Completado')
      .setGreeting(`Estimado/a ${notifiable.name}`)
      .addLine(`Su pensión ha sido recalculada exitosamente.`)
      .addLine(`Pensión neta: $${this.result.netPension.toLocaleString()}`)
      .addLine(`Fecha de cálculo: ${new Date().toLocaleDateString()}`)
      .setAction('Ver Detalles', `${process.env.APP_URL}/pensions/${this.pension.id}`);
  }

  toDatabase(notifiable: any): any {
    return {
      title: 'Cálculo de Pensión Completado',
      message: `Su pensión ${this.pension.pensionCodeId} ha sido recalculada`,
      pensionId: this.pension.id,
      netPension: this.result.netPension,
      calculatedAt: new Date(),
    };
  }
}

export class PensionErrorNotification extends Notification {
  constructor(private readonly pension: any, private readonly error: string) {
    super();
  }

  via(notifiable: any): string[] {
    return ['slack', 'database'];
  }

  toSlack(notifiable: any): SlackMessage {
    return new SlackMessage()
      .setContent(`🚨 Error en cálculo de pensión ${this.pension.pensionCodeId}: ${this.error}`)
      .setChannel('#alerts')
      .addAttachment({
        color: 'danger',
        fields: [
          { title: 'Pensión ID', value: this.pension.id, short: true },
          { title: 'Error', value: this.error, short: false },
          { title: 'Timestamp', value: new Date().toISOString(), short: true },
        ],
      });
  }

  toDatabase(notifiable: any): any {
    return {
      title: 'Error en Cálculo de Pensión',
      message: `Error procesando pensión ${this.pension.pensionCodeId}`,
      error: this.error,
      pensionId: this.pension.id,
      level: 'error',
    };
  }
}

export class MonthlyReportNotification extends Notification {
  constructor(private readonly report: any) {
    super();
  }

  via(notifiable: any): string[] {
    return ['mail'];
  }

  toMail(notifiable: any): MailMessage {
    return new MailMessage(notifiable.email)
      .setSubject('Reporte Mensual de Pensiones')
      .setGreeting(`Estimado/a ${notifiable.name}`)
      .addLine(`Se ha generado el reporte mensual de pensiones.`)
      .addLine(`Total pensiones procesadas: ${this.report.totalPensions}`)
      .addLine(`Monto total liquidado: $${this.report.totalAmount.toLocaleString()}`)
      .setAction('Descargar Reporte', this.report.downloadUrl);
  }
}

// Uso en servicios:
// await this.notificationService.send(
//   user, 
//   new PensionCalculatedNotification(pension, result)
// );
//
// await this.notificationService.sendToMany(
//   admins,
//   new PensionErrorNotification(pension, error.message)
// );

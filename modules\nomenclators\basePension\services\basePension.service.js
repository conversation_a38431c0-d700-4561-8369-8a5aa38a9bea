/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-underscore-dangle */
const mongoose = require('mongoose');

const service = {
  async getAll() {
    try {
      return mongoose.connection.db
        .collection('basePensionRules')
        .find({})
        .toArray();
    } catch (e) {
      return [];
    }
  },
  async update(data, upd = {}) {
    try {
      for (const { label, age, value } of data) {
        await mongoose.connection.db.collection('basePensionRules').findOneAndUpdate(
          {
            label,
            age
          },
          {
            $set: { value, ...upd }
          }
        );
      }
      return { completed: true, error: null };
    } catch (e) {
      return { completed: false, error: e };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-underscore-dangle */
const moment = require('moment');

const ADD_EXTRA_DAY_OF_MEDICAL_REST = [2, 3, 4, 5, 6];
const HIGH_IN_THE_DAY = 2;
const INVALID_DATE = '0001-01-01';

const calculateTotalDaysToPayForPension = (
  transtientPensions = [],
  realDays = [],
  estimatedDays = []
) =>
  transtientPensions.map(
    ({ accidentNumber, totalEstimatedDaysToPay = 0, beneficiary, causant, pensionCodeId }) => {
      let real = realDays.find(({ idSiniestro }) => accidentNumber === idSiniestro);
      let estimated = estimatedDays.find(({ idSiniestro }) => accidentNumber === idSiniestro);

      if (!real) {
        real = { daysToPay: 0 };
      }
      if (!estimated) {
        estimated = { daysToPay: 0 };
      }

      const differentialDays = real.daysToPay - totalEstimatedDaysToPay;
      const totalDaysToPay = Math.max(0, estimated.daysToPay + differentialDays);

      return {
        beneficiary,
        causant,
        pensionCodeId,
        accidentNumber,
        totalEstimatedDaysToPay: estimated.daysToPay,
        totalTransitoryDaysToPay: totalDaysToPay
      };
    }
  );

const daysForCalculus = (monthDiferencial = 0, format = 'YYYY-MM-DD') => {
  return {
    lastDayOfPreviousMonth: moment()
      .date(0)
      .add(monthDiferencial, 'month')
      .format(format),
    firstMonthDay: moment()
      .startOf('month')
      .add(monthDiferencial, 'month')
      .format(format),
    today: moment().format(format),
    tomorrow: moment()
      .add(1, 'day')
      .add(monthDiferencial, 'month')
      .format(format),
    lastMonthDay: moment()
      .add(monthDiferencial, 'month')
      .endOf('month')
      .format(format)
  };
};

const pensionConditions = ({ fechaInicioReposo, fechaAlta }) => {
  return {
    firstCondition: (firsBetween, secondBetween) => {
      const result =
        moment(fechaInicioReposo).isBetween(...firsBetween) &&
        moment(fechaAlta).isBetween(...secondBetween);
      return result;
    },

    secondCondition: (before, between) => {
      const result =
        moment(fechaInicioReposo).isBefore(before) && moment(fechaAlta).isBetween(...between);
      return result;
    },

    thirdCondition: (between, after) => {
      const result =
        moment(fechaInicioReposo).isBetween(...between) &&
        (fechaAlta === INVALID_DATE || moment(fechaAlta).isAfter(after));
      return result;
    },

    fourthCondition: (before, after) => {
      const result =
        moment(fechaInicioReposo).isBefore(before) &&
        (fechaAlta === INVALID_DATE || moment(fechaAlta).isAfter(after));
      return result;
    }
  };
};
const getDaysBetweenDates = (startDate, endDate) => {
  if (!startDate || !endDate) {
    return 0;
  }

  const start = moment(startDate);
  const end = moment(endDate);
  return end.diff(start, 'days');
};

const calculatePayDays = (startDate, endDate, tipoAlta) => {
  return (
    getDaysBetweenDates(startDate, endDate) +
    (ADD_EXTRA_DAY_OF_MEDICAL_REST.includes(+tipoAlta) ? 1 : 0)
  );
};

const applyConditions = (
  { lastDayOfPreviousMonth, tomorrow, today, firstMonthDay, lastMonthDay },
  isCurrentMonth,
  Logger
) => arrayOfMedicalRest =>
  arrayOfMedicalRest.reduce(
    (acc, curr) => {
      const { daysToPay = 0 } = acc;
      const { fechaInicioReposo, fechaAlta, tipoAlta, idSiniestro } = curr;
      const {
        firstCondition,
        secondCondition,
        thirdCondition,
        fourthCondition
      } = pensionConditions({ fechaInicioReposo, fechaAlta });

      const defaultBetween = isCurrentMonth
        ? [lastDayOfPreviousMonth, tomorrow]
        : [lastDayOfPreviousMonth, moment(lastMonthDay).add(1, 'day')];

      const after = isCurrentMonth ? today : lastMonthDay;
      let estimatedDays = 0;

      if (firstCondition(defaultBetween, defaultBetween)) {
        estimatedDays = calculatePayDays(fechaInicioReposo, fechaAlta, tipoAlta);
        Logger.info('apply firstCondition', { estimatedDays, idSiniestro });
      }

      if (secondCondition(firstMonthDay, defaultBetween)) {
        estimatedDays = calculatePayDays(firstMonthDay, fechaAlta, tipoAlta);
        Logger.info('apply secondCondition', { estimatedDays, idSiniestro });
      }

      if (thirdCondition(defaultBetween, after)) {
        estimatedDays = calculatePayDays(fechaInicioReposo, lastMonthDay, HIGH_IN_THE_DAY);
        Logger.info('apply thirdCondition', { estimatedDays, idSiniestro });
      }

      if (fourthCondition(firstMonthDay, after)) {
        estimatedDays = moment(after).daysInMonth();
        Logger.info('apply fourthCondition', { estimatedDays, idSiniestro });
      }

      return {
        idSiniestro: parseInt(idSiniestro, 10),
        daysToPay: estimatedDays + daysToPay
      };
    },
    {
      idSiniestro: '',
      daysToPay: 0
    }
  );

module.exports = {
  daysForCalculus,
  pensionConditions,
  applyConditions,
  calculateTotalDaysToPayForPension
};

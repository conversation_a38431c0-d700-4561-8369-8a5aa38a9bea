const calculate = pension => {
  const totaltaxableAssetsByReason = {};
  const taxableAssetRegex = /[ií<PERSON><PERSON>]mpon[íìïi]ble/i;
  const { discountsAndAssets = {} } = pension;
  const { assetsNonFormulable = [] } = discountsAndAssets;
  const taxableAssets = assetsNonFormulable.filter(({ assetType }) =>
    taxableAssetRegex.test(assetType)
  );
  taxableAssets.forEach(asset => {
    const { reason, amount } = asset;
    if (reason) {
      totaltaxableAssetsByReason[reason] = (totaltaxableAssetsByReason[reason] || 0) + amount;
    }
  });
  const taxableNonFormulableByReason = Object.entries(
    totaltaxableAssetsByReason
  ).map(([reason, amount]) => ({ reason, amount }));

  return { ...pension, assets: { ...pension.assets, taxableNonFormulableByReason } };
};

module.exports = calculate;

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const PensionsModel = require('../../../models/pension');
const ProcessedJobModel = require('../../sharedFiles/models/processedJob');
const pensionService = require('../../pensions/services/pension.service');
const service = require('./dbService');

const pensionsData = require('../../../resources/resetValuePensions.json');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);

  let mocks;
  beforeEach(() => {
    mocks = {
      startTransaction: jest.fn().mockResolvedValue(true),
      commitTransaction: jest.fn().mockResolvedValue(true),
      abortTransaction: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(PensionsModel, 'startSession').mockImplementationOnce(() => mocks);
  });

  it('should reset fields of enabled pensions', async () => {
    await PensionsModel.insertMany([
      { ...pensionsData[0]._doc, enabled: true },
      { ...pensionsData[1]._doc, enabled: false },
      { ...pensionsData[2]._doc, enabled: true },
      { ...pensionsData[3]._doc, enabled: true }
    ]).catch(err => console.log(err));

    const { completed, error } = await service.resetPensionValues(pensionService);
    const { result } = await pensionService.getAllWithFilter({}, {});

    expect(completed).toBe(true);
    expect(error).toBe(null);

    expect(result[0].bankRejected).toBe('No');
    expect(result[0].paycheckRefunded).toBe('No');
    expect(result[0].validatedStudyPeriod).toBe('No');
    expect(result[0].article41).toBe(0);
    expect(result[0].numberOfCharges).toBe(0);
    expect(result[0].numberOfChargesExternal).toBe(0);
    expect(result[0].rejectionHealthExemptionAmount).toBe(0);
    expect(result[0].ChangeOfPensionTypeDueToCharges).toBe(false);
    expect(result[0].rejectionHealthExemptionAmount).toBe(0);
    expect(result[0].rejectionIPS).toBe(false);

    expect(result[0].assets.aps).toBe(0);
    expect(result[0].assets.forFamilyAssignment).toBe(0);
    expect(result[0].assets.christmasBonus).toBe(0);
    expect(result[0].assets.nationalHolidaysBonus).toBe(0);
    expect(result[0].assets.winterBonus).toBe(0);
    expect(result[0].assets.marriageBonus).toBe(0);
    expect(result[0].assets.healthExemption).toBe('No');
    expect(result[0].assets.healthDiscount).toBe('No');
    expect(result[0].assets.netTotalNonFormulable).toBe(0);
    expect(result[0].assets.taxableTotalNonFormulable).toBe(0);

    expect(result[0].retroactiveAmounts.forSurvival).toBe(0);
    expect(result[0].retroactiveAmounts.forDisability).toBe(0);
    expect(result[0].retroactiveAmounts.forInstitutionalPatient).toBe(0);
    expect(result[0].retroactiveAmounts.forBonuses).toBe(0);
    expect(result[0].retroactiveAmounts.forBasePension).toBe(0);
    expect(result[0].retroactiveAmounts.forArticle40).toBe(0);
    expect(result[0].retroactiveAmounts.forArticle41).toBe(0);
    expect(result[0].retroactiveAmounts.forFamilyAssignment).toBe(0);
    expect(result[0].retroactiveAmounts.forTaxableTotalNonFormulableAssets).toBe(0);
    expect(result[0].retroactiveAmounts.forNetTotalNonFormulableAssets).toBe(0);
    expect(result[0].retroactiveAmounts.forTotalNonFormulableDiscounts).toBe(0);
    expect(result[0].retroactiveAmounts.forPayCheck).toBe(0);
    expect(result[0].retroactiveAmounts.forRejection).toBe(0);

    expect(result[0].reservedAmounts.forSurvival).toBe(0);
    expect(result[0].reservedAmounts.forDisability).toBe(0);
    expect(result[0].reservedAmounts.forInstitutionalPatient).toBe(0);
    expect(result[0].reservedAmounts.forBasePension).toBe(0);
    expect(result[0].reservedAmounts.forArticle40).toBe(0);
    expect(result[0].reservedAmounts.forArticle41).toBe(0);
    expect(result[0].reservedAmounts.forTaxableTotalNonFormulableAssets).toBe(0);
    expect(result[0].reservedAmounts.forNetTotalNonFormulableAssets).toBe(0);
    expect(result[0].reservedAmounts.forTotalNonFormulableDiscounts).toBe(0);
    expect(result[0].reservedAmounts.forBonuses).toBe(0);
    expect(result[0].reservedAmounts.forPayCheck).toBe(0);
    expect(result[0].reservedAmounts.forRejection).toBe(0);

    expect(result[0].discounts.socialCredits18).toBe(0);
    expect(result[0].discounts.socialCreditsLosAndes).toBe(0);
    expect(result[0].discounts.socialCreditsLosHeroes).toBe(0);
    expect(result[0].discounts.socialCreditsLaAraucana).toBe(0);
    expect(result[0].discounts.othersLosAndes).toBe(0);
    expect(result[0].discounts.othersLosHeroes).toBe(0);
    expect(result[0].discounts.healthLoan).toBe(0);
    expect(result[0].discounts.health).toBe(0);
    expect(result[0].discounts.afp).toBe(0);
    expect(result[0].discounts.onePercentAdjusted).toBe(0);
    expect(result[0].discounts.onePercentLaAraucana).toBe('No');
    expect(result[0].discounts.onePercent18).toBe('No');
    expect(result[0].discounts.onePercentLosAndes).toBe('No');
    expect(result[0].discounts.onePercentLosHeroes).toBe('No');
    expect(result[0].discounts.totalNonFormulable).toBe(0);

    expect(result[0].apsInfo.apsResolutionNumber).toBe(0);
    expect(result[0].apsInfo.apsResolutionDate).toBe(0);
    expect(result[0].apsInfo.apsPaymentUniqueId).toBe(0);
    expect(result[0].apsInfo.apsTransferCode).toBe(0);
    expect(result[0].apsInfo.apsOrigin).toBe('');

    expect(result[0].currentCapitalCalculation.basePensionCapital).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw19578).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw20102).toBe(0);
    expect(result[0].currentCapitalCalculation.basePensionWorkingCapital).toBe(0);
    expect(result[0].currentCapitalCalculation.basePensionNotWorkingCapital).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalLaw19578).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalLaw19578).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalLaw20102).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalLaw20102).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalPBIpc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw19578Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw19953Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalLaw20102Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.totalCapitalIpc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19403).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19539).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalBonusLaw19403).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalBonusLaw19403).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalBonusLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalBonusLaw19953).toBe(0);
    expect(result[0].currentCapitalCalculation.workingCapitalBonusLaw19539).toBe(0);
    expect(result[0].currentCapitalCalculation.notWorkingCapitalBonusLaw19539).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19403Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19953Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalBonusLaw19539Ipc).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalTotalBonus).toBe(0);
    expect(result[0].currentCapitalCalculation.capitalTotalBonusIpc).toBe(0);
    expect(result[0].currentCapitalCalculation.totalCapital).toBe(0);
    expect(result[0].numberOfChargesArticle41).toBe(0);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await PensionsModel.deleteMany({}).catch(err => console.log(err));
    await ProcessedJobModel.deleteMany({}).catch(err => console.log(err));
  });

  afterAll(afterAllTests);
});

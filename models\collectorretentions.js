const mongoose = require('mongoose');

const { Schema } = mongoose;

const collectorRetentionsSchema = new Schema(
  {
    beneficiaryRutOrigen: { type: String },
    causantRutOrigen: { type: String },
    collectorRut: { type: String },
    name: { type: String },
    lastName: { type: String },
    mothersLastName: { type: String },
    address: { type: String },
    commune: { type: String },
    city: { type: String },
    paymentGateway: { type: String },
    accountNumber: { type: String },
    bank: { type: String },
    branchOffice: { type: String },
    amount: { type: Number, min: 0, default: 0 }
  },
  { timestamps: true }
);

collectorRetentionsSchema.index({ beneficiaryRutOrigen: 1, causantRutOrigen: 1, collectorRut: 1 });
module.exports = mongoose.model('collectorretentions', collectorRetentionsSchema);

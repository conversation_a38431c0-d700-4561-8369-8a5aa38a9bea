/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const {
  reposesRules,
  isAvailableToReactivate,
  isHigherThanOtherDate,
  isHigherThanActuallyDate,
  reactivatePension
} = require('./transientHelper');
const reposes = require('../../../../resources/reposes.json');
const wrongReposes = require('../../../../resources/wrongReposes.json');
const incapacities = require('../../../../resources/incapacities.json');
const pensions = require('../../../../resources/pensions.json');
const PensionsModel = require('../../../../models/pension');

describe('transient rules', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});

  it('repose rules no match', async () => {
    const expected = reposesRules(reposes[0]);
    expect(expected).toBe(false);
  });

  it('repose rules match', async () => {
    const expected = reposesRules(reposes[1]);
    expect(expected).toBe(true);
  });

  it('is available to reactivate', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { result } = isAvailableToReactivate(reposes, endDateOfValidity);
    expect(result).toBe(true);
  });

  it('isnt available to reactivate', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { result } = isAvailableToReactivate(wrongReposes, endDateOfValidity);
    expect(result).toBe(false);
  });

  it('isnt available to reactivate by empty reposes ', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { result } = isAvailableToReactivate([], endDateOfValidity);
    expect(result).toBe(false);
  });

  it('isnt available to reactivate dont have reposesAfterEndValidity ', async () => {
    const endDateOfValidity = '2020-09-12T00:00:00';
    const { result } = isAvailableToReactivate(reposes, endDateOfValidity);
    expect(result).toBe(false);
  });

  it('is higher than other date', async () => {
    const date = '2012-09-13T00:00:00';
    const compareDate = '2012-09-12T00:00:00';

    const result = isHigherThanOtherDate(date, compareDate);
    expect(result).toBe(true);
  });

  it('isnt higher than other date', async () => {
    const date = '2012-09-13T00:00:00';
    const compareDate = '2013-09-12T00:00:00';

    const result = isHigherThanOtherDate(date, compareDate);
    expect(result).toBe(false);
  });
  it('isnt higher than actually date', async () => {
    const date = '2012-09-13T00:00:00';

    const result = isHigherThanActuallyDate(date);
    expect(result).toBe(false);
  });
  it('is higher than actually date', async () => {
    const date = '2081-09-13T00:00:00';
    const result = isHigherThanActuallyDate(date);
    expect(result).toBe(true);
  });

  it('get pension available to reactivate by vigente vitalicia and modalidad n2', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { fechaAlta } = isAvailableToReactivate(reposes, endDateOfValidity);
    const pension = await PensionsModel.create({ ...pensions[0], enabled: true });
    expect(pension._id).toBeDefined();

    const { validityType, inactivationReason } = reactivatePension({
      incapacidad: incapacities,
      fechaAlta,
      pension
    });
    expect(validityType).toBe('Vigente vitalicia');
    expect(inactivationReason).toBe('Alta médica');
  });

  it('get pension available to reactivate by vitalicia and fechaAlta higher than actually date', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    let { fechaAlta } = isAvailableToReactivate(reposes, endDateOfValidity);
    fechaAlta = moment().add(1, 'years');
    const pension = await PensionsModel.create({ ...pensions[0], enabled: true });
    expect(pension._id).toBeDefined();

    const { validityType } = reactivatePension({
      incapacidad: incapacities,
      fechaAlta,
      pension
    });
    expect(validityType).toBe('Vigente vitalicia');
  });

  it('get pension available to reactivate by vitalicia and fechaAlta 0001-01-01', async () => {
    const fechaAlta = new Date('0001-01-01T00:00:00');

    const pension = await PensionsModel.create({ ...pensions[0], enabled: true });
    expect(pension._id).toBeDefined();

    const { validityType, endDateOfValidity } = reactivatePension({
      incapacidad: incapacities,
      fechaAlta,
      pension
    });
    expect(validityType).toBe('Vigente vitalicia');
    expect(endDateOfValidity).toBe(pensions[0].endDateOfTheoricalValidity);
  });

  it('get pension available to reactivate by vitalicia and without incapacidad', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { fechaAlta } = isAvailableToReactivate(reposes, endDateOfValidity);
    const pension = await PensionsModel.create({ ...pensions[0], enabled: true });
    expect(pension._id).toBeDefined();

    const { validityType, inactivationReason } = reactivatePension({
      incapacidad: [],
      fechaAlta,
      pension
    });
    expect(validityType).toBe('Vigente vitalicia');
    expect(inactivationReason).toBe('Alta médica');
  });

  it('get pension available to reactivate by vitalicia and alta medica without make match', async () => {
    const endDateOfValidity = '2012-09-12T00:00:00';
    const { fechaAlta } = isAvailableToReactivate(reposes, endDateOfValidity);
    const pension = await PensionsModel.create({ ...pensions[0], enabled: true });
    expect(pension._id).toBeDefined();

    const { validityType, inactivationReason } = reactivatePension({
      incapacidad: [incapacities[2]],
      fechaAlta,
      pension
    });
    expect(validityType).toBe('Vigente vitalicia');
    expect(inactivationReason).toBe('Alta médica');
  });

  afterAll(afterAllTests);
});

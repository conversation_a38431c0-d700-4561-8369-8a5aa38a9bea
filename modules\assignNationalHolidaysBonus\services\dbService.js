const mongoose = require('mongoose');
const pensionService = require('../../pensions/services/pension.service');

const NATIONAL_HOLIDAYS_BONUS_TYPE_SEARCH = /^Aguinaldo de fiestas patrias$/i;
const NOT_PAY_BONUS_TEST = /No/i;

const NATIONAL_HOLIDAYS_BONUS_TYPE = 'Aguinaldo de fiestas patrias';
const YES_PAY_BONUS = 'Si';
const NOT_PAY_BONUS = 'No';

const formatTypeRuler = type =>
  type
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .replace(/[èé]/gi, 'e')
    .replace(/[áà]/gi, 'a')
    .toLowerCase()
    .trim();

const reduceRulesBonusToObj = rules => {
  return rules.reduce((rulesObj, rule) => {
    const { label, value } = rule;
    const key = formatTypeRuler(label);
    return { ...rulesObj, [key]: value };
  }, {});
};

const assignBonusPensionerNew = (pensioner, bonusAmount) => {
  const {
    temporaryimportedbonuspensioners,
    assets,
    payBonus,
    basePension = 0,
    article40 = 0,
    law19403 = 0,
    law19539 = 0,
    law19953 = 0,
    amountOtherPension = 0,
    dl1026 = 0,
    ...pensionerAux
  } = pensioner;

  const basePensionPlusLaws = basePension + article40 + law19403 + law19539 + law19953;
  if (dl1026 > basePensionPlusLaws || amountOtherPension > basePensionPlusLaws) {
    return {
      ...pensionerAux,
      payBonus: NOT_PAY_BONUS,
      assets: { ...assets, nationalHolidaysBonus: 0 }
    };
  }

  return {
    ...pensionerAux,
    payBonus: YES_PAY_BONUS,
    assets: { ...assets, nationalHolidaysBonus: bonusAmount }
  };
};

const assignBonusPensionerOld = (pensioner, bonusAmount) => {
  const { temporaryimportedbonuspensioners, assets, ...pensionerAux } = pensioner;

  const pensionIPS = temporaryimportedbonuspensioners.find(
    row => row.institutionCode === '34' && NOT_PAY_BONUS_TEST.test(row.payBonus)
  );
  if (pensionIPS) {
    return {
      ...pensionerAux,
      payBonus: NOT_PAY_BONUS,
      assets: { ...assets, nationalHolidaysBonus: 0 }
    };
  }

  return {
    ...pensionerAux,
    payBonus: YES_PAY_BONUS,
    assets: { ...assets, nationalHolidaysBonus: bonusAmount }
  };
};

const service = {
  async getRulesNationalHolidaysBonus() {
    try {
      return mongoose.connection.db
        .collection('basePensionRules')
        .find({ label: NATIONAL_HOLIDAYS_BONUS_TYPE_SEARCH })
        .toArray();
    } catch (e) {
      return [];
    }
  },

  async payBonusNationalHolidays() {
    const pensioners = await pensionService.filterPensionersAssignBonus();
    const rules = await this.getRulesNationalHolidaysBonus();
    const rulesObj = reduceRulesBonusToObj(rules);
    const bonusAmount = +rulesObj[formatTypeRuler(NATIONAL_HOLIDAYS_BONUS_TYPE)] || 0;

    return pensioners.map(pensioner => {
      const { payBonus } = pensioner;

      if (NOT_PAY_BONUS_TEST.test(payBonus)) {
        return assignBonusPensionerNew(pensioner, bonusAmount);
      }

      return assignBonusPensionerOld(pensioner, bonusAmount);
    });
  },

  async setPensionersBonusNationalHolidays() {
    const pensioners = await this.payBonusNationalHolidays();
    const { completed, error } = await pensionService.updatePensionsByIdBonus(pensioners);

    return { completed, error, pensioners };
  }
};

module.exports = { ...service };

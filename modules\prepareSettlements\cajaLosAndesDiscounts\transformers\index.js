const transformDataBeforeBulk = data => {
  const year = new Date().getFullYear();
  const month = new Date().getMonth() + 1;
  return data.reduce(
    (accumulator, { rut, movementType, creditDiscountAmount, anotherDiscountAmount }) => {
      if (!rut || (!movementType && !creditDiscountAmount && !anotherDiscountAmount)) {
        return accumulator;
      }
      const rutFormatted = rut
        .replace(/[^0-9k]/gi, '')
        .replace(/(\d{7,8})(\d|k)/i, '$1-$2')
        .toUpperCase();
      const item = {
        movementType: movementType === 1 || movementType === 3 ? movementType : undefined,
        creditDiscountAmount: parseFloat(creditDiscountAmount),
        anotherDiscountAmount: parseFloat(anotherDiscountAmount)
      };
      Object.keys(item).forEach(key => {
        if (item[key] == null || Number.isNaN(item[key])) {
          delete item[key];
        }
      });
      const findIndex = accumulator.findIndex(xObj => xObj.rut === rutFormatted);
      if (findIndex > -1) {
        accumulator[findIndex] = {
          ...accumulator[findIndex],
          year,
          month,
          ...item
        };
      } else {
        accumulator.push({ rut: rutFormatted, year, month, ...item });
      }
      return accumulator;
    },
    []
  );
};

module.exports = { transformDataBeforeBulk };

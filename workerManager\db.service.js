/* eslint-disable no-console */
const WorkersModel = require('../models/workers');

module.exports = {
  async getDependencies(name, Logger) {
    const worker = await WorkersModel.findOne({ name })
      .lean()
      .then(w => {
        if (!w) {
          Logger.info(`There is no dependencies for worker ${name}`);
          return { dependencies: {} };
        }
        return w;
      })
      .catch(err => {
        Logger.error(err);
        return { dependencies: {} };
      });
    return worker.dependencies;
  },
  async restartDependencies(name, dependencies) {
    return this.updateDependenciesExecution(
      name,
      dependencies.reduce(
        (instance, dependencyName) => ({ [dependencyName]: false, ...instance }),
        {}
      )
    ).catch(err => {
      console.error(err);
      throw new Error(err);
    });
  },

  updateDependenciesExecution: async (name, dependencies) =>
    WorkersModel.findOneAndUpdate({ name }, { $set: { dependencies, name } }, { upsert: true }),

  cleanModel: async () => WorkersModel.remove({})
};

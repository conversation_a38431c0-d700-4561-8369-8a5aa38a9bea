/* eslint-disable no-plusplus */
const isString = obj => Object.prototype.toString.call(obj) === '[object String]';

const safeObject = obj => {
  const copyObj = JSON.parse(JSON.stringify(obj));
  delete copyObj.password;
  delete copyObj.confirmPassword;
  delete copyObj.newPassword;
  delete copyObj.oldPassword;

  return copyObj;
};

const safeValue = (obj, path, defaultValue = '') => {
  try {
    const paths = path.split('.');
    let current = obj;

    for (let i = 0; i < paths.length; ++i) {
      if (current[paths[i]] === undefined) {
        return defaultValue;
      }

      current = current[paths[i]];
    }

    return current;
  } catch (error) {
    return defaultValue;
  }
};

module.exports = {
  isString,
  safeValue,
  safeObject
};

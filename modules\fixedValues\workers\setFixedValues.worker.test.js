/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const workerModule = require('./setFixedValues.worker');
const LogModel = require('../../sharedFiles/models/processedJob');

describe('Pension service Model Test', () => {
  beforeAll(beforeAllTests);
  let setFixedValues;
  let done;
  let Logger;
  let logService;
  let service;

  beforeEach(() => {
    setFixedValues = jest.fn();
    done = jest.fn();
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    logService = {
      saveLog: jest.fn().mockResolvedValue(true),
      existsLog: jest.fn(() => Promise.resolve(true)),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    service = {
      basePensionAndArticlesFixedValues: jest.fn().mockResolvedValue({ error: null })
    };
  });

  it('should run setFixedValues worker', async () => {
    await workerModule.workerFn({ Logger, done, service, logService, setFixedValues });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(Logger.error).not.toHaveBeenCalled();
    expect(service.basePensionAndArticlesFixedValues).toHaveBeenCalledWith(setFixedValues);
    expect(done).toHaveBeenCalled();
  });

  it('should log error if there is any', async () => {
    logService.saveLog = jest.fn(() => Promise.reject(new Error()));
    await workerModule.workerFn({ Logger, done, service, logService, setFixedValues });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.basePensionAndArticlesFixedValues).toHaveBeenCalledTimes(1);
    expect(logService.saveLog).toHaveBeenCalled();
    expect(Logger.error).toHaveBeenCalled();
    expect(done).toHaveBeenCalled();
  });

  it('Process was already executed', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, done, service, logService, setFixedValues });
    expect(logService.existsLogAndRetry).toHaveBeenCalled();
    expect(logService.existsLog).not.toHaveBeenCalled();
    expect(Logger.info).toHaveBeenCalled();
    expect(service.basePensionAndArticlesFixedValues).toHaveBeenCalledTimes(0);
    expect(logService.saveLog).toHaveBeenCalledTimes(0);
    expect(Logger.error).toHaveBeenCalledTimes(0);
    expect(done).toHaveBeenCalled();
  });

  afterEach(async () => {
    jest.restoreAllMocks();
    await LogModel.deleteMany({}).catch(error => console.error(error));
  });

  afterAll(afterAllTests);
});

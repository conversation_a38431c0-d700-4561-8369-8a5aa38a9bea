/* eslint-disable no-restricted-globals */
/* eslint-disable no-unused-expressions */
/* eslint no-return-await: "error" */
const IsapreModel = require('../models/isapre');
const PensionModel = require('../../../../models/pension');
const TemporaryIsaprePortalModel = require('../models/temporaryIsaprePortal');

const service = {
  async wasExecutedIsaprePortalProcess() {
    const count = await TemporaryIsaprePortalModel.countDocuments().exec();

    return {
      wasLoadData: count > 0,
      completed: count > 0,
      isError: false,
      error: null
    };
  },
  async updateIsapre({ id, ...isapreData }) {
    const criteria = { enabled: false, $or: [{ code: isapreData.code }, { rut: isapreData.rut }] };
    try {
      const oldIsapre = await IsapreModel.findOne(criteria).exec();
      if (oldIsapre && oldIsapre.id) {
        await IsapreModel.remove({ id: oldIsapre.id }).exec();
      }
      const data = await IsapreModel.findOneAndUpdate(
        { id, enabled: true },
        { $set: { ...isapreData } },
        { returnNewDocument: true, upsert: true, new: true }
      ).exec();
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },

  async insertTemporaryIsaprePortal(dataToInsert) {
    try {
      await TemporaryIsaprePortalModel.deleteMany({}).exec();
      const result = await TemporaryIsaprePortalModel.insertMany(dataToInsert);
      return { isError: !result, result };
    } catch (error) {
      return { isError: true, error };
    }
  },
  async totalTemporaryPensionsOnCurrentCount() {
    const currentCount = await TemporaryIsaprePortalModel.countDocuments();
    return currentCount;
  },
  async bulkUpdate() {
    const result = [];
    const notMatchIsapreCodes = [];

    if ((await this.totalTemporaryPensionsOnCurrentCount()) === 0) {
      return {
        isError: true,
        error: { code: 404, message: 'No se ha realizado el proceso de importación' },
        result
      };
    }

    const session = await PensionModel.startSession();
    session.startTransaction();
    try {
      const bulk = PensionModel.collection.initializeOrderedBulkOp();
      const temporaryResult = await TemporaryIsaprePortalModel.find().lean();

      // eslint-disable-next-line no-restricted-syntax
      for await (const document of temporaryResult) {
        const { _id, __v, ...data } = document;
        const { isapreId, totalDiscount, affiliateRut } = data;

        const isapre = await IsapreModel.findOne({ code: isapreId }, { name: 1 }).lean();

        if (!isapre) {
          notMatchIsapreCodes.push(isapreId);
        } else {
          bulk.find({ enabled: true, 'beneficiary.rut': affiliateRut }).update({
            $set: {
              healthAffiliation: isapre.name,
              'discounts.healthUF': totalDiscount
            }
          });
        }
      }

      if (notMatchIsapreCodes.length)
        return {
          isError: true,
          error: {
            code: 404,
            message: `No se encontraron Isapres registradas con los códigos (${notMatchIsapreCodes.toString()})`
          }
        };
      if (bulk.length) await bulk.execute();
      await session.commitTransaction();
      await TemporaryIsaprePortalModel.collection.deleteMany();
      return { isError: false, result, executionCompleted: true };
    } catch (e) {
      await session.abortTransaction();
      return { isError: true, error: e };
    }
  },
  async createIsapre(isapreData) {
    const criteria = { enabled: false, $or: [{ code: isapreData.code }, { rut: isapreData.rut }] };
    try {
      const result = await IsapreModel.findOne(criteria).exec();
      if (result) {
        const savedIsapre = await IsapreModel.findOneAndUpdate(
          criteria,
          { ...isapreData, enabled: true },
          {
            new: true,
            runValidators: true
          }
        ).exec();
        return { result: savedIsapre };
      }
      const data = await IsapreModel.create(isapreData);
      return { result: data };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async deleteIsapre(id) {
    try {
      const data = await IsapreModel.update(
        { id, enabled: true },
        { $set: { enabled: false, updatedAt: new Date() } }
      ).exec();
      return { result: data.nModified };
    } catch (error) {
      return { error, isError: true };
    }
  },
  async getIsapres(query = { enabled: true }) {
    return IsapreModel.find(query)
      .then(data => {
        return { result: data };
      })
      .catch(error => ({
        isError: true,
        error
      }));
  }
};

module.exports = { ...service };

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const setNumberOfCharges = require('./numberOfCharges');
const pensions = require('../../../../resources/pensions.json');

const widowTypes = [
  'Pensión de viudez con hijos',
  'Pensión de viudez sin hijos',
  'Pensión de madre de hijo de filiación no matrimonial con hijos',
  'Pensión de madre de hijo de filiación no matrimonial sin hijos'
];

const disabilityTypes = [
  'Pensión por accidente de trabajo',
  'Pensión por accidente de trayecto',
  'Pensión por enfermedad profesional'
];

describe('Add number of charges to widowhood and disability pension type', () => {
  beforeAll(beforeAllTests);

  it('Should add numberOfCharges field if pension matches widowhood criteria', () => {
    const widowhoodCriteria = {
      pensionType: widowTypes[Math.floor(Math.random() * widowTypes.length)],
      validityType: 'Pensión de viudez con hijos'
    };
    const pension = {
      ...pensions[0],
      ...widowhoodCriteria
    };
    delete pension.numberOfCharges;

    const { pensionType, validityType } = widowhoodCriteria;
    const modifiedPension = setNumberOfCharges(pension, pensionType, validityType);

    expect(modifiedPension.numberOfCharges).toBeDefined();
    expect(modifiedPension.numberOfCharges).toBe(0);
  });

  it('Should add numberOfCharges field if pension matches disability criteria', () => {
    const disabilityCriteria = {
      pensionType: disabilityTypes[Math.floor(Math.random() * disabilityTypes.length)],
      validityType: 'Hasta la jubilación'
    };
    const pension = {
      ...pensions[0],
      ...disabilityCriteria
    };
    delete pension.numberOfCharges;

    const { pensionType, validityType } = disabilityCriteria;
    const modifiedPension = setNumberOfCharges(pension, pensionType, validityType);

    expect(modifiedPension.numberOfCharges).toBeDefined();
    expect(modifiedPension.numberOfCharges).toBe(0);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  afterAll(afterAllTests);
});

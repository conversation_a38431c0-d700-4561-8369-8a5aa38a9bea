const readSFTPFile = async ({ file, fileHelpers, sharedHelpers }) => {
  let lines = [];
  try {
    lines = await fileHelpers.readLines(file);
    if (lines.length)
      lines = lines
        .map(
          ([
            rut,
            resultFlag,
            lastName,
            mothersLastName,
            name,
            datheOfBirth,
            gender,
            country,
            maritalStatus
          ]) => {
            return {
              rut: sharedHelpers.getFormatedRut(rut, country),
              resultFlag,
              lastName: lastName.trim(),
              mothersLastName: mothersLastName.trim(),
              name: name.trim(),
              dateOfBirth: sharedHelpers.getFormatedDate(datheOfBirth),
              gender,
              maritalStatus
            };
          }
        )
        .filter(line => line.resultFlag !== '5');

    return { lines };
  } catch (error) {
    return { lines: [], error };
  }
};

module.exports = { readSFTPFile };

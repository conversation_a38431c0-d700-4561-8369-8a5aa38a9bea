const fs = require('fs').promises;
const moment = require('moment');

const service = require('../services/dbService');
const generateBankPayroll = require('../../bankPayRoll/services/dbService');
const pensionService = require('../../pensions/services/pension.service');
const storageService = require('../../fileStorage/services');
const ufService = require('../../UFvalue/services/ufValue.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');

module.exports = {
  name: 'generateAndUploadBankFile',
  worker: deps =>
    workerModule.workerFn({
      service,
      moment,
      serviceGenerateFile: generateBankPayroll,
      pensionService,
      storageService,
      ufService,
      fsClient: fs,
      logService,
      ...deps
    }),
  repeatInterval: process.env.CRON_GENERATE_AND_UPLOAD_BANK_FILE_FREQUENCY,
  description:
    'Cron que crea y envía archivo txt con la nómina de pago de pensionados al Banco de Chile',
  endPoint: 'generateanduploadbankfile',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

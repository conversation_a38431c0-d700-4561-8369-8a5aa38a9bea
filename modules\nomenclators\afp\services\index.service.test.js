/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const AfpsModel = require('../models/afp');
const service = require('./index.service');

let Logger;
describe('Afps nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };

  it('should create', async () => {
    const { error } = await service.createAfp({
      id: 'id-1-A',
      name: 'Afp x',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '10'
    });
    const { result, isError } = await service.getAfps();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });
  it('should get afps with specific fields', async () => {
    const { error } = await service.createAfp({
      id: 'id-1-A',
      name: 'Afp x',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '10'
    });
    const { result } = await service.getAfpsWithFilters(
      {
        id: 'id-1-A'
      },
      { name: 1, percentage: 1 }
    );

    expect(error).not.toBe(true);
    expect(result.length).toBe(1);
    expect(result[0].id).not.toBeDefined();
    expect(result[0].name).toBe('Afp x');
    expect(result[0].percentage).toBe(12.02);
    expect(result[0].code).not.toBeDefined();
  });
  it('should find one and update', async () => {
    await AfpsModel.create({
      id: 'id-1-B',
      name: 'Afp x',
      rut: '16.940.573-5',
      percentage: 12.02,
      code: '11',
      enabled: false
    });

    const newAfpData = {
      id: 'id-1-A',
      name: 'Afp x',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '10',
      enabled: true
    };
    // create and save one  document
    await AfpsModel.create(newAfpData);

    const { error } = await service.updateAfp({
      ...newAfpData,
      name: 'Afp z',
      code: '11'
    });
    const { result, isError } = await service.getAfps();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('Afp z');
  });

  it('should delete a afp', async () => {
    // create and save one  document
    const afp = await AfpsModel.create({
      id: 'id-1-A',
      name: 'Afp x',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '10',
      enabled: true
    });
    const { id, createdAt, updatedAt } = { ...afp.toObject() };

    const { error } = await service.deleteAfp(id);

    const [resultWithoutAfps, resultWithAfps] = await Promise.all([
      service.getAfps(),
      service.getAfps({ enabled: false })
    ]);
    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());

    expect(resultWithoutAfps.result.length).toBe(0);
    expect(resultWithAfps.result.length).toBe(1);
    expect(resultWithAfps.result[0].enabled).toEqual(false);
  });

  it('can´t  duplicate code ', async () => {
    // create and save one  document
    await AfpsModel.create({
      id: 'id-1-A',
      name: 'Afp 1',
      rut: '16.940.573-5',
      percentage: 12.02,
      code: '10',
      enabled: true
    });
    const afp2 = await AfpsModel.create({
      id: 'id-1-B',
      name: 'Afp 2',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '11',
      enabled: true
    });

    const { id, code } = { ...afp2.toObject(), code: '10' };
    const { error } = await service.updateAfp({
      id,
      code
    });
    const { result } = await service.getAfps();

    expect(result[1].code).toBe('11');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  it('can´t  duplicate rut', async () => {
    // create and save one  document
    await AfpsModel.create({
      id: 'id-1-A',
      name: 'Afp 1',
      rut: '16.940.573-5',
      percentage: 12.02,
      code: '10',
      enabled: true
    });
    const afp2 = await AfpsModel.create({
      id: 'id-1-B',
      name: 'Afp 2',
      rut: '16.913.485-5',
      percentage: 12.02,
      code: '11',
      enabled: true
    });

    const { id, rut } = { ...afp2.toObject(), rut: '16.940.573-5' };
    const { error } = await service.updateAfp({
      id,
      rut
    });
    const { result } = await service.getAfps();

    expect(result[1].rut).toBe('16.913.485-5');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  afterEach(async () => {
    try {
      await AfpsModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

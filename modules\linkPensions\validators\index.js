/* eslint-disable no-restricted-globals */
const EMPTY_MESSAGE = 'Existen campos obligatorios vacíos';
const INVALID_MESSAGE = 'Dato es inválido';
const INVALID_DATE = 'fecha inválida';
const RUT_REGEX_ABLED_DISABILITY = /(\d{1,2})\.?(\d{3})\.?(\d{3})-(\d|k)/i;
const RUT_REGEX_ABLED_REST_OF_PENSIONS = [/(\d{1,2})\.?(\d{3})\.?(\d{3})-(\d|k)/i, /^$/];
const HEAVY_DUTY_ABLED_DISABILITY = [/^S[ií]$/i, /^No$/i];
const HEAVY_DUTY_ABLED_REST_OF_PENSIONS = [/^S[ií]$/i, /^No$/i, /^$/];
const TYPES_OF_DISABILITY_PENSIONS = [
  /Pensi[oó]n por accidente de trabajo/i,
  /Pensi[oó]n por accidente de trayecto/i,
  /Pensi[oó]n por enfermedad profesional/i
];

const ORPHANHOOD_PENSIONS_TYPE = [
  /Pensi[oó]n\s+por\s+orfandad/i,
  /Pensi[oó]n\s+de\s+orfandad\s+de\s+padre\s+y\s+madre/i
];
const BASE_PENSION_MIN_AND_MAX_DIGIT = /^\d{5,8}\.?\d{0,2}$/;

const validators = [
  {
    validator(value) {
      return value.healthUF.toString().trim();
    },
    message: () => {
      return { healthUF: EMPTY_MESSAGE };
    }
  },
  {
    validator(value) {
      return /^\d+\.?\d{0,2}$/.test(value.healthUF);
    },
    message: () => {
      return { healthUF: INVALID_MESSAGE };
    }
  }
];

const isNumeric = {
  validator: n => {
    if (!isNaN(parseFloat(n)) && isFinite(n)) {
      return true;
    }
    return false;
  },
  message: field => {
    return `${field.path} no es válido`;
  }
};

const decimalValidator = {
  validator(value) {
    try {
      return /^\d+\.?\d{0,2}$/.test(`${+value}`);
    } catch (error) {
      return false;
    }
  },
  message: field => {
    return `${field.path} ${INVALID_MESSAGE}`;
  }
};

const isDate = {
  validator(v) {
    return v instanceof Date;
  },
  message: field => {
    return `${field.path} ${INVALID_DATE}`;
  }
};

const integerValidator = {
  validator(value) {
    try {
      return /^\d+$/.test(`${+value}`);
    } catch (error) {
      return false;
    }
  },
  message: field => {
    return `${field.path} ${INVALID_MESSAGE}`;
  }
};

const rutValidator = {
  validator(value) {
    return ORPHANHOOD_PENSIONS_TYPE.some(regex => regex.test(this.pensionType))
      ? RUT_REGEX_ABLED_DISABILITY.test(value)
      : RUT_REGEX_ABLED_REST_OF_PENSIONS.some(regex => regex.test(value));
  },
  message: field => {
    return `${field.path} ${INVALID_MESSAGE}`;
  }
};

const heavyDutyValidator = {
  validator(value) {
    return TYPES_OF_DISABILITY_PENSIONS.some(regex => regex.test(this.pensionType))
      ? HEAVY_DUTY_ABLED_DISABILITY.some(regex => regex.test(value))
      : HEAVY_DUTY_ABLED_REST_OF_PENSIONS.some(regex => regex.test(value));
  },
  message: field => {
    return `${field.path} ${INVALID_MESSAGE}`;
  }
};

const basePensionValidator = {
  validator(value) {
    return BASE_PENSION_MIN_AND_MAX_DIGIT.test(`${+value}`);
  },
  message: field => {
    return `${field.path} ${INVALID_MESSAGE}`;
  }
};

module.exports = {
  validators,
  decimalValidator,
  integerValidator,
  rutValidator,
  heavyDutyValidator,
  basePensionValidator,
  isNumeric,
  isDate
};

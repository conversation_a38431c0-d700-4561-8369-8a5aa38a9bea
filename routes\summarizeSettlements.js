const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const validateAccess = require('../lib/auth/validate');
const FactoryController = require('../modules/reports/summarizeSettlements/controllers/summarizeSettlements.controller');
const summarizeSettlements = require('../modules/reports/summarizeSettlements/services/summarizeSettlements.services');
const excelService = require('../modules/reports/summarizeSettlements/services/excel.service');

module.exports = router => {
  const summarizeSettlementsController = FactoryController({
    HttpStatus,
    summarizeSettlements,
    excelService,
    ErrorBuilder,
    Logger
  });

  router.get('/time', validateAccess(), summarizeSettlementsController.getDate);
  router.get(
    '/fetchExcelData/:startingDate/:endingDate',
    validateAccess(),
    summarizeSettlementsController.getExcelData
  );
};

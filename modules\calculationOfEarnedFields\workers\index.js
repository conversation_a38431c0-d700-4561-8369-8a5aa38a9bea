const workerModule = require('./worker');
const service = require('../services/dbService');
const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');

module.exports = {
  name: 'set calculation of earned fields',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_SCHEDULING_CRONJOBS_FREQUENCY,
  description: 'Cálculo de campos retroactivos',
  endPoint: 'calculationofearnedfields',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

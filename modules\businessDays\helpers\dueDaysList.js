const transferPensionsWorker = require('../../transferPensions/workers/worker');
const manuallyInactivateMarkedPensionsWorker = require('../../manuallyInactivateMarkedPensions/workers/worker');
const basePensionWorker = require('../../basePension/workers/worker');
const queryPensionsWorker = require('../../queryPensions/worker/worker');
const unifiedBulkLoadAndIpsCronsWorker = require('../../bulkLoad/unifiedBulkLoadAndIpsCrons/workers');
const fonasaDiscountsService = require('../../prepareSettlements/fonasaDiscounts/services/discounts.service');
const cajaLosAndesDiscountsService = require('../../prepareSettlements/cajaLosAndesDiscounts/services/index.service');
const unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCronWorker = require('../../unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCron/workers/worker');
const apsCollectionWorker = require('../../apsCollection/workers/worker');
const unifiedGenerateAndUploadBankFileCronsWorker = require('../../unifiedGenerateAndUploadBankFileCrons/workers/worker');
const previredWorker = require('../../fileUpload/previred/workers/worker');
const analysisOfCurrentCapitalWorker = require('../../analysisOfCurrentCapital/workers/worker');
const linkPensionsService = require('../../linkPensions/services/link.service');

const getDueDayList = () => {
  const {
    BUSINESS_DAYS_LINK,
    BUSINESS_DAYS_INACTIVATE_PENSIONS,
    BUSINESS_DAYS_VALIDATE_MIN_PENSION,
    BUSINESS_DAYS_UPDATE_PENSIONER_DATA,
    BUSINESS_DAYS_MASSIVE_LOAD_CCAF,
    BUSINESS_DAYS_MASSIVE_LOAD_FONASA,
    BUSINESS_DAYS_SETTLEMENT_GENERATION,
    BUSINESS_DAYS_IPS_CONCILIATION,
    BUSINESS_DAYS_BANK_PAYROLL,
    BUSINESS_DAYS_GIRO_TO_THIRD,
    BUSINESS_DAYS_VALID_CAPITAL,
    BUSINESS_DAYS_FUTURE_LINK
  } = process.env;

  return [
    {
      activity: 'Enlace mes anterior',
      lastDependencyMarks: [transferPensionsWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_LINK
    },
    {
      activity: 'Inactivar/Reactivar pensiones',
      lastDependencyMarks: [manuallyInactivateMarkedPensionsWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_INACTIVATE_PENSIONS
    },
    {
      activity: 'Validar pensión mínima',
      lastDependencyMarks: [basePensionWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_VALIDATE_MIN_PENSION
    },
    {
      activity: 'Actualizar datos pensionados',
      lastDependencyMarks: [queryPensionsWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_UPDATE_PENSIONER_DATA
    },
    {
      activity: 'Carga masiva CCAF/IPS',
      lastDependencyMarks: [unifiedBulkLoadAndIpsCronsWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_MASSIVE_LOAD_CCAF
    },
    {
      activity: 'Carga masiva Fonasa/Los Andes',
      lastDependencyMarks: [
        cajaLosAndesDiscountsService.processMark,
        fonasaDiscountsService.processMark
      ],
      numberOfDays: BUSINESS_DAYS_MASSIVE_LOAD_FONASA
    },
    {
      activity: 'Generación de liquidación',
      lastDependencyMarks: [
        unifiedTotalAssetsAndDiscountsWithNetPensionLiquidationReportCronWorker.cronMark
      ],
      numberOfDays: BUSINESS_DAYS_SETTLEMENT_GENERATION
    },
    {
      activity: 'Conciliación con IPS',
      lastDependencyMarks: [apsCollectionWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_IPS_CONCILIATION
    },
    {
      activity: 'Upload de nómina bancaria',
      lastDependencyMarks: [unifiedGenerateAndUploadBankFileCronsWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_BANK_PAYROLL
    },
    {
      activity: 'Giro a terceros',
      lastDependencyMarks: [previredWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_GIRO_TO_THIRD
    },
    {
      activity: 'Generación de capitales vigentes',
      lastDependencyMarks: [analysisOfCurrentCapitalWorker.cronMark],
      numberOfDays: BUSINESS_DAYS_VALID_CAPITAL
    },
    {
      activity: 'Carga Enlace Mes N+1',
      lastDependencyMarks: [linkPensionsService.processMark],
      numberOfDays: BUSINESS_DAYS_FUTURE_LINK
    }
  ];
};

module.exports = getDueDayList;

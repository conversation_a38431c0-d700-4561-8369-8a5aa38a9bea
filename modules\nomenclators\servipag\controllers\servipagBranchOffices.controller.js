const { codeFormatter } = require('../validators/validator');

module.exports = ({
  HttpStatus,
  servipagService,
  validationResult,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
}) => {
  function manageError(res, error) {
    const [statusError, contentError] = ErrorBuilder.build(error);
    res.status(statusError).json(contentError);
  }
  return {
    getAll: async (req, res) => {
      const { isError, error, result } = await servipagService.getAll();
      Logger.info('Get servipag Branch Offices: ', req.details);
      if (isError) {
        Logger.error(`Get selection error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    },
    updateServipag: async (req, res) => {
      Logger.info('Add or Update nomenclator Servipag: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, city, name, address, code } = req.body.servipag;
      const { result, isError, error } = await servipagService.updateServipag({
        id,
        city,
        name,
        address,
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        const [statusCode] = ErrorBuilder.build(error);
        res.status(statusCode).json({ error });
      } else {
        Logger.info('Operation on nomenclator servipag successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    createServipag: async (req, res) => {
      Logger.info('create nomenclator Servipag: ', req.details);
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(422).json({ errors: errors.array() });
        return;
      }
      const { id, city, name, address, code } = req.body.servipag;
      const { result, isError, error } = await servipagService.createServipag({
        id,
        city,
        name,
        address,
        code: codeFormatter(code)
      });
      if (isError) {
        Logger.error(error);
        res.status(HttpStatus.ALREADY_EXIST).json({ error, isError });
      } else {
        Logger.info('Operation on nomenclator Servipag successfully');
        res.status(HttpStatus.OK).json({ result });
      }
    },
    delete: async (req, res) => {
      const { id } = req.params;
      const { isError, error, result } = await servipagService.delete(id);
      Logger.info('Get servipag Branch Offices: ', req.details);
      if (isError) {
        Logger.error(`Get selection error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    }
  };
};

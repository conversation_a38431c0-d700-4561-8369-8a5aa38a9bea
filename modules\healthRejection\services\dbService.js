/* eslint-disable no-restricted-syntax */
const moment = require('moment');
const tmp = require('tmp');
const lineReader = require('line-reader');

const PensionModel = require('../../../models/pension');

const EXEMPTION_REJECTION_CAUSES = [3, 17];
const REDUCTION_CAUSE = 20;
const FILE_EXTENSION = '*********';

const getFormattedRut = rut => {
  const formatedRut = rut
    .replace(/^0+/, '')
    .replace(/[^\dk]/gi, '')
    .toUpperCase();
  return formatedRut.replace(/([\dK])$/, '-$1');
};

const findRut = line => getFormattedRut(line.slice(15, 24));
const findRejectionCause = line => line.slice(126, 128);
const findExemptionAmountWithSolidarity = line => line.slice(102, 110);
const findExemptionAmountWithoutSolidarity = line => line.slice(110, 118);

const findPensionsByRutAndDate = async (rutList, month, year) =>
  PensionModel.find(
    {
      enabled: true,
      'beneficiary.rut': { $in: rutList },
      $expr: {
        $and: [{ $eq: [{ $month: '$createdAt' }, month] }, { $eq: [{ $year: '$createdAt' }, year] }]
      }
    },
    {
      beneficiary: 1,
      causant: 1,
      enabled: 1,
      pensionCodeId: 1
    }
  ).lean();

const getYYYYMM = () =>
  moment()
    .format('YYYY-MM')
    .split('-');

const getHistoricalPensionToUpdate = historicalPensions => {
  const pensionsToUpdate = [];

  for (const pension of historicalPensions) {
    if (
      pension.rejectionHealthExemptionAmount === 0 &&
      pension.rejectionHealthReductionAmount === 0
    )
      break;

    const { _id, beneficiary, causant, pensionCodeId } = pension;

    pensionsToUpdate.push({
      _id,
      beneficiary,
      causant,
      pensionCodeId,
      rejectionHealthExemptionAmount: 0,
      rejectionHealthReductionAmount: 0,
      rejectionIPS: false
    });
  }

  return pensionsToUpdate;
};

const service = {
  getHealthRejectionTimePeriod: ({ currentDate, startingDay, endingDay }) => {
    const startingDate = moment()
      .startOf('month')
      .add(startingDay - 1, 'days');
    const endingDate = moment()
      .startOf('month')
      .add(endingDay - 1, 'days');

    return moment(currentDate).isBetween(startingDate, endingDate);
  },
  downloadZipFile: async (sftpClient, basePath) => {
    try {
      const [year, month] = moment()
        .format('YY-MM')
        .split('-');
      const [lastYear, lastMonth] = moment()
        .subtract(1, 'months')
        .format('YY-MM')
        .split('-');

      const fileNameStructure = `^tbos.ips.${FILE_EXTENSION}_${lastYear}${lastMonth}_${year}${month}\\d{2}_\\d{4}.zip$`;
      const zipFileNameRegex = new RegExp(fileNameStructure, 'i');
      const [firstMatchingFile] = await sftpClient.list(basePath, zipFileNameRegex);
      const zipFileName = firstMatchingFile.name;

      const zipLocalFilepath = `${tmp.dirSync().name}/tbos.zip`;

      await sftpClient.downloadTo(`${basePath}/${zipFileName}`, zipLocalFilepath);
      return { zipLocalFilepath };
    } catch (error) {
      return { error };
    } finally {
      sftpClient.close();
    }
  },
  readFile: async filePath => {
    const yearMonth = getYYYYMM().join('');
    const fileName = `rechexesalud${yearMonth}.${FILE_EXTENSION}`;
    const fullFilePath = `${filePath}${fileName}`;

    return new Promise((resolve, reject) => {
      const fileLines = [];
      lineReader.eachLine(
        fullFilePath,
        (line, last) => {
          fileLines.push(line);
          if (last) resolve(fileLines);
        },
        err => {
          if (err) reject(err);
          else resolve(fileLines);
        }
      );
    });
  },

  parseDataFromLines: fileLines => {
    const fileRuts = [];
    const filePensionsData = [];
    fileLines.forEach(line => {
      const fileRut = findRut(line);
      const rejectionCause = +findRejectionCause(line);
      let rejectionHealthExemptionAmount = 0;
      let rejectionHealthReductionAmount = 0;
      if (EXEMPTION_REJECTION_CAUSES.includes(rejectionCause)) {
        rejectionHealthExemptionAmount = +findExemptionAmountWithSolidarity(line);
      }
      if (REDUCTION_CAUSE === rejectionCause) {
        rejectionHealthReductionAmount = +findExemptionAmountWithoutSolidarity(line);
      }

      const exemptionData = {
        rut: fileRut,
        rejectionCause,
        rejectionHealthExemptionAmount,
        rejectionHealthReductionAmount
      };

      fileRuts.push(fileRut);
      filePensionsData.push(exemptionData);
    });
    return { fileRuts, filePensionsData };
  },
  updatePensionsByHealthRejection: async (getFileContent, fileLines = []) => {
    try {
      const { fileRuts, filePensionsData } = await getFileContent(fileLines);
      const [year, month] = getYYYYMM();
      const dbPensions = await findPensionsByRutAndDate(fileRuts, +month, +year);

      const updatedPensions = dbPensions.map(pension => {
        const {
          rejectionHealthExemptionAmount,
          rejectionHealthReductionAmount
        } = filePensionsData.find(
          exemption => getFormattedRut(exemption.rut) === pension.beneficiary.rut.toUpperCase()
        );

        return {
          ...pension,
          rejectionHealthExemptionAmount,
          rejectionHealthReductionAmount,
          rejectionIPS: true
        };
      });

      return { updatedPensions };
    } catch (error) {
      return { error };
    }
  },
  updatePensionsWithoutHealthRejection: async pensionService => {
    try {
      const currentPensionsWithNoRejection = await PensionModel.find(
        {
          enabled: true,
          rejectionHealthExemptionAmount: 0,
          rejectionHealthReductionAmount: 0
        },
        { beneficiary: 1, causant: 1 }
      )
        .lean()
        .exec();

      const historicalPensionsFn = currentPensionsWithNoRejection.map(pension => async () => {
        const pensionHistory = await pensionService.getPensionHistoryByMonth(
          pension.beneficiary.rut,
          pension.causant.rut
        );

        return getHistoricalPensionToUpdate(pensionHistory);
      });

      const pensionsToUpdate = await Promise.all(historicalPensionsFn.map(async fn => fn()));

      const historicalPensions = pensionsToUpdate.reduce((acc, curr) => [...acc, ...curr], []);

      return { historicalPensions };
    } catch (error) {
      return { error };
    }
  }
};

module.exports = { ...service };

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const fs = require('fs');
const temporaryBonusPensionersModel = require('../../generateBonusAssignmentFile/models/temporaryBonusPensioners');
const temporaryImportedBonusPensionersModel = require('../models/temporaryImportedBonusPensioners');
const dataTemporaryPensionerBonus = require('../../../resources/temporaryPensionerBonus.json');

const { beforeAllTests, afterAllTests } = require('../../testsHelper');

const service = require('./temporary.service');

describe('bonusPensioners temporary service Test', () => {
  beforeAll(beforeAllTests);

  beforeEach(() => {});

  it('should return file string when there are data on database', async () => {
    await temporaryBonusPensionersModel.create(dataTemporaryPensionerBonus[0]);

    const { isError, result } = await service.downloadBonusPensionersFileString();

    expect(isError).toBe(false);
    expect(result).toBeDefined();
    expect(result.length).toBeGreaterThanOrEqual(1);
  });

  it('should not return file string when there are no data on database', async () => {
    const { isError, error } = await service.downloadBonusPensionersFileString();

    expect(isError).toBe(true);
    expect(error).toBeDefined();
    expect(error.code).toBe(404);
  });

  it('should save the bonus pensioners data when the import is successfully', async () => {
    const fileString = fs
      .readFileSync(
        `${__dirname}/../../../resources/bonusPensioners/correct_ips_bonus_pensioners.txt`
      )
      .toString('base64');

    const requestBody = {
      fileString
    };
    const { isError, result } = await service.processFile(requestBody);

    const savedData = await temporaryImportedBonusPensionersModel.find({});

    expect(isError).toBe(false);
    expect(result).toBeDefined();
    expect(result).toBe(true);
    expect(savedData).toBeDefined();
    expect(savedData.length).toBe(2);
  });

  it('should return validations result when the imported file has wrong values', async () => {
    const fileString = fs
      .readFileSync(
        `${__dirname}/../../../resources/bonusPensioners/incorrect_ips_bonus_pensioners.txt`
      )
      .toString('base64');

    const requestBody = {
      fileString
    };
    const { isError, error, result } = await service.processFile(requestBody);

    expect(isError).toBe(true);
    expect(error).toBeDefined();
    expect(error.code).toBe(400);
    expect(result).toBeDefined();
    expect(result.length).toBe(2);
  });

  afterEach(async () => {
    await temporaryBonusPensionersModel.deleteMany().catch(error => console.log(error));
    await temporaryImportedBonusPensionersModel.deleteMany().catch(error => console.log(error));
  });
  afterAll(afterAllTests);
});

const cronDescription = 'Net pensions liquidations reports';
const cronMark = 'NET_PENSIONS_LIQUIDATION_REPORTS';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual.';
const successMessage = `Proceso ${cronMark} completado correctamente.`;
const markDependencies = ['TAXABLE_PENSION', 'UF_VALUE', 'SET_AMOUNT_RETENTION'];
const getMissingDependencyMessage = `No se ha ejecutado una o más de las dependencias ${markDependencies.join(
  ', '
)}`;
const workerFn = async ({
  Logger,
  pensionService,
  liquidationService,
  afpService,
  ufService,
  netPensionsService,
  liquidationReportService,
  cajaService,
  done,
  logService
}) => {
  try {
    Logger.info('Starting cron process for net pension ...');
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    if (!(await logService.allMarksExists(markDependencies))) {
      Logger.info(getMissingDependencyMessage);
      return { message: getMissingDependencyMessage, status: 'UNAUTHORIZED' };
    }

    const { error } = await netPensionsService
      .netPension(pensionService, liquidationService, afpService, ufService, cajaService)
      .catch(err => ({ error: err }));

    if (error) throw new Error(error);

    Logger.info('Starting cron process for generating liquidation reports ...');
    const {
      errorLiquidationReport
    } = await liquidationReportService
      .createLiquidationsReports()
      .catch(err => ({ errorLiquidationReport: err }));

    if (errorLiquidationReport) throw new Error(errorLiquidationReport);

    await logService.saveLog(cronMark);
    Logger.info('End of process for generating netPension and liquidation reports');
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, markDependencies, workerFn };

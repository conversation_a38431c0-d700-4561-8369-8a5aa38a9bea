/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */
const moment = require('moment');
const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const pensionsData = require('../../../../resources/pensions.json');
const fileGenerationUtils = require('./fileGenerationUtils');

describe('Circular file 2480 file generation methods test', () => {
  beforeAll(beforeAllTests);

  const reportDateAndEntityCode = `${moment().format('YYYYMMDD')}1`;
  const pensionerRut = '19685004K';
  const pensionerLastName = `YANEZ${''.padStart(15)}`;
  const pensionerMothersLastName = `BAHAMONDES${''.padStart(10)}`;
  const pensionerName = `CHRISTOPHER${''.padStart(19)}`;
  const pensionerDateOfBirthAndGender = `19971008M`;
  const pensionerPensionTypeCode = '1';
  const pensionerAmount = '000152452';

  const expectedOutputLine = [
    reportDateAndEntityCode,
    pensionerRut,
    pensionerLastName,
    pensionerMothersLastName,
    pensionerName,
    pensionerDateOfBirthAndGender,
    pensionerPensionTypeCode,
    pensionerAmount,
    '\n'
  ].join('');

  it('should convert each pensioner object to a line for the circular file', async () => {
    const { getLine } = fileGenerationUtils;
    const pensioner = pensionsData[0];
    expect(getLine(pensioner)).toBe(expectedOutputLine);
  });

  it('should dynamically generate file name base on current date', async () => {
    const { getZipFileName, getFileName } = fileGenerationUtils;
    const tmp = { dirSync: jest.fn().mockReturnValue({ name: '/temp' }) };
    expect(getFileName(tmp)).toMatch(`ACHS_Circular2480_${moment().format('YYYYMM')}.txt`);
    expect(getZipFileName()).toBe(`SPS_ACHS_${moment().format('YYYYMM')}.zip`);
  });

  afterAll(afterAllTests);
});

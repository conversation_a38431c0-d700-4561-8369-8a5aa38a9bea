import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * Seeder para todas las reglas de cálculo de pensiones
 * Implementa todas las reglas identificadas en el sistema
 */
async function seedPensionRules() {
  console.log('🌱 Seeding pension rules...');

  // 1. REGLAS DE PENSIÓN BASE
  await seedBasePensionRules();
  
  // 2. REGLAS DE BENEFICIOS (ASSETS)
  await seedAssetsRules();
  
  // 3. REGLAS DE DESCUENTOS
  await seedDiscountsRules();
  
  // 4. REGLAS DE RETROACTIVOS
  await seedRetroactiveRules();
  
  // 5. VALORES DE REFERENCIA
  await seedReferenceValues();
  
  // 6. ESCALAS Y RANGOS
  await seedRuleScales();
  
  // 7. CONFIGURACIONES AFP Y SALUD
  await seedAfpAndHealthConfigurations();

  console.log('✅ Pension rules seeded successfully');
}

async function seedBasePensionRules() {
  const basePensionRules = [
    {
      name: 'Pensión Mínima Invalidez Total',
      description: 'Aplicar pensión mínima para invalidez total',
      category: 'MINIMUM_PENSION',
      subcategory: 'INVALIDEZ_TOTAL',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.pensionType', operator: 'equals', value: 'INVALIDEZ_TOTAL' },
        { field: 'calc:totalBasePension', operator: 'lt', value: 185000 }
      ],
      formula: 'max(totalBasePension, 185000)',
      parameters: { minimumAmount: 185000 },
      validFrom: new Date('2024-01-01'),
      priority: 100,
      executionOrder: 1,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Pensión Mínima Invalidez Parcial',
      description: 'Aplicar pensión mínima para invalidez parcial',
      category: 'MINIMUM_PENSION',
      subcategory: 'INVALIDEZ_PARCIAL',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.pensionType', operator: 'equals', value: 'INVALIDEZ_PARCIAL' },
        { field: 'calc:totalBasePension', operator: 'lt', value: 140000 }
      ],
      formula: 'max(totalBasePension, 140000)',
      parameters: { minimumAmount: 140000 },
      validFrom: new Date('2024-01-01'),
      priority: 100,
      executionOrder: 2,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Pensión Mínima Supervivencia',
      description: 'Aplicar pensión mínima para supervivencia',
      category: 'MINIMUM_PENSION',
      subcategory: 'SUPERVIVENCIA',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.pensionType', operator: 'equals', value: 'SUPERVIVENCIA' },
        { field: 'calc:totalBasePension', operator: 'lt', value: 120000 }
      ],
      formula: 'max(totalBasePension, 120000)',
      parameters: { minimumAmount: 120000 },
      validFrom: new Date('2024-01-01'),
      priority: 100,
      executionOrder: 3,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Reajuste IPC Anual',
      description: 'Aplicar reajuste IPC a pensión base',
      category: 'BASE_PENSION',
      subcategory: 'IPC_ADJUSTMENT',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'currentMonth', operator: 'equals', value: 1 } // Solo en enero
      ],
      formula: 'basePension * IPC',
      parameters: {},
      validFrom: new Date('2024-01-01'),
      priority: 90,
      executionOrder: 1,
      version: '1.0',
      createdBy: 'system'
    }
  ];

  for (const rule of basePensionRules) {
    await prisma.pensionRule.upsert({
      where: { name: rule.name },
      update: rule,
      create: rule
    });
  }
}

async function seedAssetsRules() {
  const assetsRules = [
    {
      name: 'APS - Aporte Previsional Solidario',
      description: 'Calcular APS según rangos de pensión',
      category: 'ASSETS',
      subcategory: 'APS',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.pensionType', operator: 'in', value: ['INVALIDEZ_TOTAL', 'INVALIDEZ_PARCIAL', 'SUPERVIVENCIA'] }
      ],
      formula: 'SCALE(totalBasePension, apsScales)',
      parameters: { useScales: true },
      validFrom: new Date('2024-01-01'),
      priority: 80,
      executionOrder: 1,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Asignación Familiar',
      description: 'Calcular asignación familiar por cargas',
      category: 'ASSETS',
      subcategory: 'FAMILY_ASSIGNMENT',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'numberOfCharges', operator: 'gt', value: 0 }
      ],
      formula: 'SCALE(totalBasePension, familyAssignmentScales) * numberOfCharges',
      parameters: { useScales: true },
      validFrom: new Date('2024-01-01'),
      priority: 70,
      executionOrder: 2,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Bono de Navidad',
      description: 'Bono navideño en diciembre',
      category: 'ASSETS',
      subcategory: 'CHRISTMAS_BONUS',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'currentMonth', operator: 'equals', value: 12 },
        { field: 'custom', operator: 'custom', value: { function: 'isEligibleForBonus', bonusType: 'CHRISTMAS' } }
      ],
      formula: 'IF(totalBasePension <= 200000, 30000, IF(totalBasePension <= 400000, 20000, IF(totalBasePension <= 600000, 10000, 0)))',
      parameters: { bonusType: 'CHRISTMAS' },
      validFrom: new Date('2024-01-01'),
      priority: 60,
      executionOrder: 3,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Bono Fiestas Patrias',
      description: 'Bono de fiestas patrias en septiembre',
      category: 'ASSETS',
      subcategory: 'NATIONAL_HOLIDAYS_BONUS',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'currentMonth', operator: 'equals', value: 9 },
        { field: 'custom', operator: 'custom', value: { function: 'isEligibleForBonus', bonusType: 'NATIONAL_HOLIDAYS' } }
      ],
      formula: 'IF(totalBasePension <= 200000, 25000, IF(totalBasePension <= 400000, 15000, 0))',
      parameters: { bonusType: 'NATIONAL_HOLIDAYS' },
      validFrom: new Date('2024-01-01'),
      priority: 60,
      executionOrder: 4,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Bono de Invierno',
      description: 'Bono de invierno mayo-agosto',
      category: 'ASSETS',
      subcategory: 'WINTER_BONUS',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'currentMonth', operator: 'between', value: [5, 8] },
        { field: 'custom', operator: 'custom', value: { function: 'isEligibleForBonus', bonusType: 'WINTER' } }
      ],
      formula: 'IF(totalBasePension <= 180000, 20000, IF(totalBasePension <= 350000, 12000, 0))',
      parameters: { bonusType: 'WINTER' },
      validFrom: new Date('2024-01-01'),
      priority: 60,
      executionOrder: 5,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Exención de Salud',
      description: 'Exención de descuento de salud',
      category: 'ASSETS',
      subcategory: 'HEALTH_EXEMPTION',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.assets.health_exemption', operator: 'equals', value: 'Si' }
      ],
      formula: 'min(totalBasePension * 0.07, UF * 2.78)',
      parameters: { maxUF: 2.78 },
      validFrom: new Date('2024-01-01'),
      priority: 50,
      executionOrder: 6,
      version: '1.0',
      createdBy: 'system'
    }
  ];

  for (const rule of assetsRules) {
    await prisma.pensionRule.upsert({
      where: { name: rule.name },
      update: rule,
      create: rule
    });
  }
}

async function seedDiscountsRules() {
  const discountsRules = [
    {
      name: 'Descuento AFP',
      description: 'Calcular descuento AFP según afiliación',
      category: 'DISCOUNTS',
      subcategory: 'AFP_DISCOUNT',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.afpAffiliation', operator: 'is_not_null' },
        { field: 'custom', operator: 'custom', value: { function: 'hasValidAfpAffiliation' } }
      ],
      formula: 'min(totalBasePension * afpRate / 100, UF * 80.2 * afpRate / 100)',
      parameters: { useAfpConfiguration: true },
      validFrom: new Date('2024-01-01'),
      priority: 80,
      executionOrder: 1,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Descuento Salud FONASA',
      description: 'Descuento 7% para FONASA',
      category: 'DISCOUNTS',
      subcategory: 'HEALTH_DISCOUNT',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.healthAffiliation', operator: 'equals', value: 'FONASA' }
      ],
      formula: 'totalBasePension * 0.07',
      parameters: { rate: 0.07 },
      validFrom: new Date('2024-01-01'),
      priority: 70,
      executionOrder: 2,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Descuento Salud ISAPRE',
      description: 'Descuento según UF para ISAPRES',
      category: 'DISCOUNTS',
      subcategory: 'HEALTH_DISCOUNT',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.healthAffiliation', operator: 'contains', value: 'ISAPRE' },
        { field: 'pension.discounts.healthUF', operator: 'gt', value: 0 }
      ],
      formula: 'healthUF * UF',
      parameters: { useHealthUF: true },
      validFrom: new Date('2024-01-01'),
      priority: 70,
      executionOrder: 3,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: '1% Cajas de Compensación',
      description: 'Descuento 1% para cajas de compensación',
      category: 'DISCOUNTS',
      subcategory: 'ONE_PERCENT_ADJUSTED',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.discounts', operator: 'custom', value: { function: 'hasCompensationBoxAffiliation' } }
      ],
      formula: '(basePension + totalLaws) * 0.01',
      parameters: { rate: 0.01 },
      validFrom: new Date('2024-01-01'),
      priority: 60,
      executionOrder: 4,
      version: '1.0',
      createdBy: 'system'
    }
  ];

  for (const rule of discountsRules) {
    await prisma.pensionRule.upsert({
      where: { name: rule.name },
      update: rule,
      create: rule
    });
  }
}

async function seedRetroactiveRules() {
  const retroactiveRules = [
    {
      name: 'Retroactivo Pensión Base',
      description: 'Calcular retroactivos de pensión base',
      category: 'RETROACTIVE',
      subcategory: 'FOR_BASE_PENSION',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.retroactiveAmounts.forBasePension', operator: 'gt', value: 0 }
      ],
      formula: 'retroactiveBasePension * IPC',
      parameters: { applyIPC: true },
      validFrom: new Date('2024-01-01'),
      priority: 50,
      executionOrder: 1,
      version: '1.0',
      createdBy: 'system'
    },
    {
      name: 'Retroactivo Bonos',
      description: 'Calcular retroactivos de bonos',
      category: 'RETROACTIVE',
      subcategory: 'FOR_BONUSES',
      ruleType: 'CALCULATION',
      conditions: [
        { field: 'pension.retroactiveAmounts.forBonuses', operator: 'gt', value: 0 }
      ],
      formula: 'retroactiveBonuses',
      parameters: {},
      validFrom: new Date('2024-01-01'),
      priority: 40,
      executionOrder: 2,
      version: '1.0',
      createdBy: 'system'
    }
  ];

  for (const rule of retroactiveRules) {
    await prisma.pensionRule.upsert({
      where: { name: rule.name },
      update: rule,
      create: rule
    });
  }
}

async function seedReferenceValues() {
  const referenceValues = [
    { name: 'UF', value: 35000, currency: 'CLP', unit: 'PESOS', effectiveDate: new Date('2024-01-01'), source: 'SII' },
    { name: 'IPC', value: 1.03, currency: 'CLP', unit: 'PERCENTAGE', effectiveDate: new Date('2024-01-01'), source: 'INE' },
    { name: 'MINIMUM_SALARY', value: 350000, currency: 'CLP', unit: 'PESOS', effectiveDate: new Date('2024-01-01'), source: 'MINTRAB' },
    { name: 'UTM', value: 65000, currency: 'CLP', unit: 'PESOS', effectiveDate: new Date('2024-01-01'), source: 'SII' }
  ];

  for (const value of referenceValues) {
    await prisma.referenceValue.upsert({
      where: { name: value.name },
      update: value,
      create: value
    });
  }
}

async function seedRuleScales() {
  // Escalas APS
  const apsScales = [
    { ruleName: 'APS', minValue: 0, maxValue: 150000, resultValue: 25000, validFrom: new Date('2024-01-01') },
    { ruleName: 'APS', minValue: 150001, maxValue: 250000, resultValue: 15000, validFrom: new Date('2024-01-01') },
    { ruleName: 'APS', minValue: 250001, maxValue: 350000, resultValue: 8000, validFrom: new Date('2024-01-01') }
  ];

  // Escalas Asignación Familiar
  const familyScales = [
    { ruleName: 'FAMILY_ASSIGNMENT', minValue: 0, maxValue: 280000, resultValue: 12364, validFrom: new Date('2024-01-01') },
    { ruleName: 'FAMILY_ASSIGNMENT', minValue: 280001, maxValue: 420000, resultValue: 7598, validFrom: new Date('2024-01-01') },
    { ruleName: 'FAMILY_ASSIGNMENT', minValue: 420001, maxValue: *********, resultValue: 2533, validFrom: new Date('2024-01-01') }
  ];

  const allScales = [...apsScales, ...familyScales];

  for (const scale of allScales) {
    await prisma.ruleScale.create({
      data: {
        ...scale,
        scaleType: 'PENSION_RANGE',
        resultType: 'FIXED_AMOUNT'
      }
    });
  }
}

async function seedAfpAndHealthConfigurations() {
  // Configuraciones AFP
  const afpConfigs = [
    { afpName: 'PROVIDA', commissionRate: 1.44, insuranceRate: 1.33, totalRate: 10.77, maxTaxableAmount: 2814000, validFrom: new Date('2024-01-01') },
    { afpName: 'HABITAT', commissionRate: 1.44, insuranceRate: 1.33, totalRate: 10.77, maxTaxableAmount: 2814000, validFrom: new Date('2024-01-01') },
    { afpName: 'PLANVITAL', commissionRate: 1.35, insuranceRate: 1.33, totalRate: 10.68, maxTaxableAmount: 2814000, validFrom: new Date('2024-01-01') },
    { afpName: 'CUPRUM', commissionRate: 1.11, insuranceRate: 1.33, totalRate: 10.44, maxTaxableAmount: 2814000, validFrom: new Date('2024-01-01') },
    { afpName: 'CAPITAL', commissionRate: 2.11, insuranceRate: 1.33, totalRate: 11.44, maxTaxableAmount: 2814000, validFrom: new Date('2024-01-01') }
  ];

  for (const config of afpConfigs) {
    await prisma.afpConfiguration.upsert({
      where: { afpName: config.afpName },
      update: config,
      create: config
    });
  }

  // Configuraciones de Salud
  const healthConfigs = [
    { healthProvider: 'FONASA', providerType: 'PUBLIC', baseRate: 0.07, validFrom: new Date('2024-01-01') },
    { healthProvider: 'ISAPRE_CONSALUD', providerType: 'PRIVATE', validFrom: new Date('2024-01-01') },
    { healthProvider: 'ISAPRE_COLMENA', providerType: 'PRIVATE', validFrom: new Date('2024-01-01') },
    { healthProvider: 'ISAPRE_CRUZ_BLANCA', providerType: 'PRIVATE', validFrom: new Date('2024-01-01') }
  ];

  for (const config of healthConfigs) {
    await prisma.healthConfiguration.upsert({
      where: { healthProvider: config.healthProvider },
      update: config,
      create: config
    });
  }
}

// Ejecutar seeder
seedPensionRules()
  .catch((e) => {
    console.error('❌ Error seeding pension rules:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

const cronDescription = 'set-amount-pensioners-national-holidays-bonus';
const cronMark = 'SET_AMOUNT_PENSIONERS_NATIONAL_HOLIDAYS_BONUS';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el año actual.';
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyMark = 'CRON_BASE_MINIMUN_PENSION_WORKER';

const getMissingDependencyMessage = dep => `La dependencia ${dep} aún no se ha ejecutado`;

const workerFn = async ({ Logger, logService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);

    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(getMissingDependencyMessage(dependencyMark));
      return { executionCompleted: false, message: getMissingDependencyMessage(dependencyMark) };
    }

    Logger.info(`${cronDescription} process started`);
    const { error } = await service.setPensionersBonusNationalHolidays();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} error ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

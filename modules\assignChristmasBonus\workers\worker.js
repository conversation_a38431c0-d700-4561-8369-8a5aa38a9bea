const cronMark = 'SET_AMOUNT_PENSIONERS_CHRISTMAS_BONUS';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el año actual.';
const cronDescription = 'set-amount-pensioners-bonus-christmas';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;
const dependencyChristmas = 'CRON_BASE_MINIMUN_PENSION_WORKER';

const getDependencyMessageAssignChristmas = dep => `Dependencia "${dep}" aún no ejecutada`;

const workerFn = async ({ Logger, logService, service, done, job }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);

    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    Logger.info(`${cronDescription}: start dependency verification`);
    if (!(await logService.existsLog(dependencyChristmas))) {
      Logger.info(getDependencyMessageAssignChristmas(dependencyChristmas));
      return {
        executionCompleted: false,
        message: getDependencyMessageAssignChristmas(dependencyChristmas)
      };
    }

    Logger.info(`${cronDescription} process started`);
    const { error } = await service.setPensionersBonusChristmas();
    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);

    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} error ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyChristmas, workerFn };

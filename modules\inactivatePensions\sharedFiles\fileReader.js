/* eslint-disable import/prefer-default-export */
const lineReader = require('line-reader');

const datesToSlice = {
  marriage: [278, 286],
  death: [286, 294]
};
const readLines = async (file, type) => {
  return new Promise(resolve => {
    const lines = [];
    const [startDate, endDate] = datesToSlice[type];
    lineReader.eachLine(file, (line, last) => {
      const rut = line.slice(148, 157);
      const country = line.slice(276, 277);
      const date = line.slice(startDate, endDate);
      lines.push([rut, date, country]);
      if (last) {
        resolve(lines);
        return false;
      }
      return true;
    });
  });
};

module.exports = {
  readLines
};

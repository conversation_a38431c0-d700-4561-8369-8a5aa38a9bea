const moment = require('moment');
const FactorModel = require('../../quadrature/models/factor');
const ConcurrencyModel = require('../../quadrature/models/concurrency');
const IpcModel = require('../../../models/ipcs');
const { roundValue } = require('../../sharedFiles/helpers');

const NOT_VALID = /no vigente/i;
const DEFAULT_PERCENTAGE = 100;
const cleanRut = rut => rut.replace(/[^0-9kK-]/g, '').toUpperCase();

const getPensions = async pensionService => {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  const { result } = await pensionService.getAllAndFilter(
    {
      validityType: { $not: NOT_VALID },
      enabled: true,
      $or: [
        { evaluationDate: { $exists: false } },
        {
          $or: [
            { evaluationDate: { $lt: new Date(currentYear, currentMonth, 1) } },
            { evaluationDate: { $gte: new Date(currentYear, currentMonth + 1, 1) } }
          ]
        }
      ]
    },
    {
      beneficiary: 1,
      causant: 1,
      assets: 1,
      dateOfBirth: 1,
      basePension: 1,
      increasingInLaw19578: 1,
      increasingInLaw19953: 1,
      increasingInLaw20102: 1,
      law19403: 1,
      law19539: 1,
      law19953: 1,
      currentCapitalKeys: 1,
      pensionCodeId: 1
    }
  );

  return result;
};

const calculateMonthOfBirth = birthdate => {
  const month = moment(birthdate).month();
  const day = moment(birthdate).date();
  const actualDate = new Date();
  const birth = new Date(new Date().getFullYear(), month, day);
  const nextBirthDay = new Date(
    birth.getMonth() > actualDate.getMonth()
      ? new Date().getFullYear()
      : new Date().getFullYear() + 1,
    month,
    day
  );
  return moment(nextBirthDay).diff(actualDate, 'months');
};

const getFactor = (factors, key) => {
  const { factor = 0 } = factors.find(item => item.keyFactor === key) || {};
  return factor;
};
const calculateAmountWithKeys = (currentFactors, amount, dateOfBirth, currentConcurrency) => {
  const monthsToBirth = calculateMonthOfBirth(dateOfBirth);
  const complementMonthsToBirth = 12 - monthsToBirth;
  const percentageOfConcurrency = Number(currentConcurrency.concurrencyPercentage / 100) || 0;

  if (currentFactors.length > 2) {
    const MULTIPLICATOR = 58.17;

    return roundValue(
      amount *
        percentageOfConcurrency *
        (monthsToBirth * getFactor(currentFactors, 'F124') +
          complementMonthsToBirth * getFactor(currentFactors, 'F224') -
          (monthsToBirth * getFactor(currentFactors, 'F118') +
            complementMonthsToBirth * getFactor(currentFactors, 'F218')) +
          MULTIPLICATOR *
            (monthsToBirth * getFactor(currentFactors, 'F118') +
              complementMonthsToBirth * getFactor(currentFactors, 'F218')))
    );
  }

  return roundValue(
    amount *
      (monthsToBirth * getFactor(currentFactors, 'F1') +
        complementMonthsToBirth * getFactor(currentFactors, 'F2')) *
      percentageOfConcurrency
  );
};

const workingCapital = (amount, currentConcurrency) => {
  let notWorkingCapitalAmount;
  let workingCapitalAmount;
  const percentageOfConcurrency = Number(currentConcurrency.concurrencyPercentage / 100) || 0;

  if (amount < amount * percentageOfConcurrency) {
    workingCapitalAmount = amount;
    notWorkingCapitalAmount = amount - workingCapitalAmount;
    return {
      workingCapital: roundValue(workingCapitalAmount),
      notWorkingCapital: roundValue(notWorkingCapitalAmount)
    };
  }

  if (amount >= amount * percentageOfConcurrency) {
    workingCapitalAmount = amount * percentageOfConcurrency;
    notWorkingCapitalAmount = amount - workingCapitalAmount;
    return {
      workingCapital: roundValue(workingCapitalAmount),
      notWorkingCapital: roundValue(notWorkingCapitalAmount)
    };
  }
  return {
    workingCapital: roundValue(workingCapitalAmount),
    notWorkingCapital: roundValue(notWorkingCapitalAmount)
  };
};

const calculateReadjustmentByIpc = (amount, percentage) => {
  return roundValue(amount * percentage);
};

const setCapitalPensions = ({ _id, ...pension }, factors, concurrencies, ipcs) => {
  const {
    beneficiary: { rut: beneficiaryRut },
    assets,
    dateOfBirth,
    basePension,
    increasingInLaw19578,
    increasingInLaw19953,
    increasingInLaw20102,
    law19403,
    law19539,
    law19953,
    currentCapitalKeys = {}
  } = pension;
  const { nationalHolidaysBonus, christmasBonus } = assets;
  const totalBonus = roundValue((nationalHolidaysBonus + christmasBonus) / 12);
  const capitalKeys = Object.entries(currentCapitalKeys);

  const currentFactors = capitalKeys.map(([key, value]) => ({
    ...factors.find(element => element.key === value),
    keyFactor: key
  }));
  const currentConcurrency = concurrencies.find(
    item => cleanRut(item.beneficiaryRut) === cleanRut(beneficiaryRut)
  ) || { concurrencyPercentage: DEFAULT_PERCENTAGE };
  const IPCS_LAST_ELEMENT_INDEX = ipcs.length - 1;
  const currentIpcPercentage = Number(ipcs[IPCS_LAST_ELEMENT_INDEX].percentage);
  const basePensionCapital = calculateAmountWithKeys(
    currentFactors,
    basePension,
    dateOfBirth,
    currentConcurrency
  );
  const capitalLaw19578 = calculateAmountWithKeys(
    currentFactors,
    increasingInLaw19578,
    dateOfBirth,
    currentConcurrency
  );
  const capitalLaw19953 = calculateAmountWithKeys(
    currentFactors,
    increasingInLaw19953,
    dateOfBirth,
    currentConcurrency
  );
  const capitalLaw20102 = calculateAmountWithKeys(
    currentFactors,
    increasingInLaw20102,
    dateOfBirth,
    currentConcurrency
  );

  const capitalBonusLaw19403 = calculateAmountWithKeys(
    currentFactors,
    law19403,
    dateOfBirth,
    currentConcurrency
  );
  const capitalBonusLaw19539 = calculateAmountWithKeys(
    currentFactors,
    law19539,
    dateOfBirth,
    currentConcurrency
  );
  const capitalBonusLaw19953 = calculateAmountWithKeys(
    currentFactors,
    law19953,
    dateOfBirth,
    currentConcurrency
  );
  const capitalTotalBonus = calculateAmountWithKeys(
    currentFactors,
    totalBonus,
    dateOfBirth,
    currentConcurrency
  );

  const basePensionWorkingCapital = workingCapital(basePensionCapital, currentConcurrency)
    .workingCapital;
  const basePensionNotWorkingCapital = workingCapital(basePensionCapital, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalLaw19578 = workingCapital(increasingInLaw19578, currentConcurrency)
    .workingCapital;
  const notWorkingCapitalLaw19578 = workingCapital(increasingInLaw19578, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalLaw19953 = workingCapital(increasingInLaw19953, currentConcurrency)
    .workingCapital;
  const notWorkingCapitalLaw19953 = workingCapital(increasingInLaw19953, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalLaw20102 = workingCapital(increasingInLaw20102, currentConcurrency)
    .workingCapital;
  const notWorkingCapitalLaw20102 = workingCapital(increasingInLaw20102, currentConcurrency)
    .notWorkingCapital;

  const workingCapitalBonusLaw19403 = workingCapital(law19403, currentConcurrency).workingCapital;
  const notWorkingCapitalBonusLaw19403 = workingCapital(law19403, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalBonusLaw19539 = workingCapital(law19539, currentConcurrency).workingCapital;
  const notWorkingCapitalBonusLaw19539 = workingCapital(law19539, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalBonusLaw19953 = workingCapital(law19953, currentConcurrency).workingCapital;
  const notWorkingCapitalBonusLaw19953 = workingCapital(law19953, currentConcurrency)
    .notWorkingCapital;
  const workingCapitalBonus = workingCapital(totalBonus, currentConcurrency).workingCapital;
  const notWorkingCapitalBonus = workingCapital(totalBonus, currentConcurrency).notWorkingCapital;

  const capitalPBIpc = calculateReadjustmentByIpc(basePensionCapital, currentIpcPercentage);
  const capitalLaw19578Ipc = calculateReadjustmentByIpc(capitalLaw19578, currentIpcPercentage);
  const capitalLaw19953Ipc = calculateReadjustmentByIpc(capitalLaw19953, currentIpcPercentage);
  const capitalLaw20102Ipc = calculateReadjustmentByIpc(capitalLaw20102, currentIpcPercentage);

  const capitalBonusLaw19403Ipc = calculateReadjustmentByIpc(
    capitalBonusLaw19403,
    currentIpcPercentage
  );
  const capitalBonusLaw19539Ipc = calculateReadjustmentByIpc(
    capitalBonusLaw19539,
    currentIpcPercentage
  );
  const capitalBonusLaw19953Ipc = calculateReadjustmentByIpc(
    capitalBonusLaw19953,
    currentIpcPercentage
  );
  const capitalTotalBonusIpc = calculateReadjustmentByIpc(capitalTotalBonus, currentIpcPercentage);

  const totalCapital =
    basePensionCapital +
    capitalLaw19578 +
    capitalLaw19953 +
    capitalLaw20102 +
    capitalBonusLaw19403 +
    capitalBonusLaw19539 +
    capitalBonusLaw19953 +
    capitalTotalBonus;

  const totalCapitalIpc =
    capitalPBIpc +
    capitalLaw19578Ipc +
    capitalLaw19953Ipc +
    capitalLaw20102Ipc +
    capitalBonusLaw19403Ipc +
    capitalBonusLaw19539Ipc +
    capitalBonusLaw19953Ipc +
    capitalTotalBonusIpc;

  return {
    ...pension,
    currentCapitalCalculation: {
      basePensionCapital,
      capitalLaw19578,
      capitalLaw19953,
      capitalLaw20102,
      capitalBonusLaw19403,
      capitalBonusLaw19953,
      capitalBonusLaw19539,
      basePensionWorkingCapital,
      basePensionNotWorkingCapital,
      workingCapitalLaw19578,
      notWorkingCapitalLaw19578,
      workingCapitalLaw19953,
      notWorkingCapitalLaw19953,
      workingCapitalLaw20102,
      notWorkingCapitalLaw20102,
      workingCapitalBonusLaw19403,
      notWorkingCapitalBonusLaw19403,
      workingCapitalBonusLaw19953,
      notWorkingCapitalBonusLaw19953,
      workingCapitalBonusLaw19539,
      notWorkingCapitalBonusLaw19539,
      workingCapitalBonus,
      notWorkingCapitalBonus,
      capitalPBIpc,
      capitalLaw19578Ipc,
      capitalLaw19953Ipc,
      capitalLaw20102Ipc,
      capitalBonusLaw19403Ipc,
      capitalBonusLaw19539Ipc,
      capitalBonusLaw19953Ipc,
      totalCapital,
      totalCapitalIpc,
      capitalTotalBonus,
      capitalTotalBonusIpc
    }
  };
};

const service = {
  async getFactorAndConcurrencies(pensions) {
    const rutList = pensions.map(item => item.beneficiary.rut);
    const factors = await FactorModel.find().lean();
    const concurrencies = await ConcurrencyModel.find({ beneficiaryRut: { $in: rutList } }).lean();
    const ipcs = await IpcModel.find().lean();
    return { factors, concurrencies, ipcs };
  },
  async calculateCurrentCapital(pensionService) {
    try {
      const pensions = await getPensions(pensionService);
      const { factors = [], concurrencies = [], ipcs = [] } = await this.getFactorAndConcurrencies(
        pensions
      );

      const mappedPension = pensions.map(({ _doc: item }) =>
        setCapitalPensions(item, factors, concurrencies, ipcs)
      );

      const { completed, error } = await pensionService.createUpdatePension(mappedPension);

      if (error) throw new Error(error);
      return { completed, error };
    } catch (error) {
      return { error, completed: false };
    }
  }
};

module.exports = {
  ...service,
  calculateMonthOfBirth,
  calculateAmountWithKeys,
  workingCapital,
  calculateReadjustmentByIpc,
  setCapitalPensions
};

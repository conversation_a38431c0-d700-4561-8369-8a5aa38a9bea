const cronDescription = 'establecer valores en cantidades reservadas:';
const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const cronMark = 'SET_VALUES_TO_RESERVED_AMOUNTS';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;

module.exports = async ({ Logger, logService, pensionService, service, done }) => {
  try {
    Logger.info(`${cronDescription} checking whether this process was previously executed or not`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }

    Logger.info(`${cronDescription} process started`);
    const { error } = await service.reservedDisabilityPension(pensionService);

    if (error) throw new Error(error);

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

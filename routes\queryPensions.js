const { validationResult } = require('express-validator');
const fsClient = require('fs').promises;
const ftp = require('basic-ftp');
const HttpStatus = require('../lib/constants/http-status');
const ErrorBuilder = require('../lib/builders/error-builder');
const Logger = require('../lib/logger');
const { connectToFTPServer } = require('../modules/sharedFiles/helpers');
const validateAccess = require('../lib/auth/validate');
const { setUserAndEndpointInfo } = require('../lib/middleware/setUserAndEndpointInfo');
const storageService = require('../modules/fileStorage/services');

const SEE_MORE_ENDPOINT = '/pensionados/consulta-pensionados/ver-mas';

const FactoryController = require('../modules/queryPensions/controllers/queryPensions.controller');
const QueryPensions = require('../modules/queryPensions/services/queryPensions.services');
const {
  validators,
  extendedDataValidation,
  extendedDataValidationPensioner,
  pensionTypeValidation,
  rutSanitizationAndValidation
} = require('../modules/queryPensions/validators');

const clientFTP = new ftp.Client();

const {
  PENSIONER_DOCUMENTS_FTP_HOST,
  PENSIONER_DOCUMENTS_FTP_USER,
  PENSIONER_DOCUMENTS_FTP_PASS,
  PENSIONER_DOCUMENTS_FTP_PORT
} = process.env;
const ftpCredentials = {
  host: PENSIONER_DOCUMENTS_FTP_HOST,
  user: PENSIONER_DOCUMENTS_FTP_USER,
  password: PENSIONER_DOCUMENTS_FTP_PASS,
  port: PENSIONER_DOCUMENTS_FTP_PORT
};

module.exports = router => {
  const queryPensionsController = FactoryController({
    HttpStatus,
    QueryPensions,
    validationResult,
    ErrorBuilder,
    Logger,
    fsClient,
    clientFTP,
    connectToFTPServer,
    ftpCredentials,
    storageService
  });

  router.post('/', [validateAccess(), validators], queryPensionsController.queryPensions);
  router.get(
    '/fetch-pensioner-data',
    [validateAccess(), extendedDataValidation],
    queryPensionsController.getExtendedPensioner
  );
  router.get(
    '/fetch-pensioner-data-temporal-table',
    [validateAccess(), extendedDataValidation],
    queryPensionsController.getPensionerTemporalTable
  );
  router.post(
    '/store-pensioner-files',
    validateAccess(),
    queryPensionsController.storePensionerDocuments
  );
  router.post(
    '/check-pensioner-files',
    validateAccess(),
    queryPensionsController.checkPensionerDocuments
  );
  router.get(
    '/get-pensioner-files/:filenameRegex',
    validateAccess(),
    queryPensionsController.getPensionerDocuments
  );
  router.post(
    '/update-pensioner-temporally',
    [validateAccess(), extendedDataValidationPensioner, setUserAndEndpointInfo(SEE_MORE_ENDPOINT)],
    queryPensionsController.updatePensionerInfoToTemporal
  );
  router.post(
    '/update-pensioner',
    [validateAccess(), extendedDataValidationPensioner, setUserAndEndpointInfo(SEE_MORE_ENDPOINT)],
    queryPensionsController.updatePensionerInfo
  );
  router.post(
    '/update-temporal-pension-type',
    [validateAccess(), pensionTypeValidation, setUserAndEndpointInfo(SEE_MORE_ENDPOINT)],
    queryPensionsController.updateTemporalPensionType
  );
  router.get(
    '/fetch-temporal-pension-type',
    validateAccess(),
    queryPensionsController.getTemporalPensionType
  );
  router.post(
    '/get-historical-settlements',
    [validateAccess(), rutSanitizationAndValidation],
    queryPensionsController.getHistoricalSettlementsForPDF
  );
  router.post(
    '/get-pension-certificate-data',
    [validateAccess(), rutSanitizationAndValidation],
    queryPensionsController.getPensionCertificateData
  );
};

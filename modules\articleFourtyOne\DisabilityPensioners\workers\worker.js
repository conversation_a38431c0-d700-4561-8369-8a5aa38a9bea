/* eslint-disable consistent-return */
/* eslint-disable no-unused-expressions */

const alreadyExecutedMessage = 'Este proceso ya se ejecutó para el mes actual.';
const cronMark = 'UPDATE_BY_ARTICLE_41';
const successMessage = `El proceso ${cronMark} se completó correctamente.`;
const cronDescription = 'calculo art. 41';
const dependencyMark = '';

const workerFn = async ({ Logger, done, logService, service, pensionsService }) => {
  try {
    Logger.info(`Inicio ejecucion proceso para el calculo Art. 41`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return { message: alreadyExecutedMessage, status: 'UNAUTHORIZED', alreadyExecuted: true };
    }
    const { error } = await service.updatePensions(pensionsService);
    if (error) throw new Error(error);
    await logService.saveLog(cronMark);
    Logger.info(`Fin ejecucion proceso para el calculo Art. 41`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription} ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

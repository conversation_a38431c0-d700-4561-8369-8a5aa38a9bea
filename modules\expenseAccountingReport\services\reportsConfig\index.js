module.exports = [
  {
    field: 'asigFam1106010002',
    code: '1106010002',
    label: 'ASIG FAMILIAR'
  },
  {
    field: 'asigFam1106010003',
    code: '1106010003',
    label: 'ASIG FAMILIAR'
  },
  {
    field: 'asigFam1106010001',
    code: '1106010001',
    label: 'ASIG FAMILIAR'
  },
  {
    field: 'asigFam1106010004',
    code: '1106010004',
    label: 'ASIG FAMILIAR'
  },
  {
    field: 'pensiones5103020001',
    code: '5103020001',
    label: 'PENSIONES'
  },
  {
    field: 'pensiones5103020004',
    code: '5103020004',
    label: 'PENSIONES'
  },
  {
    field: 'pensiones5103030001',
    code: '5103030001',
    label: 'PENSIONES'
  },
  {
    field: 'pensiones5103030002',
    code: '5103030002',
    label: 'PENSIONES'
  },
  {
    field: 'pensiones5103030003',
    code: '5103030003',
    label: 'PENSIONES'
  },
  {
    field: 'art531114040001',
    code: '1114040001',
    label: 'ART 53'
  },
  {
    field: 'aguin5113010001',
    code: '5113010001',
    label: 'AGUIN'
  },
  {
    field: 'bonoLey5103010003',
    code: '5103010003',
    label: 'BONO LEY'
  },
  {
    field: 'bonoViudez5103010004',
    code: '5103010004',
    label: 'BONO VIUDEZ'
  },
  {
    field: 'bonoInv1106030004',
    code: '1106030004',
    label: 'BONO INVIERNO'
  },
  {
    field: 'devDsc1114020001',
    code: '1114020001',
    label: 'DEV DSC'
  },
  {
    field: 'devDsc1106030001',
    code: '1106030001',
    label: 'DEV DSC'
  },
  {
    field: 'anticipo1106030006',
    code: '1106030006',
    label: 'ANTICIPO'
  },
  {
    field: 'anticipo1114040003',
    code: '1114040003',
    label: 'ANTCIPO'
  },
  {
    field: 'descInd1114020002',
    code: '1114020002',
    label: 'DESC IND'
  },
  {
    field: 'subsCancExceso5101010004',
    code: '5101010004',
    label: 'SUBS CANC EXCESO'
  },
  {
    field: 'aporteSalud1000000022',
    code: '1000000022',
    label: 'APORTE SALUD DESC PREV ISAPRE CCAF FNS'
  },
  {
    field: 'aporteSalud1000003765',
    code: '1000003765',
    label: 'APORTE SALUD DESC PREV ISAPRE CCAF FNS'
  },
  {
    field: 'aporteSalud1000005903',
    code: '1000005903',
    label: 'APORTE SALUD DESC PREV ISAPRE CCAF FNS'
  },
  {
    field: 'aporteSalud5000000165',
    code: '5000000165',
    label: 'APORTE SALUD DESC PREV ISAPRE CCAF FNS'
  },
  {
    field: 'pensPP2102010001',
    code: '2102010001',
    label: 'PENS PP'
  },
  {
    field: 'retJudPens2102020008',
    code: '2102020008',
    label: 'RET JUD PENS'
  },
  {
    field: 'ctaInstitucional2102020003',
    code: '2102020003',
    label: 'CTA INSTITUCIONAL'
  },
  {
    field: 'distrDePago5103020001',
    code: '5103020001',
    label: 'DISTR DE PAGO'
  }
];

const moment = require('moment');
const { appendFile } = require('fs');
const util = require('util');
const tmp = require('tmp');

const { recursiveSum, roundValue } = require('../../../sharedFiles/helpers');
const { compressFile } = require('../../../sharedFiles/helpers');
const {
  getPensionerRUT,
  getPensionerLastName,
  getPensionerMothersLastName,
  getPensionerName
} = require('../../helpers');

const getFileName = (tempLib = tmp) =>
  `${tempLib.dirSync().name}/ACHS_Circular2480_${moment().format('YYYYMM')}.txt`;
const getZipFileName = () => `SPS_ACHS_${moment().format('YYYYMM')}.zip`;

const ACCIDENT_PENSION_TYPES = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const pensionCodeMap = {
  invalidezparcial: 1,
  invalideztotal: 2,
  graninvalidez: 3,
  graninvalido: 3,
  pensiondeviudezconhijos: 4,
  pensiondeviudezsinhijos: 4,
  pensiondemadredehijodefiliacionnomatrimonialconhijos: 5,
  pensiondemadredehijodefiliacionnomatrimonialsinhijos: 5,
  pensionpororfandad: 6,
  pensiondeorfandaddepadreymadre: 6
};

const cleanType = value =>
  value
    .replace(/\s+/g, '')
    .replace(/[óò]/gi, 'o')
    .replace(/[èé]/gi, 'e')
    .replace(/[áà]/gi, 'a')
    .toLowerCase()
    .trim();

const isAccidentPension = pension => {
  const { pensionType } = pension;
  return ACCIDENT_PENSION_TYPES.some(regex => pensionType.match(regex));
};

const getReportDateAndEntityCode = () => {
  const ENTITY_CODE = 1;
  return `${moment().format('YYYYMMDD')}${ENTITY_CODE}`;
};

const getDateOfBirthAndGender = pension => {
  const { dateOfBirth, gender } = pension;
  const formatedDateOfBirth = moment(dateOfBirth).format('YYYYMMDD');
  return `${formatedDateOfBirth}${gender}`;
};

const getPensionTypeCode = mapObj => pension => {
  const { disabilityType, pensionType } = pension;
  if (isAccidentPension(pension)) {
    return mapObj[cleanType(disabilityType)];
  }
  return mapObj[cleanType(pensionType)];
};

const getAmount = pension => {
  const AMOUNT_MAX_LENGTH = 9;
  const fields = ['basePension', 'article40', 'law19403', 'law19539', 'law19953'];
  const amount = recursiveSum(pension, fields);
  return String(Math.round(roundValue(amount)))
    .padStart(AMOUNT_MAX_LENGTH, '0')
    .substring(0, AMOUNT_MAX_LENGTH);
};

const getLine = pension => {
  const lineData = [getReportDateAndEntityCode()];
  [
    getPensionerRUT,
    getPensionerLastName,
    getPensionerMothersLastName,
    getPensionerName,
    getDateOfBirthAndGender,
    getPensionTypeCode(pensionCodeMap),
    getAmount
  ].forEach(fn => lineData.push(fn(pension)));

  return `${lineData.join('')}\n`;
};

module.exports = { getLine, getFileName, getZipFileName, compressFile, appendFile, util };

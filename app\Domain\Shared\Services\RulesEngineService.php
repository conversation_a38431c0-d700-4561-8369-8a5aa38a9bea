<?php

namespace App\Domain\Shared\Services;

use App\Domain\Pension\Models\Pension;
use App\Domain\Shared\Models\BasePensionRule;
use App\Domain\Shared\Models\BonusRule;
use App\Domain\Shared\Models\CompensationBox;
use App\Domain\Shared\Models\MinimumSalary;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Collection;

/**
 * Servicio que centraliza todas las reglas de negocio del sistema de pensiones
 * Reemplaza la lógica dispersa en múltiples archivos del sistema anterior
 */
class RulesEngineService
{
    /**
     * Obtiene las reglas de pensión base (equivalente a basePensionRules en MongoDB)
     */
    public function getBasePensionRules(): Collection
    {
        return Cache::remember('base_pension_rules', 3600, function () {
            return BasePensionRule::where('enabled', true)
                ->where('type', 'pension')
                ->orderBy('priority')
                ->get();
        });
    }

    /**
     * Obtiene reglas de APS (Aporte Previsional Solidario)
     */
    public function getApsRules(): array
    {
        return Cache::remember('aps_rules', 3600, function () {
            return [
                [
                    'min_pension' => 0,
                    'max_pension' => 150000,
                    'aps_amount' => 25000
                ],
                [
                    'min_pension' => 150001,
                    'max_pension' => 250000,
                    'aps_amount' => 15000
                ],
                [
                    'min_pension' => 250001,
                    'max_pension' => 350000,
                    'aps_amount' => 8000
                ]
            ];
        });
    }

    /**
     * Obtiene reglas de asignación familiar
     */
    public function getFamilyAssignmentRules(): array
    {
        return Cache::remember('family_assignment_rules', 3600, function () {
            return [
                [
                    'min_pension' => 0,
                    'max_pension' => 280000,
                    'amount_per_charge' => 12364 // Valor actualizable por IPC
                ],
                [
                    'min_pension' => 280001,
                    'max_pension' => 420000,
                    'amount_per_charge' => 7598
                ],
                [
                    'min_pension' => 420001,
                    'max_pension' => 999999999,
                    'amount_per_charge' => 2533
                ]
            ];
        });
    }

    /**
     * Obtiene reglas de bonos por tipo
     */
    public function getBonusRules(string $bonusType): array
    {
        return Cache::remember("bonus_rules_{$bonusType}", 3600, function () use ($bonusType) {
            return BonusRule::where('bonus_type', $bonusType)
                ->where('enabled', true)
                ->orderBy('min_pension')
                ->get()
                ->map(function ($rule) {
                    return [
                        'min_pension' => $rule->min_pension,
                        'max_pension' => $rule->max_pension,
                        'bonus_amount' => $rule->bonus_amount
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Obtiene información de cajas de compensación
     */
    public function getCompensationBoxInfo(string $boxName): array
    {
        return Cache::remember("compensation_box_{$boxName}", 3600, function () use ($boxName) {
            $box = CompensationBox::where('name', $boxName)->first();
            
            return [
                'name' => $box->name ?? $boxName,
                'percentage' => $box->percentage ?? 1.0, // 1% por defecto
                'max_amount' => $box->max_amount ?? 999999
            ];
        });
    }

    /**
     * Obtiene el salario mínimo actual
     */
    public function getMinimumSalary(): float
    {
        return Cache::remember('minimum_salary', 3600, function () {
            $minimumSalary = MinimumSalary::where('enabled', true)
                ->where('effective_date', '<=', now())
                ->orderBy('effective_date', 'desc')
                ->first();

            return $minimumSalary?->amount ?? 350000; // Valor por defecto
        });
    }

    /**
     * Obtiene pensión mínima por tipo
     */
    public function getMinimumPension(string $pensionType): float
    {
        $minimumPensions = Cache::remember('minimum_pensions', 3600, function () {
            return [
                'INVALIDEZ_TOTAL' => 185000,
                'INVALIDEZ_PARCIAL' => 140000,
                'SUPERVIVENCIA' => 120000,
                'ORFANDAD' => 80000
            ];
        });

        return $minimumPensions[$pensionType] ?? 0;
    }

    /**
     * Obtiene el monto máximo de descuento AFP
     */
    public function getMaxAfpDiscount(): float
    {
        return Cache::remember('max_afp_discount', 3600, function () {
            // Basado en tope imponible AFP
            return 80.2 * $this->getCurrentUfValue(); // 80.2 UF aproximadamente
        });
    }

    /**
     * Obtiene el monto del bono matrimonio
     */
    public function getMarriageBonusAmount(): float
    {
        return Cache::remember('marriage_bonus_amount', 3600, function () {
            return 150000; // Monto fijo actualizable
        });
    }

    /**
     * Verifica si una regla aplica a una pensión específica
     */
    public function ruleAppliesTo(array $rule, Pension $pension): bool
    {
        // Verificar tipo de pensión
        if (isset($rule['pension_types']) && !in_array($pension->pension_type, $rule['pension_types'])) {
            return false;
        }

        // Verificar rango de fechas
        if (isset($rule['valid_from']) && $pension->pension_start_date < $rule['valid_from']) {
            return false;
        }

        if (isset($rule['valid_to']) && $pension->pension_start_date > $rule['valid_to']) {
            return false;
        }

        // Verificar rango de montos
        if (isset($rule['min_amount']) && $pension->base_pension < $rule['min_amount']) {
            return false;
        }

        if (isset($rule['max_amount']) && $pension->base_pension > $rule['max_amount']) {
            return false;
        }

        return true;
    }

    /**
     * Aplica reajuste IPC a un monto
     */
    public function applyIpcAdjustment(float $amount, string $fromDate, string $toDate = null): float
    {
        $toDate = $toDate ?? now()->format('Y-m-d');
        
        $ipcVariation = Cache::remember("ipc_variation_{$fromDate}_{$toDate}", 3600, function () use ($fromDate, $toDate) {
            // Aquí se calcularía la variación IPC real
            // Por ahora retornamos un valor de ejemplo
            return 1.03; // 3% de ejemplo
        });

        return round($amount * $ipcVariation, 2);
    }

    /**
     * Obtiene reglas específicas para cálculo de descuentos judiciales
     */
    public function getJudicialRetentionRules(): array
    {
        return [
            'max_percentage' => 50, // Máximo 50% de la pensión líquida
            'minimum_exempt' => 0.9, // 90% del mínimo no remuneracional está exento
            'priority_order' => [
                'pension_alimentos',
                'deudas_fiscales',
                'otros_creditos'
            ]
        ];
    }

    /**
     * Valida si un descuento está dentro de los límites legales
     */
    public function validateDiscountLimits(float $netPension, float $totalDiscounts): bool
    {
        $minimumSalary = $this->getMinimumSalary();
        $minimumExempt = $minimumSalary * 0.9;
        $maxDiscountAllowed = $netPension - $minimumExempt;

        return $totalDiscounts <= $maxDiscountAllowed;
    }

    /**
     * Obtiene el valor UF actual (método auxiliar)
     */
    private function getCurrentUfValue(): float
    {
        return Cache::remember('current_uf_value', 1800, function () {
            // Aquí se obtendría el valor real desde el servicio UF
            return 35000; // Valor de ejemplo
        });
    }

    /**
     * Ejemplo de uso completo del RulesEngine
     */
    public function calculatePensionWithRules(Pension $pension): array
    {
        $result = [];

        // 1. Aplicar reglas de pensión base
        $basePensionRules = $this->getBasePensionRules();
        foreach ($basePensionRules as $rule) {
            if ($rule->appliesTo($pension)) {
                $result['base_pension'] = $this->applyRule($rule, $pension);
                break;
            }
        }

        // 2. Aplicar reglas de bonos
        if ($this->shouldReceiveBonus($pension, 'christmas')) {
            $christmasRules = $this->getBonusRules('christmas');
            $result['christmas_bonus'] = $this->calculateBonus($christmasRules, $pension);
        }

        // 3. Aplicar reglas de descuentos
        $result['afp_discount'] = min(
            $pension->taxable_pension * 0.1, // 10% AFP
            $this->getMaxAfpDiscount()
        );

        return $result;
    }

    /**
     * Limpia todas las cachés de reglas
     */
    public function clearRulesCache(): void
    {
        $cacheKeys = [
            'base_pension_rules',
            'aps_rules',
            'family_assignment_rules',
            'minimum_salary',
            'minimum_pensions',
            'max_afp_discount',
            'marriage_bonus_amount',
            'current_uf_value'
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Limpiar cachés de bonos
        $bonusTypes = ['christmas', 'national_holidays', 'winter'];
        foreach ($bonusTypes as $type) {
            Cache::forget("bonus_rules_{$type}");
        }

        // Limpiar cachés de cajas de compensación
        $boxes = ['la_araucana', '18', 'los_andes', 'los_heroes'];
        foreach ($boxes as $box) {
            Cache::forget("compensation_box_{$box}");
        }
    }

    // Métodos auxiliares privados
    private function applyRule($rule, $pension): float
    {
        // Lógica específica de aplicación de regla
        return $pension->base_pension * $rule->adjustment_factor;
    }

    private function shouldReceiveBonus($pension, string $bonusType): bool
    {
        return ($pension->assets['pay_bonus'] ?? 'SI') === 'SI';
    }

    private function calculateBonus(array $rules, $pension): float
    {
        foreach ($rules as $rule) {
            if ($pension->total_base_pension >= $rule['min_pension'] &&
                $pension->total_base_pension <= $rule['max_pension']) {
                return $rule['bonus_amount'];
            }
        }
        return 0;
    }
}

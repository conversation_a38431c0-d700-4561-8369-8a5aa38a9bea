const service = require('../services/jobsMonitor.service');

module.exports = ({
  HttpStatus,
  // eslint-disable-next-line no-unused-vars
  ErrorBuilder = { build: _ => [501, 'not implemented'] },
  Logger
  // CriteriaBuilder @todo
}) => {
  function manageError(res, error) {
    const { code, message = 'Internal server error' } = error;
    res.status(code || HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }

  return {
    getAllJobs: async (req, res) => {
      const { result, error } = await service.getAllMonitorJobs();
      Logger.info('Get MonitorJobs: ', req.details);
      if (error) {
        Logger.error(`Get error: ${JSON.stringify(error)}`, req.details);
        manageError(res, error);
      } else {
        res.status(HttpStatus.OK).json(result);
      }
    },

    insertMarkJob: async (req, res) => {
      Logger.info('insertMarkJob: ');
      const { result, error } = await service.insertMarcaJob(req.params.cronMark);
      if (error) {
        Logger.error(error);
        res.status(HttpStatus.OK).json({ result, error });
      } else {
        Logger.info('insertMarkJob successfully');
        res.status(HttpStatus.OK).json({ result, error });
      }
    }
  };
};

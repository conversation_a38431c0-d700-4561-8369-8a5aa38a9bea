const moment = require('moment');
const { roundValue } = require('../../../sharedFiles/helpers');

const MAX_AGE = 75;

const pensionTypesRuler = [
  /Pensi[óo]n de viudez con hijos/i,
  /Pensi[óo]n de viudez sin hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial con hijos/i,
  /Pensi[óo]n de madre de hijo de filiaci[óo]n no matrimonial sin hijos/i
];

const parseExpression = (ruleExpression, v) => {
  if (!ruleExpression) {
    return true;
  }
  const operator = '(=|>|<|>=|<|<=|<>)';
  const space = '\\s*';
  const age = '(\\d{1,2})';
  const expr = `^(${age}${space}${operator}${space})?Edad${space}${operator}${space}${age}$`;
  const regexp = new RegExp(expr, 'i');
  const [lim1, op1, op2, lim2] = regexp.exec(ruleExpression).reverse();

  const operators = [
    [op1, +lim1],
    [op2, +lim2]
  ];
  const value = +v;
  return operators.reduce((acc, [op, lim], idx) => {
    let term;
    switch (op) {
      case '=':
        term = value === lim;
        break;
      case '<>':
        term = value !== lim;
        break;
      case '<':
        term = idx === 0 ? value < lim : lim < value;
        break;
      case '>':
        term = idx === 0 ? value > lim : lim > value;
        break;
      case '<=':
        term = idx === 0 ? value <= lim : lim <= value;
        break;
      case '>=':
        term = idx === 0 ? value >= lim : lim >= value;
        break;
      default:
        term = true;
    }
    return acc && term;
  }, true);
};

const getAge = pension => moment().diff(moment(pension.dateOfBirth), 'years');

const valueLaw19403 = (pensionBase, basePensionData) => {
  const baseBeneficiaryMinimun = roundValue(pensionBase) + roundValue(basePensionData.law19403);
  const baseMinimun = roundValue(basePensionData.minimun) + roundValue(basePensionData.law19403);
  if (baseBeneficiaryMinimun > baseMinimun)
    return roundValue(
      roundValue(basePensionData.law19403) -
        (roundValue(baseBeneficiaryMinimun) - roundValue(baseMinimun))
    );

  return roundValue(basePensionData.law19403);
};

const valueLaw19539A = (pensionBase, basePensionData) => {
  const law19403Awarded = valueLaw19403(pensionBase, basePensionData);
  const baseBeneficiaryMinimun = roundValue(pensionBase) + roundValue(law19403Awarded);
  const baseMinimun =
    roundValue(basePensionData.minimun) +
    roundValue(basePensionData.law19403) +
    roundValue(basePensionData.law19539);
  if (baseBeneficiaryMinimun < baseMinimun) {
    const baseBeneficiaryBonus =
      roundValue(pensionBase) + roundValue(law19403Awarded) + roundValue(basePensionData.law19539);
    if (baseBeneficiaryBonus > baseMinimun)
      return roundValue(
        roundValue(basePensionData.law19539) -
          (roundValue(baseBeneficiaryBonus) - roundValue(baseMinimun))
      );

    return roundValue(basePensionData.law19539);
  }
  return 0;
};

const valueLaw19539B = (pensionBase, basePensionData) => {
  const baseBeneficiaryMinimun = roundValue(pensionBase);
  const baseMinimun =
    roundValue(basePensionData.minimun) +
    roundValue(basePensionData.law19403) +
    roundValue(basePensionData.law19539);
  if (baseBeneficiaryMinimun < baseMinimun) {
    const baseBeneficiaryBonus = roundValue(pensionBase) + roundValue(basePensionData.law19539);
    if (baseBeneficiaryBonus > baseMinimun)
      return roundValue(
        roundValue(basePensionData.law19539) -
          (roundValue(baseBeneficiaryBonus) - roundValue(baseMinimun))
      );

    return roundValue(basePensionData.law19539);
  }
  return 0;
};

const valueLaw19953A = (pensionBase, basePensionData) => {
  const law19403Awarded = valueLaw19403(pensionBase, basePensionData);
  const law19539Awarded = valueLaw19539A(pensionBase, basePensionData);
  const baseBeneficiaryMinimun =
    roundValue(pensionBase) + roundValue(law19403Awarded) + roundValue(law19539Awarded);

  const baseMinimun =
    roundValue(basePensionData.minimun) +
    roundValue(basePensionData.law19403) +
    roundValue(basePensionData.law19539) +
    roundValue(basePensionData.law19953);
  if (baseBeneficiaryMinimun < baseMinimun) {
    const baseBeneficiaryBonus =
      roundValue(pensionBase) +
      roundValue(law19403Awarded) +
      roundValue(law19539Awarded) +
      roundValue(basePensionData.law19953);
    if (baseBeneficiaryBonus > baseMinimun)
      return roundValue(
        roundValue(basePensionData.law19953) -
          (roundValue(baseBeneficiaryBonus) - roundValue(baseMinimun))
      );

    return roundValue(basePensionData.law19953);
  }
  return 0;
};

const payLawBonds = pension => {
  const { retirement, amountOtherPension } = pension;
  return !(retirement === true || amountOtherPension > 0);
};

async function ruleFactory({ pensionTypes, getRules, getNomenclatorValue, pension }) {
  const type = pensionTypes.find(p => p.test(pension.pensionType));
  const age = getAge(pension);

  let basePensionRules = [];
  if (type) basePensionRules = pension.basePensionRules.filter(ruler => type.test(ruler.label));

  const basePensionData = basePensionRules.filter(r => parseExpression(r.age, age));
  return basePensionData
    .filter(r => r.value && r.value.minimun > 0)
    .map(({ value }) => {
      const defaultPension = { ...pension, law19403: 0, law19539: 0, law19953: 0 };
      const rules = getRules(age);
      if (rules) {
        const [newPension] = rules.map(fn => fn(pension, value)).filter(r => r);
        return {
          ...(newPension || defaultPension),
          basePension: Math.max(getNomenclatorValue(value), newPension.basePension)
        };
      }
      return null;
    });
}

module.exports = {
  parseExpression,
  getAge,
  valueLaw19403,
  valueLaw19539A,
  valueLaw19539B,
  valueLaw19953A,
  ruleFactory,
  payLawBonds,
  pensionTypesRuler,
  MAX_AGE: process.env.CRON_BASE_MINIMUN_PENSION_MAX_AGE || MAX_AGE
};

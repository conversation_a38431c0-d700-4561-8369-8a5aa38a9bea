const moment = require('moment');
const fsClient = require('fs').promises;
const textTable = require('text-table');
const uploadToSFTP = require('./sftpClient');
const { Client, connectToSFTPServer } = require('../../sharedFiles/sftpClient');
const linkService = require('../../linkPensions/services/link.service');
const logService = require('../../sharedFiles/services/jobLog.service');
const workerModule = require('./worker');
const mapperData = require('./mapper');

const {
  CIVIL_REGISTRATION_SFTP_HOST,
  CIVIL_REGISTRATION_SFTP_USER,
  CIVIL_REGISTRATION_SFTP_PASS,
  CIVIL_REGISTRATION_SFTP_PORT
} = process.env;
const sftpCredentials = {
  host: CIVIL_REGISTRATION_SFTP_HOST,
  user: CIVIL_REGISTRATION_SFTP_USER,
  password: CIVIL_REGISTRATION_SFTP_PASS,
  port: CIVIL_REGISTRATION_SFTP_PORT
};

module.exports = {
  name: 'toCivilRegistration',
  worker: deps => {
    const sftpClient = new Client();
    return workerModule.workerFn({
      service: linkService,
      logService,
      fs: fsClient,
      sftpClient,
      sftpCredentials,
      connectToSFTPServer,
      table: textTable,
      mapperData,
      moment,
      uploadToSFTP,
      ...deps
    });
  },
  repeatInterval: process.env.CRON_CIVIL_REGISTRATION,
  description: 'Enviar archivo txt con pensionados vigentes al registro civil',
  endPoint: 'tocivilregistration',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.firstDependencyMark
};

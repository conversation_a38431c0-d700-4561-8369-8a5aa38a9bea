const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const TemporaryBonusPensionersSchema = new Schema(
  {
    pensionerRut: { type: Number, required: true },
    pensionerCheckDigit: { type: String, maxlength: 1, required: true },
    pensionerName: { type: String, maxlength: 40, required: true },
    dateOfBirth: { type: Date, required: true },
    institutionCode: { type: Number, required: true },
    institutionRut: { type: Number, required: true },
    institutionCheckDigit: { type: String, maxlength: 1, required: true },
    pensionStartDate: { type: Date, required: true },
    pensionAmount: { type: Number, required: true },
    numberOfCharges: { type: Number, required: true },
    pensionType: { type: Number, required: true },
    payBonus: { type: String, maxlength: 2, required: true }
  },

  { timestamps: true }
);
TemporaryBonusPensionersSchema.plugin(paginate);
TemporaryBonusPensionersSchema.index({ pensionerRut: 1 });

module.exports = mongoose.model('TemporaryBonusPensioners', TemporaryBonusPensionersSchema);

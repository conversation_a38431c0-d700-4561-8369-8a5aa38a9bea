const HttpStatus = require('../lib/constants/http-status');
const Logger = require('../lib/logger');
const dbService = require('../modules/pensionsAccountingReport/services/dbService');
const reportService = require('../modules/pensionsAccountingReport/services/reportGeneratorService');
const FactoryController = require('../modules/pensionsAccountingReport/controllers/index.controller');
const validateAccess = require('../lib/auth/validate');

module.exports = route => {
  const controller = FactoryController({ HttpStatus, Logger, dbService, reportService });

  route.get('/', validateAccess(), controller.getReport);
  route.get('/existsdata', validateAccess(), controller.existsData);
};

const mongoose = require('mongoose');
const paginate = require('../../../lib/plugins/mongoose-paginate');

const { Schema } = mongoose;

const temporaryHorphanhood = new Schema({
  pensionId: { type: Number, required: true },
  causantRut: { type: String, required: true },
  collectorRut: { type: String, required: false },
  beneficiaryRut: { type: String, required: true },
  startDate: { type: Date, required: false },
  endDate: { type: Date, required: false },
  state: { type: Number, required: false },
  validity: { type: Boolean, required: true },
  dayOfBirth: { type: Date, required: false },
  motherRut: { type: String, required: false },
  familyGroup: { type: Number, required: false }
});

temporaryHorphanhood.plugin(paginate);
temporaryHorphanhood.index({ validity: 1 });
temporaryHorphanhood.index({ beneficiaryRut: 1, causantRut: 1 });
module.exports = mongoose.model('temporaryHorphanhood', temporaryHorphanhood);

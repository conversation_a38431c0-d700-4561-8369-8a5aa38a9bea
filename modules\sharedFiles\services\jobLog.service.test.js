/* eslint-disable no-console */
const { beforeAllTests, afterAllTests } = require('../../testsHelper');
const LogModel = require('../models/processedJob');
const service = require('./jobLog.service');

describe('Job Log Test', () => {
  beforeAllTests(beforeAllTests);

  it('should create log successfully', async () => {
    await service.saveLog('SOME_NAME');
    const doesExist = await service.existsLog('SOME_NAME').catch(e => console.error(e));
    expect(doesExist).toBe(true);
  });

  it('should return true if all marks exist and false if not', async () => {
    await service.saveLog('mark_1');
    await service.saveLog('mark_2');
    await service.saveLog('mark_3');
    const result1 = await service.allMarksExists(['mark_1', 'mark_2', 'mark_3']);
    const result2 = await service.allMarksExists(['mark_1', 'mark_2', 'mark_4']);
    expect(result1).toBe(true);
    expect(result2).toBe(false);
  });

  it('should return it has exceeded the number of attemps', async () => {
    const serviceName = 'someService';

    const executionTimes = process.env.CRONS_EXECUTION_ATTEMPTS || 10 + 2;
    await Promise.all(
      [...Array(executionTimes).keys()].map(async () => service.retryLog(serviceName, LogModel))
    );

    const { existsLog, message } = await service.existsLogAndRetry(serviceName, LogModel);

    expect(existsLog).toBe(true);
    expect(message).toBe('Intentos sobrepasados');
  });

  it('should return cron is already executed', async () => {
    const serviceName = 'someService1';

    await service.saveLog(serviceName);

    const executionTimes = process.env.CRONS_EXECUTION_ATTEMPTS || 10 - 5;
    await Promise.all(
      [...Array(executionTimes).keys()].map(async () => service.retryLog(serviceName, LogModel))
    );

    const { existsLog, message } = await service.existsLogAndRetry(serviceName, LogModel);

    expect(existsLog).toBe(true);
    expect(message).toBe('Este proceso fue ejecutado para el mes actual');
  });

  it('should return cronMark does not exist', async () => {
    const serviceName = 'someService2';

    const { existsLog, attempts } = await service.existsLogAndRetry(serviceName, LogModel);

    expect(existsLog).toBe(false);
    expect(attempts).toBeUndefined();
  });

  it('should complete a job rescheduling', async () => {
    const job = {
      attrs: {},
      save: jest.fn(() => Promise.resolve())
    };
    const timeToAdd = 5;

    const { completed } = await service.addRetryTimeToJob(job, timeToAdd);

    expect(completed).toBe(true);
  });

  afterEach(async () => {
    await LogModel.deleteMany({});
  });

  afterAll(afterAllTests);
});

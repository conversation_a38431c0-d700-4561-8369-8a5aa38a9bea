const dependencyMark = [
  'INACTIVATE_OR_REACTIVATE_FAMILY_ASSIGNEMENT_PROCESS',
  'INACTIVATE_OR_REACTIVATE_ORPHANHOOD_PROCESS'
];
const cronMark = 'INACTIVATE_OR_REACTIVATE_AF_CRONS_GROUP';

const cronDescription = 'inactivate or reactivate AFC crons group:';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';

const successMessage = `El proceso ${cronMark} se completó correctamente`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const getMissingDepsMessage = arr =>
  `No se ha ejecutado una o más de las dependencias ${arr.join(', ')} `;

const workerFn = async ({ Logger, done, logService, cronsGroup, job }) => {
  try {
    Logger.info(`inicio ejecucion cron ${cronMark}`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED'
      };
    }

    if (!(await logService.allMarksExists(dependencyMark))) {
      Logger.info(`${cronMark} ${getMissingDepsMessage(dependencyMark)}`);
      return { message: getMissingDepsMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }

    const {
      executionCompleted: changePensionTypesCompleted,
      message: changePensionTypesMessage,
      alreadyExecuted: changePensionTypesExecuted
    } = await cronsGroup.changePensionTypes({ Logger, done });

    const {
      executionCompleted: widowUnder45Completed,
      message: widowUnder45Message,
      alreadyExecuted: widowUnder45Executed
    } = await cronsGroup.widowUnder45Service({ Logger, done });

    const {
      executionCompleted: reactivateForRenewalOfStudyCertificateCompleted,
      message: reactivateForRenewalOfStudyCertificateMessage,
      alreadyExecuted: reactivateForRenewalOfStudyCertificateExecuted
    } = await cronsGroup.reactivateForRenewalOfStudyCertificate({ Logger, done });

    const {
      executionCompleted: registerStartAndEndDateOfStudyCertificateCompleted,
      message: registerStartAndEndDateOfStudyCertificateMessage,
      alreadyExecuted: registerStartAndEndDateOfStudyCertificateExecuted
    } = await cronsGroup.registerStartAndEndDateOfStudyCertificate({ Logger, done });

    const {
      executionCompleted: reactivateOrphanhoodCompleted,
      message: reactivateOrphanhoodMessage,
      alreadyExecuted: reactivateOrphanhoodExecuted
    } = await cronsGroup.reactivateOrphanhood({ Logger, done });

    const {
      executionCompleted: updateByArticleFourtyOneCompleted,
      message: updateByArticleFourtyOneMessage,
      alreadyExecuted: updateByArticleFourtyOneExecuted
    } = await cronsGroup.updateByArticleFourtyOne({ Logger, done });

    const allCompleted =
      (changePensionTypesCompleted || changePensionTypesExecuted) &&
      (widowUnder45Completed || widowUnder45Executed) &&
      (reactivateForRenewalOfStudyCertificateCompleted ||
        reactivateForRenewalOfStudyCertificateExecuted) &&
      (registerStartAndEndDateOfStudyCertificateCompleted ||
        registerStartAndEndDateOfStudyCertificateExecuted) &&
      (reactivateOrphanhoodCompleted || reactivateOrphanhoodExecuted) &&
      (updateByArticleFourtyOneCompleted || updateByArticleFourtyOneExecuted);

    const messageOutput = {
      changePensionTypesMessage,
      widowUnder45Message,
      reactivateForRenewalOfStudyCertificateMessage,
      registerStartAndEndDateOfStudyCertificateMessage,
      reactivateOrphanhoodMessage,
      updateByArticleFourtyOneMessage
    };

    if (!allCompleted) throw new Error(JSON.stringify(messageOutput));

    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription} ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

/* eslint-disable consistent-return */
const Sftp = require('../../../sharedFiles/sftpClient');
const logService = require('../../../sharedFiles/services/jobLog.service');
const fileGenerationUtils = require('../services/fileGenerationUtils');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'sendCircularFile2480',
  worker: async deps =>
    workerModule.workerFn({ service, logService, fileGenerationUtils, Sftp, ...deps }),
  repeatInterval: process.env.CRON_SEND_CIRCULAR_FILE_2480_FREQUENCY,
  description: 'Generación y envío del archivo Circular 2480 al SFTP del IPS',
  endPoint: 'sendcircularfile2480',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

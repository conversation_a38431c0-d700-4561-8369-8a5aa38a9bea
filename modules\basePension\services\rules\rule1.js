/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
const { parseExpression, getAge } = require('./helper');
const { roundValue } = require('../../../sharedFiles/helpers');

const pensionTypes = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por orfandad/i,
  /Pensi[óo]n de orfandad/i,
  /Pensi[óo]n por enfermedad profesional/i
];

const pensionTypesArticle40 = [
  /Pensi[óo]n por accidente de trabajo/i,
  /Pensi[óo]n por accidente de trayecto/i,
  /Pensi[óo]n por enfermedad profesional/i
];

async function ruleFn(pension) {
  const { pensionType, basePension, article40 = 0 } = pension;
  const regPensionType = pensionTypes.find(p => p.test(pensionType));
  const regPensionTypeArticle40 = pensionTypesArticle40.find(p => p.test(pensionType));
  let basePensionAux = basePension;
  if (regPensionTypeArticle40) {
    basePensionAux = basePension + article40;
  }

  let basePensionRules = [];
  if (regPensionType)
    basePensionRules = pension.basePensionRules.filter(ruler => regPensionType.test(ruler.label));

  const basePensionData = basePensionRules.filter(r => parseExpression(r.age, getAge(pension)));
  return basePensionData.map(({ value }) => {
    if (value && value.minimun > basePensionAux) {
      return { ...pension, basePension: roundValue(value.minimun) };
    }
    return pension;
  });
}

module.exports = ruleFn;

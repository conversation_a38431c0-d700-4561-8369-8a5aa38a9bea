/* eslint-disable consistent-return */

const cronDescription =
  'Inactivar viudas < 45 sin cargas/reasignar vitalicia viudas >=45 con cargas';

const alreadyExecutedMessage = 'This process was already executed for the current month.';
const successMessage = `${cronDescription} process completed successfully`;
const cronMark = 'INACTIVATE_WIDOW_UNDER_45';
const dependencyMark = '';

const workerFn = async ({ Logger, done, logService, service, pensionService }) => {
  try {
    Logger.info(`Inicio proceso: ${cronDescription}`);
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    await service.updatePensions(pensionService);
    await logService.saveLog(cronMark);

    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    return { message: `${cronDescription}  ${error}`, executionCompleted: false };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };

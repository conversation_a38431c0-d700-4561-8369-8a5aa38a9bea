const logService = require('../../sharedFiles/services/jobLog.service');
const pensionService = require('../../pensions/services/pension.service');
const service = require('../services/dbService');
const workerModule = require('./worker');

module.exports = {
  name: 'rejectedReservedAmount',
  worker: deps =>
    workerModule.workerFn({
      service,
      logService,
      pensionService,
      ...deps
    }),
  repeatInterval: process.env.CRON_REJECTED_RESERVED_AMOUNT_FREQUENCY,
  description: 'Cálculo de reservados por rechazo y devolución',
  endPoint: 'rejectedreservedamount',
  cronMark: workerModule.cronMark,
  dependencyMark: workerModule.dependencyMark
};

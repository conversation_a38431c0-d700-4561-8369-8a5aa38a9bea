/* eslint-disable no-console */
const puppeteer = require('puppeteer');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const { uploadFile, downloadFTPFile } = require('./upload.service');

describe('Previred file upload automation', () => {
  beforeAll(beforeAllTests);
  let mocks;
  beforeEach(() => {
    mocks = {
      newPage: jest.fn().mockResolvedValue({
        goto: jest.fn(),
        waitForSelector: jest.fn(),
        click: jest.fn(),
        select: jest.fn(),
        waitFor: jest.fn(),
        $: jest.fn().mockResolvedValue({ uploadFile: jest.fn() })
      }),
      close: jest.fn().mockResolvedValue(true)
    };
    jest.spyOn(puppeteer, 'launch').mockImplementationOnce(() => mocks);
  });

  const client = {
    list: jest.fn().mockResolvedValue(['previred.txt']),
    downloadTo: jest.fn().mockResolvedValue(true),
    close: jest.fn()
  };

  const connectToFTPServer = jest.fn().mockResolvedValue({ connected: true, error: null });
  const tmp = {
    fileSync: jest.fn().mockReturnValue({ name: 'previred.txt' }),
    dirSync: jest.fn().mockReturnValue({ name: 'tmp/folder' })
  };
  const fillField = jest.fn();
  const compressFileToUpload = jest.fn();

  it('should automate previred file upload', async () => {
    const file = 'myFile.txt';
    await uploadFile({ file, puppeteer, fillField, compressFileToUpload, tmp });
    expect(puppeteer.launch).toHaveBeenCalled();
    expect(mocks.newPage).toHaveBeenCalled();
    expect(mocks.close).toHaveBeenCalled();
  });

  it('should close browser if any error', async () => {
    mocks.newPage = jest.fn().mockImplementationOnce(() => {
      throw new Error('ERROR UPLOADIND');
    });
    try {
      const file = 'myFile.txt';
      await uploadFile({ file, puppeteer, fillField, compressFileToUpload, tmp });
      expect(mocks.close).toHaveBeenCalled();
    } catch (error) {
      expect(error).not.toBeUndefined();
    }
  });

  it('should get file from ftp', async () => {
    try {
      const { file, error } = await downloadFTPFile({
        client,
        connectToFTPServer,
        tmp
      });
      expect(error).toBe(null);
      expect(file).toBe('previred.txt');
    } catch (error) {
      console.error(error);
    }
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

const BanksModel = require('../models/banks');
const service = require('./index.service');

let Logger;

describe('Banks nomenclator service Test', () => {
  beforeAll(beforeAllTests);

  Logger = {
    error: jest.fn(),
    info: jest.fn()
  };
  it('should create', async () => {
    const { error } = await service.createBank({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const { result, isError } = await service.getBanks();

    expect(error).not.toBe(true);
    expect(isError).toBe(undefined);
    expect(result.length).toBe(1);
  });

  it('should find one and update', async () => {
    await BanksModel.create({
      id: 'id-1-B',
      name: 'Bank A',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '111',
      enabled: false
    });

    const newBankData = {
      id: 'id-1-A',
      name: 'Bank B',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '999',
      enabled: true
    };
    // create and save one  document
    await BanksModel.create(newBankData);

    const { error } = await service.updateBank({ ...newBankData, name: 'Banco z', code: '111' });
    const { result, isError } = await service.getBanks();

    expect(error).not.toBe(true);
    expect(isError).not.toBeDefined();
    expect(result.length).toBe(1);
    expect(result[0].name).toBe('Banco z');
  });

  it('should delete a bank', async () => {
    // create and save one  document
    const bank = await BanksModel.create({
      id: 'id-1-A',
      name: 'Bank A',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '999',
      enabled: true
    });

    const { id, createdAt, updatedAt } = { ...bank.toObject() };

    const { error } = await service.deleteBank(id);

    const [resultWithoutBanks, resultWithBanks] = await Promise.all([
      service.getBanks(),
      service.getBanks({ enabled: false })
    ]);
    expect(error).not.toBe(true);
    expect(new Date(createdAt).toUTCString()).toEqual(new Date(updatedAt).toUTCString());

    expect(resultWithoutBanks.result.length).toBe(0);
    expect(resultWithBanks.result.length).toBe(1);
    expect(resultWithBanks.result[0].enabled).toEqual(false);
  });

  it('can´t  duplicate code', async () => {
    // create and save one  document
    await BanksModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const servipag2 = await BanksModel.create({
      id: 'id-1-B',
      name: 'ServicePag  La Florida',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '001',
      enabled: true
    });

    const { id, code } = { ...servipag2.toObject(), code: '000' };
    const { error } = await service.updateBank({
      id,
      code
    });
    const { result } = await service.getBanks();

    expect(result[1].code).toBe('001');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  it('can´t  duplicate name', async () => {
    // create and save one  document
    await BanksModel.create({
      id: 'id-1-A',
      name: 'ServicePag  pte alto',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '000',
      enabled: true
    });
    const servipag2 = await BanksModel.create({
      id: 'id-1-B',
      name: 'ServicePag  La Florida',
      city: 'Santiago',
      direction: 'Alameda 2314',
      code: '001',
      enabled: true
    });

    const { id, name } = { ...servipag2.toObject(), name: 'ServicePag  pte alto' };
    const { error } = await service.updateBank({
      id,
      name
    });
    const { result } = await service.getBanks();

    expect(result[1].name).toBe('ServicePag  La Florida');
    expect(error.code).toBe(11000);
    expect(error.codeName).toBe('DuplicateKey');
  });

  afterEach(async () => {
    try {
      await BanksModel.deleteMany({});
    } catch (error) {
      Logger.error(error);
    }
  });

  afterAll(afterAllTests);
});

/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');
const workerModule = require('./worker');
const pensionService = require('../../../pensions/services/pension.service');
const pensionToReactivate = require('../../../../resources/pensionToReactivate.json');
const responseSap = require('../../../../resources/responseSapRequest.json');
const transientHelper = require('../services/transientHelper');

describe('reactivate transient worker', () => {
  beforeAll(beforeAllTests);
  let axios;
  let transientService;
  let linkService;
  let logService;
  let sapRequests;
  let done;
  let Logger;
  beforeEach(() => {
    axios = jest.fn(() => Promise.resolve([]));
    linkService = {
      getAllAndFilter: jest.fn(() => ({ result: [pensionToReactivate[0]] }))
    };

    sapRequests = { getDataToReactivate: jest.fn(() => Promise.resolve({ data: responseSap })) };

    transientService = {
      reactivateTransient: jest.fn(() => Promise.resolve({ error: false })),
      createMarkToReactivate: jest.fn(() => Promise.resolve({ error: null }))
    };
    logService = {
      existsLog: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
    done = jest.fn();
  });

  it('success worker', async () => {
    await workerModule.workerFn({
      Logger,
      pensionService,
      linkService,
      axios,
      transientService,
      transientHelper,
      sapRequests,
      logService,
      done
    });

    expect(transientService.reactivateTransient).toHaveBeenCalled();
  });

  it('failed worker', async () => {
    logService.existsLog = jest.fn(() => Promise.reject());
    await workerModule.workerFn({
      Logger,
      pensionService,
      linkService,
      axios,
      transientService,
      transientHelper,
      sapRequests,
      logService,
      done
    });
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
    expect(transientService.reactivateTransient).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('cron mark already exists ', async () => {
    logService = {
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: true }))
    };
    await workerModule.workerFn({
      Logger,
      pensionService,
      linkService,
      axios,
      transientService,
      transientHelper,
      sapRequests,
      logService,
      done
    });
    expect(logService.existsLogAndRetry).toBeCalled();
  });

  it('dependency  mark already exists ', async () => {
    await workerModule.workerFn({
      Logger,
      pensionService,
      linkService,
      axios,
      transientService,
      transientHelper,
      sapRequests,
      logService,
      done
    });
    expect(logService.existsLogAndRetry).toBeCalled();
    expect(logService.existsLog).toBeCalled();
  });
  afterAll(afterAllTests);
});

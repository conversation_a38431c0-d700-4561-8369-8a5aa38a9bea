/* eslint-disable import/prefer-default-export */
const lineReader = require('line-reader');

const maritalStatusArray = ['C', 'D', 'P', 'S', 'V'];
const readLines = async file => {
  return new Promise(resolve => {
    const lines = [];
    lineReader.eachLine(file, (line, last) => {
      const rut = line.slice(148, 157);
      const resultFlag = line.slice(147, 148);
      const fathersLastName = line.slice(157, 187);
      const mothersLastName = line.slice(187, 217);
      const beneficiaryNames = line.slice(217, 267);
      const birthDate = line.slice(267, 275);
      const gender = line.slice(275, 276);
      const country = line.slice(276, 277);
      let maritalStatus = line.slice(277, 278);

      if (!maritalStatusArray.includes(maritalStatus)) {
        maritalStatus = 'S';
      }

      lines.push([
        rut,
        resultFlag,
        fathersLastName,
        mothersLastName,
        beneficiaryNames,
        birthDate,
        gender,
        country,
        maritalStatus
      ]);
      if (last) {
        resolve(lines);
        return false;
      }
      return true;
    });
  });
};

module.exports = {
  readLines
};

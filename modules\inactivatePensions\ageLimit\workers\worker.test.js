/* eslint-disable no-console */
/* eslint-disable no-underscore-dangle */

const workerModule = require('./worker');

const { beforeAllTests, afterAllTests } = require('../../../testsHelper');

describe('worker inactivvate age limit Test', () => {
  beforeAll(beforeAllTests);
  let service;
  let Logger;
  let logService;
  let done;
  beforeEach(() => {
    done = jest.fn();
    service = {
      inactivatePensionsByAgeLimitCompliance: jest.fn(() =>
        Promise.resolve({ completed: true, err: null })
      )
    };
    logService = {
      existsLog: jest
        .fn(() => Promise.resolve(true))
        .mockImplementationOnce(() => Promise.resolve(false)),
      allMarksExists: jest.fn(() => Promise.resolve(true)),
      saveLog: jest.fn(() => Promise.resolve()),
      retryLog: jest.fn(() => Promise.resolve()),
      existsLogAndRetry: jest.fn(() => Promise.resolve({ existsLog: false })),
      addRetryTimeToJob: jest.fn(() => Promise.resolve())
    };
    Logger = {
      error: jest.fn(),
      info: jest.fn()
    };
  });

  it('success worker', async () => {
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.inactivatePensionsByAgeLimitCompliance).toBeCalled();
    expect(logService.saveLog).toBeCalled();
  });

  it('already worked in current month', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.resolve({ existsLog: true }));

    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.inactivatePensionsByAgeLimitCompliance).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
  });

  it('fail worker', async () => {
    logService.existsLogAndRetry = jest.fn(() => Promise.reject());
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.inactivatePensionsByAgeLimitCompliance).not.toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  it('fail worker error catching service', async () => {
    service.inactivatePensionsByAgeLimitCompliance = jest.fn(() =>
      Promise.reject(new Error('Error service'))
    );
    await workerModule.workerFn({ Logger, service, logService, done });

    expect(logService.existsLogAndRetry).toBeCalled();
    expect(service.inactivatePensionsByAgeLimitCompliance).toBeCalled();
    expect(logService.saveLog).not.toBeCalled();
    expect(Logger.error).toHaveBeenCalledTimes(1);
  });

  afterAll(afterAllTests);
});

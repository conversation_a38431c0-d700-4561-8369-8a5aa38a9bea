/* eslint-disable import/prefer-default-export */
const lineReader = require('line-reader');

const DISCOUNT_FILE_ID = '215';
const SOCIAL_CREDIT_FILE_ID = '221';
const ANOTHER_DISCOUNT_FILE_ID = '222';
const aIIItapsOptions = {
  rutStartPos: 31,
  rutEndPos: 40,
  amountStartPos: 114,
  amountEndPos: 122,
  FileType: 'aIIItaps'
};
const papsoeOptions = {
  rutStartPos: 23,
  rutEndPos: 32,
  FileType: 'papsoe'
};
const brsaludOptions = {
  rutStartPos: 24,
  rutEndPos: 33,
  FileType: 'brsalud'
};

const movementTypeFilter = line => line.length && (line[1] === '1' || line[1] === '3');

const readLines = async filePath => {
  return new Promise(resolve => {
    const lines = [];
    lineReader.eachLine(filePath, (line, last) => {
      lines.push(line);
      if (last) {
        const id = line.trim().slice(14, 17) || null;
        resolve({ id, lines });
        return false;
      }
      return true;
    });
  });
};

const getFormatedRut = rut => {
  const formatedRut = rut
    .replace(/^0+/, '')
    .replace(/[^\dk]/gi, '')
    .toLowerCase();
  return formatedRut.replace(/([\dk]{1})$/, '-$1') || null;
};

const getFormatedAmount = amount => {
  const cleanedAmount = amount.replace(/[^\d]/gi, '').toLowerCase();
  const formatedAmount = cleanedAmount.replace(/(\d{2})$/, '.$1');
  return parseFloat(formatedAmount) || null;
};

const parseIpsFileLines = (lines, options) => {
  const RES_AMOUNT_START_POS = 15;
  const RES_AMOUNT_END_POS = 23;
  const RESOLUTION_DATE_START_POS = 23;
  const RESOLUTION_DATE_END_POS = 31;
  const PAYMENT_UNIQUE_START_POS = 132;
  const PAYMENT_UNIQUE_END_POS = 148;
  const TRANSFER_CODE_START_POS = 158;
  const TRANSFER_CODE_END_POS = 168;

  const { rutStartPos, rutEndPos, amountStartPos, amountEndPos, FileType } = options;
  const parsedLines = lines.map(line => {
    const rut = getFormatedRut(line.trim().slice(rutStartPos, rutEndPos));
    const amount = parseInt(line.trim().slice(amountStartPos, amountEndPos), 10);
    let resolutionNumber = '';
    let resolutionDate = '';
    let paymentUniqueId = '';
    let transferCode = '';

    if (FileType === 'aIIItaps') {
      resolutionNumber = line.trim().slice(RES_AMOUNT_START_POS, RES_AMOUNT_END_POS);
      resolutionDate = line.trim().slice(RESOLUTION_DATE_START_POS, RESOLUTION_DATE_END_POS);
      paymentUniqueId = line.trim().slice(PAYMENT_UNIQUE_START_POS, PAYMENT_UNIQUE_END_POS);
      transferCode = line.trim().slice(TRANSFER_CODE_START_POS, TRANSFER_CODE_END_POS);
    }

    return FileType === 'aIIItaps'
      ? [rut, amount, resolutionNumber, resolutionDate, paymentUniqueId, transferCode]
      : rut;
  });
  return parsedLines;
};

const getParsedLines = (lines, type) => {
  const parsedLines = lines.map(line => {
    const rut = getFormatedRut(line.trim().slice(5, 14));
    const movementType = line.trim().slice(29, 30);
    const discountAmount = getFormatedAmount(line.trim().slice(80, 90));
    return type === 'DISCOUNT' ? [rut, movementType] : [rut, discountAmount];
  });
  return type === 'DISCOUNT' ? parsedLines.filter(movementTypeFilter) : parsedLines;
};

const getLinesFromFiles = async filesPath => {
  const filesData = await Promise.all(filesPath.map(path => readLines(path)));
  const [discountFileData] = filesData.filter(fileData => fileData.id === DISCOUNT_FILE_ID);
  const [socialCreditFileData] = filesData.filter(
    fileData => fileData.id === SOCIAL_CREDIT_FILE_ID
  );
  const [anotherDiscountFileData] = filesData.filter(
    fileData => fileData.id === ANOTHER_DISCOUNT_FILE_ID
  );
  return {
    discoundFileLines: discountFileData ? discountFileData.lines : [],
    socialCreditFileLines: socialCreditFileData ? socialCreditFileData.lines : [],
    anotherDiscountFileLines: anotherDiscountFileData ? anotherDiscountFileData.lines : []
  };
};

const getParsedLinesFromFiles = async (...filesPath) => {
  const {
    discoundFileLines,
    socialCreditFileLines,
    anotherDiscountFileLines
  } = await getLinesFromFiles(filesPath);
  const discountFileParsedLines = getParsedLines(discoundFileLines, 'DISCOUNT');
  const socialCreditFileParsedLines = getParsedLines(socialCreditFileLines);
  const anotherDiscountFileParsedLines = getParsedLines(anotherDiscountFileLines);
  return { discountFileParsedLines, socialCreditFileParsedLines, anotherDiscountFileParsedLines };
};

const getParsedLinesFromIpsFiles = async (...filesPath) => {
  const [aIIItapsFile, papsoeFile, brsaludFile] = filesPath;
  const { lines: aIIItapsLines } = await readLines(aIIItapsFile);
  const { lines: papsoeLines } = await readLines(papsoeFile);
  const { lines: brsaludLines } = await readLines(brsaludFile);
  const parsedAIIItapsLines = parseIpsFileLines(aIIItapsLines, aIIItapsOptions);
  const parsedPapsoeLines = parseIpsFileLines(papsoeLines, papsoeOptions);
  const parsedBrsaludLines = parseIpsFileLines(brsaludLines, brsaludOptions);

  return { parsedAIIItapsLines, parsedPapsoeLines, parsedBrsaludLines };
};

module.exports = { getParsedLinesFromFiles, getParsedLinesFromIpsFiles };

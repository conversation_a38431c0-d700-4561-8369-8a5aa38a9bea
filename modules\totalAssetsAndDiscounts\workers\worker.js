const cronMark = 'TOTAL_ASSETS_AND_DISCOUNTS';
const dependencyMark = 'UNIFIED_RETROACTIVE_AMOUNT_CALCULATION';
const cronDescription = 'calculate total assets and discounts';
const alreadyExecutedMessage = 'Este proceso fue ejecutado para el mes actual';
const successMessage = `El proceso ${cronMark} se completó correctamente`;
const getMissingDependencyMessage = dep => `No se ha ejecutado la dependencia "${dep}"`;
const retryFrequencyInMinutes = process.env.RETRY_CRON_EXECUTION_MINUTES_FREQUENCY;

const workerFn = async ({ done, Logger, logService, service, job }) => {
  try {
    const { existsLog } = await logService.existsLogAndRetry(cronMark);
    if (existsLog) {
      Logger.info(alreadyExecutedMessage);
      return {
        message: alreadyExecutedMessage,
        status: 'UNAUTHORIZED',
        alreadyExecuted: true
      };
    }
    if (!(await logService.existsLog(dependencyMark))) {
      Logger.info(`execution interrupted: ${getMissingDependencyMessage(dependencyMark)}`);
      return { message: getMissingDependencyMessage(dependencyMark), status: 'UNAUTHORIZED' };
    }
    Logger.info(`Inicio ejecución cron  cálculo Haberes y descuentos no formulables totales`);
    await service.calculateTotalAssetsAndDiscounts();
    await logService.saveLog(cronMark);
    Logger.info(`${cronDescription} process finished`);
    return { executionCompleted: true, message: successMessage };
  } catch (error) {
    Logger.error(`${cronDescription} ${error}`);
    await logService.retryLog(cronMark);
    await logService.addRetryTimeToJob(job, retryFrequencyInMinutes);
    return { message: `${cronDescription}  ${error}` };
  } finally {
    done();
  }
};

module.exports = { cronMark, dependencyMark, workerFn };
